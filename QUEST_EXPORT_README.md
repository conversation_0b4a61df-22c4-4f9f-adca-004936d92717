# Quest Export System - Hướng dẫn sử dụng

## Tổng quan

Tính năng Quest Export System cho phép chuyển đổi dữ liệu quest từ file YBQ sang format tương thích với gameserver C#. Hệ thống hỗ trợ các yêu cầu mở rộng như level, job, faction, guild requirements và nhiều loại phần thưởng khác nhau.

## Tính năng chính

### 1. Cấu trúc dữ liệu mở rộng
- **Quest Requirements mở rộng**: Item, Level, Job, Job Level, Ability Point, Faction, Guild
- **Quest Rewards mở rộng**: Item, Experience, Gold, Skill Point
- **Validation tự động**: Ki<PERSON><PERSON> tra tính hợp lệ của dữ liệu export
- **Multi-format export**: JSON, XML, Binary

### 2. Các loại yêu cầu quest được hỗ trợ

#### Item Requirements
```typescript
{
  type: QuestRequirementType.ITEM,
  value: 5,
  itemId: 1001,
  itemAmount: 5,
  mapId: 1,
  coordsX: 100,
  coordsY: 200,
  coordsZ: 0,
  description: "Cần 5 item ID 1001"
}
```

#### Level Requirements
```typescript
{
  type: QuestRequirementType.LEVEL,
  value: 50,
  description: "Yêu cầu level tối thiểu 50"
}
```

#### Job Requirements
```typescript
{
  type: QuestRequirementType.JOB,
  value: 1,
  jobId: 1,
  description: "Phải là nghề Warrior"
}
```

#### Job Level Requirements
```typescript
{
  type: QuestRequirementType.JOB_LEVEL,
  value: 30,
  jobId: 1,
  description: "Level nghề Warrior phải >= 30"
}
```

#### Ability Point Requirements
```typescript
{
  type: QuestRequirementType.ABILITY_POINT,
  value: 0,
  description: "Phải sử dụng hết điểm kỹ năng"
}
```

#### Faction Requirements
```typescript
{
  type: QuestRequirementType.FACTION,
  value: 1,
  factionId: 1,
  description: "Phải thuộc phe phái 1"
}
```

#### Guild Requirements
```typescript
{
  type: QuestRequirementType.GUILD,
  value: 1,
  guildId: 100,
  guildRank: 3,
  description: "Phải có rank >= 3 trong guild 100"
}
```

### 3. Các loại phần thưởng quest được hỗ trợ

#### Item Rewards
```typescript
{
  type: QuestRewardType.ITEM,
  value: 1,
  itemId: 1001,
  itemAmount: 1,
  description: "Legendary Sword"
}
```

#### Experience Rewards
```typescript
{
  type: QuestRewardType.EXPERIENCE,
  value: 10000,
  description: "10,000 điểm kinh nghiệm"
}
```

#### Gold Rewards
```typescript
{
  type: QuestRewardType.GOLD,
  value: 5000,
  description: "5,000 vàng"
}
```

#### Skill Point Rewards
```typescript
{
  type: QuestRewardType.SKILL_POINT,
  value: 3,
  description: "3 điểm kỹ năng"
}
```

## Cách sử dụng

### 1. Import các module cần thiết

```typescript
import { QuestExporter } from '@/lib/quest-exporter';
import { parseYbqFile } from '@/lib/ybq-parser';
import {
  QuestExportConfig,
  QuestExportRequest
} from '@/types/quest-export';
```

### 2. Cấu hình export

```typescript
const exportConfig: QuestExportConfig = {
  includeUnknownFields: true,    // Bao gồm unknown fields để tương thích
  includeDialogs: true,          // Bao gồm dialog text
  includeCoordinates: true,      // Bao gồm tọa độ NPC
  minifyOutput: false,           // Không minify JSON output
  validateRequirements: true     // Validate dữ liệu trước khi export
};
```

### 3. Tạo exporter và export dữ liệu

```typescript
// Tạo exporter
const exporter = new QuestExporter(exportConfig);

// Parse YBQ file
const ybqData = parseYbqFile(fileBuffer);

// Export tất cả quest
const exportData = exporter.exportQuestData(ybqData);

// Export quest cụ thể
const specificExport = exporter.exportQuestData(ybqData, [1001, 1002]);
```

### 4. Export sang các format khác nhau

```typescript
// Export to JSON
const jsonOutput = exporter.exportToJson(exportData);

// Export to XML
const xmlOutput = exporter.exportToXml(exportData);

// Lấy statistics
const stats = exporter.getStats();
console.log(`Exported ${stats.exportedQuests} quests in ${stats.processingTime}ms`);
```

## API Endpoints

### POST /api/quest/export

Export quest data sang format gameserver.

**Request Body:**
```typescript
{
  questIds?: number[],  // Optional: export quest cụ thể
  config: QuestExportConfig,
  format: 'json' | 'xml' | 'binary'
}
```

**Response:**
```typescript
{
  success: boolean,
  message: string,
  data?: QuestExportData,
  fileData?: string,    // Base64 encoded file
  fileName?: string
}
```

### GET /api/quest/export

Lấy thông tin cấu hình export và các format được hỗ trợ.

**Response:**
```typescript
{
  success: boolean,
  message: string,
  data: {
    defaultConfig: QuestExportConfig,
    availableFormats: string[],
    supportedRequirementTypes: string[],
    supportedRewardTypes: string[],
    maxQuestsPerExport: number
  }
}
```

## Cấu trúc file export

### JSON Format
```json
{
  "version": "1.0.0",
  "exportDate": "2025-09-02T12:00:00.000Z",
  "totalQuests": 100,
  "quests": [
    {
      "questId": 1001,
      "questName": "Beginner Quest",
      "questLevel": 1,
      "acceptRequirements": [...],
      "completionRequirements": [...],
      "rewards": [...],
      "questGiver": {...},
      "dialogs": {...},
      "stages": [...],
      "isSpecialQuest": false,
      "category": "normal",
      "unknownFields": {...}
    }
  ],
  "metadata": {
    "originalSign": "YBQ",
    "originalSignEx": "YULGANG_QUEST",
    "exportFormat": "gameserver_csharp"
  }
}
```

## Testing

### Chạy unit tests

```bash
npm test quest-exporter.test.ts
```

### Demo và testing

```typescript
import { demoQuestExport, demoExtendedRequirements } from '@/lib/quest-export-demo';

// Chạy demo export
await demoQuestExport();

// Demo các requirement types mới
demoExtendedRequirements();
```

## Validation

Hệ thống tự động validate:

- **Quest ID**: Phải > 0
- **Quest Name**: Không được rỗng
- **Quest Level**: Phải >= 0
- **Item Requirements**: ItemID và ItemAmount phải > 0
- **Item Rewards**: ItemID và ItemAmount phải > 0
- **NPC Info**: NPCID được khuyến nghị > 0

## Lưu ý quan trọng

1. **Tương thích ngược**: Unknown fields được giữ lại để đảm bảo tương thích với dữ liệu gốc
2. **Performance**: Với quest data lớn, nên sử dụng pagination hoặc export theo batch
3. **Memory**: Minify output để giảm kích thước file khi cần thiết
4. **Validation**: Luôn bật validation để đảm bảo chất lượng dữ liệu

## Troubleshooting

### Lỗi thường gặp

1. **"No quest data found"**: Cần upload YBQ file trước khi export
2. **"Invalid item ID"**: Kiểm tra ItemID trong requirements/rewards
3. **"Quest name is required"**: Quest phải có tên hợp lệ
4. **"Export failed"**: Kiểm tra log để xem chi tiết lỗi

### Debug

```typescript
// Bật debug logging
console.log('Export config:', exportConfig);
console.log('YBQ data loaded:', ybqData.loaded);
console.log('Total quests:', ybqData.totalQuest);

// Kiểm tra validation
const validation = exporter.validateQuest(quest);
if (!validation.isValid) {
  console.log('Validation errors:', validation.errors);
}
```

## Kết luận

Quest Export System cung cấp một giải pháp hoàn chỉnh để chuyển đổi dữ liệu quest từ format YBQ sang format tương thích với gameserver C#. Hệ thống hỗ trợ đầy đủ các yêu cầu mở rộng và đảm bảo tính ổn định, nhất quán của dữ liệu export.