# <PERSON>ệ thống Dashboard Quản trị Game Online Yulgang

Hệ thống quản trị web cho game online Yu<PERSON>gang được xây dựng với Next.js, Better-auth, Drizzle ORM và PostgreSQL.

## 🚀 Tính năng chính

### Authentication & Authorization
- ✅ Hệ thống đăng nhập bảo mật với Better-auth
- ✅ Không cho phép đăng ký công khai
- ✅ Tài khoản admin mặc định được tạo qua script seed
- ✅ Hệ thống phân quyền theo cấp bậc (Admin, Manager, Moderator, Editor)
- ✅ Middleware bảo vệ routes tự động
- ✅ Session management với cookie security

### Role-based Access Control
- **Admin (Level 1)**: Toàn quyền truy cập hệ thống
- **Manager (Level 2)**: <PERSON><PERSON><PERSON><PERSON> l<PERSON> nh<PERSON> vật, chỉnh sửa dữ liệu game
- **Moderator (Level 3)**: <PERSON><PERSON><PERSON><PERSON> lý tính năng má<PERSON> chủ, event, config
- **Editor (Level 4)**: <PERSON><PERSON><PERSON><PERSON> lý tin tức, nội dung

### Dashboard Features
- ✅ Dashboard responsive với sidebar navigation
- ✅ Hiển thị nội dung theo quyền của user
- ✅ Thông tin user và role trong header
- ✅ Charts và analytics (chỉ cho user có quyền)
- ✅ Data tables với permission guard

## 🛠 Công nghệ sử dụng

- **Frontend**: Next.js 15.4.4 (App Router)
- **Authentication**: Better-auth 1.3.4
- **Database**: PostgreSQL với Drizzle ORM 0.44.3
- **UI Components**: Shadcn/ui với Tailwind CSS
- **Icons**: Tabler Icons React
- **Package Manager**: pnpm

## 📦 Cài đặt

1. **Clone repository**
```bash
git clone <repository-url>
cd web-admin
```

2. **Cài đặt dependencies**
```bash
pnpm install
```

3. **Cấu hình environment variables**
Tạo file `.env.local`:
```env
DATABASE_URL="postgresql://username:password@localhost:5432/yulgang_admin"
BETTER_AUTH_SECRET="your-secret-key-here"
BETTER_AUTH_URL="http://localhost:3000"
```

4. **Thiết lập database**
```bash
# Push schema to database
pnpm db:push

# Seed admin account và roles
pnpm db:seed
```

5. **Chạy development server**
```bash
pnpm dev
```

Mở [http://localhost:3000](http://localhost:3000) để xem kết quả.

## 🔐 Tài khoản mặc định

Sau khi chạy `pnpm db:seed`, hệ thống sẽ tạo tài khoản admin mặc định:

- **Email**: `<EMAIL>`
- **Password**: `Admin@123456`
- **Role**: Admin (toàn quyền)

⚠️ **Quan trọng**: Hãy đổi mật khẩu sau lần đăng nhập đầu tiên!

## 📁 Cấu trúc dự án

```
src/
├── app/                    # Next.js App Router
│   ├── dashboard/         # Dashboard pages
│   ├── login/            # Login page
│   ├── register/         # Register disabled page
│   └── api/auth/         # Auth API routes
├── components/            # React components
│   ├── auth/             # Auth-related components
│   ├── ui/               # UI components (shadcn)
│   └── ...               # Other components
├── hooks/                # Custom React hooks
│   └── use-auth.ts       # Auth hooks
├── lib/                  # Utility libraries
│   ├── auth.ts           # Better-auth config
│   ├── auth-utils.ts     # Auth utility functions
│   ├── auth-client.ts    # Client-side auth
│   └── db.ts             # Database connection
├── middleware.ts         # Route protection middleware
└── ...
scripts/
└── seed-admin.ts         # Admin seeding script
auth-schema.ts            # Database schema
```

## 🔧 Scripts có sẵn

```bash
# Development
pnpm dev              # Chạy dev server với Turbopack
pnpm build            # Build production
pnpm start            # Chạy production server

# Database
pnpm db:generate      # Generate migrations
pnpm db:push          # Push schema to database
pnpm db:studio        # Mở Drizzle Studio
pnpm db:migrate       # Run migrations
pnpm db:pull          # Pull schema from database
pnpm db:seed          # Seed admin account và roles

# Code quality
pnpm lint             # Run ESLint
```

## 🛡 Security Features

- **Route Protection**: Middleware tự động redirect user chưa đăng nhập
- **Role-based Access**: Components chỉ hiển thị khi user có quyền
- **Session Security**: Cookie-based sessions với expiration
- **Password Hashing**: Bcrypt với salt rounds 12
- **CSRF Protection**: Built-in với Better-auth
- **Account Status**: Kiểm tra tài khoản active/inactive

## 🎯 Sử dụng Permission System

### Trong Components
```tsx
import { PermissionGuard } from '@/components/auth/permission-guard';
import { PERMISSIONS } from '@/lib/auth-utils';

// Chỉ hiển thị cho user có quyền
<PermissionGuard permission={PERMISSIONS.USER_CREATE}>
  <CreateUserButton />
</PermissionGuard>

// Bảo vệ toàn bộ page
<PageGuard permission={PERMISSIONS.ADMIN_ACCESS}>
  <AdminPanel />
</PageGuard>
```

### Trong Hooks
```tsx
import { usePermission, useAuth } from '@/hooks/use-auth';

function MyComponent() {
  const { user } = useAuth();
  const { hasPermission } = usePermission(PERMISSIONS.USER_DELETE);

  return (
    <div>
      {hasPermission && <DeleteButton />}
    </div>
  );
}
```

## 🔄 Workflow Development

1. **Tạo tính năng mới**: Thêm permission vào `PERMISSIONS` constant
2. **Bảo vệ routes**: Sử dụng `PermissionGuard` hoặc `PageGuard`
3. **Kiểm tra quyền**: Sử dụng `usePermission` hook
4. **Test**: Đăng nhập với các role khác nhau để test

## 📝 Logs và Debugging

- **Authentication errors**: Check browser console và network tab
- **Permission issues**: Kiểm tra user roles trong database
- **Database issues**: Sử dụng `pnpm db:studio` để xem data
- **Middleware logs**: Check server console cho route protection

## 🚀 Production Deployment

1. **Build application**
```bash
pnpm build
```

2. **Set production environment variables**
```env
DATABASE_URL="your-production-db-url"
BETTER_AUTH_SECRET="your-production-secret"
BETTER_AUTH_URL="https://your-domain.com"
```

3. **Run migrations và seeding**
```bash
pnpm db:push
pnpm db:seed
```

4. **Start production server**
```bash
pnpm start
```

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

This project is licensed under the MIT License.
