# Advanced Pagination System

Hệ thống pagination nâng cao đã được thêm vào YBI Editor để cải thiện trải nghiệm người dùng khi làm việc với dữ liệu lớn.

## Tính năng chính

### 🎯 **Pagination ở 2 vị trí**
- **Top Pagination**: Cùng hàng với search box để dễ dàng điều hướng
- **Bottom Pagination**: Ở cuối danh sách dữ liệu như truyền thống

### 🚀 **Quick Jump Controls**
- **Jump to Page**: Nhập số trang để chuyển đến trực tiếp
- **Quick Forward**: Nút tiến nhanh 100, 10, 5 trang (lớn đến nhỏ, từ trong ra ngoài)
- **Quick Backward**: Nút lùi nhanh 5, 10, 100 trang (nhỏ đến lớn, từ ngoài vào trong)
- **First/Last Page**: <PERSON>yển đến trang đầu/cuối ngay lập tức

### 📊 **Thông tin chi tiết**
- Hiển thị số items hiện tại (ví dụ: "Hiển thị 1-50 của 15,171 items")
- Hiển thị trang hiện tại và tổng số trang
- Responsive design cho mobile và desktop

## Cách sử dụng

### 1. Navigation cơ bản
- **Previous/Next**: Sử dụng nút mũi tên để chuyển trang liền kề
- **First/Last**: Sử dụng nút double arrow để chuyển đến trang đầu/cuối

### 2. Quick Jump
- **Nhập số trang**: Gõ số trang vào ô input và nhấn "Đi" hoặc Enter
- **Quick buttons**: Nhấn nút +5, +10, +100 để tiến nhanh
- **Quick buttons**: Nhấn nút -5, -10, -100 để lùi nhanh

### 3. Keyboard shortcuts
- **Enter**: Trong ô jump to page để chuyển đến trang đã nhập
- **Tab**: Di chuyển giữa các controls

## Test Results

Dựa trên test với dữ liệu thực (file YBi.cfg 48MB):

### 📈 **Performance**
- **Large dataset (100k items)**: 0.03ms
- **Small dataset (1k items)**: 0.01ms
- **Memory efficient**: Chỉ render items trên trang hiện tại

### 📊 **Data Scenarios**
- **Items**: 15,171 items → 304 trang (50 items/trang)
- **Skills**: 1,347 skills → 54 trang (25 skills/trang)  
- **NPCs**: 3,134 NPCs → 32 trang (100 NPCs/trang)
- **Maps**: 1,155 maps → 24 trang (50 maps/trang)

### 🔍 **Search Integration**
- Pagination tự động reset về trang 1 khi search
- Hiển thị kết quả search với pagination riêng
- Thông tin filtered results rõ ràng

## UI/UX Improvements

### 🎨 **Visual Design**
- **Compact layout**: Tối ưu không gian màn hình
- **Clear icons**: Sử dụng Lucide icons dễ hiểu
- **Responsive**: Hoạt động tốt trên mobile và desktop
- **Disabled states**: Buttons bị disable khi không thể sử dụng

### 📱 **Mobile Friendly**
- **Flexible layout**: Chuyển từ row sang column trên mobile
- **Touch-friendly**: Buttons đủ lớn cho touch
- **Readable text**: Font size phù hợp cho mobile

### ⚡ **Smart Controls**
- **Conditional buttons**: Chỉ hiển thị quick jump buttons khi cần thiết
- **Auto-validation**: Input validation cho jump to page
- **Edge case handling**: Xử lý tốt các trường hợp biên

## Technical Implementation

### 🔧 **Component Structure**
```typescript
<AdvancedPagination
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={setCurrentPage}
  itemsPerPage={itemsPerPage}
  totalItems={filteredData.length}
  className="optional-styling"
/>
```

### 📍 **Integration Points**
1. **Top of data section**: Cùng hàng với search
2. **Bottom of data section**: Sau bảng dữ liệu
3. **Search integration**: Auto-reset khi search thay đổi
4. **Tab integration**: Reset khi chuyển tab

### 🎛️ **Configuration**
- **Items per page**: Có thể tùy chỉnh cho từng data type
- **Quick jump steps**: 5, 10, 100 trang (có thể mở rộng)
- **Responsive breakpoints**: Tự động adapt layout

## Benefits

### 👥 **User Experience**
- **Faster navigation**: Quick jump giúp di chuyển nhanh trong dataset lớn
- **Better orientation**: Luôn biết đang ở đâu trong dữ liệu
- **Flexible access**: Nhiều cách để đến trang mong muốn
- **Consistent placement**: Pagination ở cả trên và dưới

### 🚀 **Performance**
- **Efficient rendering**: Chỉ render items cần thiết
- **Fast calculations**: Pagination logic tối ưu
- **Memory friendly**: Không load toàn bộ dữ liệu vào DOM

### 🔧 **Developer Experience**
- **Reusable component**: Có thể dùng cho các editor khác
- **Type-safe**: Full TypeScript support
- **Customizable**: Dễ dàng tùy chỉnh styling và behavior

## Future Enhancements

### 🎯 **Planned Features**
- **Items per page selector**: Cho phép user chọn số items/trang
- **Bookmark pages**: Lưu trang yêu thích
- **Keyboard navigation**: Arrow keys để chuyển trang
- **URL persistence**: Lưu trang hiện tại trong URL

### 📊 **Analytics Integration**
- **Usage tracking**: Theo dõi patterns sử dụng pagination
- **Performance monitoring**: Đo thời gian response
- **User behavior**: Hiểu cách user navigate trong dữ liệu

## Migration Notes

### ✅ **Backward Compatibility**
- Không breaking changes cho existing code
- Old pagination vẫn hoạt động nếu không update
- Smooth transition cho users

### 🔄 **Upgrade Path**
1. Import `AdvancedPagination` component
2. Replace old pagination với new component
3. Update styling nếu cần
4. Test với dữ liệu thực

Hệ thống pagination mới này cung cấp trải nghiệm người dùng tốt hơn đáng kể, đặc biệt khi làm việc với datasets lớn như YBI files!
