# YBI Migration Tool - <PERSON><PERSON><PERSON><PERSON> dẫn sử dụng

## Tổng quan

YBI Migration Tool là công cụ cho phép migrate (chuyển đổi) dữ liệu giữa các file YBI với config khác nhau. Tool này đặc biệt hữu ích khi bạn cần:

- <PERSON><PERSON><PERSON>n đổi dữ liệu từ phiên bản Y<PERSON> cũ sang mới
- Migrate text/translation giữa các server khác nhau
- Cập nhật tên, mô tả của items, skills, NPCs, maps từ file khác

## Tính năng chính

### 1. Hỗ trợ nhiều parser config
- V24.2 (Latest) - <PERSON>ên bản mới nhất
- V24.1 - Phiên bản V24.1
- V23 - <PERSON>ên bản V23 với skill format đặc biệt
- V20 (Legacy) - Phi<PERSON><PERSON> b<PERSON>n cũ

### 2. Migration fields được hỗ trợ

#### Items (<PERSON><PERSON><PERSON> phẩ<PERSON>)
- `name` - Tê<PERSON> vật phẩm
- `desc` - <PERSON><PERSON> tả vật phẩm

#### Skills (Kỹ năng)
- `name` - T<PERSON><PERSON> kỹ năng  
- `desc` - Mô tả kỹ năng

#### Abilities (khí công)
- `name` - Tên khí công
- `desc` - Mô tả khí công

#### NPCs
- `name` - Tên NPC
- `desc` - Mô tả chính của NPC
- `desc2` - Mô tả phụ của NPC
- `desc3` - Mô tả bổ sung của NPC

#### Maps (Bản đồ)
- `name` - Tên bản đồ

### 3. Tính năng bảo toàn dữ liệu
- Chỉ thay đổi các field được chọn
- Giữ nguyên cấu trúc file gốc
- Tự động padding với 0x00
- Preserve offset và binary structure

## Cách sử dụng

### Bước 1: Truy cập tool
- Vào Dashboard → Editor Tools
- Chọn tab "YBI Migration"

### Bước 2: Chuẩn bị file
1. **File nguồn (Source)**: File chứa dữ liệu cần migrate
2. **File đích (Target)**: File sẽ được cập nhật

### Bước 3: Cấu hình parser
1. Chọn parser config phù hợp cho file nguồn
2. Chọn parser config phù hợp cho file đích
3. Upload cả hai file

### Bước 4: Chọn fields cần migrate
1. Chuyển qua các tab: Items, Skills, Abilities, NPCs, Maps
2. Tick chọn các field cần migrate
3. **Lưu ý**: Title (danh hiệu) sẽ không được migrate

### Bước 5: Thực hiện migration
1. Nhấn "Thực hiện Migration"
2. Chờ quá trình xử lý hoàn tất
3. Nhấn "Tải file kết quả" để download

## Lưu ý quan trọng

### 1. Backup dữ liệu
- **Luôn backup file gốc** trước khi thực hiện migration
- Tool tạo file mới, không ghi đè file gốc

### 2. Matching logic
- Migration dựa trên ID matching
- Chỉ migrate khi tìm thấy record có cùng ID trong cả hai file
- Nếu không tìm thấy ID tương ứng, record sẽ được bỏ qua

### 3. Encoding
- Sử dụng Latin-1 encoding (tương thích với game client)
- Ký tự không hỗ trợ sẽ được thay bằng '?'

### 4. File size và performance
- Tool xử lý trực tiếp trên decrypted buffer
- Thời gian xử lý phụ thuộc vào kích thước file và số lượng fields

## Ví dụ sử dụng

### Scenario 1: Migrate từ V23 sang V24.2
```
Source: ybi_v23.cfg (config: V23)
Target: ybi_v24.cfg (config: V24.2)
Fields: Items.name, Items.desc, Skills.name, NPCs.name
```

### Scenario 2: Update translation
```
Source: ybi_english.cfg (config: V24.2)
Target: ybi_vietnamese.cfg (config: V24.2)  
Fields: Items.name, Items.desc, Skills.desc, NPCs.desc
```

## Troubleshooting

### Lỗi thường gặp

1. **"Vui lòng chọn config cho file"**
   - Chọn parser config trước khi upload file

2. **"Lỗi khi đọc file"**
   - Kiểm tra file có đúng format không
   - Thử parser config khác

3. **"Chưa có dữ liệu để xuất"**
   - Thực hiện migration trước khi download

4. **Migration count = 0**
   - Kiểm tra ID matching giữa hai file
   - Đảm bảo đã chọn đúng fields

### Tips tối ưu

1. **Kiểm tra trước khi migrate**
   - Xem số lượng records trong mỗi file
   - Đảm bảo có ID matching

2. **Chọn fields phù hợp**
   - Chỉ chọn fields thực sự cần migrate
   - Tránh migrate quá nhiều fields cùng lúc

3. **Test với file nhỏ**
   - Thử nghiệm với file nhỏ trước
   - Kiểm tra kết quả trước khi áp dụng cho file lớn

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra console log để xem chi tiết lỗi
2. Đảm bảo file input đúng format
3. Thử với parser config khác nếu cần
4. Liên hệ dev team nếu vấn đề vẫn tồn tại
