# YBI Migration Tool - C<PERSON><PERSON> thiện mới

## Tổng quan các cải thiện

YBI Migration Tool đã được cải thiện đáng kể với các tính năng mới và hỗ trợ đầy đủ các field thực tế từ các type trong `ybi-parser.ts`.

## 🆕 Tính năng mới

### 1. Migration Profiles (Quick Selection)
Thêm các profile sẵn để chọn nhanh các field cần migrate:

- **🔄 Migrate All**: Migrate tất cả các field có thể
- **📝 Migrate Strings**: Chỉ migrate các field text/string  
- **🔢 Migrate Numbers**: Chỉ migrate các field số
- **🏷️ Names Only**: Chỉ migrate tên (name fields)
- **📄 Descriptions Only**: Chỉ migrate mô tả (desc fields)

### 2. Hỗ trợ đầy đủ các field thực tế

#### Items (YbiItem) - 19 fields
- **String fields**: name, desc
- **Number fields**: level, jobLevel, sex, maxAtk, minAtk, def, weight, shield, gold, recycleGold, nj, el, wx, wxjd, qg_sll, itemType, setBonusDamage, lock

#### Skills (YbiSkill) - 12 fields  
- **String fields**: name, desc
- **Number fields**: job, jobLevel, level, mana, atk, sp, effect, sp_upgrade, atkEachLevel, sp_tt

#### Abilities (YbiAbility) - 17 fields
- **String fields**: name, desc, o_1, o_1_prefix, o_2, o_2_prefix, o_3, o_3_prefix, o_4, o_4_prefix, o_5, o_5_prefix
- **Number fields**: job, level, jobLevel
- **Float fields**: o_1_perLevel, o_2_perLevel, o_3_perLevel, o_4_perLevel, o_5_perLevel

#### NPCs (YbiNpcInfo) - 18 fields
- **String fields**: name, desc, desc2, desc3, desc4, desc5, desc6, desc7, desc8, desc9, desc10, desc11
- **Number fields**: level, hp, menu1, menu2, menu3, menu4

#### Maps (YbiMapInfo) - 12 fields
- **String fields**: name, bgm1, bgm2, u_168, u_1e8, u_268
- **Float fields**: x, y, z, x_2, y_2, z_2

### 3. UI/UX Improvements

#### Category Management
- **Select All/Deselect All** cho mỗi category
- **Selection Counter**: Hiển thị số field đã chọn trong mỗi category
- **Type Badges**: Hiển thị loại dữ liệu của mỗi field (string, number, float)

#### Visual Enhancements
- **Color-coded type badges**:
  - 🔵 String: Blue
  - 🟢 Number: Green  
  - 🟣 Float: Purple
  - 🟠 Boolean: Orange
- **Hover effects** cho các field items
- **Selection summary** với Clear All button

## 🔧 Technical Improvements

### 1. Field Offset Mappings
Tạo các mapping chính xác cho tất cả field offsets:
- `ITEM_FIELD_OFFSETS`: 19 fields với offset và type chính xác
- `SKILL_FIELD_OFFSETS`: 12 fields 
- `ABILITY_FIELD_OFFSETS`: 17 fields
- `NPC_FIELD_OFFSETS`: 18 fields
- `MAP_FIELD_OFFSETS`: 12 fields

### 2. Enhanced Migration Functions
- **Generic field writing**: Hỗ trợ string, number, float, boolean
- **Type-safe migration**: Kiểm tra type và length trước khi write
- **Error handling**: Warning cho unknown fields
- **Improved buffer writing**: Chính xác theo C++ YbiReader

### 3. Helper Functions
- `writeFieldToBuffer()`: Generic function để write các loại data
- `writeStringToBuffer()`: Optimized string writing với padding
- Dynamic profile population dựa trên field types

## 📋 Hướng dẫn sử dụng mới

### Quick Selection với Profiles
1. Chọn một trong các profile sẵn có
2. Profile sẽ tự động chọn các field phù hợp
3. Có thể fine-tune bằng cách chọn/bỏ chọn thêm

### Category-based Selection  
1. Chuyển qua các tab (Items, Skills, Abilities, NPCs, Maps)
2. Sử dụng "Select All" hoặc "Deselect All" cho category
3. Chọn từng field cụ thể nếu cần

### Field Type Awareness
- Quan sát type badge để hiểu loại dữ liệu
- String fields: Migrate text, names, descriptions
- Number fields: Migrate stats, IDs, levels
- Float fields: Migrate coordinates, percentages

## 🚀 Performance & Reliability

### Improved Migration Logic
- **Batch processing**: Xử lý nhiều field cùng lúc
- **Memory efficient**: Sử dụng lại buffer thay vì tạo mới
- **Type validation**: Đảm bảo data integrity

### Error Handling
- **Field validation**: Kiểm tra field tồn tại trước khi migrate
- **Type checking**: Đảm bảo type compatibility
- **Graceful degradation**: Continue migration nếu một field fail

## 🔮 Future Enhancements

### Planned Features
- **Custom Profiles**: Cho phép user tạo profile riêng
- **Field Mapping**: Map field từ source sang target khác tên
- **Batch Migration**: Migrate nhiều file cùng lúc
- **Migration History**: Lưu lại các migration đã thực hiện

### Advanced Options
- **Conditional Migration**: Migrate dựa trên điều kiện
- **Data Transformation**: Transform data trong quá trình migrate
- **Backup Integration**: Tự động backup trước khi migrate

## 📝 Notes

### Breaking Changes
- Không có breaking changes với version cũ
- Tất cả migration cũ vẫn hoạt động bình thường
- Thêm tính năng mới mà không ảnh hưởng existing workflow

### Compatibility
- Hỗ trợ tất cả YBI parser configs (V20, V23, V24.1, V24.2)
- Backward compatible với migration files cũ
- Forward compatible với parser configs mới

---

*Cập nhật: 2025-08-26*
*Version: 2.0.0*
