# YBI Parser Configurations

<PERSON><PERSON> thống YBI Parser đã được cập nhật để hỗ trợ nhiều phiên bản khác nhau của file Ybi.cfg thông qua các parser configurations riêng biệt.

## Tổng quan

Thay vì việc tự động detect version nh<PERSON> trước, giờ đây người dùng có thể chọn parser configuration phù hợp trước khi mở file Ybi.cfg. Điều này giúp:

- **Chính xác hơn**: Tránh việc detect sai version
- **<PERSON>h hoạt hơn**: Người dùng có thể thử các parser khác nhau
- **Rõ ràng hơn**: Biết chính xác parser nào đang được sử dụng
- **<PERSON><PERSON> bảo trì**: Thêm version mới dễ dàng hơn

## C<PERSON><PERSON> Parser Configurations có sẵn

### 1. V24.2 (Latest) - `v24_2`
- **<PERSON><PERSON> tả**: Phiên bản mới nhất với NPC format v24.2 và skill format normal
- **Skill Format**: normal (6992 bytes)
- **NPC Format**: v24_2 (7868 bytes)
- **Max NPCs**: 3,134
- **Sử dụng cho**: File Ybi.cfg mới nhất

### 2. V24.1 - `v24_1`
- **Mô tả**: Phiên bản V24.1 với NPC format v24.1 và skill format normal
- **Skill Format**: normal (6992 bytes)
- **NPC Format**: v24_1 (7868 bytes)
- **Max NPCs**: 3,134
- **Sử dụng cho**: File Ybi.cfg phiên bản ổn định

### 3. V23 - `v23`
- **Mô tả**: Phiên bản V23 với skill format v23 và NPC format v20
- **Skill Format**: v23 (6928 bytes)
- **NPC Format**: v20 (7860 bytes)
- **Max NPCs**: 2,182
- **Sử dụng cho**: File Ybi.cfg cũ hơn với skill format khác

### 4. V20 (Legacy) - `v20`
- **Mô tả**: Phiên bản cũ V20 với NPC format v20 và skill format normal
- **Skill Format**: normal (6992 bytes)
- **NPC Format**: v20 (7860 bytes)
- **Max NPCs**: 2,182
- **Sử dụng cho**: File Ybi.cfg rất cũ (legacy)

## Cách sử dụng

### 1. Trong UI (Web Admin)

1. Nhấn nút "Mở File" trong YBI Editor
2. Chọn file Ybi.cfg từ máy tính
3. Dialog "Chọn Parser Version" sẽ hiện ra
4. Chọn parser configuration phù hợp
5. Nhấn "Xác nhận" để parse file

### 2. Trong Code

```typescript
import { YbiParser, YBI_PARSER_CONFIGS, getParserConfig } from '@/lib/parsers/ybi-parser';

// Sử dụng auto-detection (như trước)
const autoFile = YbiParser.parse(buffer, 'Ybi.cfg');

// Sử dụng parser config cụ thể
const v24Config = getParserConfig('v24_2');
const manualFile = YbiParser.parseWithConfig(buffer, v24Config, 'Ybi.cfg');

// Lấy tất cả configs có sẵn
console.log(YBI_PARSER_CONFIGS);
```

## Gợi ý chọn Parser

### Làm sao biết file của tôi thuộc version nào?

1. **Thử V24.2 trước**: Đây là version mới nhất và phổ biến nhất
2. **Kiểm tra số lượng data**:
   - Nếu có > 3000 NPCs → V24.x
   - Nếu có < 2500 NPCs → V23 hoặc V20
   - Nếu skills ít bất thường → thử V23
3. **Thử từng version**: Nếu không chắc, thử từng version và xem kết quả nào hợp lý nhất

### Dấu hiệu parser sai

- **Số lượng items/skills/NPCs bất thường** (quá ít hoặc quá nhiều)
- **Tên items/skills hiển thị lỗi** (ký tự lạ, rỗng)
- **Lỗi parsing** (crash, exception)

## Thêm Parser Configuration mới

Để thêm parser config mới, cập nhật file `src/lib/parsers/ybi-parser.ts`:

```typescript
export const YBI_PARSER_CONFIGS: YbiParserConfig[] = [
  // ... existing configs
  {
    id: 'v25',
    name: 'V25 (New)',
    description: 'Phiên bản V25 mới với format cập nhật',
    skillFormat: 'normal',
    npcFormat: 'v25',
    constants: {
      skillByteLength: 7000,
      skillByteSize: 0x1A8,
      npcByteLength: 8000,
      maxNpcs: 4000,
      npcOffsetAdjustment: 3200,
      mapOffsetAdjustment: 0
    }
  }
];
```

## Testing

Sử dụng test script để kiểm tra các parser configs:

```bash
npx tsx scripts/test-parser-configs.ts
```

Script này sẽ:
- Liệt kê tất cả configs có sẵn
- Test helper functions
- So sánh sự khác biệt giữa các configs
- Test parsing với file thật (nếu có)

## Lưu ý kỹ thuật

- **Auto-detection vẫn hoạt động**: Phương thức `YbiParser.parse()` vẫn tự động detect version
- **Backward compatibility**: Code cũ vẫn hoạt động bình thường
- **Parser config được lưu**: Mỗi YbiFile sẽ lưu parser config đã sử dụng
- **Generate file**: Khi save file, sẽ sử dụng parser config gốc để đảm bảo tính nhất quán
