-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE SEQUENCE "public"."tbl_xwwl_pvp_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE SEQUENCE "public"."thienmathancung_danhsach_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE TABLE "bachbaocacrecord" (
	"id" serial NOT NULL,
	"userid" text,
	"username" text,
	"vatpham_id" text,
	"vatphamten" text,
	"vatphamsoluong" integer,
	"nguyenbaosoluong" integer,
	"thoigian" timestamp
);
--> statement-breakpoint
CREATE TABLE "bangchien_tiendatcuoc" (
	"id" serial NOT NULL,
	"userid" text,
	"username" text,
	"bangphaiid" integer,
	"nguyenbaosoluong" integer
);
--> statement-breakpoint
CREATE TABLE "congthanhchien_thanhchu" (
	"id" serial NOT NULL,
	"congthanhchien_tenbang" text,
	"tenthanhchu" text,
	"bangphaiid" integer,
	"congthanhthoigian" timestamp,
	"congthanhbanthuongthoigian" timestamp
);
--> statement-breakpoint
CREATE TABLE "drugrecord" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "drugrecord_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"accountid" varchar NOT NULL,
	"charactername" varchar NOT NULL,
	"itemid" integer NOT NULL,
	"amount" integer NOT NULL,
	"created_at" date DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "eventtop" (
	"id" serial NOT NULL,
	"tennhanvat" text,
	"bangphai" text,
	"theluc" text,
	"dangcap" integer,
	"gietnguoisoluong" integer,
	"tuvongsoluong" integer,
	"phankhutintuc" text,
	"diem_chinhphai" integer,
	"diem_taphai" integer,
	"createdat" date DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "eventtop_dch" (
	"id" serial NOT NULL,
	"tennhanvat" text,
	"bangphai" text,
	"theluc" text,
	"dangcap" integer,
	"gietnguoisoluong" integer,
	"tuvongsoluong" integer,
	"phankhutintuc" text,
	"dame_tru" integer,
	"hoptacgietnguoi" integer,
	"diem_dch_chinhphai" integer,
	"diem_dch_taphai" integer,
	"createdat" date DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "exchangecharacter" (
	"id" bigserial NOT NULL,
	"buyer_id" text,
	"seller_id" text,
	"character" text,
	"status" text,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "giftcode" (
	"id" serial NOT NULL,
	"username" text,
	"code" text,
	"cash" integer,
	"bonus" integer,
	"noi_dung" text,
	"da_su_dung" integer,
	"nguoi_su_dung" text
);
--> statement-breakpoint
CREATE TABLE "itemrecord" (
	"id" serial NOT NULL,
	"userid" text,
	"username" text,
	"touserid" text,
	"tousername" text,
	"global_id" text,
	"vatpham_id" text,
	"vatphamten" text,
	"vatphamsoluong" integer,
	"vatphamthuoctinh" text,
	"sotien" integer,
	"loaihinh" text,
	"thoigian" timestamp
);
--> statement-breakpoint
CREATE TABLE "log_deleteitem" (
	"id" serial NOT NULL,
	"maitem" integer,
	"iditem" integer,
	"thoigian" timestamp,
	"levelitem" text,
	"username" text,
	"trangthai" integer
);
--> statement-breakpoint
CREATE TABLE "log_thelucchien" (
	"id" serial NOT NULL,
	"fld_name" text,
	"giet" integer,
	"chet" integer,
	"ngay" date,
	"theluc" text,
	"monphai" text
);
--> statement-breakpoint
CREATE TABLE "loginrecord" (
	"id" serial NOT NULL,
	"userid" text,
	"username" text,
	"userip" text,
	"loaihinh" text,
	"thoigian" timestamp,
	"mac_address" text
);
--> statement-breakpoint
CREATE TABLE "loginrecord_mac" (
	"id" serial NOT NULL,
	"userid" text,
	"username" text,
	"userip" text,
	"loaihinh" text,
	"thoigian" timestamp,
	"mac_address" text,
	"serverid" integer
);
--> statement-breakpoint
CREATE TABLE "logpk" (
	"id" serial NOT NULL,
	"nguoigiet" text,
	"nguoibigiet" text,
	"thoigian" timestamp
);
--> statement-breakpoint
CREATE TABLE "logshop" (
	"id" serial NOT NULL,
	"userid" text,
	"username" text,
	"maitem" integer,
	"iditem" integer,
	"tenitem" text,
	"magic1" integer,
	"magic2" integer,
	"magic3" integer,
	"magic4" integer,
	"magic5" integer,
	"soluong" integer,
	"giatien" integer,
	"thoigian" timestamp,
	"cashconlai" integer,
	"co_hay_khong_mo_ra" integer,
	"da_su_dung" integer,
	"mua_id" integer,
	"thanh_cong" integer
);
--> statement-breakpoint
CREATE TABLE "logshopvohuan" (
	"id" serial NOT NULL,
	"userid" text,
	"username" text,
	"maitem" integer,
	"iditem" integer,
	"tenitem" text,
	"magic1" integer,
	"magic2" integer,
	"magic3" integer,
	"magic4" integer,
	"magic5" integer,
	"soluong" integer,
	"giatien" integer,
	"thoigian" timestamp,
	"vohuanconlai" integer,
	"co_hay_khong_mo_ra" integer,
	"da_su_dung" integer,
	"mua_id" integer,
	"thanh_cong" integer
);
--> statement-breakpoint
CREATE TABLE "syntheticrecord" (
	"id" serial PRIMARY KEY NOT NULL,
	"fld_id" varchar(30) NOT NULL,
	"fld_name" varchar(30) NOT NULL,
	"fld_qjid" bigint,
	"fld_pid" integer,
	"fld_iname" varchar(50),
	"fld_magic0" integer DEFAULT 0,
	"fld_magic1" integer DEFAULT 0,
	"fld_magic2" integer DEFAULT 0,
	"fld_magic3" integer DEFAULT 0,
	"fld_magic4" integer DEFAULT 0,
	"fld_type" varchar(30),
	"fld_czid" integer,
	"fld_success" varchar(30),
	"fld_qhjd" integer DEFAULT 0,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
CREATE TABLE "tbl_faction_quest_progress" (
	"id" serial NOT NULL,
	"factionid" integer,
	"questid" integer,
	"currentcount" integer,
	"status" smallint,
	"acceptedtime" timestamp,
	"lastupdatetime" timestamp,
	"completedtime" timestamp,
	"cancelledtime" timestamp,
	"lastresettime" timestamp,
	"lastcompletedtime" timestamp
);
--> statement-breakpoint
CREATE TABLE "tbl_group_quest" (
	"id" serial NOT NULL,
	"questname" text,
	"questdesc" text,
	"questtype" integer,
	"targettype" integer,
	"targetid" integer,
	"targetcount" integer,
	"requiredlevel" integer,
	"rewardexp" integer,
	"rewardmoney" bigint,
	"rewarditem" integer,
	"rewarditemcount" integer,
	"resettype" integer,
	"isactive" boolean,
	"createdate" timestamp
);
--> statement-breakpoint
CREATE TABLE "tbl_group_quest_contribution" (
	"id" serial NOT NULL,
	"questid" integer,
	"progressid" integer,
	"playerid" integer,
	"playername" text,
	"guildid" integer,
	"factionid" integer,
	"actiontype" integer,
	"targetid" integer,
	"targetname" text,
	"contributioncount" integer,
	"contributiontime" timestamp,
	"hasreceivedreward" boolean
);
--> statement-breakpoint
CREATE TABLE "tbl_group_quest_contribution_log" (
	"id" serial NOT NULL,
	"contributionid" integer,
	"progressid" integer,
	"playerid" integer,
	"playername" text,
	"actiontype" integer,
	"targetid" integer,
	"targetname" text,
	"contributioncount" integer,
	"contributiontime" timestamp
);
--> statement-breakpoint
CREATE TABLE "tbl_group_quest_history" (
	"id" serial NOT NULL,
	"questid" integer,
	"guildid" integer,
	"guildname" text,
	"factionid" integer,
	"completedtime" timestamp,
	"completedby" text
);
--> statement-breakpoint
CREATE TABLE "tbl_guild_quest_progress" (
	"id" serial NOT NULL,
	"guildid" integer,
	"questid" integer,
	"currentcount" integer,
	"status" smallint,
	"acceptedtime" timestamp,
	"lastupdatetime" timestamp,
	"completedtime" timestamp,
	"cancelledtime" timestamp,
	"lastresettime" timestamp,
	"lastcompletedtime" timestamp
);
--> statement-breakpoint
CREATE TABLE "tbl_sudosolieu" (
	"id" serial NOT NULL,
	"fld_tname" text,
	"fld_index" integer,
	"fld_sname" text,
	"fld_tlevel" integer,
	"fld_stlevel" integer,
	"fld_styhd" integer,
	"fld_stwg1" integer,
	"fld_stwg2" integer,
	"fld_stwg3" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_truyenthuhethong" (
	"id" serial NOT NULL,
	"nguoinhanthu_nhatvatten" text,
	"guithu_npc" integer,
	"nguoiguithu_ten" text,
	"truyenthunoidung" text,
	"truyenthuthoigian" timestamp,
	"danhdaudaxem" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_vinhduhethong" (
	"id" serial NOT NULL,
	"fld_type" integer,
	"fld_nghenghiep" integer,
	"fld_tennhanvat" text,
	"fld_dangcap" integer,
	"fld_theluc" integer,
	"fld_bangphai" text,
	"fld_bangphai_bangchu" text,
	"fld_diemso" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_char" (
	"id" serial PRIMARY KEY NOT NULL,
	"fld_id" text,
	"fld_name" text,
	"fld_index" integer,
	"fld_level" integer DEFAULT 1,
	"fld_face" "bytea",
	"fld_job" integer,
	"fld_exp" text,
	"fld_zx" integer DEFAULT 0,
	"fld_job_level" integer DEFAULT 0,
	"fld_x" double precision DEFAULT 422,
	"fld_y" double precision DEFAULT 2162,
	"fld_z" double precision DEFAULT 15,
	"fld_menow" integer DEFAULT 101,
	"fld_money" text,
	"fld_hp" integer DEFAULT 0,
	"fld_mp" integer DEFAULT 0,
	"fld_sp" integer DEFAULT 0,
	"fld_wx" integer DEFAULT 0,
	"fld_se" integer DEFAULT 0,
	"fld_point" integer DEFAULT 0,
	"fld_skills" "bytea",
	"fld_wearitem" "bytea",
	"fld_item" "bytea",
	"fld_qitem" "bytea",
	"fld_ntcitem" "bytea",
	"fld_coatitem" "bytea",
	"fld_kongfu" "bytea",
	"fld_hits" "bytea",
	"fld_doors" "bytea",
	"fld_quest" "bytea",
	"fld_lumpid" integer DEFAULT 0,
	"fld_fight_exp" integer DEFAULT 0,
	"fld_j9" integer DEFAULT 0,
	"fld_jq" integer DEFAULT 0,
	"fld_jl" text,
	"fld_nametype" "bytea",
	"fld_zbver" integer DEFAULT 1,
	"fld_zztype" integer DEFAULT 0,
	"fld_zzsl" integer DEFAULT 0,
	"fld_ctime" "bytea",
	"fld_ctimenew" "bytea",
	"fld_stime" "bytea",
	"fld_qlname" text,
	"fld_qljzname" text,
	"fld_qldu" integer DEFAULT 0,
	"fld_qldumax" integer DEFAULT 0,
	"fld_qlrank" integer DEFAULT 0,
	"fld_thangthienkhicong" "bytea",
	"fld_thangthienvocong" "bytea",
	"fld_thangthienlichluyen" integer DEFAULT 0,
	"fld_thangthienvocongdiemso" integer DEFAULT 0,
	"fld_add_hp" integer DEFAULT 0,
	"fld_add_at" integer DEFAULT 0,
	"fld_add_df" integer DEFAULT 0,
	"fld_add_hb" integer DEFAULT 0,
	"fld_add_mp" integer DEFAULT 0,
	"fld_add_mz" integer DEFAULT 0,
	"fld_zs" integer DEFAULT 0,
	"fld_online" integer DEFAULT 0,
	"fld_get_wx" integer DEFAULT 0,
	"fld_tongkim" integer DEFAULT 0,
	"fld_taisinh" integer DEFAULT 0,
	"fld_vipdj" integer DEFAULT 0,
	"在线时间" "bytea",
	"fld_七彩" integer DEFAULT 0,
	"fld_vip_at" integer DEFAULT 0,
	"fld_vip_df" integer DEFAULT 0,
	"fld_vip_hp" integer DEFAULT 0,
	"fld_vip_level" integer DEFAULT 0,
	"fld_zscs" integer DEFAULT 0,
	"fld_sjjl" integer DEFAULT 0,
	"fld_在线时间" double precision DEFAULT 0,
	"fld_在线等级" integer,
	"fld_领奖标志" integer DEFAULT 0,
	"fld_reserved" integer DEFAULT 0,
	"fld_签名类型" integer DEFAULT 0,
	"fld_任务等级4" integer DEFAULT 0,
	"fld_师傅" text,
	"fld_徒弟1" text,
	"fld_徒弟2" text,
	"fld_徒弟3" text,
	"fld_师徒武功1_1" integer DEFAULT 0,
	"fld_师徒武功1_2" integer DEFAULT 0,
	"fld_师徒武功1_3" integer DEFAULT 0,
	"fld_dayquest" text,
	"fld_tlc" integer DEFAULT 0,
	"fld_fqid" text,
	"solangietnguoi" bigint DEFAULT 0,
	"bigietsolan" bigint DEFAULT 0,
	"fld_suphu" "bytea",
	"fld_detu" "bytea",
	"fld_sudovocong" "bytea",
	"fld_giaitruthoigian" text,
	"fld_titlepoints" integer DEFAULT 0,
	"congthanhchienthoigian" timestamp,
	"fld_xb" integer DEFAULT 0,
	"fld_rosetitlepoints" integer DEFAULT 0,
	"fld_speakingtype" integer DEFAULT 0,
	"fld_nszitem" "bytea",
	"fld_ljkongfu" "bytea",
	"bangphai_doconghien" integer DEFAULT 0,
	"fld_mlz" integer DEFAULT 0,
	"fld_pvp_piont" integer DEFAULT 0,
	"fld_thannuvocongdiemso" integer DEFAULT 0,
	"fld_love_word" text,
	"fld_marital_status" integer DEFAULT 0,
	"fld_married" integer DEFAULT 0,
	"fld_jh_date" timestamp,
	"fld_fb_time" integer,
	"fld_lost_wx" integer DEFAULT 0,
	"fld_hd_time" integer DEFAULT 0,
	"fld_kieutoc" "bytea",
	"fld_khuonmat" "bytea",
	"fld_whtb" integer DEFAULT 0,
	"fld_chtime" "bytea",
	"fld_config" text,
	"fld_fashion_item" "bytea",
	"fld_quest_finish" "bytea",
	"fld_add_clvc" integer DEFAULT 0,
	"fld_add_ptvc" integer DEFAULT 0,
	"fld_add_kc" integer DEFAULT 0,
	"version" integer DEFAULT 24,
	"nhanqualandau" boolean DEFAULT false,
	"tlc_random_phe" text,
	"vohuan_gioihan_theongay" integer DEFAULT 10000,
	"vohuan_time" text,
	"fld_moneyextralevel" integer DEFAULT 0,
	"fld_pinkbag_item" "bytea",
	"fld_asc7_anti_qigong" "bytea"
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_cw" (
	"id" serial NOT NULL,
	"zrname" text,
	"itmeid" integer,
	"name" text,
	"fld_zcd" integer,
	"fld_exp" text,
	"fld_level" integer,
	"fld_bs" integer,
	"fld_job" integer,
	"fld_job_level" integer,
	"fld_hp" integer,
	"fld_mp" integer,
	"fld_kongfu" "bytea",
	"fld_wearitem" "bytea",
	"fld_item" "bytea",
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_magic5" integer,
	"fld_sxbl" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_cwarehouse" (
	"id" serial NOT NULL,
	"fld_id" text,
	"fld_name" text,
	"fld_item" "bytea"
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_guild" (
	"id" serial PRIMARY KEY NOT NULL,
	"g_name" text NOT NULL,
	"g_master" text,
	"g_notice" text,
	"leve" integer,
	"thanhdanh" integer,
	"monhuy" "bytea",
	"monphucword" integer,
	"monphucmausac" integer,
	"bangphaivohuan" integer,
	"thang" integer,
	"thua" integer,
	"hoa" integer,
	"monphaitaisan" text,
	"thongbao_congthanh" integer,
	"lienminh_minhchu" text,
	"createdat" timestamp DEFAULT now() NOT NULL,
	"updatedat" timestamp DEFAULT now() NOT NULL,
	"active" boolean DEFAULT true NOT NULL,
	CONSTRAINT "tbl_xwwl_guild_unique" UNIQUE("g_name")
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_guildmember" (
	"id" serial NOT NULL,
	"fld_name" text,
	"g_name" text,
	"leve" integer,
	"fld_level" integer,
	"fld_guildpoint" integer,
	"fld_newguildpoint" integer,
	"active" boolean DEFAULT true NOT NULL,
	"createdat" date DEFAULT now() NOT NULL,
	"updatedat" date DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_pklog" (
	"id" serial NOT NULL,
	"fld_killer" text,
	"fld_killer_guild" text,
	"fld_death" text,
	"fld_death_guild" text,
	"fld_num" integer,
	"fld_wx" integer,
	"fld_lasttime" timestamp
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_publicwarehouse" (
	"id" serial NOT NULL,
	"fld_id" text,
	"fld_money" text,
	"fld_item" "bytea",
	"fld_itime" "bytea",
	"fld_zbver" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_rosetop" (
	"id" serial NOT NULL,
	"fld_name" text,
	"fld_sex" integer,
	"fld_zx" integer,
	"fld_innum" integer,
	"fld_outnum" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_warehouse" (
	"id" serial NOT NULL,
	"fld_id" text,
	"fld_name" text,
	"fld_money" text,
	"fld_item" "bytea"
);
--> statement-breakpoint
CREATE TABLE "vinhdubangphaixephang" (
	"id" serial NOT NULL,
	"fld_name" text,
	"fld_zx" integer,
	"fld_level" integer,
	"fld_bp" text,
	"fld_job" integer,
	"fld_job_level" integer,
	"fld_ry" integer,
	"thoigian" timestamp,
	"fld_fq" text
);
--> statement-breakpoint
CREATE TABLE "xwwl_gameserverinfo" (
	"serverid" integer,
	"itemcount" integer
);
--> statement-breakpoint
CREATE TABLE "thienmathancung_danhsach" (
	"bang_chiem_thanh" text,
	"ngay_chiem_thanh" text,
	"cong_thanh_cuonghoa_level" text,
	"thoigian_lammoi_congthanh" timestamp,
	"id" serial PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_pvp" (
	"santapten" text,
	"a_nguoichoi" text,
	"b_nguoichoi" text,
	"agietnguoisoluong" integer,
	"bgietnguoisoluong" integer,
	"a_chaytronsolan" integer,
	"b_chaytronsolan" integer,
	"athuduocnguyenbao" integer,
	"bthuduocnguyenbao" integer,
	"tranhtaiketqua" text,
	"id" serial PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE INDEX "idx_syntheticrecord_created_at" ON "syntheticrecord" USING btree ("created_at" timestamp_ops);--> statement-breakpoint
CREATE INDEX "idx_syntheticrecord_fld_id" ON "syntheticrecord" USING btree ("fld_id" text_ops);--> statement-breakpoint
CREATE INDEX "idx_syntheticrecord_fld_name" ON "syntheticrecord" USING btree ("fld_name" text_ops);--> statement-breakpoint
CREATE INDEX "idx_syntheticrecord_fld_pid" ON "syntheticrecord" USING btree ("fld_pid" int4_ops);--> statement-breakpoint
CREATE INDEX "idx_syntheticrecord_fld_success" ON "syntheticrecord" USING btree ("fld_success" text_ops);--> statement-breakpoint
CREATE INDEX "idx_syntheticrecord_fld_type" ON "syntheticrecord" USING btree ("fld_type" text_ops);--> statement-breakpoint
CREATE INDEX "tbl_xwwl_char_fld_id_idx" ON "tbl_xwwl_char" USING btree ("fld_id" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "tbl_xwwl_char_fld_name_idx" ON "tbl_xwwl_char" USING btree ("fld_name" text_ops);--> statement-breakpoint
CREATE VIEW "public"."v_synthesis_summary" AS (SELECT fld_name AS character_name, fld_type AS synthesis_type, fld_success AS result, count(*) AS total_attempts, count( CASE WHEN fld_success::text = 'Success'::text THEN 1 ELSE NULL::integer END) AS successful_attempts, count( CASE WHEN fld_success::text = 'Failed'::text THEN 1 ELSE NULL::integer END) AS failed_attempts, round(count( CASE WHEN fld_success::text = 'Success'::text THEN 1 ELSE NULL::integer END)::numeric * 100.0 / count(*)::numeric, 2) AS success_rate_percent FROM syntheticrecord WHERE created_at >= (CURRENT_DATE - '30 days'::interval) GROUP BY fld_name, fld_type, fld_success ORDER BY fld_name, fld_type);
*/