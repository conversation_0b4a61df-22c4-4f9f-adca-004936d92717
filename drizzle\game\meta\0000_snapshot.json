{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.bachbaocacrecord": {"name": "bachbaocacrecord", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "userid": {"name": "userid", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "vatpham_id": {"name": "vatpham_id", "type": "text", "primaryKey": false, "notNull": false}, "vatphamten": {"name": "vat<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "vatphamsoluong": {"name": "vatphamsoluong", "type": "integer", "primaryKey": false, "notNull": false}, "nguyenbaosoluong": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.bangchien_tiendatcuoc": {"name": "bangchien_tiendatcuoc", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "userid": {"name": "userid", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "bangphaiid": {"name": "bangphaii<PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "nguyenbaosoluong": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.congthanhchien_thanhchu": {"name": "congth<PERSON><PERSON><PERSON><PERSON>_thanhchu", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "congthanhchien_tenbang": {"name": "congth<PERSON><PERSON><PERSON><PERSON>_tenbang", "type": "text", "primaryKey": false, "notNull": false}, "tenthanhchu": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "bangphaiid": {"name": "bangphaii<PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "congthanhthoigian": {"name": "congthanhthoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "congthanhbanthuongthoigian": {"name": "congthanhbanthuongthoigian", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.drugrecord": {"name": "drugrecord", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "drugrecord_id_seq", "increment": "1", "minValue": "1", "maxValue": "**********", "startWith": "1", "cache": "1", "cycle": false, "schema": "public"}}, "accountid": {"name": "accountid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "charactername": {"name": "charactername", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "itemid": {"name": "itemid", "type": "integer", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "date", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.eventtop": {"name": "eventtop", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "tennhanvat": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "bangphai": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "theluc": {"name": "theluc", "type": "text", "primaryKey": false, "notNull": false}, "dangcap": {"name": "dangcap", "type": "integer", "primaryKey": false, "notNull": false}, "gietnguoisoluong": {"name": "gietnguoisoluong", "type": "integer", "primaryKey": false, "notNull": false}, "tuvongsoluong": {"name": "tuvon<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "phankhutintuc": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "diem_chinhphai": {"name": "diem_chin<PERSON>hai", "type": "integer", "primaryKey": false, "notNull": false}, "diem_taphai": {"name": "diem_taphai", "type": "integer", "primaryKey": false, "notNull": false}, "createdat": {"name": "createdat", "type": "date", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.eventtop_dch": {"name": "eventtop_dch", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "tennhanvat": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "bangphai": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "theluc": {"name": "theluc", "type": "text", "primaryKey": false, "notNull": false}, "dangcap": {"name": "dangcap", "type": "integer", "primaryKey": false, "notNull": false}, "gietnguoisoluong": {"name": "gietnguoisoluong", "type": "integer", "primaryKey": false, "notNull": false}, "tuvongsoluong": {"name": "tuvon<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "phankhutintuc": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "dame_tru": {"name": "dame_tru", "type": "integer", "primaryKey": false, "notNull": false}, "hoptacgietnguoi": {"name": "hoptacgietnguoi", "type": "integer", "primaryKey": false, "notNull": false}, "diem_dch_chinhphai": {"name": "diem_dch_chin<PERSON>hai", "type": "integer", "primaryKey": false, "notNull": false}, "diem_dch_taphai": {"name": "diem_dch_taphai", "type": "integer", "primaryKey": false, "notNull": false}, "createdat": {"name": "createdat", "type": "date", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.exchangecharacter": {"name": "exchangecharacter", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": false, "notNull": true}, "buyer_id": {"name": "buyer_id", "type": "text", "primaryKey": false, "notNull": false}, "seller_id": {"name": "seller_id", "type": "text", "primaryKey": false, "notNull": false}, "character": {"name": "character", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.giftcode": {"name": "giftcode", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "cash": {"name": "cash", "type": "integer", "primaryKey": false, "notNull": false}, "bonus": {"name": "bonus", "type": "integer", "primaryKey": false, "notNull": false}, "noi_dung": {"name": "noi_dung", "type": "text", "primaryKey": false, "notNull": false}, "da_su_dung": {"name": "da_su_dung", "type": "integer", "primaryKey": false, "notNull": false}, "nguoi_su_dung": {"name": "nguoi_su_dung", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.itemrecord": {"name": "itemrecord", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "userid": {"name": "userid", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "touserid": {"name": "to<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "tousername": {"name": "tousername", "type": "text", "primaryKey": false, "notNull": false}, "global_id": {"name": "global_id", "type": "text", "primaryKey": false, "notNull": false}, "vatpham_id": {"name": "vatpham_id", "type": "text", "primaryKey": false, "notNull": false}, "vatphamten": {"name": "vat<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "vatphamsoluong": {"name": "vatphamsoluong", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamthuoctinh": {"name": "vat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h", "type": "text", "primaryKey": false, "notNull": false}, "sotien": {"name": "so<PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "loaihinh": {"name": "loaihinh", "type": "text", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.log_deleteitem": {"name": "log_deleteitem", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "maitem": {"name": "maitem", "type": "integer", "primaryKey": false, "notNull": false}, "iditem": {"name": "iditem", "type": "integer", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "levelitem": {"name": "levelitem", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "trangthai": {"name": "trang<PERSON>i", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.log_thelucchien": {"name": "log_thelucchien", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "giet": {"name": "giet", "type": "integer", "primaryKey": false, "notNull": false}, "chet": {"name": "chet", "type": "integer", "primaryKey": false, "notNull": false}, "ngay": {"name": "ngay", "type": "date", "primaryKey": false, "notNull": false}, "theluc": {"name": "theluc", "type": "text", "primaryKey": false, "notNull": false}, "monphai": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.loginrecord": {"name": "loginrecord", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "userid": {"name": "userid", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "userip": {"name": "userip", "type": "text", "primaryKey": false, "notNull": false}, "loaihinh": {"name": "loaihinh", "type": "text", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "mac_address": {"name": "mac_address", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.loginrecord_mac": {"name": "loginrecord_mac", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "userid": {"name": "userid", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "userip": {"name": "userip", "type": "text", "primaryKey": false, "notNull": false}, "loaihinh": {"name": "loaihinh", "type": "text", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "mac_address": {"name": "mac_address", "type": "text", "primaryKey": false, "notNull": false}, "serverid": {"name": "serverid", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.logpk": {"name": "logpk", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "nguoigiet": {"name": "nguoigiet", "type": "text", "primaryKey": false, "notNull": false}, "nguoibigiet": {"name": "nguoibigiet", "type": "text", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.logshop": {"name": "logshop", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "userid": {"name": "userid", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "maitem": {"name": "maitem", "type": "integer", "primaryKey": false, "notNull": false}, "iditem": {"name": "iditem", "type": "integer", "primaryKey": false, "notNull": false}, "tenitem": {"name": "tenitem", "type": "text", "primaryKey": false, "notNull": false}, "magic1": {"name": "magic1", "type": "integer", "primaryKey": false, "notNull": false}, "magic2": {"name": "magic2", "type": "integer", "primaryKey": false, "notNull": false}, "magic3": {"name": "magic3", "type": "integer", "primaryKey": false, "notNull": false}, "magic4": {"name": "magic4", "type": "integer", "primaryKey": false, "notNull": false}, "magic5": {"name": "magic5", "type": "integer", "primaryKey": false, "notNull": false}, "soluong": {"name": "soluong", "type": "integer", "primaryKey": false, "notNull": false}, "giatien": {"name": "<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "cashconlai": {"name": "cashconlai", "type": "integer", "primaryKey": false, "notNull": false}, "co_hay_khong_mo_ra": {"name": "co_hay_khong_mo_ra", "type": "integer", "primaryKey": false, "notNull": false}, "da_su_dung": {"name": "da_su_dung", "type": "integer", "primaryKey": false, "notNull": false}, "mua_id": {"name": "mua_id", "type": "integer", "primaryKey": false, "notNull": false}, "thanh_cong": {"name": "thanh_cong", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.logshopvohuan": {"name": "logshopvohuan", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "userid": {"name": "userid", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "maitem": {"name": "maitem", "type": "integer", "primaryKey": false, "notNull": false}, "iditem": {"name": "iditem", "type": "integer", "primaryKey": false, "notNull": false}, "tenitem": {"name": "tenitem", "type": "text", "primaryKey": false, "notNull": false}, "magic1": {"name": "magic1", "type": "integer", "primaryKey": false, "notNull": false}, "magic2": {"name": "magic2", "type": "integer", "primaryKey": false, "notNull": false}, "magic3": {"name": "magic3", "type": "integer", "primaryKey": false, "notNull": false}, "magic4": {"name": "magic4", "type": "integer", "primaryKey": false, "notNull": false}, "magic5": {"name": "magic5", "type": "integer", "primaryKey": false, "notNull": false}, "soluong": {"name": "soluong", "type": "integer", "primaryKey": false, "notNull": false}, "giatien": {"name": "<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "vohuanconlai": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "co_hay_khong_mo_ra": {"name": "co_hay_khong_mo_ra", "type": "integer", "primaryKey": false, "notNull": false}, "da_su_dung": {"name": "da_su_dung", "type": "integer", "primaryKey": false, "notNull": false}, "mua_id": {"name": "mua_id", "type": "integer", "primaryKey": false, "notNull": false}, "thanh_cong": {"name": "thanh_cong", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.syntheticrecord": {"name": "syntheticrecord", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "fld_id": {"name": "fld_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "fld_name": {"name": "fld_name", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "fld_qjid": {"name": "fld_qjid", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_iname": {"name": "fld_iname", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "fld_magic0": {"name": "fld_magic0", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_type": {"name": "fld_type", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "fld_czid": {"name": "fld_czid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_success": {"name": "fld_success", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "fld_qhjd": {"name": "fld_qhjd", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_syntheticrecord_created_at": {"name": "idx_syntheticrecord_created_at", "columns": [{"expression": "created_at", "asc": true, "nulls": "last", "opclass": "timestamp_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_syntheticrecord_fld_id": {"name": "idx_syntheticrecord_fld_id", "columns": [{"expression": "fld_id", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_syntheticrecord_fld_name": {"name": "idx_syntheticrecord_fld_name", "columns": [{"expression": "fld_name", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_syntheticrecord_fld_pid": {"name": "idx_syntheticrecord_fld_pid", "columns": [{"expression": "fld_pid", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_syntheticrecord_fld_success": {"name": "idx_syntheticrecord_fld_success", "columns": [{"expression": "fld_success", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_syntheticrecord_fld_type": {"name": "idx_syntheticrecord_fld_type", "columns": [{"expression": "fld_type", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_faction_quest_progress": {"name": "tbl_faction_quest_progress", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "factionid": {"name": "factionid", "type": "integer", "primaryKey": false, "notNull": false}, "questid": {"name": "questid", "type": "integer", "primaryKey": false, "notNull": false}, "currentcount": {"name": "currentcount", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "smallint", "primaryKey": false, "notNull": false}, "acceptedtime": {"name": "acceptedtime", "type": "timestamp", "primaryKey": false, "notNull": false}, "lastupdatetime": {"name": "lastupdatetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "completedtime": {"name": "completedtime", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancelledtime": {"name": "cancelledtime", "type": "timestamp", "primaryKey": false, "notNull": false}, "lastresettime": {"name": "last<PERSON><PERSON><PERSON>", "type": "timestamp", "primaryKey": false, "notNull": false}, "lastcompletedtime": {"name": "lastcompletedtime", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_group_quest": {"name": "tbl_group_quest", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "questname": {"name": "questname", "type": "text", "primaryKey": false, "notNull": false}, "questdesc": {"name": "questdesc", "type": "text", "primaryKey": false, "notNull": false}, "questtype": {"name": "questtype", "type": "integer", "primaryKey": false, "notNull": false}, "targettype": {"name": "targettype", "type": "integer", "primaryKey": false, "notNull": false}, "targetid": {"name": "targetid", "type": "integer", "primaryKey": false, "notNull": false}, "targetcount": {"name": "targetcount", "type": "integer", "primaryKey": false, "notNull": false}, "requiredlevel": {"name": "requiredlevel", "type": "integer", "primaryKey": false, "notNull": false}, "rewardexp": {"name": "rewardexp", "type": "integer", "primaryKey": false, "notNull": false}, "rewardmoney": {"name": "rewardmoney", "type": "bigint", "primaryKey": false, "notNull": false}, "rewarditem": {"name": "rewarditem", "type": "integer", "primaryKey": false, "notNull": false}, "rewarditemcount": {"name": "rewarditemcount", "type": "integer", "primaryKey": false, "notNull": false}, "resettype": {"name": "resettype", "type": "integer", "primaryKey": false, "notNull": false}, "isactive": {"name": "isactive", "type": "boolean", "primaryKey": false, "notNull": false}, "createdate": {"name": "createdate", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_group_quest_contribution": {"name": "tbl_group_quest_contribution", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "questid": {"name": "questid", "type": "integer", "primaryKey": false, "notNull": false}, "progressid": {"name": "progressid", "type": "integer", "primaryKey": false, "notNull": false}, "playerid": {"name": "playerid", "type": "integer", "primaryKey": false, "notNull": false}, "playername": {"name": "playername", "type": "text", "primaryKey": false, "notNull": false}, "guildid": {"name": "guildid", "type": "integer", "primaryKey": false, "notNull": false}, "factionid": {"name": "factionid", "type": "integer", "primaryKey": false, "notNull": false}, "actiontype": {"name": "actiontype", "type": "integer", "primaryKey": false, "notNull": false}, "targetid": {"name": "targetid", "type": "integer", "primaryKey": false, "notNull": false}, "targetname": {"name": "targetname", "type": "text", "primaryKey": false, "notNull": false}, "contributioncount": {"name": "contributioncount", "type": "integer", "primaryKey": false, "notNull": false}, "contributiontime": {"name": "contributiontime", "type": "timestamp", "primaryKey": false, "notNull": false}, "hasreceivedreward": {"name": "hasre<PERSON><PERSON><PERSON>", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_group_quest_contribution_log": {"name": "tbl_group_quest_contribution_log", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "contributionid": {"name": "contributionid", "type": "integer", "primaryKey": false, "notNull": false}, "progressid": {"name": "progressid", "type": "integer", "primaryKey": false, "notNull": false}, "playerid": {"name": "playerid", "type": "integer", "primaryKey": false, "notNull": false}, "playername": {"name": "playername", "type": "text", "primaryKey": false, "notNull": false}, "actiontype": {"name": "actiontype", "type": "integer", "primaryKey": false, "notNull": false}, "targetid": {"name": "targetid", "type": "integer", "primaryKey": false, "notNull": false}, "targetname": {"name": "targetname", "type": "text", "primaryKey": false, "notNull": false}, "contributioncount": {"name": "contributioncount", "type": "integer", "primaryKey": false, "notNull": false}, "contributiontime": {"name": "contributiontime", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_group_quest_history": {"name": "tbl_group_quest_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "questid": {"name": "questid", "type": "integer", "primaryKey": false, "notNull": false}, "guildid": {"name": "guildid", "type": "integer", "primaryKey": false, "notNull": false}, "guildname": {"name": "guildname", "type": "text", "primaryKey": false, "notNull": false}, "factionid": {"name": "factionid", "type": "integer", "primaryKey": false, "notNull": false}, "completedtime": {"name": "completedtime", "type": "timestamp", "primaryKey": false, "notNull": false}, "completedby": {"name": "completedby", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_guild_quest_progress": {"name": "tbl_guild_quest_progress", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "guildid": {"name": "guildid", "type": "integer", "primaryKey": false, "notNull": false}, "questid": {"name": "questid", "type": "integer", "primaryKey": false, "notNull": false}, "currentcount": {"name": "currentcount", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "smallint", "primaryKey": false, "notNull": false}, "acceptedtime": {"name": "acceptedtime", "type": "timestamp", "primaryKey": false, "notNull": false}, "lastupdatetime": {"name": "lastupdatetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "completedtime": {"name": "completedtime", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancelledtime": {"name": "cancelledtime", "type": "timestamp", "primaryKey": false, "notNull": false}, "lastresettime": {"name": "last<PERSON><PERSON><PERSON>", "type": "timestamp", "primaryKey": false, "notNull": false}, "lastcompletedtime": {"name": "lastcompletedtime", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_sudosolieu": {"name": "tbl_sudosolieu", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_tname": {"name": "fld_tname", "type": "text", "primaryKey": false, "notNull": false}, "fld_index": {"name": "fld_index", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sname": {"name": "fld_sname", "type": "text", "primaryKey": false, "notNull": false}, "fld_tlevel": {"name": "fld_tlevel", "type": "integer", "primaryKey": false, "notNull": false}, "fld_stlevel": {"name": "fld_stlevel", "type": "integer", "primaryKey": false, "notNull": false}, "fld_styhd": {"name": "fld_styhd", "type": "integer", "primaryKey": false, "notNull": false}, "fld_stwg1": {"name": "fld_stwg1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_stwg2": {"name": "fld_stwg2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_stwg3": {"name": "fld_stwg3", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_truyenthuhethong": {"name": "tbl_truyent<PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "nguoinhanthu_nhatvatten": {"name": "nguo<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>ten", "type": "text", "primaryKey": false, "notNull": false}, "guithu_npc": {"name": "guithu_npc", "type": "integer", "primaryKey": false, "notNull": false}, "nguoiguithu_ten": {"name": "nguoiguithu_ten", "type": "text", "primaryKey": false, "notNull": false}, "truyenthunoidung": {"name": "truyenthunoidung", "type": "text", "primaryKey": false, "notNull": false}, "truyenthuthoigian": {"name": "truyenthuthoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "danhdaudaxem": {"name": "danhdaudaxem", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_vinhduhethong": {"name": "tbl_vin<PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_type": {"name": "fld_type", "type": "integer", "primaryKey": false, "notNull": false}, "fld_nghenghiep": {"name": "fld_nghenghiep", "type": "integer", "primaryKey": false, "notNull": false}, "fld_tennhanvat": {"name": "fld_tennhanvat", "type": "text", "primaryKey": false, "notNull": false}, "fld_dangcap": {"name": "fld_dangcap", "type": "integer", "primaryKey": false, "notNull": false}, "fld_theluc": {"name": "fld_theluc", "type": "integer", "primaryKey": false, "notNull": false}, "fld_bangphai": {"name": "fld_bangphai", "type": "text", "primaryKey": false, "notNull": false}, "fld_bangphai_bangchu": {"name": "fld_bangphai_bangchu", "type": "text", "primaryKey": false, "notNull": false}, "fld_diemso": {"name": "fld_diemso", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_char": {"name": "tbl_xwwl_char", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "fld_id": {"name": "fld_id", "type": "text", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_index": {"name": "fld_index", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "fld_face": {"name": "fld_face", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_job": {"name": "fld_job", "type": "integer", "primaryKey": false, "notNull": false}, "fld_exp": {"name": "fld_exp", "type": "text", "primaryKey": false, "notNull": false}, "fld_zx": {"name": "fld_zx", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_job_level": {"name": "fld_job_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_x": {"name": "fld_x", "type": "double precision", "primaryKey": false, "notNull": false, "default": 422}, "fld_y": {"name": "fld_y", "type": "double precision", "primaryKey": false, "notNull": false, "default": 2162}, "fld_z": {"name": "fld_z", "type": "double precision", "primaryKey": false, "notNull": false, "default": 15}, "fld_menow": {"name": "fld_menow", "type": "integer", "primaryKey": false, "notNull": false, "default": 101}, "fld_money": {"name": "fld_money", "type": "text", "primaryKey": false, "notNull": false}, "fld_hp": {"name": "fld_hp", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_mp": {"name": "fld_mp", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_sp": {"name": "fld_sp", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_wx": {"name": "fld_wx", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_se": {"name": "fld_se", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_point": {"name": "fld_point", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_skills": {"name": "fld_skills", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_wearitem": {"name": "fld_wearitem", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_item": {"name": "fld_item", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_qitem": {"name": "fld_qitem", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_ntcitem": {"name": "fld_ntcitem", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_coatitem": {"name": "fld_coatitem", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_kongfu": {"name": "fld_kongfu", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_hits": {"name": "fld_hits", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_doors": {"name": "fld_doors", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_quest": {"name": "fld_quest", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_lumpid": {"name": "fld_lumpid", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_fight_exp": {"name": "fld_fight_exp", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_j9": {"name": "fld_j9", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_jq": {"name": "fld_jq", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_jl": {"name": "fld_jl", "type": "text", "primaryKey": false, "notNull": false}, "fld_nametype": {"name": "fld_nametype", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_zbver": {"name": "fld_zbver", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "fld_zztype": {"name": "fld_zztype", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_zzsl": {"name": "fld_zzsl", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_ctime": {"name": "fld_ctime", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_ctimenew": {"name": "fld_ctimenew", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_stime": {"name": "fld_stime", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_qlname": {"name": "fld_qlname", "type": "text", "primaryKey": false, "notNull": false}, "fld_qljzname": {"name": "fld_qljzname", "type": "text", "primaryKey": false, "notNull": false}, "fld_qldu": {"name": "fld_qldu", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_qldumax": {"name": "fld_qldumax", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_qlrank": {"name": "fld_qlrank", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_thangthienkhicong": {"name": "fld_thangthienkhicong", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_thangthienvocong": {"name": "fld_than<PERSON>ien<PERSON>ong", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_thangthienlichluyen": {"name": "fld_thangthien<PERSON>luyen", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_thangthienvocongdiemso": {"name": "fld_thangthienvocongdiemso", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_hp": {"name": "fld_add_hp", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_at": {"name": "fld_add_at", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_df": {"name": "fld_add_df", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_hb": {"name": "fld_add_hb", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_mp": {"name": "fld_add_mp", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_mz": {"name": "fld_add_mz", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_zs": {"name": "fld_zs", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_online": {"name": "fld_online", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_get_wx": {"name": "fld_get_wx", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_tongkim": {"name": "fld_tongkim", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_taisinh": {"name": "fld_taisinh", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_vipdj": {"name": "fld_vipdj", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "在线时间": {"name": "在线时间", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_七彩": {"name": "fld_七彩", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_vip_at": {"name": "fld_vip_at", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_vip_df": {"name": "fld_vip_df", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_vip_hp": {"name": "fld_vip_hp", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_vip_level": {"name": "fld_vip_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_zscs": {"name": "fld_zscs", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_sjjl": {"name": "fld_sjjl", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_在线时间": {"name": "fld_在线时间", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "fld_在线等级": {"name": "fld_在线等级", "type": "integer", "primaryKey": false, "notNull": false}, "fld_领奖标志": {"name": "fld_领奖标志", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_reserved": {"name": "fld_reserved", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_签名类型": {"name": "fld_签名类型", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_任务等级4": {"name": "fld_任务等级4", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_师傅": {"name": "fld_师傅", "type": "text", "primaryKey": false, "notNull": false}, "fld_徒弟1": {"name": "fld_徒弟1", "type": "text", "primaryKey": false, "notNull": false}, "fld_徒弟2": {"name": "fld_徒弟2", "type": "text", "primaryKey": false, "notNull": false}, "fld_徒弟3": {"name": "fld_徒弟3", "type": "text", "primaryKey": false, "notNull": false}, "fld_师徒武功1_1": {"name": "fld_师徒武功1_1", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_师徒武功1_2": {"name": "fld_师徒武功1_2", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_师徒武功1_3": {"name": "fld_师徒武功1_3", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_dayquest": {"name": "fld_dayquest", "type": "text", "primaryKey": false, "notNull": false}, "fld_tlc": {"name": "fld_tlc", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_fqid": {"name": "fld_fqid", "type": "text", "primaryKey": false, "notNull": false}, "solangietnguoi": {"name": "solangietnguoi", "type": "bigint", "primaryKey": false, "notNull": false, "default": 0}, "bigietsolan": {"name": "bigietsolan", "type": "bigint", "primaryKey": false, "notNull": false, "default": 0}, "fld_suphu": {"name": "fld_suphu", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_detu": {"name": "fld_detu", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_sudovocong": {"name": "fld_sudovocong", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_giaitruthoigian": {"name": "fld_giaitruthoigian", "type": "text", "primaryKey": false, "notNull": false}, "fld_titlepoints": {"name": "fld_titlepoints", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "congthanhchienthoigian": {"name": "congthan<PERSON><PERSON>enthoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "fld_xb": {"name": "fld_xb", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_rosetitlepoints": {"name": "fld_rosetitlepoints", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_speakingtype": {"name": "fld_speakingtype", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_nszitem": {"name": "fld_nszitem", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_ljkongfu": {"name": "fld_ljkongfu", "type": "bytea", "primaryKey": false, "notNull": false}, "bangphai_doconghien": {"name": "<PERSON><PERSON><PERSON>_doconghien", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_mlz": {"name": "fld_mlz", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_pvp_piont": {"name": "fld_pvp_piont", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_thannuvocongdiemso": {"name": "fld_thannuvocongdiemso", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_love_word": {"name": "fld_love_word", "type": "text", "primaryKey": false, "notNull": false}, "fld_marital_status": {"name": "fld_marital_status", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_married": {"name": "fld_married", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_jh_date": {"name": "fld_jh_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "fld_fb_time": {"name": "fld_fb_time", "type": "integer", "primaryKey": false, "notNull": false}, "fld_lost_wx": {"name": "fld_lost_wx", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_hd_time": {"name": "fld_hd_time", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_kieutoc": {"name": "fld_kieutoc", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_khuonmat": {"name": "fld_khuonmat", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_whtb": {"name": "fld_whtb", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_chtime": {"name": "fld_chtime", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_config": {"name": "fld_config", "type": "text", "primaryKey": false, "notNull": false}, "fld_fashion_item": {"name": "fld_fashion_item", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_quest_finish": {"name": "fld_quest_finish", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_add_clvc": {"name": "fld_add_clvc", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_ptvc": {"name": "fld_add_ptvc", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_add_kc": {"name": "fld_add_kc", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "default": 24}, "nhanqualandau": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "tlc_random_phe": {"name": "tlc_random_phe", "type": "text", "primaryKey": false, "notNull": false}, "vohuan_gioihan_theongay": {"name": "vohuan_gio<PERSON>an_theongay", "type": "integer", "primaryKey": false, "notNull": false, "default": 10000}, "vohuan_time": {"name": "vohuan_time", "type": "text", "primaryKey": false, "notNull": false}, "fld_moneyextralevel": {"name": "fld_moneyextralevel", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "fld_pinkbag_item": {"name": "fld_pinkbag_item", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_asc7_anti_qigong": {"name": "fld_asc7_anti_qigong", "type": "bytea", "primaryKey": false, "notNull": false}}, "indexes": {"tbl_xwwl_char_fld_id_idx": {"name": "tbl_xwwl_char_fld_id_idx", "columns": [{"expression": "fld_id", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tbl_xwwl_char_fld_name_idx": {"name": "tbl_xwwl_char_fld_name_idx", "columns": [{"expression": "fld_name", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_cw": {"name": "tbl_xwwl_cw", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "zrname": {"name": "zrname", "type": "text", "primaryKey": false, "notNull": false}, "itmeid": {"name": "itmeid", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "fld_zcd": {"name": "fld_zcd", "type": "integer", "primaryKey": false, "notNull": false}, "fld_exp": {"name": "fld_exp", "type": "text", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_bs": {"name": "fld_bs", "type": "integer", "primaryKey": false, "notNull": false}, "fld_job": {"name": "fld_job", "type": "integer", "primaryKey": false, "notNull": false}, "fld_job_level": {"name": "fld_job_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_hp": {"name": "fld_hp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_mp": {"name": "fld_mp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_kongfu": {"name": "fld_kongfu", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_wearitem": {"name": "fld_wearitem", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_item": {"name": "fld_item", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic5": {"name": "fld_magic5", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sxbl": {"name": "fld_sxbl", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_cwarehouse": {"name": "tbl_xwwl_cwarehouse", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_id": {"name": "fld_id", "type": "text", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_item": {"name": "fld_item", "type": "bytea", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_guild": {"name": "tbl_xwwl_guild", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "g_name": {"name": "g_name", "type": "text", "primaryKey": false, "notNull": true}, "g_master": {"name": "g_master", "type": "text", "primaryKey": false, "notNull": false}, "g_notice": {"name": "g_notice", "type": "text", "primaryKey": false, "notNull": false}, "leve": {"name": "leve", "type": "integer", "primaryKey": false, "notNull": false}, "thanhdanh": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "monhuy": {"name": "<PERSON><PERSON><PERSON>", "type": "bytea", "primaryKey": false, "notNull": false}, "monphucword": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "monphucmausac": {"name": "monphucmausac", "type": "integer", "primaryKey": false, "notNull": false}, "bangphaivohuan": {"name": "bangphaivohuan", "type": "integer", "primaryKey": false, "notNull": false}, "thang": {"name": "thang", "type": "integer", "primaryKey": false, "notNull": false}, "thua": {"name": "thua", "type": "integer", "primaryKey": false, "notNull": false}, "hoa": {"name": "hoa", "type": "integer", "primaryKey": false, "notNull": false}, "monphaitaisan": {"name": "mon<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "thongbao_congthanh": {"name": "thong<PERSON>_congthanh", "type": "integer", "primaryKey": false, "notNull": false}, "lienminh_minhchu": {"name": "lienminh_minhchu", "type": "text", "primaryKey": false, "notNull": false}, "createdat": {"name": "createdat", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedat": {"name": "updatedat", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tbl_xwwl_guild_unique": {"columns": ["g_name"], "nullsNotDistinct": false, "name": "tbl_xwwl_guild_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_guildmember": {"name": "tbl_xwwl_guildmember", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "g_name": {"name": "g_name", "type": "text", "primaryKey": false, "notNull": false}, "leve": {"name": "leve", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_guildpoint": {"name": "fld_guildpoint", "type": "integer", "primaryKey": false, "notNull": false}, "fld_newguildpoint": {"name": "fld_newguildpoint", "type": "integer", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdat": {"name": "createdat", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedat": {"name": "updatedat", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_pklog": {"name": "tbl_xwwl_pklog", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_killer": {"name": "fld_killer", "type": "text", "primaryKey": false, "notNull": false}, "fld_killer_guild": {"name": "fld_killer_guild", "type": "text", "primaryKey": false, "notNull": false}, "fld_death": {"name": "fld_death", "type": "text", "primaryKey": false, "notNull": false}, "fld_death_guild": {"name": "fld_death_guild", "type": "text", "primaryKey": false, "notNull": false}, "fld_num": {"name": "fld_num", "type": "integer", "primaryKey": false, "notNull": false}, "fld_wx": {"name": "fld_wx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_lasttime": {"name": "fld_lasttime", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_publicwarehouse": {"name": "tbl_xwwl_publicwarehouse", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_id": {"name": "fld_id", "type": "text", "primaryKey": false, "notNull": false}, "fld_money": {"name": "fld_money", "type": "text", "primaryKey": false, "notNull": false}, "fld_item": {"name": "fld_item", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_itime": {"name": "fld_itime", "type": "bytea", "primaryKey": false, "notNull": false}, "fld_zbver": {"name": "fld_zbver", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_rosetop": {"name": "tbl_xwwl_rosetop", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_sex": {"name": "fld_sex", "type": "integer", "primaryKey": false, "notNull": false}, "fld_zx": {"name": "fld_zx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_innum": {"name": "fld_innum", "type": "integer", "primaryKey": false, "notNull": false}, "fld_outnum": {"name": "fld_outnum", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_warehouse": {"name": "tbl_xwwl_warehouse", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_id": {"name": "fld_id", "type": "text", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_money": {"name": "fld_money", "type": "text", "primaryKey": false, "notNull": false}, "fld_item": {"name": "fld_item", "type": "bytea", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vinhdubangphaixephang": {"name": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_zx": {"name": "fld_zx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_bp": {"name": "fld_bp", "type": "text", "primaryKey": false, "notNull": false}, "fld_job": {"name": "fld_job", "type": "integer", "primaryKey": false, "notNull": false}, "fld_job_level": {"name": "fld_job_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_ry": {"name": "fld_ry", "type": "integer", "primaryKey": false, "notNull": false}, "thoigian": {"name": "thoigian", "type": "timestamp", "primaryKey": false, "notNull": false}, "fld_fq": {"name": "fld_fq", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.xwwl_gameserverinfo": {"name": "xwwl_gameserverinfo", "schema": "", "columns": {"serverid": {"name": "serverid", "type": "integer", "primaryKey": false, "notNull": false}, "itemcount": {"name": "itemcount", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.thienmathancung_danhsach": {"name": "thienmathan<PERSON>ng_danh<PERSON>ch", "schema": "", "columns": {"bang_chiem_thanh": {"name": "bang_chiem_thanh", "type": "text", "primaryKey": false, "notNull": false}, "ngay_chiem_thanh": {"name": "ngay_chiem_thanh", "type": "text", "primaryKey": false, "notNull": false}, "cong_thanh_cuonghoa_level": {"name": "cong_thanh_cuonghoa_level", "type": "text", "primaryKey": false, "notNull": false}, "thoigian_lammoi_congthanh": {"name": "thoigian_lammoi_congthanh", "type": "timestamp", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_pvp": {"name": "tbl_xwwl_pvp", "schema": "", "columns": {"santapten": {"name": "santapten", "type": "text", "primaryKey": false, "notNull": false}, "a_nguoichoi": {"name": "a_nguoichoi", "type": "text", "primaryKey": false, "notNull": false}, "b_nguoichoi": {"name": "b_nguo<PERSON>oi", "type": "text", "primaryKey": false, "notNull": false}, "agietnguoisoluong": {"name": "agietnguoisoluong", "type": "integer", "primaryKey": false, "notNull": false}, "bgietnguoisoluong": {"name": "bgietnguoisoluong", "type": "integer", "primaryKey": false, "notNull": false}, "a_chaytronsolan": {"name": "a_chay<PERSON><PERSON>an", "type": "integer", "primaryKey": false, "notNull": false}, "b_chaytronsolan": {"name": "b_ch<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "athuduocnguyenbao": {"name": "athuduocnguyenbao", "type": "integer", "primaryKey": false, "notNull": false}, "bthuduocnguyenbao": {"name": "bthuduocnguyenbao", "type": "integer", "primaryKey": false, "notNull": false}, "tranhtaiketqua": {"name": "tran<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {"public.tbl_xwwl_pvp_id_seq": {"name": "tbl_xwwl_pvp_id_seq", "schema": "public", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "increment": "1", "cycle": false, "cache": "1"}, "public.thienmathancung_danhsach_id_seq": {"name": "thienmathancung_danhsach_id_seq", "schema": "public", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "increment": "1", "cycle": false, "cache": "1"}}, "roles": {}, "policies": {}, "views": {"public.v_synthesis_summary": {"name": "v_synthesis_summary", "schema": "public", "columns": {"character_name": {"name": "character_name", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "synthesis_type": {"name": "synthesis_type", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "result": {"name": "result", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "total_attempts": {"name": "total_attempts", "type": "bigint", "primaryKey": false, "notNull": false}, "successful_attempts": {"name": "successful_attempts", "type": "bigint", "primaryKey": false, "notNull": false}, "failed_attempts": {"name": "failed_attempts", "type": "bigint", "primaryKey": false, "notNull": false}, "success_rate_percent": {"name": "success_rate_percent", "type": "numeric", "primaryKey": false, "notNull": false}}, "isExisting": false, "definition": "SELECT fld_name AS character_name, fld_type AS synthesis_type, fld_success AS result, count(*) AS total_attempts, count( CASE WHEN fld_success::text = 'Success'::text THEN 1 ELSE NULL::integer END) AS successful_attempts, count( CASE WHEN fld_success::text = 'Failed'::text THEN 1 ELSE NULL::integer END) AS failed_attempts, round(count( CASE WHEN fld_success::text = 'Success'::text THEN 1 ELSE NULL::integer END)::numeric * 100.0 / count(*)::numeric, 2) AS success_rate_percent FROM syntheticrecord WHERE created_at >= (CURRENT_DATE - '30 days'::interval) GROUP BY fld_name, fld_type, fld_success ORDER BY fld_name, fld_type", "materialized": false}}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}