import { pgTable, serial, text, integer, timestamp, varchar, date, bigserial, index, bigint, smallint, boolean, uniqueIndex, doublePrecision, unique, pgView, numeric, pgSequence, customType } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

// Custom bytea type for PostgreSQL
const bytea = customType<{ data: string; notNull: false; default: false }>({
  dataType() {
    return "bytea";
  },
  toDriver(value: string): string {
    return value;
  },
  fromDriver(value: unknown): string {
    return value as string;
  },
});


export const tblXwwlPvpIdSeq = pgSequence("tbl_xwwl_pvp_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false })
export const thienmathancungDanhsachIdSeq = pgSequence("thienmathancung_danhsach_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false })

export const bachbaocacrecord = pgTable("bachbaocacrecord", {
	id: serial().notNull(),
	userid: text(),
	username: text(),
	vatphamId: text("vatpham_id"),
	vatphamten: text(),
	vatphamsoluong: integer(),
	nguyenbaosoluong: integer(),
	thoigian: timestamp({ mode: 'string' }),
});

export const bangchienTiendatcuoc = pgTable("bangchien_tiendatcuoc", {
	id: serial().notNull(),
	userid: text(),
	username: text(),
	bangphaiid: integer(),
	nguyenbaosoluong: integer(),
});

export const congthanhchienThanhchu = pgTable("congthanhchien_thanhchu", {
	id: serial().notNull(),
	congthanhchienTenbang: text("congthanhchien_tenbang"),
	tenthanhchu: text(),
	bangphaiid: integer(),
	congthanhthoigian: timestamp({ mode: 'string' }),
	congthanhbanthuongthoigian: timestamp({ mode: 'string' }),
});

export const drugrecord = pgTable("drugrecord", {
	id: integer().primaryKey().generatedByDefaultAsIdentity({ name: "drugrecord_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	accountid: varchar().notNull(),
	charactername: varchar().notNull(),
	itemid: integer().notNull(),
	amount: integer().notNull(),
	createdAt: date("created_at").defaultNow(),
});

export const eventtop = pgTable("eventtop", {
	id: serial().notNull(),
	tennhanvat: text(),
	bangphai: text(),
	theluc: text(),
	dangcap: integer(),
	gietnguoisoluong: integer(),
	tuvongsoluong: integer(),
	phankhutintuc: text(),
	diemChinhphai: integer("diem_chinhphai"),
	diemTaphai: integer("diem_taphai"),
	createdat: date().defaultNow(),
});

export const eventtopDch = pgTable("eventtop_dch", {
	id: serial().notNull(),
	tennhanvat: text(),
	bangphai: text(),
	theluc: text(),
	dangcap: integer(),
	gietnguoisoluong: integer(),
	tuvongsoluong: integer(),
	phankhutintuc: text(),
	dameTru: integer("dame_tru"),
	hoptacgietnguoi: integer(),
	diemDchChinhphai: integer("diem_dch_chinhphai"),
	diemDchTaphai: integer("diem_dch_taphai"),
	createdat: date().defaultNow(),
});

export const exchangecharacter = pgTable("exchangecharacter", {
	id: bigserial({ mode: "bigint" }).notNull(),
	buyerId: text("buyer_id"),
	sellerId: text("seller_id"),
	character: text(),
	status: text(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const giftcode = pgTable("giftcode", {
	id: serial().notNull(),
	username: text(),
	code: text(),
	cash: integer(),
	bonus: integer(),
	noiDung: text("noi_dung"),
	daSuDung: integer("da_su_dung"),
	nguoiSuDung: text("nguoi_su_dung"),
});

export const itemrecord = pgTable("itemrecord", {
	id: serial().notNull(),
	userid: text(),
	username: text(),
	touserid: text(),
	tousername: text(),
	globalId: text("global_id"),
	vatphamId: text("vatpham_id"),
	vatphamten: text(),
	vatphamsoluong: integer(),
	vatphamthuoctinh: text(),
	sotien: integer(),
	loaihinh: text(),
	thoigian: timestamp({ mode: 'string' }),
});

export const logDeleteitem = pgTable("log_deleteitem", {
	id: serial().notNull(),
	maitem: integer(),
	iditem: integer(),
	thoigian: timestamp({ mode: 'string' }),
	levelitem: text(),
	username: text(),
	trangthai: integer(),
});

export const logThelucchien = pgTable("log_thelucchien", {
	id: serial().notNull(),
	fldName: text("fld_name"),
	giet: integer(),
	chet: integer(),
	ngay: date(),
	theluc: text(),
	monphai: text(),
});

export const loginrecord = pgTable("loginrecord", {
	id: serial().notNull(),
	userid: text(),
	username: text(),
	userip: text(),
	loaihinh: text(),
	thoigian: timestamp({ mode: 'string' }),
	macAddress: text("mac_address"),
});

export const loginrecordMac = pgTable("loginrecord_mac", {
	id: serial().notNull(),
	userid: text(),
	username: text(),
	userip: text(),
	loaihinh: text(),
	thoigian: timestamp({ mode: 'string' }),
	macAddress: text("mac_address"),
	serverid: integer(),
});

export const logpk = pgTable("logpk", {
	id: serial().notNull(),
	nguoigiet: text(),
	nguoibigiet: text(),
	thoigian: timestamp({ mode: 'string' }),
});

export const logshop = pgTable("logshop", {
	id: serial().notNull(),
	userid: text(),
	username: text(),
	maitem: integer(),
	iditem: integer(),
	tenitem: text(),
	magic1: integer(),
	magic2: integer(),
	magic3: integer(),
	magic4: integer(),
	magic5: integer(),
	soluong: integer(),
	giatien: integer(),
	thoigian: timestamp({ mode: 'string' }),
	cashconlai: integer(),
	coHayKhongMoRa: integer("co_hay_khong_mo_ra"),
	daSuDung: integer("da_su_dung"),
	muaId: integer("mua_id"),
	thanhCong: integer("thanh_cong"),
});

export const logshopvohuan = pgTable("logshopvohuan", {
	id: serial().notNull(),
	userid: text(),
	username: text(),
	maitem: integer(),
	iditem: integer(),
	tenitem: text(),
	magic1: integer(),
	magic2: integer(),
	magic3: integer(),
	magic4: integer(),
	magic5: integer(),
	soluong: integer(),
	giatien: integer(),
	thoigian: timestamp({ mode: 'string' }),
	vohuanconlai: integer(),
	coHayKhongMoRa: integer("co_hay_khong_mo_ra"),
	daSuDung: integer("da_su_dung"),
	muaId: integer("mua_id"),
	thanhCong: integer("thanh_cong"),
});

export const syntheticrecord = pgTable("syntheticrecord", {
	id: serial().primaryKey().notNull(),
	fldId: varchar("fld_id", { length: 30 }).notNull(),
	fldName: varchar("fld_name", { length: 30 }).notNull(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldQjid: bigint("fld_qjid", { mode: "number" }),
	fldPid: integer("fld_pid"),
	fldIname: varchar("fld_iname", { length: 50 }),
	fldMagic0: integer("fld_magic0").default(0),
	fldMagic1: integer("fld_magic1").default(0),
	fldMagic2: integer("fld_magic2").default(0),
	fldMagic3: integer("fld_magic3").default(0),
	fldMagic4: integer("fld_magic4").default(0),
	fldType: varchar("fld_type", { length: 30 }),
	fldCzid: integer("fld_czid"),
	fldSuccess: varchar("fld_success", { length: 30 }),
	fldQhjd: integer("fld_qhjd").default(0),
	createdAt: timestamp("created_at", { mode: 'string' }).default(sql`CURRENT_TIMESTAMP`),
}, (table) => [
	index("idx_syntheticrecord_created_at").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("idx_syntheticrecord_fld_id").using("btree", table.fldId.asc().nullsLast().op("text_ops")),
	index("idx_syntheticrecord_fld_name").using("btree", table.fldName.asc().nullsLast().op("text_ops")),
	index("idx_syntheticrecord_fld_pid").using("btree", table.fldPid.asc().nullsLast().op("int4_ops")),
	index("idx_syntheticrecord_fld_success").using("btree", table.fldSuccess.asc().nullsLast().op("text_ops")),
	index("idx_syntheticrecord_fld_type").using("btree", table.fldType.asc().nullsLast().op("text_ops")),
]);

export const tblFactionQuestProgress = pgTable("tbl_faction_quest_progress", {
	id: serial().notNull(),
	factionid: integer(),
	questid: integer(),
	currentcount: integer(),
	status: smallint(),
	acceptedtime: timestamp({ mode: 'string' }),
	lastupdatetime: timestamp({ mode: 'string' }),
	completedtime: timestamp({ mode: 'string' }),
	cancelledtime: timestamp({ mode: 'string' }),
	lastresettime: timestamp({ mode: 'string' }),
	lastcompletedtime: timestamp({ mode: 'string' }),
});

export const tblGroupQuest = pgTable("tbl_group_quest", {
	id: serial().notNull(),
	questname: text(),
	questdesc: text(),
	questtype: integer(),
	targettype: integer(),
	targetid: integer(),
	targetcount: integer(),
	requiredlevel: integer(),
	rewardexp: integer(),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	rewardmoney: bigint({ mode: "number" }),
	rewarditem: integer(),
	rewarditemcount: integer(),
	resettype: integer(),
	isactive: boolean(),
	createdate: timestamp({ mode: 'string' }),
});

export const tblGroupQuestContribution = pgTable("tbl_group_quest_contribution", {
	id: serial().notNull(),
	questid: integer(),
	progressid: integer(),
	playerid: integer(),
	playername: text(),
	guildid: integer(),
	factionid: integer(),
	actiontype: integer(),
	targetid: integer(),
	targetname: text(),
	contributioncount: integer(),
	contributiontime: timestamp({ mode: 'string' }),
	hasreceivedreward: boolean(),
});

export const tblGroupQuestContributionLog = pgTable("tbl_group_quest_contribution_log", {
	id: serial().notNull(),
	contributionid: integer(),
	progressid: integer(),
	playerid: integer(),
	playername: text(),
	actiontype: integer(),
	targetid: integer(),
	targetname: text(),
	contributioncount: integer(),
	contributiontime: timestamp({ mode: 'string' }),
});

export const tblGroupQuestHistory = pgTable("tbl_group_quest_history", {
	id: serial().notNull(),
	questid: integer(),
	guildid: integer(),
	guildname: text(),
	factionid: integer(),
	completedtime: timestamp({ mode: 'string' }),
	completedby: text(),
});

export const tblGuildQuestProgress = pgTable("tbl_guild_quest_progress", {
	id: serial().notNull(),
	guildid: integer(),
	questid: integer(),
	currentcount: integer(),
	status: smallint(),
	acceptedtime: timestamp({ mode: 'string' }),
	lastupdatetime: timestamp({ mode: 'string' }),
	completedtime: timestamp({ mode: 'string' }),
	cancelledtime: timestamp({ mode: 'string' }),
	lastresettime: timestamp({ mode: 'string' }),
	lastcompletedtime: timestamp({ mode: 'string' }),
});

export const tblSudosolieu = pgTable("tbl_sudosolieu", {
	id: serial().notNull(),
	fldTname: text("fld_tname"),
	fldIndex: integer("fld_index"),
	fldSname: text("fld_sname"),
	fldTlevel: integer("fld_tlevel"),
	fldStlevel: integer("fld_stlevel"),
	fldStyhd: integer("fld_styhd"),
	fldStwg1: integer("fld_stwg1"),
	fldStwg2: integer("fld_stwg2"),
	fldStwg3: integer("fld_stwg3"),
});

export const tblTruyenthuhethong = pgTable("tbl_truyenthuhethong", {
	id: serial().notNull(),
	nguoinhanthuNhatvatten: text("nguoinhanthu_nhatvatten"),
	guithuNpc: integer("guithu_npc"),
	nguoiguithuTen: text("nguoiguithu_ten"),
	truyenthunoidung: text(),
	truyenthuthoigian: timestamp({ mode: 'string' }),
	danhdaudaxem: integer(),
});

export const tblVinhduhethong = pgTable("tbl_vinhduhethong", {
	id: serial().notNull(),
	fldType: integer("fld_type"),
	fldNghenghiep: integer("fld_nghenghiep"),
	fldTennhanvat: text("fld_tennhanvat"),
	fldDangcap: integer("fld_dangcap"),
	fldTheluc: integer("fld_theluc"),
	fldBangphai: text("fld_bangphai"),
	fldBangphaiBangchu: text("fld_bangphai_bangchu"),
	fldDiemso: integer("fld_diemso"),
});

export const tblXwwlChar = pgTable("tbl_xwwl_char", {
	id: serial().primaryKey().notNull(),
	fldId: text("fld_id"),
	fldName: text("fld_name"),
	fldIndex: integer("fld_index"),
	fldLevel: integer("fld_level").default(1),
	// bytea type for face data
	fldFace: bytea("fld_face"),
	fldJob: integer("fld_job"),
	fldExp: text("fld_exp"),
	fldZx: integer("fld_zx").default(0),
	fldJobLevel: integer("fld_job_level").default(0),
	fldX: doublePrecision("fld_x").default(422),
	fldY: doublePrecision("fld_y").default(2162),
	fldZ: doublePrecision("fld_z").default(15),
	fldMenow: integer("fld_menow").default(101),
	fldMoney: text("fld_money"),
	fldHp: integer("fld_hp").default(0),
	fldMp: integer("fld_mp").default(0),
	fldSp: integer("fld_sp").default(0),
	fldWx: integer("fld_wx").default(0),
	fldSe: integer("fld_se").default(0),
	fldPoint: integer("fld_point").default(0),
	// bytea type for skills data
	fldSkills: bytea("fld_skills"),
	// bytea type for wear items (17 slots)
	fldWearitem: bytea("fld_wearitem"),
	// bytea type for inventory items
	fldItem: bytea("fld_item"),
	// bytea type for quest items (36 slots)
	fldQitem: bytea("fld_qitem"),
	// bytea type for gem bag items (6 slots)
	fldNtcitem: bytea("fld_ntcitem"),
	// bytea type for coat items
	fldCoatitem: bytea("fld_coatitem"),
	// bytea type for kungfu data
	fldKongfu: bytea("fld_kongfu"),
	// bytea type for hits data
	fldHits: bytea("fld_hits"),
	// bytea type for doors data
	fldDoors: bytea("fld_doors"),
	// bytea type for quest data
	fldQuest: bytea("fld_quest"),
	fldLumpid: integer("fld_lumpid").default(0),
	fldFightExp: integer("fld_fight_exp").default(0),
	fldJ9: integer("fld_j9").default(0),
	fldJq: integer("fld_jq").default(0),
	fldJl: text("fld_jl"),
	// bytea type for name type data
	fldNametype: bytea("fld_nametype"),
	fldZbver: integer("fld_zbver").default(1),
	fldZztype: integer("fld_zztype").default(0),
	fldZzsl: integer("fld_zzsl").default(0),
	// bytea type for creation time data
	fldCtime: bytea("fld_ctime"),
	// bytea type for new creation time data
	fldCtimenew: bytea("fld_ctimenew"),
	// bytea type for session time data
	fldStime: bytea("fld_stime"),
	fldQlname: text("fld_qlname"),
	fldQljzname: text("fld_qljzname"),
	fldQldu: integer("fld_qldu").default(0),
	fldQldumax: integer("fld_qldumax").default(0),
	fldQlrank: integer("fld_qlrank").default(0),
	// bytea type for thang thien khi cong data
	fldThangthienkhicong: bytea("fld_thangthienkhicong"),
	// bytea type for thang thien vo cong data
	fldThangthienvocong: bytea("fld_thangthienvocong"),
	fldThangthienlichluyen: integer("fld_thangthienlichluyen").default(0),
	fldThangthienvocongdiemso: integer("fld_thangthienvocongdiemso").default(0),
	fldAddHp: integer("fld_add_hp").default(0),
	fldAddAt: integer("fld_add_at").default(0),
	fldAddDf: integer("fld_add_df").default(0),
	fldAddHb: integer("fld_add_hb").default(0),
	fldAddMp: integer("fld_add_mp").default(0),
	fldAddMz: integer("fld_add_mz").default(0),
	fldZs: integer("fld_zs").default(0),
	fldOnline: integer("fld_online").default(0),
	fldGetWx: integer("fld_get_wx").default(0),
	fldTongkim: integer("fld_tongkim").default(0),
	fldTaisinh: integer("fld_taisinh").default(0),
	fldVipdj: integer("fld_vipdj").default(0),
	// bytea type for online time data
	"在线时间": bytea("在线时间"),
	"fld七彩": integer("fld_七彩").default(0),
	fldVipAt: integer("fld_vip_at").default(0),
	fldVipDf: integer("fld_vip_df").default(0),
	fldVipHp: integer("fld_vip_hp").default(0),
	fldVipLevel: integer("fld_vip_level").default(0),
	fldZscs: integer("fld_zscs").default(0),
	fldSjjl: integer("fld_sjjl").default(0),
	"fld在线时间": doublePrecision("fld_在线时间").default(0),
	"fld在线等级": integer("fld_在线等级"),
	"fld领奖标志": integer("fld_领奖标志").default(0),
	fldReserved: integer("fld_reserved").default(0),
	"fld签名类型": integer("fld_签名类型").default(0),
	"fld任务等级4": integer("fld_任务等级4").default(0),
	"fld师傅": text("fld_师傅"),
	"fld徒弟1": text("fld_徒弟1"),
	"fld徒弟2": text("fld_徒弟2"),
	"fld徒弟3": text("fld_徒弟3"),
	"fld师徒武功11": integer("fld_师徒武功1_1").default(0),
	"fld师徒武功12": integer("fld_师徒武功1_2").default(0),
	"fld师徒武功13": integer("fld_师徒武功1_3").default(0),
	fldDayquest: text("fld_dayquest"),
	fldTlc: integer("fld_tlc").default(0),
	fldFqid: text("fld_fqid"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	solangietnguoi: bigint({ mode: "number" }).default(0),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	bigietsolan: bigint({ mode: "number" }).default(0),
	// bytea type for su phu data
	fldSuphu: bytea("fld_suphu"),
	// bytea type for de tu data
	fldDetu: bytea("fld_detu"),
	// bytea type for su do vo cong data
	fldSudovocong: bytea("fld_sudovocong"),
	fldGiaitruthoigian: text("fld_giaitruthoigian"),
	fldTitlepoints: integer("fld_titlepoints").default(0),
	congthanhchienthoigian: timestamp({ mode: 'string' }),
	fldXb: integer("fld_xb").default(0),
	fldRosetitlepoints: integer("fld_rosetitlepoints").default(0),
	fldSpeakingtype: integer("fld_speakingtype").default(0),
	// bytea type for nsz item data
	fldNszitem: bytea("fld_nszitem"),
	// bytea type for lj kongfu data
	fldLjkongfu: bytea("fld_ljkongfu"),
	bangphaiDoconghien: integer("bangphai_doconghien").default(0),
	fldMlz: integer("fld_mlz").default(0),
	fldPvpPiont: integer("fld_pvp_piont").default(0),
	fldThannuvocongdiemso: integer("fld_thannuvocongdiemso").default(0),
	fldLoveWord: text("fld_love_word"),
	fldMaritalStatus: integer("fld_marital_status").default(0),
	fldMarried: integer("fld_married").default(0),
	fldJhDate: timestamp("fld_jh_date", { mode: 'string' }),
	fldFbTime: integer("fld_fb_time"),
	fldLostWx: integer("fld_lost_wx").default(0),
	fldHdTime: integer("fld_hd_time").default(0),
	// bytea type for hair style data
	fldKieutoc: bytea("fld_kieutoc"),
	// bytea type for face data
	fldKhuonmat: bytea("fld_khuonmat"),
	fldWhtb: integer("fld_whtb").default(0),
	// bytea type for change time data
	fldChtime: bytea("fld_chtime"),
	fldConfig: text("fld_config"),
	// bytea type for fashion items (60 slots)
	fldFashionItem: bytea("fld_fashion_item"),
	// bytea type for quest finish data
	fldQuestFinish: bytea("fld_quest_finish"),
	fldAddClvc: integer("fld_add_clvc").default(0),
	fldAddPtvc: integer("fld_add_ptvc").default(0),
	fldAddKc: integer("fld_add_kc").default(0),
	version: integer().default(24),
	nhanqualandau: boolean().default(false),
	tlcRandomPhe: text("tlc_random_phe"),
	vohuanGioihanTheongay: integer("vohuan_gioihan_theongay").default(10000),
	vohuanTime: text("vohuan_time"),
	fldMoneyextralevel: integer("fld_moneyextralevel").default(0),
	// bytea type for pink bag items (24 slots)
	fldPinkbagItem: bytea("fld_pinkbag_item"),
	// bytea type for asc7 anti qigong data
	fldAsc7AntiQigong: bytea("fld_asc7_anti_qigong"),
}, (table) => [
	index("tbl_xwwl_char_fld_id_idx").using("btree", table.fldId.asc().nullsLast().op("text_ops")),
	uniqueIndex("tbl_xwwl_char_fld_name_idx").using("btree", table.fldName.asc().nullsLast().op("text_ops")),
]);

export const tblXwwlCw = pgTable("tbl_xwwl_cw", {
	id: serial().notNull(),
	zrname: text(),
	itmeid: integer(),
	name: text(),
	fldZcd: integer("fld_zcd"),
	fldExp: text("fld_exp"),
	fldLevel: integer("fld_level"),
	fldBs: integer("fld_bs"),
	fldJob: integer("fld_job"),
	fldJobLevel: integer("fld_job_level"),
	fldHp: integer("fld_hp"),
	fldMp: integer("fld_mp"),
	// bytea type for kungfu data
	fldKongfu: bytea("fld_kongfu"),
	// bytea type for wear items
	fldWearitem: bytea("fld_wearitem"),
	// bytea type for items
	fldItem: bytea("fld_item"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldMagic5: integer("fld_magic5"),
	fldSxbl: integer("fld_sxbl"),
});

export const tblXwwlCwarehouse = pgTable("tbl_xwwl_cwarehouse", {
	id: serial().notNull(),
	fldId: text("fld_id"),
	fldName: text("fld_name"),
	// bytea type for cwarehouse items
	fldItem: bytea("fld_item"),
});

export const tblXwwlGuild = pgTable("tbl_xwwl_guild", {
	id: serial().primaryKey().notNull(),
	gName: text("g_name").notNull(),
	gMaster: text("g_master"),
	gNotice: text("g_notice"),
	leve: integer(),
	thanhdanh: integer(),
	// bytea type for guild data
	monhuy: bytea("monhuy"),
	monphucword: integer(),
	monphucmausac: integer(),
	bangphaivohuan: integer(),
	thang: integer(),
	thua: integer(),
	hoa: integer(),
	monphaitaisan: text(),
	thongbaoCongthanh: integer("thongbao_congthanh"),
	lienminhMinhchu: text("lienminh_minhchu"),
	createdat: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedat: timestamp({ mode: 'string' }).defaultNow().notNull(),
	active: boolean().default(true).notNull(),
}, (table) => [
	unique("tbl_xwwl_guild_unique").on(table.gName),
]);

export const tblXwwlGuildmember = pgTable("tbl_xwwl_guildmember", {
	id: serial().notNull(),
	fldName: text("fld_name"),
	gName: text("g_name"),
	leve: integer(),
	fldLevel: integer("fld_level"),
	fldGuildpoint: integer("fld_guildpoint"),
	fldNewguildpoint: integer("fld_newguildpoint"),
	active: boolean().default(true).notNull(),
	createdat: date().defaultNow().notNull(),
	updatedat: date().defaultNow().notNull(),
});

export const tblXwwlPklog = pgTable("tbl_xwwl_pklog", {
	id: serial().notNull(),
	fldKiller: text("fld_killer"),
	fldKillerGuild: text("fld_killer_guild"),
	fldDeath: text("fld_death"),
	fldDeathGuild: text("fld_death_guild"),
	fldNum: integer("fld_num"),
	fldWx: integer("fld_wx"),
	fldLasttime: timestamp("fld_lasttime", { mode: 'string' }),
});

export const tblXwwlPublicwarehouse = pgTable("tbl_xwwl_publicwarehouse", {
	id: serial().notNull(),
	fldId: text("fld_id"),
	fldMoney: text("fld_money"),
	// bytea type for public warehouse items (60 slots)
	fldItem: bytea("fld_item"),
	// bytea type for item time data
	fldItime: bytea("fld_itime"),
	fldZbver: integer("fld_zbver"),
});

export const tblXwwlRosetop = pgTable("tbl_xwwl_rosetop", {
	id: serial().notNull(),
	fldName: text("fld_name"),
	fldSex: integer("fld_sex"),
	fldZx: integer("fld_zx"),
	fldInnum: integer("fld_innum"),
	fldOutnum: integer("fld_outnum"),
});

export const tblXwwlWarehouse = pgTable("tbl_xwwl_warehouse", {
	id: serial().notNull(),
	fldId: text("fld_id"),
	fldName: text("fld_name"),
	fldMoney: text("fld_money"),
	// bytea type for private warehouse items (60 slots)
	fldItem: bytea("fld_item"),
});

export const vinhdubangphaixephang = pgTable("vinhdubangphaixephang", {
	id: serial().notNull(),
	fldName: text("fld_name"),
	fldZx: integer("fld_zx"),
	fldLevel: integer("fld_level"),
	fldBp: text("fld_bp"),
	fldJob: integer("fld_job"),
	fldJobLevel: integer("fld_job_level"),
	fldRy: integer("fld_ry"),
	thoigian: timestamp({ mode: 'string' }),
	fldFq: text("fld_fq"),
});

export const xwwlGameserverinfo = pgTable("xwwl_gameserverinfo", {
	serverid: integer(),
	itemcount: integer(),
});

export const thienmathancungDanhsach = pgTable("thienmathancung_danhsach", {
	bangChiemThanh: text("bang_chiem_thanh"),
	ngayChiemThanh: text("ngay_chiem_thanh"),
	congThanhCuonghoaLevel: text("cong_thanh_cuonghoa_level"),
	thoigianLammoiCongthanh: timestamp("thoigian_lammoi_congthanh", { mode: 'string' }),
	id: serial().primaryKey().notNull(),
});

export const tblXwwlPvp = pgTable("tbl_xwwl_pvp", {
	santapten: text(),
	aNguoichoi: text("a_nguoichoi"),
	bNguoichoi: text("b_nguoichoi"),
	agietnguoisoluong: integer(),
	bgietnguoisoluong: integer(),
	aChaytronsolan: integer("a_chaytronsolan"),
	bChaytronsolan: integer("b_chaytronsolan"),
	athuduocnguyenbao: integer(),
	bthuduocnguyenbao: integer(),
	tranhtaiketqua: text(),
	id: serial().primaryKey().notNull(),
});
export const vSynthesisSummary = pgView("v_synthesis_summary", {	characterName: varchar("character_name", { length: 30 }),
	synthesisType: varchar("synthesis_type", { length: 30 }),
	result: varchar({ length: 30 }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	totalAttempts: bigint("total_attempts", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	successfulAttempts: bigint("successful_attempts", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	failedAttempts: bigint("failed_attempts", { mode: "number" }),
	successRatePercent: numeric("success_rate_percent"),
}).as(sql`SELECT fld_name AS character_name, fld_type AS synthesis_type, fld_success AS result, count(*) AS total_attempts, count( CASE WHEN fld_success::text = 'Success'::text THEN 1 ELSE NULL::integer END) AS successful_attempts, count( CASE WHEN fld_success::text = 'Failed'::text THEN 1 ELSE NULL::integer END) AS failed_attempts, round(count( CASE WHEN fld_success::text = 'Success'::text THEN 1 ELSE NULL::integer END)::numeric * 100.0 / count(*)::numeric, 2) AS success_rate_percent FROM syntheticrecord WHERE created_at >= (CURRENT_DATE - '30 days'::interval) GROUP BY fld_name, fld_type, fld_success ORDER BY fld_name, fld_type`);