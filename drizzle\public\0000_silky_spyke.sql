-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TABLE "cheduocvatphamdanhsach" (
	"id" serial NOT NULL,
	"vatpham_id" integer,
	"vatphamten" text,
	"vatphamsoluong" integer,
	"canvatpham" text
);
--> statement-breakpoint
CREATE TABLE "chetacvatphamdanhsach" (
	"id" serial NOT NULL,
	"vatpham_id" integer,
	"vatphamten" text,
	"vatphamsoluong" integer,
	"chetaoloaihinh" integer,
	"chetaodangcap" integer,
	"canvatpham" text
);
--> statement-breakpoint
CREATE TABLE "dangcapbanthuong" (
	"id" serial NOT NULL,
	"dangcap" integer,
	"vohuan" integer,
	"nguyenbao" integer,
	"tienbac" integer,
	"sinhmenh" integer,
	"congkich" integer,
	"phongngu" integer,
	"netranh" integer,
	"trungdich" integer,
	"noicong" integer,
	"setitem" integer,
	"goivatpham" text,
	CONSTRAINT "dangcapbanthuong_unique" UNIQUE("id")
);
--> statement-breakpoint
CREATE TABLE "giftcode" (
	"id" serial NOT NULL,
	"code" text,
	"type" integer
);
--> statement-breakpoint
CREATE TABLE "giftcode_rewards" (
	"id" serial NOT NULL,
	"type" integer,
	"rewards" text,
	"note" text
);
--> statement-breakpoint
CREATE TABLE "kiemtrathietbi" (
	"id" serial NOT NULL,
	"vatphamloaihinh" integer,
	"vatphamcaonhatcongkichgiatri" integer,
	"vatphamcaonhatphongngugiatri" integer,
	"vatphamcaonhathpgiatri" integer,
	"vatphamcaonhatnoiconggiatri" integer,
	"vatphamcaonhattrungdichgiatri" integer,
	"vatphamcaonhatnetranhgiatri" integer,
	"vatphamcaonhatcongkichvoconggiatri" integer,
	"vatphamcaonhatkhiconggiatri" integer,
	"vatphamcaonhatphuhongiatri" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_itemoption" (
	"id" serial NOT NULL,
	"fld_pid" integer,
	"fld_name" text,
	"bonus_hp" integer,
	"bonus_percenthp" integer,
	"bonus_mp" integer,
	"bonus_percentmp" integer,
	"bonus_atk" integer,
	"bonus_percentatk" integer,
	"bonus_percentdf" integer,
	"bonus_df" integer,
	"bonus_percentatkskill" integer,
	"bonus_defskill" integer,
	"bonus_qigong" integer,
	"bonus_dropgold" integer,
	"bonus_exp" integer,
	"bonus_lucky" integer,
	"bonus_accuracy" integer,
	"bonus_evasion" integer,
	"bonus_diemhoangkim" integer,
	"bonus_atkmonster" integer,
	"bonus_defmonster" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_upgrade_item" (
	"id" serial NOT NULL,
	"itemid" integer,
	"itemname" text,
	"itemlevel" integer,
	"itemtype" integer,
	"upgrade_pp" integer,
	"nguyenlieu_id" integer,
	"giamcuonghoa" integer,
	"yeucaucuonghoa" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_bossdrop" (
	"fld_level1" integer,
	"fld_level2" integer,
	"fld_pid" integer,
	"fld_name" text,
	"fld_magic0" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_socapphuhon" integer,
	"fld_trungcapphuhon" integer,
	"fld_tienhoa" integer,
	"fld_khoalai" integer,
	"fld_pp" integer,
	"fld_sunx" text,
	"comothongbao" integer,
	"fld_days" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_drop" (
	"fld_level1" integer,
	"fld_level2" integer,
	"fld_pid" integer,
	"fld_name" text,
	"fld_magic0" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_socapphuhon" integer,
	"fld_trungcapphuhon" integer,
	"fld_tienhoa" integer,
	"fld_khoalai" integer,
	"fld_pp" integer,
	"fld_sunx" text,
	"comothongbao" integer,
	"fld_days" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_drop_dch" (
	"fld_level1" integer,
	"fld_level2" integer,
	"fld_pid" integer,
	"fld_name" text,
	"fld_magic0" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_socapphuhon" integer,
	"fld_trungcapphuhon" integer,
	"fld_tienhoa" integer,
	"fld_khoalai" integer,
	"fld_pp" integer,
	"fld_sunx" text,
	"comothongbao" integer,
	"fld_days" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_drop_gs" (
	"fld_level1" integer,
	"fld_level2" integer,
	"fld_pid" integer,
	"fld_name" text,
	"fld_magic0" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_socapphuhon" integer,
	"fld_trungcapphuhon" integer,
	"fld_tienhoa" integer,
	"fld_khoalai" integer,
	"fld_pp" integer,
	"fld_sunx" text,
	"comothongbao" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_gg" (
	"id" serial NOT NULL,
	"txt" text,
	"type" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_item" (
	"fld_pid" integer,
	"fld_name" text,
	"fld_reside1" integer,
	"fld_reside2" integer,
	"fld_sex" integer,
	"fld_level" integer,
	"fld_up_level" integer,
	"fld_recycle_money" integer,
	"fld_sale_money" integer,
	"fld_questitem" integer,
	"fld_nj" integer,
	"fld_df" integer,
	"fld_at1" integer,
	"fld_at2" integer,
	"fld_ap" integer,
	"fld_job_level" integer,
	"fld_zx" integer,
	"fld_el" integer,
	"fld_wx" integer,
	"fld_wxjd" integer,
	"fld_money" integer,
	"fld_weight" integer,
	"fld_type" integer,
	"fld_need_money" integer,
	"fld_need_fightexp" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_magic5" integer,
	"fld_side" integer,
	"fld_sell_type" integer,
	"fld_lock" integer,
	"fld_series" integer,
	"fld_integration" integer,
	"fld_des" text,
	"fld_head_wear" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_mission" (
	"fld_id" integer,
	"fld_pid" integer,
	"fld_name" text,
	"fld_level" integer,
	"fld_zx" integer,
	"fld_npcid" integer,
	"fld_npcname" text,
	"fld_need_item" text,
	"fld_stages" integer,
	"fld_msg" text,
	"fld_on" integer,
	"fld_map" integer,
	"fld_x" integer,
	"fld_y" integer,
	"fld_type" integer,
	"fld_job" integer,
	"fld_get_item" text,
	"fld_data" "bytea"
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_monster" (
	"fld_pid" integer,
	"fld_name" text,
	"fld_level" integer,
	"fld_hp" integer,
	"fld_at" integer,
	"fld_df" integer,
	"fld_exp" integer,
	"fld_boss" integer,
	"fld_auto" integer,
	"fld_npc" integer,
	"fld_quest" integer,
	"fld_questid" integer,
	"fld_stages" integer,
	"fld_questitem" integer,
	"fld_pp" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_monster_set_base" (
	"fld_index" serial PRIMARY KEY NOT NULL,
	"fld_pid" integer,
	"fld_x" double precision,
	"fld_z" double precision,
	"fld_y" double precision,
	"fld_face0" double precision,
	"fld_face" double precision,
	"fld_mid" integer,
	"fld_name" text,
	"fld_hp" bigint,
	"fld_at" bigint,
	"fld_df" bigint,
	"fld_npc" integer,
	"fld_newtime" integer,
	"fld_level" integer,
	"fld_exp" integer,
	"fld_auto" integer,
	"fld_boss" integer,
	"fld_gold" integer,
	"fld_accuracy" integer,
	"fld_evasion" integer,
	"fld_qitemdrop" integer,
	"fld_qdroppp" integer,
	"fld_freedrop" integer,
	"fld_amount" integer,
	"fld_aoe" integer,
	"fld_active" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_map" (
	"fld_mid" integer,
	"fld_name" text,
	"fld_file" text,
	"x" double precision,
	"y" double precision,
	"name" text
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_npc" (
	"fld_index" integer,
	"fld_pid" integer,
	"fld_x" double precision,
	"fld_z" double precision,
	"fld_y" double precision,
	"fld_face0" double precision,
	"fld_face" double precision,
	"fld_mid" integer,
	"fld_name" text,
	"fld_hp" bigint,
	"fld_at" bigint,
	"fld_df" bigint,
	"fld_npc" integer,
	"fld_newtime" integer,
	"fld_level" integer,
	"fld_exp" integer,
	"fld_auto" integer,
	"fld_boss" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_open" (
	"fld_pid" integer,
	"fld_pidx" integer,
	"fld_number" integer,
	"fld_name" text,
	"fld_namex" text,
	"fld_pp" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_magic5" integer,
	"fld_fj_thuctinh" integer,
	"fld_fj_tienhoa" integer,
	"fld_fj_trungcapphuhon" integer,
	"fld_bd" integer,
	"fld_days" integer,
	"comothongbao" integer,
	"stt_hop_event" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_opn" (
	"fld_index" integer,
	"fld_pid" integer,
	"fld_name" text,
	"fld_pidx" integer,
	"fld_namex" text,
	"fld_number" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_magic5" integer,
	"fld_fj_觉醒" integer,
	"fld_fj_进化" integer,
	"fld_fj_中级附魂" integer,
	"fld_bd" integer,
	"fld_days" integer,
	"fld_pp" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_sell" (
	"id" serial NOT NULL,
	"fld_npcname" text,
	"fld_nid" bigint,
	"fld_index" integer,
	"fld_pid" bigint,
	"fld_money" bigint,
	"fld_magic0" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_canvohuan" integer,
	"fld_days" integer,
	"fld_bd" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_skill" (
	"fld_id" integer,
	"fld_pid" integer,
	"fld_index" integer,
	"fld_job" integer,
	"fld_name" text,
	"fld_bonusratevalueperpoint1" double precision,
	"fld_bonusratevalueperpoint2" double precision,
	"fld_des" text
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_stone" (
	"id" serial NOT NULL,
	"fld_type" integer,
	"fld_value" integer,
	"fld_tanggiam" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_vome" (
	"id" serial NOT NULL,
	"map" integer,
	"x" double precision,
	"y" double precision,
	"z" double precision,
	"tomap" integer,
	"tox" double precision,
	"toy" double precision,
	"toz" double precision
);
--> statement-breakpoint
CREATE TABLE "thangthienkhicong" (
	"id" serial NOT NULL,
	"khicongid" integer,
	"fld_bonusratevalueperpoint" double precision,
	"vatpham_id" integer,
	"khicongten" text,
	"nhanvatnghenghiep1" integer,
	"nhanvatnghenghiep2" integer,
	"nhanvatnghenghiep3" integer,
	"nhanvatnghenghiep4" integer,
	"nhanvatnghenghiep5" integer,
	"nhanvatnghenghiep6" integer,
	"nhanvatnghenghiep7" integer,
	"nhanvatnghenghiep8" integer,
	"nhanvatnghenghiep9" integer,
	"nhanvatnghenghiep10" integer,
	"nhanvatnghenghiep11" integer,
	"nhanvatnghenghiep12" integer,
	"nhanvatnghenghiep13" integer
);
--> statement-breakpoint
CREATE TABLE "vatphamtraodoi" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "vatphamtraodoi_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 27 CACHE 1),
	"canvatpham" text,
	"vohuan" integer,
	"nguyenbao" integer,
	"tienbac" text,
	"sinhmenh" integer,
	"congkich" integer,
	"phongngu" integer,
	"netranh" integer,
	"trungdich" integer,
	"noicong" integer,
	"setitem" integer,
	"goivatpham" text,
	"mieuta" text
);
--> statement-breakpoint
CREATE TABLE "xwwl_kill" (
	"id" serial NOT NULL,
	"txt" text,
	"sffh" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_xwwl_kongfu" (
	"fld_pid" integer,
	"fld_name" text,
	"fld_source_at" integer,
	"fld_at" integer,
	"fld_mp" integer,
	"fld_level" integer,
	"fld_needexp" integer,
	"fld_job" integer,
	"fld_zx" integer,
	"fld_joblevel" integer,
	"fld_type" integer,
	"fld_effert" integer,
	"fld_index" integer,
	"fld_congkichsoluong" integer,
	"fld_vocongloaihinh" integer,
	"fld_moicapnguyhai" text,
	"fld_moicapthemnguyhai" integer,
	"fld_moicapthemmp" integer,
	"fld_moicapthemlichluyen" integer,
	"fld_moicapthemtuluyendangcap" integer,
	"fld_moicapvocongdiemso" integer,
	"fld_vocongtoicaodangcap" integer,
	"fld_time" integer,
	"fld_deathtime" integer,
	"fld_cdtime" integer,
	"time_animation" integer,
	"id" serial PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "itmeclss" (
	"id" integer PRIMARY KEY NOT NULL,
	"fld_type" integer,
	"fld_name" text,
	"fld_reside" integer,
	"fld_magic0" integer,
	"fld_magic1" integer,
	"fld_magic2" integer,
	"fld_magic3" integer,
	"fld_magic4" integer,
	"fld_magic5" integer,
	"fld_fj_nj" integer,
	"fld_days" integer,
	"fld_fj_thuctinh" integer,
	"fld_fj_trungcapphuhon" integer,
	"fld_fj_tienhoa" integer,
	"fld_sql" text,
	"fld_bd" integer
);
--> statement-breakpoint
CREATE TABLE "tbl_pill" (
	"id" serial PRIMARY KEY NOT NULL,
	"pill_id" integer NOT NULL,
	"pill_name" varchar,
	"level_use" varchar,
	"bonus_hp" integer NOT NULL,
	"bonus_hppercent" integer NOT NULL,
	"bonus_mp" integer NOT NULL,
	"bonus_mppercent" integer NOT NULL,
	"bonus_atk" integer NOT NULL,
	"bonus_atkpercent" integer NOT NULL,
	"bonus_df" integer NOT NULL,
	"bonus_dfpercent" integer NOT NULL,
	"bonus_evasion" integer NOT NULL,
	"bonus_evapercent" integer NOT NULL,
	"bonus_accuracy" integer NOT NULL,
	"bonus_accupercent" integer NOT NULL,
	"bonus_atkskillpercent" integer NOT NULL,
	"bonus_dfskill" integer NOT NULL,
	"bonus_dfskillpercent" integer NOT NULL,
	"bonus_abilities" integer NOT NULL,
	"bonus_lucky" integer NOT NULL,
	"bonus_goldpercent" integer NOT NULL,
	"bonus_droppercent" integer NOT NULL,
	"bonus_exppercent" integer NOT NULL,
	"upgrade_weapon" integer NOT NULL,
	"upgrade_armor" integer NOT NULL,
	"pill_time" integer NOT NULL,
	"pill_days" integer NOT NULL,
	"public_pill" integer NOT NULL,
	"pill_merge" integer NOT NULL,
	"cant_use" varchar,
	"on_off" integer NOT NULL,
	"hatch_item" integer NOT NULL,
	"bonus_diemhoangkim" integer NOT NULL,
	"tanghoa" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tbl_hatchitem" (
	"ID" serial PRIMARY KEY NOT NULL,
	"FLD_PID" integer NOT NULL,
	"FLD_NAME" varchar(200),
	"FLD_PIDX" integer NOT NULL,
	"FLD_NAMEX" varchar(200),
	"FLD_Number" integer NOT NULL,
	"FLD_PP" integer NOT NULL,
	"FLD_MAGIC0" integer NOT NULL,
	"FLD_MAGIC1" integer NOT NULL,
	"FLD_MAGIC2" integer NOT NULL,
	"FLD_MAGIC3" integer NOT NULL,
	"FLD_MAGIC4" integer NOT NULL,
	"FLD_LowSoul" integer NOT NULL,
	"FLD_MedSoul" integer NOT NULL,
	"FLD_Quality" integer NOT NULL,
	"FLD_Lock" integer NOT NULL,
	"FLD_ExpiryDate" integer NOT NULL,
	"FLD_Announce" integer NOT NULL
);

*/