{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.cheduocvatphamdanhsach": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "vatpham_id": {"name": "vatpham_id", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamten": {"name": "vat<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "vatphamsoluong": {"name": "vatphamsoluong", "type": "integer", "primaryKey": false, "notNull": false}, "canvatpham": {"name": "<PERSON>vat<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.chetacvatphamdanhsach": {"name": "<PERSON>eta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "vatpham_id": {"name": "vatpham_id", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamten": {"name": "vat<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "vatphamsoluong": {"name": "vatphamsoluong", "type": "integer", "primaryKey": false, "notNull": false}, "chetaoloaihinh": {"name": "cheta<PERSON>aihinh", "type": "integer", "primaryKey": false, "notNull": false}, "chetaodangcap": {"name": "chetaodangcap", "type": "integer", "primaryKey": false, "notNull": false}, "canvatpham": {"name": "<PERSON>vat<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.dangcapbanthuong": {"name": "dang<PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "dangcap": {"name": "dangcap", "type": "integer", "primaryKey": false, "notNull": false}, "vohuan": {"name": "vohuan", "type": "integer", "primaryKey": false, "notNull": false}, "nguyenbao": {"name": "nguyenbao", "type": "integer", "primaryKey": false, "notNull": false}, "tienbac": {"name": "tienbac", "type": "integer", "primaryKey": false, "notNull": false}, "sinhmenh": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "congkich": {"name": "congkich", "type": "integer", "primaryKey": false, "notNull": false}, "phongngu": {"name": "phongngu", "type": "integer", "primaryKey": false, "notNull": false}, "netranh": {"name": "netranh", "type": "integer", "primaryKey": false, "notNull": false}, "trungdich": {"name": "trung<PERSON>h", "type": "integer", "primaryKey": false, "notNull": false}, "noicong": {"name": "noicong", "type": "integer", "primaryKey": false, "notNull": false}, "setitem": {"name": "setitem", "type": "integer", "primaryKey": false, "notNull": false}, "goivatpham": {"name": "goivatpham", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"dangcapbanthuong_unique": {"columns": ["id"], "nullsNotDistinct": false, "name": "dangcapbanthuong_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.giftcode": {"name": "giftcode", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.giftcode_rewards": {"name": "giftcode_rewards", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "integer", "primaryKey": false, "notNull": false}, "rewards": {"name": "rewards", "type": "text", "primaryKey": false, "notNull": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.kiemtrathietbi": {"name": "kiemtrathietbi", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "vatphamloaihinh": {"name": "vat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhatcongkichgiatri": {"name": "vatphamcaonhatcongki<PERSON>tri", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhatphongngugiatri": {"name": "vatphamcaonhat<PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhathpgiatri": {"name": "vatphamcaonhathp<PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhatnoiconggiatri": {"name": "vatphamcaonhatnoiconggiatri", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhattrungdichgiatri": {"name": "vatphamcaonhattrungdichgiatri", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhatnetranhgiatri": {"name": "vatphamcaonhatnetranh<PERSON>tri", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhatcongkichvoconggiatri": {"name": "vatphamcaonhatcongkichvoconggiatri", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhatkhiconggiatri": {"name": "vatphamcaonhatkhicong<PERSON>tri", "type": "integer", "primaryKey": false, "notNull": false}, "vatphamcaonhatphuhongiatri": {"name": "vat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_itemoption": {"name": "tbl_itemoption", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "bonus_hp": {"name": "bonus_hp", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_percenthp": {"name": "bonus_percenthp", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_mp": {"name": "bonus_mp", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_percentmp": {"name": "bonus_percentmp", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_atk": {"name": "bonus_atk", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_percentatk": {"name": "bonus_percentatk", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_percentdf": {"name": "bonus_percentdf", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_df": {"name": "bonus_df", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_percentatkskill": {"name": "bonus_percentatkskill", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_defskill": {"name": "bonus_defskill", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_qigong": {"name": "bonus_qigong", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_dropgold": {"name": "bonus_dropgold", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_exp": {"name": "bonus_exp", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_lucky": {"name": "bonus_lucky", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_accuracy": {"name": "bonus_accuracy", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_evasion": {"name": "bonus_evasion", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_diemhoangkim": {"name": "bonus_diem<PERSON><PERSON>kim", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_atkmonster": {"name": "bonus_atk<PERSON>ter", "type": "integer", "primaryKey": false, "notNull": false}, "bonus_defmonster": {"name": "bonus_defmonster", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_upgrade_item": {"name": "tbl_upgrade_item", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "itemid": {"name": "itemid", "type": "integer", "primaryKey": false, "notNull": false}, "itemname": {"name": "itemname", "type": "text", "primaryKey": false, "notNull": false}, "itemlevel": {"name": "itemlevel", "type": "integer", "primaryKey": false, "notNull": false}, "itemtype": {"name": "itemtype", "type": "integer", "primaryKey": false, "notNull": false}, "upgrade_pp": {"name": "upgrade_pp", "type": "integer", "primaryKey": false, "notNull": false}, "nguyenlieu_id": {"name": "nguyenlieu_id", "type": "integer", "primaryKey": false, "notNull": false}, "giamcuonghoa": {"name": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "yeucaucuonghoa": {"name": "yeucaucuonghoa", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_bossdrop": {"name": "tbl_xwwl_bossdrop", "schema": "", "columns": {"fld_level1": {"name": "fld_level1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level2": {"name": "fld_level2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_magic0": {"name": "fld_magic0", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_socapphuhon": {"name": "fld_socapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_trungcapphuhon": {"name": "fld_trungcapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_tienhoa": {"name": "fld_tienhoa", "type": "integer", "primaryKey": false, "notNull": false}, "fld_khoalai": {"name": "fld_khoalai", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pp": {"name": "fld_pp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sunx": {"name": "fld_sunx", "type": "text", "primaryKey": false, "notNull": false}, "comothongbao": {"name": "comothongbao", "type": "integer", "primaryKey": false, "notNull": false}, "fld_days": {"name": "fld_days", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_drop": {"name": "tbl_xwwl_drop", "schema": "", "columns": {"fld_level1": {"name": "fld_level1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level2": {"name": "fld_level2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_magic0": {"name": "fld_magic0", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_socapphuhon": {"name": "fld_socapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_trungcapphuhon": {"name": "fld_trungcapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_tienhoa": {"name": "fld_tienhoa", "type": "integer", "primaryKey": false, "notNull": false}, "fld_khoalai": {"name": "fld_khoalai", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pp": {"name": "fld_pp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sunx": {"name": "fld_sunx", "type": "text", "primaryKey": false, "notNull": false}, "comothongbao": {"name": "comothongbao", "type": "integer", "primaryKey": false, "notNull": false}, "fld_days": {"name": "fld_days", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_drop_dch": {"name": "tbl_xwwl_drop_dch", "schema": "", "columns": {"fld_level1": {"name": "fld_level1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level2": {"name": "fld_level2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_magic0": {"name": "fld_magic0", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_socapphuhon": {"name": "fld_socapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_trungcapphuhon": {"name": "fld_trungcapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_tienhoa": {"name": "fld_tienhoa", "type": "integer", "primaryKey": false, "notNull": false}, "fld_khoalai": {"name": "fld_khoalai", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pp": {"name": "fld_pp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sunx": {"name": "fld_sunx", "type": "text", "primaryKey": false, "notNull": false}, "comothongbao": {"name": "comothongbao", "type": "integer", "primaryKey": false, "notNull": false}, "fld_days": {"name": "fld_days", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_drop_gs": {"name": "tbl_xwwl_drop_gs", "schema": "", "columns": {"fld_level1": {"name": "fld_level1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level2": {"name": "fld_level2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_magic0": {"name": "fld_magic0", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_socapphuhon": {"name": "fld_socapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_trungcapphuhon": {"name": "fld_trungcapphuhon", "type": "integer", "primaryKey": false, "notNull": false}, "fld_tienhoa": {"name": "fld_tienhoa", "type": "integer", "primaryKey": false, "notNull": false}, "fld_khoalai": {"name": "fld_khoalai", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pp": {"name": "fld_pp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sunx": {"name": "fld_sunx", "type": "text", "primaryKey": false, "notNull": false}, "comothongbao": {"name": "comothongbao", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_gg": {"name": "tbl_xwwl_gg", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "txt": {"name": "txt", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_item": {"name": "tbl_xwwl_item", "schema": "", "columns": {"fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_reside1": {"name": "fld_reside1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_reside2": {"name": "fld_reside2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sex": {"name": "fld_sex", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_up_level": {"name": "fld_up_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_recycle_money": {"name": "fld_recycle_money", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sale_money": {"name": "fld_sale_money", "type": "integer", "primaryKey": false, "notNull": false}, "fld_questitem": {"name": "fld_questitem", "type": "integer", "primaryKey": false, "notNull": false}, "fld_nj": {"name": "fld_nj", "type": "integer", "primaryKey": false, "notNull": false}, "fld_df": {"name": "fld_df", "type": "integer", "primaryKey": false, "notNull": false}, "fld_at1": {"name": "fld_at1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_at2": {"name": "fld_at2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_ap": {"name": "fld_ap", "type": "integer", "primaryKey": false, "notNull": false}, "fld_job_level": {"name": "fld_job_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_zx": {"name": "fld_zx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_el": {"name": "fld_el", "type": "integer", "primaryKey": false, "notNull": false}, "fld_wx": {"name": "fld_wx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_wxjd": {"name": "fld_wxjd", "type": "integer", "primaryKey": false, "notNull": false}, "fld_money": {"name": "fld_money", "type": "integer", "primaryKey": false, "notNull": false}, "fld_weight": {"name": "fld_weight", "type": "integer", "primaryKey": false, "notNull": false}, "fld_type": {"name": "fld_type", "type": "integer", "primaryKey": false, "notNull": false}, "fld_need_money": {"name": "fld_need_money", "type": "integer", "primaryKey": false, "notNull": false}, "fld_need_fightexp": {"name": "fld_need_fightexp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic5": {"name": "fld_magic5", "type": "integer", "primaryKey": false, "notNull": false}, "fld_side": {"name": "fld_side", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sell_type": {"name": "fld_sell_type", "type": "integer", "primaryKey": false, "notNull": false}, "fld_lock": {"name": "fld_lock", "type": "integer", "primaryKey": false, "notNull": false}, "fld_series": {"name": "fld_series", "type": "integer", "primaryKey": false, "notNull": false}, "fld_integration": {"name": "fld_integration", "type": "integer", "primaryKey": false, "notNull": false}, "fld_des": {"name": "fld_des", "type": "text", "primaryKey": false, "notNull": false}, "fld_head_wear": {"name": "fld_head_wear", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_mission": {"name": "tbl_xwwl_mission", "schema": "", "columns": {"fld_id": {"name": "fld_id", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_zx": {"name": "fld_zx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_npcid": {"name": "fld_npcid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_npcname": {"name": "fld_npcname", "type": "text", "primaryKey": false, "notNull": false}, "fld_need_item": {"name": "fld_need_item", "type": "text", "primaryKey": false, "notNull": false}, "fld_stages": {"name": "fld_stages", "type": "integer", "primaryKey": false, "notNull": false}, "fld_msg": {"name": "fld_msg", "type": "text", "primaryKey": false, "notNull": false}, "fld_on": {"name": "fld_on", "type": "integer", "primaryKey": false, "notNull": false}, "fld_map": {"name": "fld_map", "type": "integer", "primaryKey": false, "notNull": false}, "fld_x": {"name": "fld_x", "type": "integer", "primaryKey": false, "notNull": false}, "fld_y": {"name": "fld_y", "type": "integer", "primaryKey": false, "notNull": false}, "fld_type": {"name": "fld_type", "type": "integer", "primaryKey": false, "notNull": false}, "fld_job": {"name": "fld_job", "type": "integer", "primaryKey": false, "notNull": false}, "fld_get_item": {"name": "fld_get_item", "type": "text", "primaryKey": false, "notNull": false}, "fld_data": {"name": "fld_data", "type": "bytea", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_monster": {"name": "tbl_xwwl_monster", "schema": "", "columns": {"fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_hp": {"name": "fld_hp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_at": {"name": "fld_at", "type": "integer", "primaryKey": false, "notNull": false}, "fld_df": {"name": "fld_df", "type": "integer", "primaryKey": false, "notNull": false}, "fld_exp": {"name": "fld_exp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_boss": {"name": "fld_boss", "type": "integer", "primaryKey": false, "notNull": false}, "fld_auto": {"name": "fld_auto", "type": "integer", "primaryKey": false, "notNull": false}, "fld_npc": {"name": "fld_npc", "type": "integer", "primaryKey": false, "notNull": false}, "fld_quest": {"name": "fld_quest", "type": "integer", "primaryKey": false, "notNull": false}, "fld_questid": {"name": "fld_questid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_stages": {"name": "fld_stages", "type": "integer", "primaryKey": false, "notNull": false}, "fld_questitem": {"name": "fld_questitem", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pp": {"name": "fld_pp", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_monster_set_base": {"name": "tbl_xwwl_monster_set_base", "schema": "", "columns": {"fld_index": {"name": "fld_index", "type": "serial", "primaryKey": true, "notNull": true}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_x": {"name": "fld_x", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_z": {"name": "fld_z", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_y": {"name": "fld_y", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_face0": {"name": "fld_face0", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_face": {"name": "fld_face", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_mid": {"name": "fld_mid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_hp": {"name": "fld_hp", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_at": {"name": "fld_at", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_df": {"name": "fld_df", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_npc": {"name": "fld_npc", "type": "integer", "primaryKey": false, "notNull": false}, "fld_newtime": {"name": "fld_newtime", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_exp": {"name": "fld_exp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_auto": {"name": "fld_auto", "type": "integer", "primaryKey": false, "notNull": false}, "fld_boss": {"name": "fld_boss", "type": "integer", "primaryKey": false, "notNull": false}, "fld_gold": {"name": "fld_gold", "type": "integer", "primaryKey": false, "notNull": false}, "fld_accuracy": {"name": "fld_accuracy", "type": "integer", "primaryKey": false, "notNull": false}, "fld_evasion": {"name": "fld_evasion", "type": "integer", "primaryKey": false, "notNull": false}, "fld_qitemdrop": {"name": "fld_qitemdrop", "type": "integer", "primaryKey": false, "notNull": false}, "fld_qdroppp": {"name": "fld_qdroppp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_freedrop": {"name": "fld_freedrop", "type": "integer", "primaryKey": false, "notNull": false}, "fld_amount": {"name": "fld_amount", "type": "integer", "primaryKey": false, "notNull": false}, "fld_aoe": {"name": "fld_aoe", "type": "integer", "primaryKey": false, "notNull": false}, "fld_active": {"name": "fld_active", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_map": {"name": "tbl_xwwl_map", "schema": "", "columns": {"fld_mid": {"name": "fld_mid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_file": {"name": "fld_file", "type": "text", "primaryKey": false, "notNull": false}, "x": {"name": "x", "type": "double precision", "primaryKey": false, "notNull": false}, "y": {"name": "y", "type": "double precision", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_npc": {"name": "tbl_xwwl_npc", "schema": "", "columns": {"fld_index": {"name": "fld_index", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_x": {"name": "fld_x", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_z": {"name": "fld_z", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_y": {"name": "fld_y", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_face0": {"name": "fld_face0", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_face": {"name": "fld_face", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_mid": {"name": "fld_mid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_hp": {"name": "fld_hp", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_at": {"name": "fld_at", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_df": {"name": "fld_df", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_npc": {"name": "fld_npc", "type": "integer", "primaryKey": false, "notNull": false}, "fld_newtime": {"name": "fld_newtime", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_exp": {"name": "fld_exp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_auto": {"name": "fld_auto", "type": "integer", "primaryKey": false, "notNull": false}, "fld_boss": {"name": "fld_boss", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_open": {"name": "tbl_xwwl_open", "schema": "", "columns": {"fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pidx": {"name": "fld_pidx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_number": {"name": "fld_number", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_namex": {"name": "fld_namex", "type": "text", "primaryKey": false, "notNull": false}, "fld_pp": {"name": "fld_pp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic5": {"name": "fld_magic5", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_thuctinh": {"name": "fld_fj_thuctinh", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_tienhoa": {"name": "fld_fj_tie<PERSON>oa", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_trungcapphuhon": {"name": "fld_fj_trung<PERSON><PERSON><PERSON>on", "type": "integer", "primaryKey": false, "notNull": false}, "fld_bd": {"name": "fld_bd", "type": "integer", "primaryKey": false, "notNull": false}, "fld_days": {"name": "fld_days", "type": "integer", "primaryKey": false, "notNull": false}, "comothongbao": {"name": "comothongbao", "type": "integer", "primaryKey": false, "notNull": false}, "stt_hop_event": {"name": "stt_hop_event", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_opn": {"name": "tbl_xwwl_opn", "schema": "", "columns": {"fld_index": {"name": "fld_index", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_pidx": {"name": "fld_pidx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_namex": {"name": "fld_namex", "type": "text", "primaryKey": false, "notNull": false}, "fld_number": {"name": "fld_number", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic5": {"name": "fld_magic5", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_觉醒": {"name": "fld_fj_觉醒", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_进化": {"name": "fld_fj_进化", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_中级附魂": {"name": "fld_fj_中级附魂", "type": "integer", "primaryKey": false, "notNull": false}, "fld_bd": {"name": "fld_bd", "type": "integer", "primaryKey": false, "notNull": false}, "fld_days": {"name": "fld_days", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pp": {"name": "fld_pp", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_sell": {"name": "tbl_xwwl_sell", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_npcname": {"name": "fld_npcname", "type": "text", "primaryKey": false, "notNull": false}, "fld_nid": {"name": "fld_nid", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_index": {"name": "fld_index", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_money": {"name": "fld_money", "type": "bigint", "primaryKey": false, "notNull": false}, "fld_magic0": {"name": "fld_magic0", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_canvohuan": {"name": "fld_canvohuan", "type": "integer", "primaryKey": false, "notNull": false}, "fld_days": {"name": "fld_days", "type": "integer", "primaryKey": false, "notNull": false}, "fld_bd": {"name": "fld_bd", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_skill": {"name": "tbl_xwwl_skill", "schema": "", "columns": {"fld_id": {"name": "fld_id", "type": "integer", "primaryKey": false, "notNull": false}, "fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_index": {"name": "fld_index", "type": "integer", "primaryKey": false, "notNull": false}, "fld_job": {"name": "fld_job", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_bonusratevalueperpoint1": {"name": "fld_bonusratevalueperpoint1", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_bonusratevalueperpoint2": {"name": "fld_bonusratevalueperpoint2", "type": "double precision", "primaryKey": false, "notNull": false}, "fld_des": {"name": "fld_des", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_stone": {"name": "tbl_xwwl_stone", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "fld_type": {"name": "fld_type", "type": "integer", "primaryKey": false, "notNull": false}, "fld_value": {"name": "fld_value", "type": "integer", "primaryKey": false, "notNull": false}, "fld_tanggiam": {"name": "fld_tanggiam", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_vome": {"name": "tbl_xwwl_vome", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "map": {"name": "map", "type": "integer", "primaryKey": false, "notNull": false}, "x": {"name": "x", "type": "double precision", "primaryKey": false, "notNull": false}, "y": {"name": "y", "type": "double precision", "primaryKey": false, "notNull": false}, "z": {"name": "z", "type": "double precision", "primaryKey": false, "notNull": false}, "tomap": {"name": "tomap", "type": "integer", "primaryKey": false, "notNull": false}, "tox": {"name": "tox", "type": "double precision", "primaryKey": false, "notNull": false}, "toy": {"name": "toy", "type": "double precision", "primaryKey": false, "notNull": false}, "toz": {"name": "toz", "type": "double precision", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.thangthienkhicong": {"name": "thangthienkhicong", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "khicongid": {"name": "k<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "fld_bonusratevalueperpoint": {"name": "fld_bonusratevalueperpoint", "type": "double precision", "primaryKey": false, "notNull": false}, "vatpham_id": {"name": "vatpham_id", "type": "integer", "primaryKey": false, "notNull": false}, "khicongten": {"name": "k<PERSON>ongten", "type": "text", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep1": {"name": "nhanvatnghenghiep1", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep2": {"name": "nhanvatnghenghiep2", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep3": {"name": "nhanvatnghenghiep3", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep4": {"name": "nhanvatnghenghiep4", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep5": {"name": "nhanvatnghenghiep5", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep6": {"name": "nhanvatnghenghiep6", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep7": {"name": "nhanvatnghenghiep7", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep8": {"name": "nhanvatnghenghiep8", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep9": {"name": "nhanvatnghenghiep9", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep10": {"name": "nhanvatnghenghiep10", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep11": {"name": "nhanvatnghenghiep11", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep12": {"name": "nhanvatnghenghiep12", "type": "integer", "primaryKey": false, "notNull": false}, "nhanvatnghenghiep13": {"name": "nhanvatnghenghiep13", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vatphamtraodoi": {"name": "vat<PERSON><PERSON><PERSON><PERSON>oi", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "vatphamtraodoi_id_seq", "increment": "1", "minValue": "1", "maxValue": "2147483647", "startWith": "27", "cache": "1", "cycle": false, "schema": "public"}}, "canvatpham": {"name": "<PERSON>vat<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "vohuan": {"name": "vohuan", "type": "integer", "primaryKey": false, "notNull": false}, "nguyenbao": {"name": "nguyenbao", "type": "integer", "primaryKey": false, "notNull": false}, "tienbac": {"name": "tienbac", "type": "text", "primaryKey": false, "notNull": false}, "sinhmenh": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "congkich": {"name": "congkich", "type": "integer", "primaryKey": false, "notNull": false}, "phongngu": {"name": "phongngu", "type": "integer", "primaryKey": false, "notNull": false}, "netranh": {"name": "netranh", "type": "integer", "primaryKey": false, "notNull": false}, "trungdich": {"name": "trung<PERSON>h", "type": "integer", "primaryKey": false, "notNull": false}, "noicong": {"name": "noicong", "type": "integer", "primaryKey": false, "notNull": false}, "setitem": {"name": "setitem", "type": "integer", "primaryKey": false, "notNull": false}, "goivatpham": {"name": "goivatpham", "type": "text", "primaryKey": false, "notNull": false}, "mieuta": {"name": "mieuta", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.xwwl_kill": {"name": "xwwl_kill", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}, "txt": {"name": "txt", "type": "text", "primaryKey": false, "notNull": false}, "sffh": {"name": "sffh", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_xwwl_kongfu": {"name": "tbl_xwwl_kongfu", "schema": "", "columns": {"fld_pid": {"name": "fld_pid", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_source_at": {"name": "fld_source_at", "type": "integer", "primaryKey": false, "notNull": false}, "fld_at": {"name": "fld_at", "type": "integer", "primaryKey": false, "notNull": false}, "fld_mp": {"name": "fld_mp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_level": {"name": "fld_level", "type": "integer", "primaryKey": false, "notNull": false}, "fld_needexp": {"name": "fld_needexp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_job": {"name": "fld_job", "type": "integer", "primaryKey": false, "notNull": false}, "fld_zx": {"name": "fld_zx", "type": "integer", "primaryKey": false, "notNull": false}, "fld_joblevel": {"name": "fld_joblevel", "type": "integer", "primaryKey": false, "notNull": false}, "fld_type": {"name": "fld_type", "type": "integer", "primaryKey": false, "notNull": false}, "fld_effert": {"name": "fld_effert", "type": "integer", "primaryKey": false, "notNull": false}, "fld_index": {"name": "fld_index", "type": "integer", "primaryKey": false, "notNull": false}, "fld_congkichsoluong": {"name": "fld_congkichsoluong", "type": "integer", "primaryKey": false, "notNull": false}, "fld_vocongloaihinh": {"name": "fld_vocongloaihinh", "type": "integer", "primaryKey": false, "notNull": false}, "fld_moicapnguyhai": {"name": "fld_moicapnguyhai", "type": "text", "primaryKey": false, "notNull": false}, "fld_moicapthemnguyhai": {"name": "fld_moicapthemnguyhai", "type": "integer", "primaryKey": false, "notNull": false}, "fld_moicapthemmp": {"name": "fld_moicapthemmp", "type": "integer", "primaryKey": false, "notNull": false}, "fld_moicapthemlichluyen": {"name": "fld_moicapthemlichluyen", "type": "integer", "primaryKey": false, "notNull": false}, "fld_moicapthemtuluyendangcap": {"name": "fld_moicapthemtuluyendangcap", "type": "integer", "primaryKey": false, "notNull": false}, "fld_moicapvocongdiemso": {"name": "fld_moicapvocongdiemso", "type": "integer", "primaryKey": false, "notNull": false}, "fld_vocongtoicaodangcap": {"name": "fld_vocongtoicaodangcap", "type": "integer", "primaryKey": false, "notNull": false}, "fld_time": {"name": "fld_time", "type": "integer", "primaryKey": false, "notNull": false}, "fld_deathtime": {"name": "fld_deathtime", "type": "integer", "primaryKey": false, "notNull": false}, "fld_cdtime": {"name": "fld_cdtime", "type": "integer", "primaryKey": false, "notNull": false}, "time_animation": {"name": "time_animation", "type": "integer", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.itmeclss": {"name": "itmeclss", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "fld_type": {"name": "fld_type", "type": "integer", "primaryKey": false, "notNull": false}, "fld_name": {"name": "fld_name", "type": "text", "primaryKey": false, "notNull": false}, "fld_reside": {"name": "fld_reside", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic0": {"name": "fld_magic0", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic1": {"name": "fld_magic1", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic2": {"name": "fld_magic2", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic3": {"name": "fld_magic3", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic4": {"name": "fld_magic4", "type": "integer", "primaryKey": false, "notNull": false}, "fld_magic5": {"name": "fld_magic5", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_nj": {"name": "fld_fj_nj", "type": "integer", "primaryKey": false, "notNull": false}, "fld_days": {"name": "fld_days", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_thuctinh": {"name": "fld_fj_thuctinh", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_trungcapphuhon": {"name": "fld_fj_trung<PERSON><PERSON><PERSON>on", "type": "integer", "primaryKey": false, "notNull": false}, "fld_fj_tienhoa": {"name": "fld_fj_tie<PERSON>oa", "type": "integer", "primaryKey": false, "notNull": false}, "fld_sql": {"name": "fld_sql", "type": "text", "primaryKey": false, "notNull": false}, "fld_bd": {"name": "fld_bd", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_pill": {"name": "tbl_pill", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "pill_id": {"name": "pill_id", "type": "integer", "primaryKey": false, "notNull": true}, "pill_name": {"name": "pill_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "level_use": {"name": "level_use", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "bonus_hp": {"name": "bonus_hp", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_hppercent": {"name": "bonus_hppercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_mp": {"name": "bonus_mp", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_mppercent": {"name": "bonus_mppercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_atk": {"name": "bonus_atk", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_atkpercent": {"name": "bonus_atkpercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_df": {"name": "bonus_df", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_dfpercent": {"name": "bonus_dfpercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_evasion": {"name": "bonus_evasion", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_evapercent": {"name": "bonus_evapercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_accuracy": {"name": "bonus_accuracy", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_accupercent": {"name": "bonus_accupercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_atkskillpercent": {"name": "bonus_atkskillpercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_dfskill": {"name": "bonus_dfskill", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_dfskillpercent": {"name": "bonus_dfskillpercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_abilities": {"name": "bonus_abilities", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_lucky": {"name": "bonus_lucky", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_goldpercent": {"name": "bonus_goldpercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_droppercent": {"name": "bonus_droppercent", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_exppercent": {"name": "bonus_exppercent", "type": "integer", "primaryKey": false, "notNull": true}, "upgrade_weapon": {"name": "upgrade_weapon", "type": "integer", "primaryKey": false, "notNull": true}, "upgrade_armor": {"name": "upgrade_armor", "type": "integer", "primaryKey": false, "notNull": true}, "pill_time": {"name": "pill_time", "type": "integer", "primaryKey": false, "notNull": true}, "pill_days": {"name": "pill_days", "type": "integer", "primaryKey": false, "notNull": true}, "public_pill": {"name": "public_pill", "type": "integer", "primaryKey": false, "notNull": true}, "pill_merge": {"name": "pill_merge", "type": "integer", "primaryKey": false, "notNull": true}, "cant_use": {"name": "cant_use", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "on_off": {"name": "on_off", "type": "integer", "primaryKey": false, "notNull": true}, "hatch_item": {"name": "hatch_item", "type": "integer", "primaryKey": false, "notNull": true}, "bonus_diemhoangkim": {"name": "bonus_diem<PERSON><PERSON>kim", "type": "integer", "primaryKey": false, "notNull": true}, "tanghoa": {"name": "tanghoa", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tbl_hatchitem": {"name": "tbl_hatchitem", "schema": "", "columns": {"ID": {"name": "ID", "type": "serial", "primaryKey": true, "notNull": true}, "FLD_PID": {"name": "FLD_PID", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_NAME": {"name": "FLD_NAME", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "FLD_PIDX": {"name": "FLD_PIDX", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_NAMEX": {"name": "FLD_NAMEX", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "FLD_Number": {"name": "FLD_Number", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_PP": {"name": "FLD_PP", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_MAGIC0": {"name": "FLD_MAGIC0", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_MAGIC1": {"name": "FLD_MAGIC1", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_MAGIC2": {"name": "FLD_MAGIC2", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_MAGIC3": {"name": "FLD_MAGIC3", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_MAGIC4": {"name": "FLD_MAGIC4", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_LowSoul": {"name": "FLD_LowSoul", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_MedSoul": {"name": "FLD_MedSoul", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_Quality": {"name": "FLD_Quality", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_Lock": {"name": "FLD_Lock", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_ExpiryDate": {"name": "FLD_ExpiryDate", "type": "integer", "primaryKey": false, "notNull": true}, "FLD_Announce": {"name": "FLD_Announce", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}