import { pgTable, serial, integer, text, unique, doublePrecision, bigint, varchar, smallserial, customType } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

const bytea = customType<{ data: string; notNull: false; default: false }>({
  dataType() {
	return "bytea";
  },
  toDriver(value: string): string {
	return value;
  },
  fromDriver(value: unknown): string {
	return value as string;
  },
});

export const cheduocvatphamdanhsach = pgTable("cheduocvatphamdanhsach", {
	id: serial().notNull(),
	vatphamId: integer("vatpham_id"),
	vatphamten: text(),
	vatphamsoluong: integer(),
	canvatpham: text(),
});

export const chetacvatphamdanhsach = pgTable("chetacvatphamdanhsach", {
	id: serial().notNull(),
	vatphamId: integer("vatpham_id"),
	vatphamten: text(),
	vatphamsoluong: integer(),
	chetaoloaihinh: integer(),
	chetaodangcap: integer(),
	canvatpham: text(),
});

export const dangcapbanthuong = pgTable("dangcapbanthuong", {
	id: serial().notNull(),
	dangcap: integer(),
	vohuan: integer(),
	nguyenbao: integer(),
	tienbac: integer(),
	sinhmenh: integer(),
	congkich: integer(),
	phongngu: integer(),
	netranh: integer(),
	trungdich: integer(),
	noicong: integer(),
	setitem: integer(),
	goivatpham: text(),
}, (table) => [
	unique("dangcapbanthuong_unique").on(table.id),
]);

export const giftcode = pgTable("giftcode", {
	id: serial().notNull(),
	code: text(),
	type: integer(),
});

export const giftcodeRewards = pgTable("giftcode_rewards", {
	id: serial().notNull(),
	type: integer(),
	rewards: text(),
	note: text(),
});

export const kiemtrathietbi = pgTable("kiemtrathietbi", {
	id: serial().notNull(),
	vatphamloaihinh: integer(),
	vatphamcaonhatcongkichgiatri: integer(),
	vatphamcaonhatphongngugiatri: integer(),
	vatphamcaonhathpgiatri: integer(),
	vatphamcaonhatnoiconggiatri: integer(),
	vatphamcaonhattrungdichgiatri: integer(),
	vatphamcaonhatnetranhgiatri: integer(),
	vatphamcaonhatcongkichvoconggiatri: integer(),
	vatphamcaonhatkhiconggiatri: integer(),
	vatphamcaonhatphuhongiatri: integer(),
});

export const tblItemoption = pgTable("tbl_itemoption", {
	id: serial().notNull(),
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	bonusHp: integer("bonus_hp"),
	bonusPercenthp: integer("bonus_percenthp"),
	bonusMp: integer("bonus_mp"),
	bonusPercentmp: integer("bonus_percentmp"),
	bonusAtk: integer("bonus_atk"),
	bonusPercentatk: integer("bonus_percentatk"),
	bonusPercentdf: integer("bonus_percentdf"),
	bonusDf: integer("bonus_df"),
	bonusPercentatkskill: integer("bonus_percentatkskill"),
	bonusDefskill: integer("bonus_defskill"),
	bonusQigong: integer("bonus_qigong"),
	bonusDropgold: integer("bonus_dropgold"),
	bonusExp: integer("bonus_exp"),
	bonusLucky: integer("bonus_lucky"),
	bonusAccuracy: integer("bonus_accuracy"),
	bonusEvasion: integer("bonus_evasion"),
	bonusDiemhoangkim: integer("bonus_diemhoangkim"),
	bonusAtkmonster: integer("bonus_atkmonster"),
	bonusDefmonster: integer("bonus_defmonster"),
});

export const tblUpgradeItem = pgTable("tbl_upgrade_item", {
	id: serial().notNull(),
	itemid: integer(),
	itemname: text(),
	itemlevel: integer(),
	itemtype: integer(),
	upgradePp: integer("upgrade_pp"),
	nguyenlieuId: integer("nguyenlieu_id"),
	giamcuonghoa: integer(),
	yeucaucuonghoa: integer(),
});

export const tblXwwlBossdrop = pgTable("tbl_xwwl_bossdrop", {
	fldLevel1: integer("fld_level1"),
	fldLevel2: integer("fld_level2"),
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldMagic0: integer("fld_magic0"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldSocapphuhon: integer("fld_socapphuhon"),
	fldTrungcapphuhon: integer("fld_trungcapphuhon"),
	fldTienhoa: integer("fld_tienhoa"),
	fldKhoalai: integer("fld_khoalai"),
	fldPp: integer("fld_pp"),
	fldSunx: text("fld_sunx"),
	comothongbao: integer(),
	fldDays: integer("fld_days"),
});

export const tblXwwlDrop = pgTable("tbl_xwwl_drop", {
	fldLevel1: integer("fld_level1"),
	fldLevel2: integer("fld_level2"),
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldMagic0: integer("fld_magic0"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldSocapphuhon: integer("fld_socapphuhon"),
	fldTrungcapphuhon: integer("fld_trungcapphuhon"),
	fldTienhoa: integer("fld_tienhoa"),
	fldKhoalai: integer("fld_khoalai"),
	fldPp: integer("fld_pp"),
	fldSunx: text("fld_sunx"),
	comothongbao: integer(),
	fldDays: integer("fld_days"),
});

export const tblXwwlDropDch = pgTable("tbl_xwwl_drop_dch", {
	fldLevel1: integer("fld_level1"),
	fldLevel2: integer("fld_level2"),
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldMagic0: integer("fld_magic0"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldSocapphuhon: integer("fld_socapphuhon"),
	fldTrungcapphuhon: integer("fld_trungcapphuhon"),
	fldTienhoa: integer("fld_tienhoa"),
	fldKhoalai: integer("fld_khoalai"),
	fldPp: integer("fld_pp"),
	fldSunx: text("fld_sunx"),
	comothongbao: integer(),
	fldDays: integer("fld_days"),
});

export const tblXwwlDropGs = pgTable("tbl_xwwl_drop_gs", {
	fldLevel1: integer("fld_level1"),
	fldLevel2: integer("fld_level2"),
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldMagic0: integer("fld_magic0"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldSocapphuhon: integer("fld_socapphuhon"),
	fldTrungcapphuhon: integer("fld_trungcapphuhon"),
	fldTienhoa: integer("fld_tienhoa"),
	fldKhoalai: integer("fld_khoalai"),
	fldPp: integer("fld_pp"),
	fldSunx: text("fld_sunx"),
	comothongbao: integer(),
});

export const tblXwwlGg = pgTable("tbl_xwwl_gg", {
	id: serial().notNull(),
	txt: text(),
	type: integer(),
});

export const tblXwwlItem = pgTable("tbl_xwwl_item", {
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldReside1: integer("fld_reside1"),
	fldReside2: integer("fld_reside2"),
	fldSex: integer("fld_sex"),
	fldLevel: integer("fld_level"),
	fldUpLevel: integer("fld_up_level"),
	fldRecycleMoney: integer("fld_recycle_money"),
	fldSaleMoney: integer("fld_sale_money"),
	fldQuestitem: integer("fld_questitem"),
	fldNj: integer("fld_nj"),
	fldDf: integer("fld_df"),
	fldAt1: integer("fld_at1"),
	fldAt2: integer("fld_at2"),
	fldAp: integer("fld_ap"),
	fldJobLevel: integer("fld_job_level"),
	fldZx: integer("fld_zx"),
	fldEl: integer("fld_el"),
	fldWx: integer("fld_wx"),
	fldWxjd: integer("fld_wxjd"),
	fldMoney: integer("fld_money"),
	fldWeight: integer("fld_weight"),
	fldType: integer("fld_type"),
	fldNeedMoney: integer("fld_need_money"),
	fldNeedFightexp: integer("fld_need_fightexp"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldMagic5: integer("fld_magic5"),
	fldSide: integer("fld_side"),
	fldSellType: integer("fld_sell_type"),
	fldLock: integer("fld_lock"),
	fldSeries: integer("fld_series"),
	fldIntegration: integer("fld_integration"),
	fldDes: text("fld_des"),
	fldHeadWear: integer("fld_head_wear"),
});

export const tblXwwlMission = pgTable("tbl_xwwl_mission", {
	fldId: integer("fld_id"),
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldLevel: integer("fld_level"),
	fldZx: integer("fld_zx"),
	fldNpcid: integer("fld_npcid"),
	fldNpcname: text("fld_npcname"),
	fldNeedItem: text("fld_need_item"),
	fldStages: integer("fld_stages"),
	fldMsg: text("fld_msg"),
	fldOn: integer("fld_on"),
	fldMap: integer("fld_map"),
	fldX: integer("fld_x"),
	fldY: integer("fld_y"),
	fldType: integer("fld_type"),
	fldJob: integer("fld_job"),
	fldGetItem: text("fld_get_item"),
	// TODO: failed to parse database type 'bytea'
	fldData: bytea("fld_data"),
});

export const tblXwwlMonster = pgTable("tbl_xwwl_monster", {
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldLevel: integer("fld_level"),
	fldHp: integer("fld_hp"),
	fldAt: integer("fld_at"),
	fldDf: integer("fld_df"),
	fldExp: integer("fld_exp"),
	fldBoss: integer("fld_boss"),
	fldAuto: integer("fld_auto"),
	fldNpc: integer("fld_npc"),
	fldQuest: integer("fld_quest"),
	fldQuestid: integer("fld_questid"),
	fldStages: integer("fld_stages"),
	fldQuestitem: integer("fld_questitem"),
	fldPp: integer("fld_pp"),
});

export const tblXwwlMonsterSetBase = pgTable("tbl_xwwl_monster_set_base", {
	fldIndex: serial("fld_index").primaryKey().notNull(),
	fldPid: integer("fld_pid"),
	fldX: doublePrecision("fld_x"),
	fldZ: doublePrecision("fld_z"),
	fldY: doublePrecision("fld_y"),
	fldFace0: doublePrecision("fld_face0"),
	fldFace: doublePrecision("fld_face"),
	fldMid: integer("fld_mid"),
	fldName: text("fld_name"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldHp: bigint("fld_hp", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldAt: bigint("fld_at", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldDf: bigint("fld_df", { mode: "number" }),
	fldNpc: integer("fld_npc"),
	fldNewtime: integer("fld_newtime"),
	fldLevel: integer("fld_level"),
	fldExp: integer("fld_exp"),
	fldAuto: integer("fld_auto"),
	fldBoss: integer("fld_boss"),
	fldGold: integer("fld_gold"),
	fldAccuracy: integer("fld_accuracy"),
	fldEvasion: integer("fld_evasion"),
	fldQitemdrop: integer("fld_qitemdrop"),
	fldQdroppp: integer("fld_qdroppp"),
	fldFreedrop: integer("fld_freedrop"),
	fldAmount: integer("fld_amount"),
	fldAoe: integer("fld_aoe"),
	fldActive: integer("fld_active"),
});

export const tblXwwlMap = pgTable("tbl_xwwl_map", {
	fldMid: integer("fld_mid"),
	fldName: text("fld_name"),
	fldFile: text("fld_file"),
	x: doublePrecision(),
	y: doublePrecision(),
	name: text(),
});

export const tblXwwlNpc = pgTable("tbl_xwwl_npc", {
	fldIndex: integer("fld_index"),
	fldPid: integer("fld_pid"),
	fldX: doublePrecision("fld_x"),
	fldZ: doublePrecision("fld_z"),
	fldY: doublePrecision("fld_y"),
	fldFace0: doublePrecision("fld_face0"),
	fldFace: doublePrecision("fld_face"),
	fldMid: integer("fld_mid"),
	fldName: text("fld_name"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldHp: bigint("fld_hp", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldAt: bigint("fld_at", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldDf: bigint("fld_df", { mode: "number" }),
	fldNpc: integer("fld_npc"),
	fldNewtime: integer("fld_newtime"),
	fldLevel: integer("fld_level"),
	fldExp: integer("fld_exp"),
	fldAuto: integer("fld_auto"),
	fldBoss: integer("fld_boss"),
});

export const tblXwwlOpn = pgTable("tbl_xwwl_opn", {
	fldIndex: integer("fld_index"),
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldPidx: integer("fld_pidx"),
	fldNamex: text("fld_namex"),
	fldNumber: integer("fld_number"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldMagic5: integer("fld_magic5"),
	"fldFj觉醒": integer("fld_fj_觉醒"),
	"fldFj进化": integer("fld_fj_进化"),
	"fldFj中级附魂": integer("fld_fj_中级附魂"),
	fldBd: integer("fld_bd"),
	fldDays: integer("fld_days"),
	fldPp: integer("fld_pp"),
});

export const tblXwwlSell = pgTable("tbl_xwwl_sell", {
	id: serial().notNull(),
	fldNpcname: text("fld_npcname"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldNid: bigint("fld_nid", { mode: "number" }),
	fldIndex: integer("fld_index"),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldPid: bigint("fld_pid", { mode: "number" }),
	// You can use { mode: "bigint" } if numbers are exceeding js number limitations
	fldMoney: bigint("fld_money", { mode: "number" }),
	fldMagic0: integer("fld_magic0"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldCanvohuan: integer("fld_canvohuan"),
	fldDays: integer("fld_days"),
	fldBd: integer("fld_bd"),
});

export const tblXwwlSkill = pgTable("tbl_xwwl_skill", {
	fldId: integer("fld_id"),
	fldPid: integer("fld_pid"),
	fldIndex: integer("fld_index"),
	fldJob: integer("fld_job"),
	fldName: text("fld_name"),
	fldBonusratevalueperpoint1: doublePrecision("fld_bonusratevalueperpoint1"),
	fldBonusratevalueperpoint2: doublePrecision("fld_bonusratevalueperpoint2"),
	fldDes: text("fld_des"),
});

export const tblXwwlVome = pgTable("tbl_xwwl_vome", {
	id: serial().notNull(),
	map: integer(),
	x: doublePrecision(),
	y: doublePrecision(),
	z: doublePrecision(),
	tomap: integer(),
	tox: doublePrecision(),
	toy: doublePrecision(),
	toz: doublePrecision(),
});

export const thangthienkhicong = pgTable("thangthienkhicong", {
	id: serial().notNull(),
	khicongid: integer(),
	fldBonusratevalueperpoint: doublePrecision("fld_bonusratevalueperpoint"),
	vatphamId: integer("vatpham_id"),
	khicongten: text(),
	nhanvatnghenghiep1: integer(),
	nhanvatnghenghiep2: integer(),
	nhanvatnghenghiep3: integer(),
	nhanvatnghenghiep4: integer(),
	nhanvatnghenghiep5: integer(),
	nhanvatnghenghiep6: integer(),
	nhanvatnghenghiep7: integer(),
	nhanvatnghenghiep8: integer(),
	nhanvatnghenghiep9: integer(),
	nhanvatnghenghiep10: integer(),
	nhanvatnghenghiep11: integer(),
	nhanvatnghenghiep12: integer(),
	nhanvatnghenghiep13: integer(),
});

export const vatphamtraodoi = pgTable("vatphamtraodoi", {
	id: integer().primaryKey().generatedByDefaultAsIdentity({ name: "vatphamtraodoi_id_seq", startWith: 27, increment: 1, minValue: 1, maxValue: 2147483647, cache: 1 }),
	canvatpham: text(),
	vohuan: integer(),
	nguyenbao: integer(),
	tienbac: text(),
	sinhmenh: integer(),
	congkich: integer(),
	phongngu: integer(),
	netranh: integer(),
	trungdich: integer(),
	noicong: integer(),
	setitem: integer(),
	goivatpham: text(),
	mieuta: text(),
});

export const xwwlKill = pgTable("xwwl_kill", {
	id: serial().notNull(),
	txt: text(),
	sffh: integer(),
});

export const tblXwwlKongfu = pgTable("tbl_xwwl_kongfu", {
	fldPid: integer("fld_pid"),
	fldName: text("fld_name"),
	fldSourceAt: integer("fld_source_at"),
	fldAt: integer("fld_at"),
	fldMp: integer("fld_mp"),
	fldLevel: integer("fld_level"),
	fldNeedexp: integer("fld_needexp"),
	fldJob: integer("fld_job"),
	fldZx: integer("fld_zx"),
	fldJoblevel: integer("fld_joblevel"),
	fldType: integer("fld_type"),
	fldEffert: integer("fld_effert"),
	fldIndex: integer("fld_index"),
	fldCongkichsoluong: integer("fld_congkichsoluong"),
	fldVocongloaihinh: integer("fld_vocongloaihinh"),
	fldMoicapnguyhai: text("fld_moicapnguyhai"),
	fldMoicapthemnguyhai: integer("fld_moicapthemnguyhai"),
	fldMoicapthemmp: integer("fld_moicapthemmp"),
	fldMoicapthemlichluyen: integer("fld_moicapthemlichluyen"),
	fldMoicapthemtuluyendangcap: integer("fld_moicapthemtuluyendangcap"),
	fldMoicapvocongdiemso: integer("fld_moicapvocongdiemso"),
	fldVocongtoicaodangcap: integer("fld_vocongtoicaodangcap"),
	fldTime: integer("fld_time"),
	fldDeathtime: integer("fld_deathtime"),
	fldCdtime: integer("fld_cdtime"),
	timeAnimation: integer("time_animation"),
	id: serial().primaryKey().notNull(),
});

export const itmeclss = pgTable("itmeclss", {
	id: integer().primaryKey().notNull(),
	fldType: integer("fld_type"),
	fldName: text("fld_name"),
	fldReside: integer("fld_reside"),
	fldMagic0: integer("fld_magic0"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldMagic5: integer("fld_magic5"),
	fldFjNj: integer("fld_fj_nj"),
	fldDays: integer("fld_days"),
	fldFjThuctinh: integer("fld_fj_thuctinh"),
	fldFjTrungcapphuhon: integer("fld_fj_trungcapphuhon"),
	fldFjTienhoa: integer("fld_fj_tienhoa"),
	fldSql: text("fld_sql"),
	fldBd: integer("fld_bd"),
});

export const tblPill = pgTable("tbl_pill", {
	id: serial().primaryKey().notNull(),
	pillId: integer("pill_id").notNull(),
	pillName: varchar("pill_name"),
	levelUse: varchar("level_use"),
	bonusHp: integer("bonus_hp").notNull(),
	bonusHppercent: integer("bonus_hppercent").notNull(),
	bonusMp: integer("bonus_mp").notNull(),
	bonusMppercent: integer("bonus_mppercent").notNull(),
	bonusAtk: integer("bonus_atk").notNull(),
	bonusAtkpercent: integer("bonus_atkpercent").notNull(),
	bonusDf: integer("bonus_df").notNull(),
	bonusDfpercent: integer("bonus_dfpercent").notNull(),
	bonusEvasion: integer("bonus_evasion").notNull(),
	bonusEvapercent: integer("bonus_evapercent").notNull(),
	bonusAccuracy: integer("bonus_accuracy").notNull(),
	bonusAccupercent: integer("bonus_accupercent").notNull(),
	bonusAtkskillpercent: integer("bonus_atkskillpercent").notNull(),
	bonusDfskill: integer("bonus_dfskill").notNull(),
	bonusDfskillpercent: integer("bonus_dfskillpercent").notNull(),
	bonusAbilities: integer("bonus_abilities").notNull(),
	bonusLucky: integer("bonus_lucky").notNull(),
	bonusGoldpercent: integer("bonus_goldpercent").notNull(),
	bonusDroppercent: integer("bonus_droppercent").notNull(),
	bonusExppercent: integer("bonus_exppercent").notNull(),
	upgradeWeapon: integer("upgrade_weapon").notNull(),
	upgradeArmor: integer("upgrade_armor").notNull(),
	pillTime: integer("pill_time").notNull(),
	pillDays: integer("pill_days").notNull(),
	publicPill: integer("public_pill").notNull(),
	pillMerge: integer("pill_merge").notNull(),
	cantUse: varchar("cant_use"),
	onOff: integer("on_off").notNull(),
	hatchItem: integer("hatch_item").notNull(),
	bonusDiemhoangkim: integer("bonus_diemhoangkim").notNull(),
	tanghoa: integer().notNull(),
});

export const tblHatchitem = pgTable("tbl_hatchitem", {
	id: serial("ID").primaryKey().notNull(),
	fldPid: integer("FLD_PID").notNull(),
	fldName: varchar("FLD_NAME", { length: 200 }),
	fldPidx: integer("FLD_PIDX").notNull(),
	fldNamex: varchar("FLD_NAMEX", { length: 200 }),
	fldNumber: integer("FLD_Number").notNull(),
	fldPp: integer("FLD_PP").notNull(),
	fldMagic0: integer("FLD_MAGIC0").notNull(),
	fldMagic1: integer("FLD_MAGIC1").notNull(),
	fldMagic2: integer("FLD_MAGIC2").notNull(),
	fldMagic3: integer("FLD_MAGIC3").notNull(),
	fldMagic4: integer("FLD_MAGIC4").notNull(),
	fldLowSoul: integer("FLD_LowSoul").notNull(),
	fldMedSoul: integer("FLD_MedSoul").notNull(),
	fldQuality: integer("FLD_Quality").notNull(),
	fldLock: integer("FLD_Lock").notNull(),
	fldExpiryDate: integer("FLD_ExpiryDate").notNull(),
	fldAnnounce: integer("FLD_Announce").notNull(),
});

export const tblXwwlStone = pgTable("tbl_xwwl_stone", {
	id: serial().notNull(),
	fldType: integer("fld_type"),
	fldValue: integer("fld_value"),
	fldTanggiam: integer("fld_tanggiam"),
	index: smallserial().notNull(),
});

export const tblXwwlOpen = pgTable("tbl_xwwl_open", {
	fldPid: integer("fld_pid"),
	fldPidx: integer("fld_pidx"),
	fldNumber: integer("fld_number"),
	fldName: text("fld_name"),
	fldNamex: text("fld_namex"),
	fldPp: integer("fld_pp"),
	fldMagic1: integer("fld_magic1"),
	fldMagic2: integer("fld_magic2"),
	fldMagic3: integer("fld_magic3"),
	fldMagic4: integer("fld_magic4"),
	fldMagic5: integer("fld_magic5"),
	fldFjThuctinh: integer("fld_fj_thuctinh"),
	fldFjTienhoa: integer("fld_fj_tienhoa"),
	fldFjTrungcapphuhon: integer("fld_fj_trungcapphuhon"),
	fldBd: integer("fld_bd"),
	fldDays: integer("fld_days"),
	comothongbao: integer(),
	sttHopEvent: integer("stt_hop_event"),
	id: smallserial().primaryKey().notNull(),
});
