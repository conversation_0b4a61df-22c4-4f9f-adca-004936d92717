import { relations } from "drizzle-orm/relations";
import { user, account, session, userRoles, roles } from "./schema";

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	accounts: many(account),
	sessions: many(session),
	userRoles_userId: many(userRoles, {
		relationName: "userRoles_userId_user_id"
	}),
	userRoles_assignedBy: many(userRoles, {
		relationName: "userRoles_assignedBy_user_id"
	}),
}));

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const userRolesRelations = relations(userRoles, ({one}) => ({
	user_userId: one(user, {
		fields: [userRoles.userId],
		references: [user.id],
		relationName: "userRoles_userId_user_id"
	}),
	role: one(roles, {
		fields: [userRoles.roleId],
		references: [roles.id]
	}),
	user_assignedBy: one(user, {
		fields: [userRoles.assignedBy],
		references: [user.id],
		relationName: "userRoles_assignedBy_user_id"
	}),
}));

export const rolesRelations = relations(roles, ({many}) => ({
	userRoles: many(userRoles),
}));