{"name": "web-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3131", "build": "next build", "start": "next start --port 3131", "db:generate": "npx drizzle-kit generate", "db:push": "npx drizzle-kit push", "db:studio": "npx drizzle-kit studio", "db:migrate": "npx drizzle-kit migrate", "db:pull": "npx drizzle-kit pull", "db:seed": "tsx scripts/seed-admin.ts", "test:auth": "tsx scripts/test-auth.ts", "fix:admin": "tsx scripts/fix-admin.ts", "lint": "next lint"}, "dependencies": {"@better-auth/utils": "^0.2.6", "@better-fetch/fetch": "^1.1.18", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.1", "@tanstack/react-table": "^8.21.3", "bcryptjs": "^3.0.2", "better-auth": "^1.3.4", "better-fetch": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.3", "lucide-react": "^0.526.0", "nanoid": "^5.1.5", "next": "15.4.4", "next-themes": "^0.4.6", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}