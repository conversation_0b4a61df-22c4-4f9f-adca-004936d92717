/**
 * Analyze Map Offsets for different YBI versions
 * Run with: npx tsx scripts/analyze-map-offsets.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function analyzeMapOffsets() {
  console.log('🗺️  Analyzing Map Offsets for YBI Versions\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading test file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // Decrypt the file for analysis
    const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
    const view = new DataView(decryptedBuffer);

    // Parse header to get basic info
    const header = (YbiParser as any).parseHeader(view);
    console.log(`📊 Header Info:`);
    console.log(`   Total Items: ${header.totalItems.toLocaleString()}`);
    console.log(`   Header Size: ${header.headerSize}\n`);

    // Calculate base offsets
    const HEADER_SIZE = 8;
    const BYTE_OF_SEPARATION = 0x354; // 852 bytes per item
    const ABILITY_BYTE_LENGTH = 2964;
    const HERO_TITLE_BYTE_LENGTH = 0x48; // 72 bytes
    const MAP_INFO_BYTE_LENGTH = 0x2e8; // 744 bytes

    const skillOffset = HEADER_SIZE + (header.totalItems + 1) * BYTE_OF_SEPARATION + 64;
    console.log(`🎯 Base Offsets:`);
    console.log(`   Skill Offset: 0x${skillOffset.toString(16)} (${skillOffset.toLocaleString()})`);

    // Test each parser config
    for (const config of YBI_PARSER_CONFIGS) {
      console.log(`\n🔍 Testing ${config.name} (${config.id}):`);
      
      const skillLen = config.constants.skillByteLength;
      const abilityOffset = skillOffset + 1024 * skillLen;
      const classNameOffset = abilityOffset + 1024 * ABILITY_BYTE_LENGTH;
      const npcOffset = classNameOffset + 256 * HERO_TITLE_BYTE_LENGTH;
      
      console.log(`   Skill Length: ${skillLen} bytes`);
      console.log(`   Ability Offset: 0x${abilityOffset.toString(16)} (${abilityOffset.toLocaleString()})`);
      console.log(`   Class Name Offset: 0x${classNameOffset.toString(16)} (${classNameOffset.toLocaleString()})`);
      console.log(`   NPC Offset: 0x${npcOffset.toString(16)} (${npcOffset.toLocaleString()})`);

      // Calculate current map offset
      const currentMapOffset = npcOffset + config.constants.maxNpcs * config.constants.npcByteLength + config.constants.npcOffsetAdjustment;
      console.log(`   Current Map Offset: 0x${currentMapOffset.toString(16)} (${currentMapOffset.toLocaleString()})`);

      // Try to find reasonable map data by testing different offsets
      const testOffsets = [
        currentMapOffset,
        currentMapOffset + 1000,
        currentMapOffset + 2000,
        currentMapOffset + 3000,
        currentMapOffset + 4000,
        currentMapOffset + 5000,
        currentMapOffset - 1000,
        currentMapOffset - 2000,
        currentMapOffset - 3000,
      ];

      let bestOffset = currentMapOffset;
      let bestScore = 0;
      let bestMapCount = 0;

      for (const testOffset of testOffsets) {
        if (testOffset < 0 || testOffset >= view.buffer.byteLength) continue;

        try {
          let validMaps = 0;
          let score = 0;
          
          // Test first 100 maps to see if they look reasonable
          for (let i = 0; i < 100; i++) {
            const mapOffset = testOffset + i * MAP_INFO_BYTE_LENGTH;
            
            if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;

            // Read map ID and name
            const mapId = view.getUint32(mapOffset, true);
            const nameBytes = new Uint8Array(view.buffer, mapOffset + 4, 0x40);
            let name = '';
            for (let j = 0; j < nameBytes.length; j++) {
              if (nameBytes[j] === 0) break;
              name += String.fromCharCode(nameBytes[j]);
            }
            name = name.trim();

            // Check if this looks like valid map data
            if (mapId > 0 && mapId < 10000 && name.length > 0 && name.length < 50) {
              validMaps++;
              score += 10;
              
              // Bonus for reasonable coordinates
              const x = view.getFloat32(mapOffset + 0x44, true);
              const y = view.getFloat32(mapOffset + 0x4C, true);
              const z = view.getFloat32(mapOffset + 0x48, true);
              
              if (!isNaN(x) && !isNaN(y) && !isNaN(z) && 
                  Math.abs(x) < 10000 && Math.abs(y) < 10000 && Math.abs(z) < 10000) {
                score += 5;
              }
            }
          }

          if (score > bestScore) {
            bestScore = score;
            bestOffset = testOffset;
            bestMapCount = validMaps;
          }

        } catch (error) {
          // Skip invalid offsets
        }
      }

      console.log(`   Best Map Offset: 0x${bestOffset.toString(16)} (${bestOffset.toLocaleString()})`);
      console.log(`   Valid Maps Found: ${bestMapCount}/100 tested`);
      console.log(`   Score: ${bestScore}`);
      
      if (bestOffset !== currentMapOffset) {
        const adjustment = bestOffset - currentMapOffset;
        console.log(`   ⚠️  Suggested Adjustment: ${adjustment > 0 ? '+' : ''}${adjustment} bytes`);
        console.log(`   ⚠️  New mapOffsetAdjustment: ${config.constants.npcOffsetAdjustment + adjustment}`);
      } else {
        console.log(`   ✅ Current offset looks correct`);
      }

      // Test actual parsing with current config
      try {
        const parsedFile = YbiParser.parseWithConfig(fileBuffer.buffer, config, 'test.cfg');
        console.log(`   Parsed Maps: ${parsedFile.mapInfos.length}`);
        
        if (parsedFile.mapInfos.length > 0) {
          const firstMap = parsedFile.mapInfos[0];
          const lastMap = parsedFile.mapInfos[parsedFile.mapInfos.length - 1];
          console.log(`   First Map: ID=${firstMap.id}, Name="${firstMap.name}"`);
          console.log(`   Last Map: ID=${lastMap.id}, Name="${lastMap.name}"`);
        }
      } catch (error) {
        console.log(`   ❌ Parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

  } catch (error) {
    console.error(`❌ Error analyzing map offsets: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Map offset analysis completed!');
}

// Run the analysis
analyzeMapOffsets().catch(console.error);
