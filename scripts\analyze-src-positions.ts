#!/usr/bin/env tsx

/**
 * Analyze SRC_POSITIONS array
 */

function analyzeSrcPositions() {
  console.log('🔍 Analyzing SRC_POSITIONS Array\n');

  const SRC_POSITIONS = [
    26, 31, 17, 10, 30, 16, 24, 2,
    29, 8, 20, 15, 28, 11, 13,
    4, 19, 23, 0, 12, 14, 27,
    6, 18, 21, 3, 9, 7, 22,
    1, 25, 5
  ];

  console.log('SRC_POSITIONS array:');
  console.log(SRC_POSITIONS);
  console.log('Length:', SRC_POSITIONS.length);

  // Check for duplicates
  const seen = new Set();
  const duplicates = [];
  for (const pos of SRC_POSITIONS) {
    if (seen.has(pos)) {
      duplicates.push(pos);
    }
    seen.add(pos);
  }

  console.log('\nDuplicates:', duplicates.length > 0 ? duplicates : 'None');

  // Check range
  const min = Math.min(...SRC_POSITIONS);
  const max = Math.max(...SRC_POSITIONS);
  console.log('Range:', min, 'to', max);

  // Check if all positions 0-31 are covered
  const missing = [];
  for (let i = 0; i < 32; i++) {
    if (!SRC_POSITIONS.includes(i)) {
      missing.push(i);
    }
  }

  console.log('Missing positions (0-31):', missing.length > 0 ? missing : 'None');

  // Create mapping
  console.log('\nMapping (old -> new):');
  for (let i = 0; i < SRC_POSITIONS.length; i++) {
    console.log(`${SRC_POSITIONS[i].toString().padStart(2, ' ')} -> ${i.toString().padStart(2, ' ')}`);
  }

  // Create reverse mapping
  console.log('\nReverse mapping (new -> old):');
  const reverseMap = new Array(32);
  for (let i = 0; i < SRC_POSITIONS.length; i++) {
    reverseMap[SRC_POSITIONS[i]] = i;
  }

  for (let i = 0; i < 32; i++) {
    const mapped = reverseMap[i];
    console.log(`${i.toString().padStart(2, ' ')} -> ${mapped !== undefined ? mapped.toString().padStart(2, ' ') : 'XX'}`);
  }

  // Check if transformation is bijective
  const isBijective = SRC_POSITIONS.length === 32 && 
                     duplicates.length === 0 && 
                     missing.length === 0;

  console.log('\nIs bijective (reversible):', isBijective ? '✅ YES' : '❌ NO');

  if (!isBijective) {
    console.log('❌ This explains why encryption is not reversible!');
    console.log('   - Length should be 32, got:', SRC_POSITIONS.length);
    console.log('   - Should have no duplicates, got:', duplicates.length);
    console.log('   - Should have no missing positions, got:', missing.length);
  }

  // Test what happens with missing positions
  if (missing.length > 0) {
    console.log('\n🔍 Testing with missing positions:');
    
    // Create test data with bits set in missing positions
    let testValue = 0;
    for (const pos of missing) {
      testValue |= (1 << pos);
    }
    
    console.log('Test value with missing bits set:', '0x' + (testValue >>> 0).toString(16).padStart(8, '0'));
    console.log('Binary:', (testValue >>> 0).toString(2).padStart(32, '0'));
    
    // Apply transformation
    let transformed = 0;
    for (let i = 0; i < SRC_POSITIONS.length; i++) {
      const bit = (testValue >> SRC_POSITIONS[i]) & 1;
      transformed |= bit << i;
    }
    
    console.log('Transformed:', '0x' + (transformed >>> 0).toString(16).padStart(8, '0'));
    console.log('Result: Missing bits are lost!');
  }
}

analyzeSrcPositions();
