/**
 * Calculate correct map offset adjustments
 * Run with: npx tsx scripts/calculate-correct-offsets.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function calculateCorrectOffsets() {
  console.log('🧮 Calculating correct map offset adjustments\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading and decrypting file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
    const view = new DataView(decryptedBuffer);
    
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // The best map offset we found
    const BEST_MAP_OFFSET = 0x2d96500;
    
    console.log(`🎯 Target map offset: 0x${BEST_MAP_OFFSET.toString(16)} (${BEST_MAP_OFFSET.toLocaleString()})\n`);

    // Calculate what each parser config currently produces
    for (const config of YBI_PARSER_CONFIGS) {
      console.log(`📊 ${config.name} (${config.id}):`);
      
      // Calculate current offsets
      const header = (YbiParser as any).parseHeader(view);
      const skillOffset = 8 + (header.totalItems + 1) * 0x354 + 64;
      const skillLen = config.constants.skillByteLength;
      const abilityOffset = skillOffset + 1024 * skillLen;
      const classNameOffset = abilityOffset + 1024 * 2964;
      const npcOffset = classNameOffset + 256 * 0x48;
      const currentMapOffset = npcOffset + config.constants.maxNpcs * config.constants.npcByteLength + config.constants.npcOffsetAdjustment;
      
      console.log(`   Header items: ${header.totalItems.toLocaleString()}`);
      console.log(`   Skill offset: 0x${skillOffset.toString(16)} (${skillOffset.toLocaleString()})`);
      console.log(`   Skill length: ${skillLen} bytes`);
      console.log(`   Ability offset: 0x${abilityOffset.toString(16)} (${abilityOffset.toLocaleString()})`);
      console.log(`   Class name offset: 0x${classNameOffset.toString(16)} (${classNameOffset.toLocaleString()})`);
      console.log(`   NPC offset: 0x${npcOffset.toString(16)} (${npcOffset.toLocaleString()})`);
      console.log(`   Max NPCs: ${config.constants.maxNpcs.toLocaleString()}`);
      console.log(`   NPC byte length: ${config.constants.npcByteLength}`);
      console.log(`   Current NPC offset adjustment: ${config.constants.npcOffsetAdjustment}`);
      console.log(`   Current map offset: 0x${currentMapOffset.toString(16)} (${currentMapOffset.toLocaleString()})`);
      
      // Calculate needed adjustment
      const neededAdjustment = BEST_MAP_OFFSET - currentMapOffset;
      const newMapOffsetAdjustment = config.constants.npcOffsetAdjustment + neededAdjustment;
      
      console.log(`   Needed adjustment: ${neededAdjustment > 0 ? '+' : ''}${neededAdjustment}`);
      console.log(`   New mapOffsetAdjustment: ${newMapOffsetAdjustment}`);
      console.log('');
    }

    // Verify the calculation by testing one config
    console.log('🔍 Verification test with V24.2:\n');
    
    const v24Config = YBI_PARSER_CONFIGS.find(c => c.id === 'v24_2')!;
    const header = (YbiParser as any).parseHeader(view);
    const skillOffset = 8 + (header.totalItems + 1) * 0x354 + 64;
    const skillLen = v24Config.constants.skillByteLength;
    const abilityOffset = skillOffset + 1024 * skillLen;
    const classNameOffset = abilityOffset + 1024 * 2964;
    const npcOffset = classNameOffset + 256 * 0x48;
    const currentMapOffset = npcOffset + v24Config.constants.maxNpcs * v24Config.constants.npcByteLength + v24Config.constants.npcOffsetAdjustment;
    
    const neededAdjustment = BEST_MAP_OFFSET - currentMapOffset;
    const newMapOffsetAdjustment = v24Config.constants.npcOffsetAdjustment + neededAdjustment;
    
    console.log(`Current calculation: 0x${currentMapOffset.toString(16)}`);
    console.log(`Target offset: 0x${BEST_MAP_OFFSET.toString(16)}`);
    console.log(`Adjustment needed: ${neededAdjustment}`);
    console.log(`New mapOffsetAdjustment: ${newMapOffsetAdjustment}`);
    
    // Test the new calculation
    const testMapOffset = npcOffset + v24Config.constants.maxNpcs * v24Config.constants.npcByteLength + newMapOffsetAdjustment;
    console.log(`Test calculation: 0x${testMapOffset.toString(16)}`);
    console.log(`Match target: ${testMapOffset === BEST_MAP_OFFSET ? '✅ YES' : '❌ NO'}`);

  } catch (error) {
    console.error(`❌ Error calculating offsets: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Offset calculation completed!');
}

// Run the calculation
calculateCorrectOffsets().catch(console.error);
