// <PERSON><PERSON>t để so sánh encryption key với Go version
const fs = require('fs');

// JavaScript version encryption key
const JS_ENCRYPTION_KEY = (() => {
  const key = new Array(256);
  for (let i = 0; i < 256; i++) {
    const part1 = (i >> 4) & 1;
    const part2 = (i >> 2) & 0x18;
    const part3 = (i >> 1) & 0x40;
    const part4 = 2 * ((i & 3) | (4 * ((i & 4) | (2 * (i & 0xF8)))));
    key[i] = part1 | part2 | part3 | part4;
  }
  return key;
})();

// Go version encryption key (từ decrypt.cfg)
function generateGoEncryptionKey() {
  const key = new Array(256);
  for (let i = 0; i < 256; i++) {
    // Theo Go code: ((uint(i>>4) & 1) | (uint(i>>2) & 0x18) | (uint(i>>1) & 0x40) | uint(2*((i&3)|(4*((i&4)|(2*(i&0xF8)))))))
    const val = ((i >> 4) & 1) | ((i >> 2) & 0x18) | ((i >> 1) & 0x40) | (2 * ((i & 3) | (4 * ((i & 4) | (2 * (i & 0xF8))))));
    key[i] = val & 0xFF; // Ensure byte value
  }
  return key;
}

function testEncryptionKeys() {
  console.log('🔑 Comparing Encryption Keys...\n');
  
  const goKey = generateGoEncryptionKey();
  
  console.log('First 20 values comparison:');
  console.log('Index | JS Key | Go Key | Match');
  console.log('------|--------|--------|------');
  
  let matches = 0;
  for (let i = 0; i < 20; i++) {
    const jsVal = JS_ENCRYPTION_KEY[i];
    const goVal = goKey[i];
    const match = jsVal === goVal ? '✅' : '❌';
    if (jsVal === goVal) matches++;
    
    console.log(`${i.toString().padStart(5)} | ${jsVal.toString().padStart(6)} | ${goVal.toString().padStart(6)} | ${match}`);
  }
  
  console.log(`\nMatches in first 20: ${matches}/20`);
  
  // Test all 256 values
  let totalMatches = 0;
  for (let i = 0; i < 256; i++) {
    if (JS_ENCRYPTION_KEY[i] === goKey[i]) totalMatches++;
  }
  
  console.log(`Total matches: ${totalMatches}/256`);
  
  if (totalMatches === 256) {
    console.log('✅ Encryption keys match perfectly!');
  } else {
    console.log('❌ Encryption keys do NOT match!');
    
    // Show first few mismatches
    console.log('\nFirst 10 mismatches:');
    console.log('Index | JS Key | Go Key | Diff');
    console.log('------|--------|--------|-----');
    
    let mismatchCount = 0;
    for (let i = 0; i < 256 && mismatchCount < 10; i++) {
      if (JS_ENCRYPTION_KEY[i] !== goKey[i]) {
        const diff = JS_ENCRYPTION_KEY[i] - goKey[i];
        console.log(`${i.toString().padStart(5)} | ${JS_ENCRYPTION_KEY[i].toString().padStart(6)} | ${goKey[i].toString().padStart(6)} | ${diff.toString().padStart(4)}`);
        mismatchCount++;
      }
    }
  }
  
  return { jsKey: JS_ENCRYPTION_KEY, goKey, totalMatches };
}

// Test với file decrypt.cfg từ Go version
function testWithGoDecryptFile() {
  const decryptPath = 'E:\\YulgangDev\\golang\\YBQToolReloaded\\ybq-editor-go\\decrypt.cfg';
  
  if (!fs.existsSync(decryptPath)) {
    console.log(`❌ Go decrypt file not found: ${decryptPath}`);
    return;
  }
  
  console.log('\n📄 Testing with Go decrypt.cfg...');
  
  const data = fs.readFileSync(decryptPath);
  const bytes = Array.from(data);
  
  console.log(`File size: ${bytes.length} bytes`);
  console.log(`First 20 bytes: [${bytes.slice(0, 20).join(', ')}]`);
  
  // Try to parse first quest ID
  let pos = 0;
  const questIDBytes = [];
  
  while (pos < bytes.length && bytes[pos] !== 32) {
    questIDBytes.push(bytes[pos]);
    pos++;
  }
  
  const questIDStr = String.fromCharCode(...questIDBytes);
  const questID = parseInt(questIDStr);
  
  console.log(`First quest ID bytes: [${questIDBytes.join(', ')}]`);
  console.log(`First quest ID string: "${questIDStr}"`);
  console.log(`First quest ID value: ${questID}`);
  
  if (questID > 0) {
    console.log('✅ Go decrypt file has valid quest ID');
  } else {
    console.log('❌ Go decrypt file has invalid quest ID');
  }
}

function main() {
  console.log('🔍 Encryption Key Comparison Tool');
  console.log('==================================\n');
  
  const result = testEncryptionKeys();
  testWithGoDecryptFile();
  
  return result;
}

if (require.main === module) {
  main();
}

module.exports = { testEncryptionKeys, generateGoEncryptionKey };
