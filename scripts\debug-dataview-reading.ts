#!/usr/bin/env tsx

/**
 * Debug DataView reading to understand the discrepancy
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function debugDataViewReading() {
  console.log('🔍 Debugging DataView Reading\n');

  try {
    // Step 1: Load and parse original file
    console.log('1. Loading original file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    const originalBuffer = fs.readFileSync(originalFilePath);
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    
    const firstItem = originalFile.items[0];
    console.log(`   📋 First item from parsing:`);
    console.log(`      - ID: ${firstItem.id}`);
    console.log(`      - Level: ${firstItem.level}`);
    console.log(`      - Offset: 0x${firstItem.offset.toString(16)}`);

    // Step 2: Get original decrypted buffer
    const originalDecrypted = originalFile.originalDecryptedBuffer!;
    console.log(`   📍 Original decrypted buffer: ${originalDecrypted.byteLength} bytes`);

    // Step 3: Create multiple DataViews and compare readings
    console.log('\n2. Testing DataView readings...');
    
    const itemOffset = firstItem.offset;
    const ID_OFFSET = 0x00;
    const LEVEL_OFFSET = 0x4C;
    
    // Method 1: Direct DataView
    const view1 = new DataView(originalDecrypted);
    const id1 = view1.getUint32(itemOffset + ID_OFFSET, true);
    const level1 = view1.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    // Method 2: DataView from slice
    const sliced = originalDecrypted.slice(itemOffset, itemOffset + 100);
    const view2 = new DataView(sliced);
    const id2 = view2.getUint32(ID_OFFSET, true);
    const level2 = view2.getUint16(LEVEL_OFFSET, true);
    
    // Method 3: Uint8Array reading
    const bytes = new Uint8Array(originalDecrypted);
    const idBytes = [
      bytes[itemOffset + 0],
      bytes[itemOffset + 1], 
      bytes[itemOffset + 2],
      bytes[itemOffset + 3]
    ];
    const levelBytes = [
      bytes[itemOffset + LEVEL_OFFSET],
      bytes[itemOffset + LEVEL_OFFSET + 1]
    ];
    
    // Manual little-endian conversion
    const id3 = idBytes[0] | (idBytes[1] << 8) | (idBytes[2] << 16) | (idBytes[3] << 24);
    const level3 = levelBytes[0] | (levelBytes[1] << 8);
    
    console.log(`   📍 Reading methods comparison:`);
    console.log(`      Method 1 (Direct DataView): ID=${id1}, Level=${level1}`);
    console.log(`      Method 2 (Sliced DataView): ID=${id2}, Level=${level2}`);
    console.log(`      Method 3 (Manual bytes): ID=${id3}, Level=${level3}`);
    console.log(`      Parser result: ID=${firstItem.id}, Level=${firstItem.level}`);

    // Step 4: Show raw bytes at the offset
    console.log('\n3. Raw bytes at item offset:');
    let hexStr = '';
    for (let i = 0; i < 16; i++) {
      const byte = bytes[itemOffset + i];
      hexStr += byte.toString(16).padStart(2, '0') + ' ';
    }
    console.log(`   Offset 0x${itemOffset.toString(16)}: ${hexStr}`);
    
    // Show bytes at level offset specifically
    hexStr = '';
    for (let i = 0; i < 4; i++) {
      const byte = bytes[itemOffset + LEVEL_OFFSET + i];
      hexStr += byte.toString(16).padStart(2, '0') + ' ';
    }
    console.log(`   Level at 0x${(itemOffset + LEVEL_OFFSET).toString(16)}: ${hexStr}`);

    // Step 5: Test after encrypt/decrypt cycle
    console.log('\n4. Testing after encrypt/decrypt cycle...');
    const YbiParserClass = YbiParser as any;
    const encrypted = YbiParserClass.cryptData(originalDecrypted);
    const decrypted = YbiParserClass.cryptData(encrypted);
    
    // Same reading methods on decrypted buffer
    const viewAfter = new DataView(decrypted);
    const idAfter = viewAfter.getUint32(itemOffset + ID_OFFSET, true);
    const levelAfter = viewAfter.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    const bytesAfter = new Uint8Array(decrypted);
    const idBytesAfter = [
      bytesAfter[itemOffset + 0],
      bytesAfter[itemOffset + 1], 
      bytesAfter[itemOffset + 2],
      bytesAfter[itemOffset + 3]
    ];
    const levelBytesAfter = [
      bytesAfter[itemOffset + LEVEL_OFFSET],
      bytesAfter[itemOffset + LEVEL_OFFSET + 1]
    ];
    
    const idManualAfter = idBytesAfter[0] | (idBytesAfter[1] << 8) | (idBytesAfter[2] << 16) | (idBytesAfter[3] << 24);
    const levelManualAfter = levelBytesAfter[0] | (levelBytesAfter[1] << 8);
    
    console.log(`   📍 After encrypt/decrypt cycle:`);
    console.log(`      DataView: ID=${idAfter}, Level=${levelAfter}`);
    console.log(`      Manual: ID=${idManualAfter}, Level=${levelManualAfter}`);

    // Show raw bytes after cycle
    hexStr = '';
    for (let i = 0; i < 16; i++) {
      const byte = bytesAfter[itemOffset + i];
      hexStr += byte.toString(16).padStart(2, '0') + ' ';
    }
    console.log(`   Raw bytes after: ${hexStr}`);

    // Step 6: Compare buffers byte by byte at the specific offset
    console.log('\n5. Byte-by-byte comparison at item offset:');
    for (let i = 0; i < 16; i++) {
      const original = bytes[itemOffset + i];
      const after = bytesAfter[itemOffset + i];
      const match = original === after ? '✅' : '❌';
      console.log(`   Offset +${i.toString(16).padStart(2, '0')}: 0x${original.toString(16).padStart(2, '0')} → 0x${after.toString(16).padStart(2, '0')} ${match}`);
    }

    // Step 7: Check if the issue is buffer reference
    console.log('\n6. Buffer reference check:');
    console.log(`   Original buffer === decrypted buffer: ${originalDecrypted === decrypted}`);
    console.log(`   Original buffer length: ${originalDecrypted.byteLength}`);
    console.log(`   Decrypted buffer length: ${decrypted.byteLength}`);
    
    // Check if buffers are truly identical
    const originalArray = new Uint8Array(originalDecrypted);
    const decryptedArray = new Uint8Array(decrypted);
    
    let totalDiffs = 0;
    for (let i = 0; i < Math.min(originalArray.length, decryptedArray.length); i++) {
      if (originalArray[i] !== decryptedArray[i]) {
        totalDiffs++;
      }
    }
    
    console.log(`   Total buffer differences: ${totalDiffs}`);
    console.log(`   Buffers are identical: ${totalDiffs === 0 ? '✅' : '❌'}`);

    // Step 8: Final analysis
    console.log('\n🎯 ANALYSIS:');
    
    const readingConsistent = (id1 === id2 && id2 === id3 && level1 === level2 && level2 === level3);
    const cyclePreserved = (idAfter === id1 && levelAfter === level1);
    const parserMatches = (firstItem.id === id1 && firstItem.level === level1);
    
    console.log(`   - Reading methods consistent: ${readingConsistent ? '✅' : '❌'}`);
    console.log(`   - Encrypt/decrypt cycle preserved: ${cyclePreserved ? '✅' : '❌'}`);
    console.log(`   - Parser matches DataView: ${parserMatches ? '✅' : '❌'}`);
    
    if (!readingConsistent) {
      console.log('\n🎯 CONCLUSION: DataView reading methods are inconsistent');
    } else if (!cyclePreserved) {
      console.log('\n🎯 CONCLUSION: Encrypt/decrypt cycle is changing values despite identical buffers');
    } else if (!parserMatches) {
      console.log('\n🎯 CONCLUSION: Parser is using different logic than DataView');
    } else {
      console.log('\n🎯 CONCLUSION: All readings are consistent - issue is elsewhere');
    }

  } catch (error) {
    console.error('❌ Debug failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

debugDataViewReading().catch(console.error);
