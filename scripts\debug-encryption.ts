#!/usr/bin/env tsx

/**
 * Debug encryption step by step
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';

function debugEncryption() {
  console.log('🔍 Debugging Encryption Step by Step\n');

  // Test with simple 4-byte data
  const testData = new ArrayBuffer(4);
  const testView = new DataView(testData);
  testView.setUint32(0, 0x12345678, true); // Little endian
  
  const originalBytes = new Uint8Array(testData);
  console.log('Original bytes:', Array.from(originalBytes).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
  
  // Manual encryption step by step
  const SRC_POSITIONS = [
    26, 31, 17, 10, 30, 16, 24, 2,
    29, 8, 20, 15, 28, 11, 13,
    4, 19, 23, 0, 12, 14, 27,
    6, 18, 21, 3, 9, 7, 22,
    1, 25, 5
  ];
  
  // Read as signed 32-bit integer
  let src = originalBytes[0] | (originalBytes[1] << 8) | (originalBytes[2] << 16) | (originalBytes[3] << 24);
  src = src | 0; // Convert to signed 32-bit
  
  console.log('Source integer:', '0x' + (src >>> 0).toString(16).padStart(8, '0'), '(' + src + ')');
  console.log('Source binary:', (src >>> 0).toString(2).padStart(32, '0'));
  
  // Show bit positions
  console.log('\nBit positions in source:');
  for (let i = 0; i < 32; i++) {
    const bit = (src >> i) & 1;
    console.log(`Bit ${i.toString().padStart(2, ' ')}: ${bit}`);
  }
  
  // Apply transformation
  let num = 0;
  console.log('\nTransformation:');
  for (let i = 0; i < SRC_POSITIONS.length; i++) {
    const oldLoc = SRC_POSITIONS[i];
    const newLoc = i;
    const bit = (src >> oldLoc) & 1;
    const moved = bit << newLoc;
    num |= moved;
    console.log(`Move bit from pos ${oldLoc.toString().padStart(2, ' ')} to pos ${newLoc.toString().padStart(2, ' ')}: ${bit} -> contributes 0x${moved.toString(16).padStart(8, '0')}`);
  }
  
  console.log('\nResult integer:', '0x' + (num >>> 0).toString(16).padStart(8, '0'), '(' + num + ')');
  console.log('Result binary:', (num >>> 0).toString(2).padStart(32, '0'));
  
  // Convert back to bytes
  const resultBytes = [
    num & 0xFF,
    (num >>> 8) & 0xFF,
    (num >>> 16) & 0xFF,
    (num >>> 24) & 0xFF
  ];
  
  console.log('Result bytes:', resultBytes.map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
  
  // Test with actual function
  const YbiParserClass = YbiParser as any;
  const encrypted = YbiParserClass.cryptData(testData);
  const encryptedBytes = new Uint8Array(encrypted);
  console.log('Function result:', Array.from(encryptedBytes).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
  
  // Check if they match
  const match = resultBytes.every((b, i) => b === encryptedBytes[i]);
  console.log('Manual vs Function match:', match ? '✅ YES' : '❌ NO');
  
  // Now try reverse transformation
  console.log('\n🔄 Reverse transformation:');
  
  // Create reverse SRC_POSITIONS mapping
  const reverseSrcPositions = new Array(32);
  for (let i = 0; i < SRC_POSITIONS.length; i++) {
    reverseSrcPositions[SRC_POSITIONS[i]] = i;
  }
  
  console.log('Reverse mapping:', reverseSrcPositions);
  
  // Apply reverse transformation
  let reversed = 0;
  for (let i = 0; i < 32; i++) {
    if (reverseSrcPositions[i] !== undefined) {
      const bit = (num >> reverseSrcPositions[i]) & 1;
      reversed |= bit << i;
    }
  }
  
  console.log('Reversed integer:', '0x' + (reversed >>> 0).toString(16).padStart(8, '0'), '(' + reversed + ')');
  console.log('Original integer:', '0x' + (src >>> 0).toString(16).padStart(8, '0'), '(' + src + ')');
  console.log('Reverse match:', reversed === src ? '✅ YES' : '❌ NO');
  
  // Test double encryption
  const doubleEncrypted = YbiParserClass.cryptData(encrypted);
  const doubleEncryptedBytes = new Uint8Array(doubleEncrypted);
  console.log('\nDouble encryption result:', Array.from(doubleEncryptedBytes).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
  
  const doubleMatch = originalBytes.every((b, i) => b === doubleEncryptedBytes[i]);
  console.log('Double encryption match:', doubleMatch ? '✅ YES' : '❌ NO');
}

debugEncryption();
