#!/usr/bin/env tsx

/**
 * Debug item write functionality
 * Compare original vs written item bytes
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function debugItemWrite() {
  console.log('🔍 Debugging Item Write Functionality\n');

  try {
    // Load original file
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    const originalBuffer = fs.readFileSync(originalFilePath);
    console.log(`✅ Loaded file: ${originalBuffer.length} bytes`);

    // Parse original file
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    console.log(`✅ Parsed successfully: ${originalFile.items.length} items`);

    // Get first item for debugging
    const firstItem = originalFile.items[0];
    console.log(`\n🔍 First item details:`);
    console.log(`   ID: ${firstItem.id}`);
    console.log(`   Name: "${firstItem.name}"`);
    console.log(`   Level: ${firstItem.level}`);
    console.log(`   Offset: 0x${firstItem.offset.toString(16)}`);

    // Get original decrypted buffer
    const originalDecrypted = originalFile.originalDecryptedBuffer!;
    const originalView = new DataView(originalDecrypted);

    // Extract original item bytes
    const itemSize = 0x354; // BYTE_OF_SEPARATION
    const itemOffset = firstItem.offset;
    const originalItemBytes = new Uint8Array(originalDecrypted, itemOffset, itemSize);

    console.log(`\n📋 Original item bytes (first 64 bytes):`);
    let hexStr = '';
    for (let i = 0; i < Math.min(64, originalItemBytes.length); i++) {
      hexStr += originalItemBytes[i].toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n';
    }
    console.log(hexStr);

    // Generate new buffer
    const regeneratedBuffer = YbiParser.generate(originalFile);
    const regeneratedView = new DataView(regeneratedBuffer);

    // Extract regenerated item bytes
    const regeneratedItemBytes = new Uint8Array(regeneratedBuffer, itemOffset, itemSize);

    console.log(`\n📝 Regenerated item bytes (first 64 bytes):`);
    hexStr = '';
    for (let i = 0; i < Math.min(64, regeneratedItemBytes.length); i++) {
      hexStr += regeneratedItemBytes[i].toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n';
    }
    console.log(hexStr);

    // Compare byte by byte
    console.log(`\n🔍 Byte-by-byte comparison (first 64 bytes):`);
    let differences = 0;
    for (let i = 0; i < Math.min(64, itemSize); i++) {
      const orig = originalItemBytes[i];
      const regen = regeneratedItemBytes[i];
      if (orig !== regen) {
        console.log(`   Offset +${i.toString(16).padStart(2, '0')}: 0x${orig.toString(16).padStart(2, '0')} → 0x${regen.toString(16).padStart(2, '0')} (${String.fromCharCode(orig)} → ${String.fromCharCode(regen)})`);
        differences++;
      }
    }

    if (differences === 0) {
      console.log(`   ✅ First 64 bytes are identical!`);
    } else {
      console.log(`   ❌ Found ${differences} differences in first 64 bytes`);
    }

    // Check name field specifically
    const NAME_OFFSET = 0x08;
    const NAME_LENGTH = 0x40;
    
    console.log(`\n🔍 Name field comparison:`);
    console.log(`   Name offset: 0x${NAME_OFFSET.toString(16)}`);
    console.log(`   Name length: ${NAME_LENGTH} bytes`);
    
    const originalNameBytes = new Uint8Array(originalDecrypted, itemOffset + NAME_OFFSET, NAME_LENGTH);
    const regeneratedNameBytes = new Uint8Array(regeneratedBuffer, itemOffset + NAME_OFFSET, NAME_LENGTH);
    
    console.log(`   Original name bytes:`);
    hexStr = '';
    for (let i = 0; i < NAME_LENGTH; i++) {
      hexStr += originalNameBytes[i].toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n';
    }
    console.log(hexStr);
    
    console.log(`   Regenerated name bytes:`);
    hexStr = '';
    for (let i = 0; i < NAME_LENGTH; i++) {
      hexStr += regeneratedNameBytes[i].toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n';
    }
    console.log(hexStr);

    // Compare name bytes
    let nameDifferences = 0;
    for (let i = 0; i < NAME_LENGTH; i++) {
      if (originalNameBytes[i] !== regeneratedNameBytes[i]) {
        console.log(`   Name byte ${i}: 0x${originalNameBytes[i].toString(16)} → 0x${regeneratedNameBytes[i].toString(16)}`);
        nameDifferences++;
      }
    }

    if (nameDifferences === 0) {
      console.log(`   ✅ Name bytes are identical!`);
    } else {
      console.log(`   ❌ Found ${nameDifferences} differences in name bytes`);
    }

  } catch (error) {
    console.error('❌ Debug failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the debug
debugItemWrite().catch(console.error);
