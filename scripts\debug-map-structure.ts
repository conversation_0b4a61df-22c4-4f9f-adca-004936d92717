/**
 * Debug Map data structure in YBI file
 * Run with: npx tsx scripts/debug-map-structure.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function debugMapStructure() {
  console.log('🔬 Debugging Map data structure\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading and decrypting file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
    const view = new DataView(decryptedBuffer);
    
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // Test the best offset we found earlier
    const bestOffset = 0x2d9c838; // From previous analysis
    const MAP_INFO_BYTE_LENGTH = 0x2e8; // 744 bytes

    console.log(`🎯 Analyzing map data at offset 0x${bestOffset.toString(16)} (${bestOffset.toLocaleString()}):\n`);

    // Analyze first few maps in detail
    for (let i = 0; i < 5; i++) {
      const mapOffset = bestOffset + i * MAP_INFO_BYTE_LENGTH;
      
      if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) {
        console.log(`Map ${i + 1}: Beyond file boundary`);
        break;
      }

      console.log(`📍 Map ${i + 1} at offset 0x${mapOffset.toString(16)}:`);

      // Read raw bytes for analysis
      const mapBytes = new Uint8Array(view.buffer, mapOffset, Math.min(MAP_INFO_BYTE_LENGTH, 200));
      
      // ID (4 bytes at offset 0x0)
      const id = view.getUint32(mapOffset, true);
      console.log(`   ID (0x00): ${id} (0x${id.toString(16)})`);

      // Name (64 bytes at offset 0x4)
      const nameBytes = new Uint8Array(view.buffer, mapOffset + 4, 64);
      let name = '';
      let nameHex = '';
      for (let j = 0; j < Math.min(nameBytes.length, 20); j++) {
        nameHex += nameBytes[j].toString(16).padStart(2, '0') + ' ';
        if (nameBytes[j] === 0) break;
        if (nameBytes[j] >= 32 && nameBytes[j] <= 126) {
          name += String.fromCharCode(nameBytes[j]);
        } else {
          name += '.';
        }
      }
      console.log(`   Name (0x04): "${name}" [${nameHex.trim()}]`);

      // Coordinates (floats at 0x44, 0x48, 0x4C)
      const x = view.getFloat32(mapOffset + 0x44, true);
      const z = view.getFloat32(mapOffset + 0x48, true);
      const y = view.getFloat32(mapOffset + 0x4C, true);
      console.log(`   X (0x44): ${x} (0x${view.getUint32(mapOffset + 0x44, true).toString(16)})`);
      console.log(`   Z (0x48): ${z} (0x${view.getUint32(mapOffset + 0x48, true).toString(16)})`);
      console.log(`   Y (0x4C): ${y} (0x${view.getUint32(mapOffset + 0x4C, true).toString(16)})`);

      // BGM (128 bytes at 0x68)
      const bgmBytes = new Uint8Array(view.buffer, mapOffset + 0x68, 128);
      let bgm = '';
      let bgmHex = '';
      for (let j = 0; j < Math.min(bgmBytes.length, 20); j++) {
        bgmHex += bgmBytes[j].toString(16).padStart(2, '0') + ' ';
        if (bgmBytes[j] === 0) break;
        if (bgmBytes[j] >= 32 && bgmBytes[j] <= 126) {
          bgm += String.fromCharCode(bgmBytes[j]);
        } else {
          bgm += '.';
        }
      }
      console.log(`   BGM (0x68): "${bgm}" [${bgmHex.trim()}]`);

      // Show first 32 bytes as hex dump
      console.log(`   Hex dump (first 32 bytes):`);
      let hexLine = '   ';
      let asciiLine = '   ';
      for (let j = 0; j < Math.min(32, mapBytes.length); j++) {
        hexLine += mapBytes[j].toString(16).padStart(2, '0') + ' ';
        asciiLine += (mapBytes[j] >= 32 && mapBytes[j] <= 126) ? String.fromCharCode(mapBytes[j]) : '.';
        if ((j + 1) % 16 === 0) {
          console.log(hexLine + ' | ' + asciiLine);
          hexLine = '   ';
          asciiLine = '   ';
        }
      }
      if (hexLine.trim().length > 0) {
        console.log(hexLine.padEnd(51) + ' | ' + asciiLine);
      }

      console.log('');
    }

    // Try different interpretations of the data
    console.log('🔄 Trying different data interpretations:\n');

    // Maybe the structure is different - try reading as different formats
    const testFormats = [
      { name: 'Standard Format', idOffset: 0x0, nameOffset: 0x4, nameLength: 0x40 },
      { name: 'Alternative 1', idOffset: 0x4, nameOffset: 0x8, nameLength: 0x40 },
      { name: 'Alternative 2', idOffset: 0x0, nameOffset: 0x8, nameLength: 0x40 },
      { name: 'Alternative 3', idOffset: 0x0, nameOffset: 0x4, nameLength: 0x20 },
    ];

    for (const format of testFormats) {
      console.log(`📋 Testing ${format.name}:`);
      
      for (let i = 0; i < 3; i++) {
        const mapOffset = bestOffset + i * MAP_INFO_BYTE_LENGTH;
        
        if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;

        const id = view.getUint32(mapOffset + format.idOffset, true);
        const nameBytes = new Uint8Array(view.buffer, mapOffset + format.nameOffset, format.nameLength);
        let name = '';
        for (let j = 0; j < nameBytes.length; j++) {
          if (nameBytes[j] === 0) break;
          if (nameBytes[j] >= 32 && nameBytes[j] <= 126) {
            name += String.fromCharCode(nameBytes[j]);
          }
        }
        name = name.trim();

        console.log(`   Map ${i + 1}: ID=${id}, Name="${name}"`);
      }
      console.log('');
    }

    // Check if we're reading the right section by looking for patterns
    console.log('🔍 Looking for data patterns around the offset:\n');
    
    const searchRange = 10000; // Search 10KB before and after
    const startSearch = Math.max(0, bestOffset - searchRange);
    const endSearch = Math.min(view.buffer.byteLength, bestOffset + searchRange);
    
    console.log(`Searching from 0x${startSearch.toString(16)} to 0x${endSearch.toString(16)}`);
    
    // Look for sequences that might indicate map boundaries
    let potentialMapStarts: number[] = [];
    
    for (let offset = startSearch; offset < endSearch - 8; offset += 4) {
      const id = view.getUint32(offset, true);
      
      // Look for reasonable map IDs (1-1000)
      if (id > 0 && id < 1000) {
        // Check if there's a reasonable name after it
        const nameBytes = new Uint8Array(view.buffer, offset + 4, 32);
        let hasValidName = false;
        let nameLength = 0;
        
        for (let j = 0; j < nameBytes.length; j++) {
          if (nameBytes[j] === 0) break;
          if (nameBytes[j] >= 32 && nameBytes[j] <= 126) {
            nameLength++;
          } else {
            break;
          }
        }
        
        if (nameLength > 2 && nameLength < 30) {
          hasValidName = true;
        }
        
        if (hasValidName) {
          potentialMapStarts.push(offset);
        }
      }
    }
    
    console.log(`Found ${potentialMapStarts.length} potential map start positions:`);
    potentialMapStarts.slice(0, 10).forEach((offset, idx) => {
      const id = view.getUint32(offset, true);
      const nameBytes = new Uint8Array(view.buffer, offset + 4, 32);
      let name = '';
      for (let j = 0; j < nameBytes.length; j++) {
        if (nameBytes[j] === 0) break;
        name += String.fromCharCode(nameBytes[j]);
      }
      console.log(`   ${idx + 1}. 0x${offset.toString(16)}: ID=${id}, Name="${name}"`);
    });

  } catch (error) {
    console.error(`❌ Error debugging map structure: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Map structure debugging completed!');
}

// Run the debug
debugMapStructure().catch(console.error);
