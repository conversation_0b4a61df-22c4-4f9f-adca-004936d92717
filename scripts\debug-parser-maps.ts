/**
 * Debug parser map parsing directly
 * Run with: npx tsx scripts/debug-parser-maps.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function debugParserMaps() {
  console.log('🔬 Debugging parser map parsing\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading test file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // Test V24.2 config specifically
    const v24Config = YBI_PARSER_CONFIGS.find(c => c.id === 'v24_2')!;
    console.log(`🎯 Testing ${v24Config.name} configuration:\n`);

    try {
      const parsedFile = YbiParser.parseWithConfig(fileBuffer.buffer, v24Config, 'test.cfg');
      
      console.log(`✅ Parsing successful!`);
      console.log(`📊 Maps found: ${parsedFile.mapInfos.length}`);
      
      if (parsedFile.mapInfos.length > 0) {
        console.log(`\n🔍 First 10 maps:`);
        for (let i = 0; i < Math.min(10, parsedFile.mapInfos.length); i++) {
          const map = parsedFile.mapInfos[i];
          console.log(`   ${i + 1}. ID=${map.id}, Name="${map.name}", Pos=(${map.x}, ${map.y}, ${map.z}), Offset=0x${map.offset?.toString(16)}`);
        }
        
        console.log(`\n🔍 Last 10 maps:`);
        const start = Math.max(0, parsedFile.mapInfos.length - 10);
        for (let i = start; i < parsedFile.mapInfos.length; i++) {
          const map = parsedFile.mapInfos[i];
          console.log(`   ${i + 1}. ID=${map.id}, Name="${map.name}", Pos=(${map.x}, ${map.y}, ${map.z}), Offset=0x${map.offset?.toString(16)}`);
        }
      }

      // Check what offset the parser actually calculated
      console.log(`\n🧮 Parser offset calculation:`);
      const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
      const view = new DataView(decryptedBuffer);
      const header = (YbiParser as any).parseHeader(view);
      
      const skillOffset = 8 + (header.totalItems + 1) * 0x354 + 64;
      const skillLen = v24Config.constants.skillByteLength;
      const abilityOffset = skillOffset + 1024 * skillLen;
      const classNameOffset = abilityOffset + 1024 * 2964;
      const npcOffset = classNameOffset + 256 * 0x48;
      const mapOffset = npcOffset + v24Config.constants.maxNpcs * v24Config.constants.npcByteLength + v24Config.constants.npcOffsetAdjustment;
      
      console.log(`   Calculated map offset: 0x${mapOffset.toString(16)} (${mapOffset.toLocaleString()})`);
      console.log(`   Expected map offset: 0x2d96500 (47,801,600)`);
      console.log(`   Match: ${mapOffset === 0x2d96500 ? '✅ YES' : '❌ NO'}`);
      
      // Manually test parsing at the calculated offset
      console.log(`\n🔧 Manual test at calculated offset:`);
      const MAP_INFO_BYTE_LENGTH = 0x2e8;
      
      for (let i = 0; i < 5; i++) {
        const testOffset = mapOffset + i * MAP_INFO_BYTE_LENGTH;
        
        if (testOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;
        
        const id = view.getUint32(testOffset, true);
        const nameBytes = new Uint8Array(view.buffer, testOffset + 0x4, 0x40);
        let name = '';
        for (let j = 0; j < nameBytes.length; j++) {
          if (nameBytes[j] === 0) break;
          if (nameBytes[j] >= 32 && nameBytes[j] <= 126) {
            name += String.fromCharCode(nameBytes[j]);
          }
        }
        name = name.trim();
        
        const x = view.getFloat32(testOffset + 0x44, true);
        const y = view.getFloat32(testOffset + 0x4C, true);
        const z = view.getFloat32(testOffset + 0x48, true);
        
        console.log(`   Map ${i + 1}: ID=${id}, Name="${name}", Pos=(${x}, ${y}, ${z})`);
      }
      
      // Test at the known good offset
      console.log(`\n🎯 Manual test at known good offset (0x2d96500):`);
      const goodOffset = 0x2d96500;
      
      for (let i = 0; i < 5; i++) {
        const testOffset = goodOffset + i * MAP_INFO_BYTE_LENGTH;
        
        if (testOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;
        
        const id = view.getUint32(testOffset, true);
        const nameBytes = new Uint8Array(view.buffer, testOffset + 0x4, 0x40);
        let name = '';
        for (let j = 0; j < nameBytes.length; j++) {
          if (nameBytes[j] === 0) break;
          if (nameBytes[j] >= 32 && nameBytes[j] <= 126) {
            name += String.fromCharCode(nameBytes[j]);
          }
        }
        name = name.trim();
        
        const x = view.getFloat32(testOffset + 0x44, true);
        const y = view.getFloat32(testOffset + 0x4C, true);
        const z = view.getFloat32(testOffset + 0x48, true);
        
        console.log(`   Map ${i + 1}: ID=${id}, Name="${name}", Pos=(${x}, ${y}, ${z})`);
      }

    } catch (error) {
      console.log(`❌ Parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error(error);
    }

  } catch (error) {
    console.error(`❌ Error debugging parser maps: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Parser map debugging completed!');
}

// Run the debug
debugParserMaps().catch(console.error);
