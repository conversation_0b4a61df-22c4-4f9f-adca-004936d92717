#!/usr/bin/env tsx

/**
 * Debug raw bytes in detail to find the exact issue
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function debugRawBytesDetailed() {
  console.log('🔍 Debugging Raw Bytes in Detail\n');

  try {
    // Step 1: Load and parse original file
    console.log('1. Loading original file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    const originalBuffer = fs.readFileSync(originalFilePath);
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    
    const firstItem = originalFile.items[0];
    console.log(`   📋 First item from parsing:`);
    console.log(`      - ID: ${firstItem.id}`);
    console.log(`      - Level: ${firstItem.level}`);
    console.log(`      - Offset: 0x${firstItem.offset.toString(16)}`);

    // Step 2: Check raw bytes in original decrypted buffer
    console.log('\n2. Checking raw bytes in original decrypted buffer...');
    const originalDecrypted = originalFile.originalDecryptedBuffer!;
    const originalView = new DataView(originalDecrypted);
    
    const itemOffset = firstItem.offset;
    const ID_OFFSET = 0x00;
    const LEVEL_OFFSET = 0x4C;
    
    console.log(`   📍 Raw bytes at item offset 0x${itemOffset.toString(16)}:`);
    
    // Show first 32 bytes of item
    let hexStr = '';
    for (let i = 0; i < 32; i++) {
      const byte = originalView.getUint8(itemOffset + i);
      hexStr += byte.toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n      ';
    }
    console.log(`      ${hexStr}`);
    
    // Check specific fields
    const rawId = originalView.getUint32(itemOffset + ID_OFFSET, true);
    const rawLevel = originalView.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    console.log(`   📍 Raw field values:`);
    console.log(`      - ID at offset 0x${(itemOffset + ID_OFFSET).toString(16)}: ${rawId}`);
    console.log(`      - Level at offset 0x${(itemOffset + LEVEL_OFFSET).toString(16)}: ${rawLevel}`);

    // Step 3: Modify level directly and check raw bytes
    console.log('\n3. Modifying level directly...');
    const testBuffer = originalDecrypted.slice(0);
    const testView = new DataView(testBuffer);
    
    // Set level to 99
    testView.setUint16(itemOffset + LEVEL_OFFSET, 99, true);
    
    // Check raw bytes after modification
    const modifiedRawId = testView.getUint32(itemOffset + ID_OFFSET, true);
    const modifiedRawLevel = testView.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    console.log(`   📍 After direct modification:`);
    console.log(`      - ID: ${modifiedRawId} (should be ${rawId})`);
    console.log(`      - Level: ${modifiedRawLevel} (should be 99)`);
    
    // Show first 32 bytes after modification
    hexStr = '';
    for (let i = 0; i < 32; i++) {
      const byte = testView.getUint8(itemOffset + i);
      hexStr += byte.toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n      ';
    }
    console.log(`      Modified bytes: ${hexStr}`);

    // Step 4: Encrypt the modified buffer
    console.log('\n4. Encrypting modified buffer...');
    const YbiParserClass = YbiParser as any;
    const encryptedModified = YbiParserClass.cryptData(testBuffer);
    
    // Step 5: Decrypt again and check raw bytes
    console.log('\n5. Decrypting again and checking raw bytes...');
    const decryptedAgain = YbiParserClass.cryptData(encryptedModified);
    const decryptedView = new DataView(decryptedAgain);
    
    const finalRawId = decryptedView.getUint32(itemOffset + ID_OFFSET, true);
    const finalRawLevel = decryptedView.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    console.log(`   📍 After encrypt/decrypt cycle:`);
    console.log(`      - ID: ${finalRawId} (should be ${rawId})`);
    console.log(`      - Level: ${finalRawLevel} (should be 99)`);
    
    // Show first 32 bytes after encrypt/decrypt
    hexStr = '';
    for (let i = 0; i < 32; i++) {
      const byte = decryptedView.getUint8(itemOffset + i);
      hexStr += byte.toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n      ';
    }
    console.log(`      Final bytes: ${hexStr}`);

    // Step 6: Parse the final encrypted buffer and see what parser gets
    console.log('\n6. Parsing final encrypted buffer...');
    const finalParsed = YbiParser.parse(encryptedModified, 'YBi.cfg');
    const finalFirstItem = finalParsed.items[0];
    
    console.log(`   📋 Parser results:`);
    console.log(`      - ID: ${finalFirstItem.id}`);
    console.log(`      - Level: ${finalFirstItem.level}`);
    console.log(`      - Offset: 0x${finalFirstItem.offset.toString(16)}`);

    // Step 7: Check if parser is using wrong offset
    console.log('\n7. Checking if parser uses wrong offset...');
    const parserDecrypted = finalParsed.originalDecryptedBuffer!;
    const parserView = new DataView(parserDecrypted);
    
    // Check what parser thinks is at the item offset
    const parserItemOffset = finalFirstItem.offset;
    const parserRawId = parserView.getUint32(parserItemOffset + ID_OFFSET, true);
    const parserRawLevel = parserView.getUint16(parserItemOffset + LEVEL_OFFSET, true);
    
    console.log(`   📍 Parser's decrypted buffer at offset 0x${parserItemOffset.toString(16)}:`);
    console.log(`      - ID: ${parserRawId}`);
    console.log(`      - Level: ${parserRawLevel}`);
    
    // Show first 32 bytes from parser's perspective
    hexStr = '';
    for (let i = 0; i < 32; i++) {
      const byte = parserView.getUint8(parserItemOffset + i);
      hexStr += byte.toString(16).padStart(2, '0') + ' ';
      if ((i + 1) % 16 === 0) hexStr += '\n      ';
    }
    console.log(`      Parser bytes: ${hexStr}`);

    // Step 8: Compare buffers byte by byte
    console.log('\n8. Comparing decrypted buffers...');
    const originalBytes = new Uint8Array(originalDecrypted);
    const finalBytes = new Uint8Array(decryptedAgain);
    const parserBytes = new Uint8Array(parserDecrypted);
    
    let differences = 0;
    let firstDiff = -1;
    
    for (let i = 0; i < Math.min(originalBytes.length, finalBytes.length); i++) {
      if (originalBytes[i] !== finalBytes[i]) {
        if (firstDiff === -1) firstDiff = i;
        differences++;
        
        if (differences <= 10) {
          console.log(`   Diff at 0x${i.toString(16)}: 0x${originalBytes[i].toString(16)} → 0x${finalBytes[i].toString(16)}`);
        }
      }
    }
    
    console.log(`   📊 Buffer comparison:`);
    console.log(`      - Total differences: ${differences}`);
    console.log(`      - First difference at: 0x${firstDiff.toString(16)}`);
    console.log(`      - Parser buffer same as final: ${parserBytes.length === finalBytes.length && parserBytes.every((b, i) => b === finalBytes[i])}`);

    // Final analysis
    console.log('\n🎯 ANALYSIS:');
    
    const modificationPreserved = (modifiedRawId === rawId && modifiedRawLevel === 99);
    const encryptionPreserved = (finalRawId === rawId && finalRawLevel === 99);
    const parsingCorrect = (parserRawId === rawId && parserRawLevel === 99);
    
    console.log(`   - Direct modification preserved: ${modificationPreserved ? '✅' : '❌'}`);
    console.log(`   - Encryption/decryption preserved: ${encryptionPreserved ? '✅' : '❌'}`);
    console.log(`   - Parser reading correctly: ${parsingCorrect ? '✅' : '❌'}`);
    console.log(`   - Parser offset matches: ${parserItemOffset === itemOffset ? '✅' : '❌'}`);
    
    if (modificationPreserved && encryptionPreserved && !parsingCorrect) {
      console.log('\n🎯 CONCLUSION: Parser is reading from wrong location after re-parsing');
    } else if (modificationPreserved && !encryptionPreserved) {
      console.log('\n🎯 CONCLUSION: Encryption/decryption is corrupting data');
    } else if (!modificationPreserved) {
      console.log('\n🎯 CONCLUSION: Direct modification is not working correctly');
    } else if (parsingCorrect) {
      console.log('\n🎯 CONCLUSION: Everything is working correctly - issue elsewhere');
    }

  } catch (error) {
    console.error('❌ Debug failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

debugRawBytesDetailed().catch(console.error);
