/**
 * Find actual Map data in YBI file by scanning for patterns
 * Run with: npx tsx scripts/find-map-data.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function findMapData() {
  console.log('🔍 Scanning YBI file for Map data patterns\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading and decrypting file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
    const view = new DataView(decryptedBuffer);
    
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes`);
    console.log(`   Decrypted size: ${decryptedBuffer.byteLength.toLocaleString()} bytes\n`);

    const MAP_INFO_BYTE_LENGTH = 0x2e8; // 744 bytes

    // Scan for potential map data by looking for patterns
    console.log('🔍 Scanning for map-like data patterns...\n');
    
    const candidates: Array<{offset: number, score: number, sample: any}> = [];
    const scanStep = 1000; // Scan every 1000 bytes to speed up
    
    for (let offset = 0; offset < view.buffer.byteLength - MAP_INFO_BYTE_LENGTH; offset += scanStep) {
      try {
        let score = 0;
        let validMaps = 0;
        const sampleMaps: any[] = [];
        
        // Test 10 consecutive maps at this offset
        for (let i = 0; i < 10; i++) {
          const mapOffset = offset + i * MAP_INFO_BYTE_LENGTH;
          
          if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;

          // Read potential map data
          const mapId = view.getUint32(mapOffset, true);
          const nameBytes = new Uint8Array(view.buffer, mapOffset + 4, 0x40);
          let name = '';
          for (let j = 0; j < nameBytes.length; j++) {
            if (nameBytes[j] === 0) break;
            if (nameBytes[j] < 32 || nameBytes[j] > 126) break; // Only printable ASCII
            name += String.fromCharCode(nameBytes[j]);
          }
          name = name.trim();

          // Read coordinates
          const x = view.getFloat32(mapOffset + 0x44, true);
          const y = view.getFloat32(mapOffset + 0x4C, true);
          const z = view.getFloat32(mapOffset + 0x48, true);

          // Score this potential map
          let mapScore = 0;
          
          // ID should be reasonable
          if (mapId > 0 && mapId < 10000) mapScore += 20;
          
          // Name should exist and be reasonable
          if (name.length > 0 && name.length < 50) mapScore += 30;
          
          // Coordinates should be reasonable floats
          if (!isNaN(x) && !isNaN(y) && !isNaN(z) && 
              Math.abs(x) < 100000 && Math.abs(y) < 100000 && Math.abs(z) < 100000) {
            mapScore += 20;
          }

          // BGM names (should be strings)
          const bgm1Bytes = new Uint8Array(view.buffer, mapOffset + 0x68, 0x80);
          let bgm1 = '';
          for (let j = 0; j < Math.min(bgm1Bytes.length, 20); j++) {
            if (bgm1Bytes[j] === 0) break;
            if (bgm1Bytes[j] < 32 || bgm1Bytes[j] > 126) break;
            bgm1 += String.fromCharCode(bgm1Bytes[j]);
          }
          if (bgm1.length > 0) mapScore += 10;

          if (mapScore > 30) { // Threshold for "looks like a map"
            validMaps++;
            score += mapScore;
            
            if (sampleMaps.length < 3) {
              sampleMaps.push({
                id: mapId,
                name: name,
                x: x,
                y: y,
                z: z,
                bgm1: bgm1
              });
            }
          }
        }

        if (validMaps >= 3 && score > 200) { // At least 3 valid maps with good score
          candidates.push({
            offset: offset,
            score: score,
            sample: {
              validMaps: validMaps,
              maps: sampleMaps
            }
          });
        }

      } catch (error) {
        // Skip invalid offsets
      }
    }

    // Sort candidates by score
    candidates.sort((a, b) => b.score - a.score);

    console.log(`📊 Found ${candidates.length} potential map data locations:\n`);

    // Show top 10 candidates
    for (let i = 0; i < Math.min(10, candidates.length); i++) {
      const candidate = candidates[i];
      console.log(`${i + 1}. Offset: 0x${candidate.offset.toString(16)} (${candidate.offset.toLocaleString()})`);
      console.log(`   Score: ${candidate.score}`);
      console.log(`   Valid Maps: ${candidate.sample.validMaps}/10`);
      console.log(`   Sample Maps:`);
      
      candidate.sample.maps.forEach((map: any, idx: number) => {
        console.log(`     ${idx + 1}. ID=${map.id}, Name="${map.name}", Pos=(${map.x.toFixed(1)}, ${map.y.toFixed(1)}, ${map.z.toFixed(1)}), BGM="${map.bgm1}"`);
      });
      console.log('');
    }

    // Compare with current parser configs
    console.log('🔧 Comparing with current parser configurations:\n');
    
    for (const config of YBI_PARSER_CONFIGS) {
      const header = (YbiParser as any).parseHeader(view);
      const skillOffset = 8 + (header.totalItems + 1) * 0x354 + 64;
      const skillLen = config.constants.skillByteLength;
      const abilityOffset = skillOffset + 1024 * skillLen;
      const classNameOffset = abilityOffset + 1024 * 2964;
      const npcOffset = classNameOffset + 256 * 0x48;
      const currentMapOffset = npcOffset + config.constants.maxNpcs * config.constants.npcByteLength + config.constants.npcOffsetAdjustment;
      
      console.log(`${config.name} (${config.id}):`);
      console.log(`   Current Map Offset: 0x${currentMapOffset.toString(16)} (${currentMapOffset.toLocaleString()})`);
      
      // Find closest candidate
      let closestCandidate = null;
      let minDistance = Infinity;
      
      for (const candidate of candidates) {
        const distance = Math.abs(candidate.offset - currentMapOffset);
        if (distance < minDistance) {
          minDistance = distance;
          closestCandidate = candidate;
        }
      }
      
      if (closestCandidate) {
        const adjustment = closestCandidate.offset - currentMapOffset;
        console.log(`   Closest Match: 0x${closestCandidate.offset.toString(16)} (score: ${closestCandidate.score})`);
        console.log(`   Distance: ${minDistance.toLocaleString()} bytes`);
        console.log(`   Suggested Adjustment: ${adjustment > 0 ? '+' : ''}${adjustment}`);
        console.log(`   New mapOffsetAdjustment: ${config.constants.npcOffsetAdjustment + adjustment}`);
      } else {
        console.log(`   No good matches found`);
      }
      console.log('');
    }

  } catch (error) {
    console.error(`❌ Error finding map data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('✅ Map data search completed!');
}

// Run the search
findMapData().catch(console.error);
