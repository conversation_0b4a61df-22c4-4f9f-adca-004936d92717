/**
 * Find Map data by looking for sequential ID patterns and coordinate structures
 * Run with: npx tsx scripts/find-maps-by-pattern.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function findMapsByPattern() {
  console.log('🎯 Finding Map data by ID and coordinate patterns\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading and decrypting file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
    const view = new DataView(decryptedBuffer);
    
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    const MAP_INFO_BYTE_LENGTH = 0x2e8; // 744 bytes
    const candidates: Array<{offset: number, score: number, pattern: string, sample: any}> = [];

    console.log('🔍 Scanning for sequential ID patterns...\n');

    // Scan through the file looking for sequential patterns
    const scanStep = 100; // Scan every 100 bytes
    
    for (let offset = 0; offset < view.buffer.byteLength - MAP_INFO_BYTE_LENGTH * 10; offset += scanStep) {
      try {
        // Test different potential map structures
        const testStructures = [
          { idOffset: 0x0, nameOffset: 0x4, coordOffset: 0x44 },
          { idOffset: 0x0, nameOffset: 0x8, coordOffset: 0x48 },
          { idOffset: 0x4, nameOffset: 0x8, coordOffset: 0x4C },
          { idOffset: 0x0, nameOffset: 0x10, coordOffset: 0x50 },
        ];

        for (const structure of testStructures) {
          let score = 0;
          let sequentialIds = 0;
          let validCoords = 0;
          let validNames = 0;
          const sampleMaps: any[] = [];
          
          // Test 10 consecutive maps
          const testCount = 10;
          let lastId = -1;
          
          for (let i = 0; i < testCount; i++) {
            const mapOffset = offset + i * MAP_INFO_BYTE_LENGTH;
            
            if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;

            // Read ID
            const mapId = view.getUint32(mapOffset + structure.idOffset, true);
            
            // Read name
            const nameBytes = new Uint8Array(view.buffer, mapOffset + structure.nameOffset, 64);
            let name = '';
            let nameValid = true;
            for (let j = 0; j < Math.min(nameBytes.length, 32); j++) {
              if (nameBytes[j] === 0) break;
              if (nameBytes[j] < 32 || nameBytes[j] > 126) {
                nameValid = false;
                break;
              }
              name += String.fromCharCode(nameBytes[j]);
            }
            name = name.trim();
            
            // Read coordinates
            const x = view.getFloat32(mapOffset + structure.coordOffset, true);
            const y = view.getFloat32(mapOffset + structure.coordOffset + 4, true);
            const z = view.getFloat32(mapOffset + structure.coordOffset + 8, true);
            
            // Score this map
            let mapScore = 0;
            
            // ID should be reasonable and potentially sequential
            if (mapId > 0 && mapId < 10000) {
              mapScore += 10;
              if (lastId > 0 && (mapId === lastId + 1 || mapId === lastId || Math.abs(mapId - lastId) <= 10)) {
                sequentialIds++;
                mapScore += 20;
              }
              lastId = mapId;
            }
            
            // Name should be valid
            if (nameValid && name.length > 0 && name.length < 50) {
              validNames++;
              mapScore += 15;
            }
            
            // Coordinates should be reasonable floats
            if (!isNaN(x) && !isNaN(y) && !isNaN(z) && 
                Math.abs(x) < 100000 && Math.abs(y) < 100000 && Math.abs(z) < 100000) {
              validCoords++;
              mapScore += 10;
            }
            
            score += mapScore;
            
            if (sampleMaps.length < 5 && mapScore > 20) {
              sampleMaps.push({
                id: mapId,
                name: name,
                x: x,
                y: y,
                z: z
              });
            }
          }
          
          // Bonus for sequential patterns
          if (sequentialIds > 3) score += sequentialIds * 10;
          if (validCoords > 5) score += validCoords * 5;
          if (validNames > 3) score += validNames * 5;
          
          if (score > 200 && sampleMaps.length > 2) { // High threshold for quality
            candidates.push({
              offset: offset,
              score: score,
              pattern: `ID@+0x${structure.idOffset.toString(16)}, Name@+0x${structure.nameOffset.toString(16)}, Coord@+0x${structure.coordOffset.toString(16)}`,
              sample: {
                structure: structure,
                validMaps: sampleMaps.length,
                sequentialIds: sequentialIds,
                validCoords: validCoords,
                validNames: validNames,
                maps: sampleMaps
              }
            });
          }
        }

      } catch (error) {
        // Skip invalid offsets
      }
    }

    // Sort candidates by score
    candidates.sort((a, b) => b.score - a.score);

    console.log(`📊 Found ${candidates.length} potential map data locations:\n`);

    // Show top 10 candidates
    for (let i = 0; i < Math.min(10, candidates.length); i++) {
      const candidate = candidates[i];
      console.log(`${i + 1}. Offset: 0x${candidate.offset.toString(16)} (${candidate.offset.toLocaleString()})`);
      console.log(`   Score: ${candidate.score}`);
      console.log(`   Pattern: ${candidate.pattern}`);
      console.log(`   Sequential IDs: ${candidate.sample.sequentialIds}/10`);
      console.log(`   Valid Coords: ${candidate.sample.validCoords}/10`);
      console.log(`   Valid Names: ${candidate.sample.validNames}/10`);
      console.log(`   Sample Maps:`);
      
      candidate.sample.maps.forEach((map: any, idx: number) => {
        const coords = `(${map.x.toFixed(1)}, ${map.y.toFixed(1)}, ${map.z.toFixed(1)})`;
        const name = map.name.length > 0 ? `"${map.name}"` : '(no name)';
        console.log(`     ${idx + 1}. ID=${map.id}, Name=${name}, Pos=${coords}`);
      });
      console.log('');
    }

    // If we found good candidates, suggest corrections
    if (candidates.length > 0) {
      console.log('🔧 Suggested corrections for parser configurations:\n');
      
      const bestCandidate = candidates[0];
      
      for (const config of YBI_PARSER_CONFIGS) {
        const header = (YbiParser as any).parseHeader(view);
        const skillOffset = 8 + (header.totalItems + 1) * 0x354 + 64;
        const skillLen = config.constants.skillByteLength;
        const abilityOffset = skillOffset + 1024 * skillLen;
        const classNameOffset = abilityOffset + 1024 * 2964;
        const npcOffset = classNameOffset + 256 * 0x48;
        const currentMapOffset = npcOffset + config.constants.maxNpcs * config.constants.npcByteLength + config.constants.npcOffsetAdjustment;
        
        const adjustment = bestCandidate.offset - currentMapOffset;
        
        console.log(`${config.name} (${config.id}):`);
        console.log(`   Current Map Offset: 0x${currentMapOffset.toString(16)} (${currentMapOffset.toLocaleString()})`);
        console.log(`   Best Match Offset: 0x${bestCandidate.offset.toString(16)} (${bestCandidate.offset.toLocaleString()})`);
        console.log(`   Adjustment needed: ${adjustment > 0 ? '+' : ''}${adjustment}`);
        console.log(`   New mapOffsetAdjustment: ${config.constants.npcOffsetAdjustment + adjustment}`);
        console.log('');
      }
    }

  } catch (error) {
    console.error(`❌ Error finding maps by pattern: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Pattern-based map search completed!');
}

// Run the search
findMapsByPattern().catch(console.error);
