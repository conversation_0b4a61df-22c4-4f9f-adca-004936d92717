#!/usr/bin/env node

// Script để chạy test YBQ parser
const { testParseYbqFileGoStyle, testEncryptionKey } = require('./ybq-parser-debug.js');

console.log('🚀 Running YBQ Parser Tests...\n');

// Test encryption key
testEncryptionKey();

// Test với file YBQ thật
const ybqFilePath = 'E:\\YulgangDev\\golang\\YBQToolReloaded\\ybq-editor-go\\YBq.cfg';
const result = testParseYbqFileGoStyle(ybqFilePath);

console.log('\n📊 Test Results:');
console.log('================');
if (result.success) {
  console.log('✅ Parser test PASSED');
  console.log(`📄 Sign: "${result.sign}"`);
  console.log(`📄 SignEx: "${result.signEx}"`);
  console.log(`🔢 Total Quests: ${result.totalQuest}`);
  console.log(`📦 Encrypted Size: ${result.encryptedSize} bytes`);
  console.log(`📦 Decrypted Size: ${result.decryptedSize} bytes`);
  console.log(`🎯 First Quest ID: ${result.firstQuestID}`);
} else {
  console.log('❌ Parser test FAILED');
  console.log(`💥 Error: ${result.error}`);
}

console.log('\n🏁 Test completed!');
