/**
 * Search for real Map data by looking for known map names
 * Run with: npx tsx scripts/search-real-maps.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function searchRealMaps() {
  console.log('🗺️  Searching for real Map data by patterns\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading and decrypting file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
    const view = new DataView(decryptedBuffer);
    
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // Common map names to search for
    const commonMapNames = [
      'village', 'town', 'city', 'field', 'forest', 'mountain', 'cave', 'dungeon',
      'map', 'area', 'zone', 'region', 'world', 'stage', 'level',
      'newbie', 'beginner', 'start', 'tutorial',
      'pvp', 'guild', 'war', 'battle',
      'shop', 'market', 'store'
    ];

    console.log('🔍 Searching for common map name patterns...\n');

    const foundOffsets: Array<{offset: number, name: string, context: string}> = [];

    // Search for each common name
    for (const searchName of commonMapNames) {
      const searchBytes = new TextEncoder().encode(searchName.toLowerCase());
      
      for (let offset = 0; offset < view.buffer.byteLength - searchBytes.length; offset++) {
        let match = true;
        for (let i = 0; i < searchBytes.length; i++) {
          if (view.getUint8(offset + i) !== searchBytes[i]) {
            match = false;
            break;
          }
        }
        
        if (match) {
          // Found a potential match, get context
          const contextStart = Math.max(0, offset - 50);
          const contextEnd = Math.min(view.buffer.byteLength, offset + searchBytes.length + 50);
          const contextBytes = new Uint8Array(view.buffer, contextStart, contextEnd - contextStart);
          
          let context = '';
          for (let i = 0; i < contextBytes.length; i++) {
            if (contextBytes[i] >= 32 && contextBytes[i] <= 126) {
              context += String.fromCharCode(contextBytes[i]);
            } else {
              context += '.';
            }
          }
          
          foundOffsets.push({
            offset: offset,
            name: searchName,
            context: context
          });
          
          // Skip ahead to avoid duplicate matches
          offset += searchBytes.length;
        }
      }
    }

    console.log(`Found ${foundOffsets.length} potential map name matches:\n`);

    // Show first 20 matches
    foundOffsets.slice(0, 20).forEach((match, idx) => {
      console.log(`${idx + 1}. "${match.name}" at 0x${match.offset.toString(16)} (${match.offset.toLocaleString()})`);
      console.log(`   Context: "${match.context}"`);
      console.log('');
    });

    // Now try to find structured map data around these offsets
    console.log('🎯 Analyzing potential map structures around found names:\n');

    const MAP_INFO_BYTE_LENGTH = 0x2e8; // 744 bytes
    const candidateOffsets: Array<{offset: number, score: number, sample: any}> = [];

    for (const match of foundOffsets.slice(0, 10)) { // Test first 10 matches
      // Try different alignments around the found name
      const testOffsets = [];
      
      // Try aligning to map boundaries (744 byte intervals)
      for (let align = -5; align <= 5; align++) {
        const baseOffset = match.offset + align * MAP_INFO_BYTE_LENGTH;
        if (baseOffset > 0 && baseOffset < view.buffer.byteLength - MAP_INFO_BYTE_LENGTH * 10) {
          testOffsets.push(baseOffset);
        }
      }
      
      // Also try some nearby offsets
      for (let nearby = -2000; nearby <= 2000; nearby += 100) {
        const testOffset = match.offset + nearby;
        if (testOffset > 0 && testOffset < view.buffer.byteLength - MAP_INFO_BYTE_LENGTH * 10) {
          testOffsets.push(testOffset);
        }
      }

      for (const testOffset of testOffsets) {
        try {
          let score = 0;
          let validMaps = 0;
          const sampleMaps: any[] = [];
          
          // Test 5 consecutive maps at this offset
          for (let i = 0; i < 5; i++) {
            const mapOffset = testOffset + i * MAP_INFO_BYTE_LENGTH;
            
            if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;

            // Try different ID positions
            const idPositions = [0x0, 0x4, 0x8];
            const namePositions = [0x4, 0x8, 0x10, 0x20];
            
            let bestMapScore = 0;
            let bestMapData = null;
            
            for (const idPos of idPositions) {
              for (const namePos of namePositions) {
                if (namePos <= idPos) continue;
                
                const mapId = view.getUint32(mapOffset + idPos, true);
                const nameBytes = new Uint8Array(view.buffer, mapOffset + namePos, 64);
                let name = '';
                for (let j = 0; j < nameBytes.length; j++) {
                  if (nameBytes[j] === 0) break;
                  if (nameBytes[j] < 32 || nameBytes[j] > 126) break;
                  name += String.fromCharCode(nameBytes[j]);
                }
                name = name.trim();

                let mapScore = 0;
                if (mapId > 0 && mapId < 10000) mapScore += 20;
                if (name.length > 0 && name.length < 50) mapScore += 30;
                
                if (mapScore > bestMapScore) {
                  bestMapScore = mapScore;
                  bestMapData = { id: mapId, name: name, idPos: idPos, namePos: namePos };
                }
              }
            }
            
            if (bestMapScore > 30) {
              validMaps++;
              score += bestMapScore;
              
              if (sampleMaps.length < 3) {
                sampleMaps.push(bestMapData);
              }
            }
          }

          if (validMaps >= 2 && score > 100) {
            candidateOffsets.push({
              offset: testOffset,
              score: score,
              sample: {
                validMaps: validMaps,
                maps: sampleMaps,
                nearName: match.name,
                nameOffset: match.offset
              }
            });
          }

        } catch (error) {
          // Skip invalid offsets
        }
      }
    }

    // Sort candidates by score
    candidateOffsets.sort((a, b) => b.score - a.score);

    console.log(`📊 Found ${candidateOffsets.length} potential map structure locations:\n`);

    // Show top 10 candidates
    for (let i = 0; i < Math.min(10, candidateOffsets.length); i++) {
      const candidate = candidateOffsets[i];
      console.log(`${i + 1}. Offset: 0x${candidate.offset.toString(16)} (${candidate.offset.toLocaleString()})`);
      console.log(`   Score: ${candidate.score}`);
      console.log(`   Valid Maps: ${candidate.sample.validMaps}/5`);
      console.log(`   Near name: "${candidate.sample.nearName}" at 0x${candidate.sample.nameOffset.toString(16)}`);
      console.log(`   Sample Maps:`);
      
      candidate.sample.maps.forEach((map: any, idx: number) => {
        console.log(`     ${idx + 1}. ID=${map.id} (at +0x${map.idPos.toString(16)}), Name="${map.name}" (at +0x${map.namePos.toString(16)})`);
      });
      console.log('');
    }

  } catch (error) {
    console.error(`❌ Error searching for real maps: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Real map search completed!');
}

// Run the search
searchRealMaps().catch(console.error);
