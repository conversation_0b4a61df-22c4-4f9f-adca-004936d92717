/**
 * Test adjusted map offset (moved back 1 map)
 * Run with: npx tsx scripts/test-adjusted-map-offset.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function testAdjustedMapOffset() {
  console.log('🔍 Testing adjusted map offset (moved back 1 map)\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading test file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // Test V24.2 config with adjusted offset
    const v24Config = YBI_PARSER_CONFIGS.find(c => c.id === 'v24_2')!;
    console.log(`🎯 Testing ${v24Config.name} with adjusted offset:\n`);

    try {
      const parsedFile = YbiParser.parseWithConfig(fileBuffer.buffer, v24Config, 'test.cfg');
      
      console.log(`✅ Parsing successful!`);
      console.log(`📊 Maps found: ${parsedFile.mapInfos.length} (was 1155, expecting 1156)\n`);
      
      if (parsedFile.mapInfos.length > 0) {
        console.log(`🔍 First 15 maps (including the new first map):`);
        for (let i = 0; i < Math.min(15, parsedFile.mapInfos.length); i++) {
          const map = parsedFile.mapInfos[i];
          const coords = `(${map.x}, ${map.y}, ${map.z})`;
          const bgm = map.bgm1 ? `BGM:"${map.bgm1}"` : '(no BGM)';
          console.log(`   ${i + 1}. ID=${map.id}, Name="${map.name}", Pos=${coords}, ${bgm}`);
        }
        
        console.log(`\n🔍 Last 5 maps:`);
        const start = Math.max(0, parsedFile.mapInfos.length - 5);
        for (let i = start; i < parsedFile.mapInfos.length; i++) {
          const map = parsedFile.mapInfos[i];
          const coords = `(${map.x}, ${map.y}, ${map.z})`;
          console.log(`   ${i + 1}. ID=${map.id}, Name="${map.name}", Pos=${coords}`);
        }
      }

      // Check what offset the parser actually calculated
      console.log(`\n🧮 Parser offset calculation:`);
      const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
      const view = new DataView(decryptedBuffer);
      const header = (YbiParser as any).parseHeader(view);
      
      const skillOffset = 8 + (header.totalItems + 1) * 0x354 + 64;
      const skillLen = v24Config.constants.skillByteLength;
      const abilityOffset = skillOffset + 1024 * skillLen;
      const classNameOffset = abilityOffset + 1024 * 2964;
      const npcOffset = classNameOffset + 256 * 0x48;
      const mapOffset = npcOffset + v24Config.constants.maxNpcs * v24Config.constants.npcByteLength + v24Config.constants.mapOffsetAdjustment;
      
      console.log(`   Calculated map offset: 0x${mapOffset.toString(16)} (${mapOffset.toLocaleString()})`);
      console.log(`   Previous offset: 0x2d96500 (47,801,600)`);
      console.log(`   New offset: 0x${(0x2d96500 - 0x2e8).toString(16)} (${(0x2d96500 - 0x2e8).toLocaleString()})`);
      console.log(`   Match expected: ${mapOffset === (0x2d96500 - 0x2e8) ? '✅ YES' : '❌ NO'}`);
      
      // Manually test the new first map
      console.log(`\n🔧 Manual test of new first map at 0x${mapOffset.toString(16)}:`);
      
      const id = view.getUint32(mapOffset, true);
      const nameBytes = new Uint8Array(view.buffer, mapOffset + 0x4, 0x40);
      let name = '';
      for (let j = 0; j < nameBytes.length; j++) {
        if (nameBytes[j] === 0) break;
        if (nameBytes[j] >= 32 && nameBytes[j] <= 126) {
          name += String.fromCharCode(nameBytes[j]);
        }
      }
      name = name.trim();
      
      const x = view.getFloat32(mapOffset + 0x44, true);
      const y = view.getFloat32(mapOffset + 0x4C, true);
      const z = view.getFloat32(mapOffset + 0x48, true);
      
      const bgmBytes = new Uint8Array(view.buffer, mapOffset + 0x68, 0x80);
      let bgm = '';
      for (let j = 0; j < bgmBytes.length; j++) {
        if (bgmBytes[j] === 0) break;
        if (bgmBytes[j] >= 32 && bgmBytes[j] <= 126) {
          bgm += String.fromCharCode(bgmBytes[j]);
        }
      }
      bgm = bgm.trim();
      
      console.log(`   New first map: ID=${id}, Name="${name}", Pos=(${x}, ${y}, ${z}), BGM="${bgm}"`);
      
      // Check if this looks like a valid map
      const isValid = id > 0 && id < 10000 && name.length > 0;
      console.log(`   Looks valid: ${isValid ? '✅ YES' : '❌ NO'}`);

      // Compare with old first map
      console.log(`\n📊 Comparison:`);
      console.log(`   Old first map was: ID=201, Name="Tam TaÌ Quan"`);
      console.log(`   New first map is: ID=${id}, Name="${name}"`);
      
      if (isValid) {
        console.log(`   🎉 Successfully found the missing map!`);
      } else {
        console.log(`   ⚠️  New first map doesn't look valid, may need different adjustment`);
      }

    } catch (error) {
      console.log(`❌ Parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error(error);
    }

  } catch (error) {
    console.error(`❌ Error testing adjusted map offset: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Adjusted map offset testing completed!');
}

// Run the test
testAdjustedMapOffset().catch(console.error);
