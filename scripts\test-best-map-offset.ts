/**
 * Test the best map offset we found directly
 * Run with: npx tsx scripts/test-best-map-offset.ts
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';

async function testBestMapOffset() {
  console.log('🎯 Testing best map offset directly\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading and decrypting file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    const decryptedBuffer = (YbiParser as any).cryptData(fileBuffer.buffer);
    const view = new DataView(decryptedBuffer);
    
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // Test the best offset we found: 0x2d96500
    const bestOffset = 0x2d96500;
    const MAP_INFO_BYTE_LENGTH = 0x2e8; // 744 bytes

    console.log(`🔍 Testing maps at offset 0x${bestOffset.toString(16)} (${bestOffset.toLocaleString()}):\n`);

    // Test different structure interpretations
    const structures = [
      { name: 'Original', idOffset: 0x0, nameOffset: 0x4, coordOffset: 0x44 },
      { name: 'Corrected', idOffset: 0x0, nameOffset: 0x10, coordOffset: 0x50 },
      { name: 'Alternative 1', idOffset: 0x0, nameOffset: 0x8, coordOffset: 0x48 },
      { name: 'Alternative 2', idOffset: 0x4, nameOffset: 0x10, coordOffset: 0x50 },
    ];

    for (const structure of structures) {
      console.log(`📋 Testing ${structure.name} structure:`);
      console.log(`   ID@+0x${structure.idOffset.toString(16)}, Name@+0x${structure.nameOffset.toString(16)}, Coord@+0x${structure.coordOffset.toString(16)}`);
      
      for (let i = 0; i < 10; i++) {
        const mapOffset = bestOffset + i * MAP_INFO_BYTE_LENGTH;
        
        if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;

        // Read ID
        const mapId = view.getUint32(mapOffset + structure.idOffset, true);
        
        // Read name
        const nameBytes = new Uint8Array(view.buffer, mapOffset + structure.nameOffset, 64);
        let name = '';
        for (let j = 0; j < nameBytes.length; j++) {
          if (nameBytes[j] === 0) break;
          if (nameBytes[j] < 32 || nameBytes[j] > 126) break;
          name += String.fromCharCode(nameBytes[j]);
        }
        name = name.trim();
        
        // Read coordinates
        const x = view.getFloat32(mapOffset + structure.coordOffset, true);
        const y = view.getFloat32(mapOffset + structure.coordOffset + 4, true);
        const z = view.getFloat32(mapOffset + structure.coordOffset + 8, true);
        
        console.log(`   Map ${i + 1}: ID=${mapId}, Name="${name}", Pos=(${x.toFixed(1)}, ${y.toFixed(1)}, ${z.toFixed(1)})`);
      }
      console.log('');
    }

    // Test with manual parsing using the corrected structure
    console.log('🔧 Manual parsing with corrected structure:\n');
    
    const parseMapManual = (offset: number) => {
      const id = view.getUint32(offset, true);
      const nameBytes = new Uint8Array(view.buffer, offset + 0x10, 64);
      let name = '';
      for (let j = 0; j < nameBytes.length; j++) {
        if (nameBytes[j] === 0) break;
        if (nameBytes[j] >= 32 && nameBytes[j] <= 126) {
          name += String.fromCharCode(nameBytes[j]);
        }
      }
      name = name.trim();
      
      const x = view.getFloat32(offset + 0x50, true);
      const y = view.getFloat32(offset + 0x54, true);
      const z = view.getFloat32(offset + 0x58, true);
      
      return { id, name, x, y, z };
    };

    let validMaps = 0;
    let totalMaps = 0;
    
    for (let i = 0; i < 50; i++) {
      const mapOffset = bestOffset + i * MAP_INFO_BYTE_LENGTH;
      
      if (mapOffset + MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) break;
      
      const map = parseMapManual(mapOffset);
      totalMaps++;
      
      if (map.id > 0 && map.id < 10000 && map.name.length > 0) {
        validMaps++;
        if (validMaps <= 10) {
          console.log(`Valid Map ${validMaps}: ID=${map.id}, Name="${map.name}", Pos=(${map.x.toFixed(1)}, ${map.y.toFixed(1)}, ${map.z.toFixed(1)})`);
        }
      }
    }
    
    console.log(`\n📊 Results: ${validMaps}/${totalMaps} maps appear valid (${(validMaps/totalMaps*100).toFixed(1)}%)`);

    // Try to find the actual start of valid map data
    console.log('\n🔍 Searching for actual valid map data start:\n');
    
    const searchStart = bestOffset - 10000;
    const searchEnd = bestOffset + 10000;
    
    for (let testOffset = searchStart; testOffset < searchEnd; testOffset += MAP_INFO_BYTE_LENGTH) {
      if (testOffset < 0 || testOffset + MAP_INFO_BYTE_LENGTH * 5 > view.buffer.byteLength) continue;
      
      let validCount = 0;
      const sampleMaps = [];
      
      for (let i = 0; i < 5; i++) {
        const mapOffset = testOffset + i * MAP_INFO_BYTE_LENGTH;
        const map = parseMapManual(mapOffset);
        
        if (map.id > 0 && map.id < 1000 && map.name.length > 0 && map.name.length < 30) {
          validCount++;
          sampleMaps.push(map);
        }
      }
      
      if (validCount >= 3) {
        console.log(`Found potential map data at 0x${testOffset.toString(16)} (${testOffset.toLocaleString()}):`);
        console.log(`   Valid maps: ${validCount}/5`);
        sampleMaps.forEach((map, idx) => {
          console.log(`   ${idx + 1}. ID=${map.id}, Name="${map.name}"`);
        });
        console.log('');
        
        // Only show first few good candidates
        if (validCount >= 4) break;
      }
    }

  } catch (error) {
    console.error(`❌ Error testing best map offset: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Best map offset testing completed!');
}

// Run the test
testBestMapOffset().catch(console.error);
