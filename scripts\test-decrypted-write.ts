#!/usr/bin/env tsx

/**
 * Test decrypted write functionality
 * This script will:
 * 1. Load a YBi.cfg file
 * 2. Parse it (gets decrypted buffer)
 * 3. Generate without encryption (returns decrypted buffer)
 * 4. Compare with original decrypted buffer
 * 5. Verify write logic is correct
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testDecryptedWrite() {
  console.log('🧪 Testing Decrypted Write Functionality\n');

  try {
    // Step 1: Load original file
    console.log('1. Loading original YBi.cfg file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(originalFilePath)) {
      console.log(`❌ Original file not found: ${originalFilePath}`);
      console.log('Please place a YBi.cfg file in the scripts directory');
      return;
    }

    const originalBuffer = fs.readFileSync(originalFilePath);
    console.log(`   ✅ Loaded file: ${originalBuffer.length} bytes`);

    // Step 2: Parse original file
    console.log('\n2. Parsing original file...');
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    console.log(`   ✅ Parsed successfully:`);
    console.log(`      - Items: ${originalFile.items.length}`);
    console.log(`      - Has original decrypted buffer: ${originalFile.originalDecryptedBuffer ? 'YES' : 'NO'}`);

    // Step 3: Get original decrypted buffer for comparison
    const originalDecryptedBuffer = originalFile.originalDecryptedBuffer;
    if (!originalDecryptedBuffer) {
      console.log(`❌ No original decrypted buffer available`);
      return;
    }
    console.log(`   ✅ Original decrypted buffer: ${originalDecryptedBuffer.byteLength} bytes`);

    // Step 4: Generate without encryption (should return decrypted buffer)
    console.log('\n3. Generating decrypted file without modifications...');
    const regeneratedDecryptedBuffer = YbiParser.generate(originalFile);
    const regeneratedFilePath = path.join(process.cwd(), 'scripts', 'YBi_decrypted_test.bin');
    
    fs.writeFileSync(regeneratedFilePath, Buffer.from(regeneratedDecryptedBuffer));
    console.log(`   ✅ Saved regenerated decrypted file: ${regeneratedFilePath}`);
    console.log(`      - Original decrypted size: ${originalDecryptedBuffer.byteLength} bytes`);
    console.log(`      - Regenerated decrypted size: ${regeneratedDecryptedBuffer.byteLength} bytes`);
    console.log(`      - Size difference: ${regeneratedDecryptedBuffer.byteLength - originalDecryptedBuffer.byteLength} bytes`);

    // Step 5: Compare decrypted buffers byte by byte
    console.log('\n4. Comparing decrypted buffers byte by byte...');
    const originalDecryptedBytes = new Uint8Array(originalDecryptedBuffer);
    const regeneratedDecryptedBytes = new Uint8Array(regeneratedDecryptedBuffer);
    
    let differentBytes = 0;
    let firstDifferenceOffset = -1;
    const maxBytesToCheck = Math.min(originalDecryptedBytes.length, regeneratedDecryptedBytes.length);
    
    for (let i = 0; i < maxBytesToCheck; i++) {
      if (originalDecryptedBytes[i] !== regeneratedDecryptedBytes[i]) {
        if (firstDifferenceOffset === -1) {
          firstDifferenceOffset = i;
        }
        differentBytes++;
        
        // Show first few differences for debugging
        if (differentBytes <= 10) {
          console.log(`   Difference at offset 0x${i.toString(16)}: 0x${originalDecryptedBytes[i].toString(16)} → 0x${regeneratedDecryptedBytes[i].toString(16)}`);
        }
      }
    }
    
    console.log(`   📊 Comparison results:`);
    console.log(`      - Total bytes checked: ${maxBytesToCheck}`);
    console.log(`      - Different bytes: ${differentBytes}`);
    console.log(`      - Similarity: ${((maxBytesToCheck - differentBytes) / maxBytesToCheck * 100).toFixed(6)}%`);
    
    if (firstDifferenceOffset !== -1) {
      console.log(`      - First difference at offset: 0x${firstDifferenceOffset.toString(16)} (${firstDifferenceOffset})`);
    }

    // Step 6: Parse the regenerated decrypted buffer to verify structure
    console.log('\n5. Parsing regenerated decrypted buffer to verify structure...');
    
    // We need to encrypt it first to parse it properly
    const YbiParserClass = YbiParser as any;
    const encryptedRegenerated = YbiParserClass.cryptData(regeneratedDecryptedBuffer);
    
    const reloadedFile = YbiParser.parse(encryptedRegenerated, 'YBi_decrypted_test.cfg');
    console.log(`   ✅ Parsed regenerated file successfully:`);
    console.log(`      - Items: ${reloadedFile.items.length} (should be ${originalFile.items.length})`);
    console.log(`      - Skills: ${reloadedFile.skills.length} (should be ${originalFile.skills.length})`);
    console.log(`      - Abilities: ${reloadedFile.abilities.length} (should be ${originalFile.abilities.length})`);

    // Step 7: Compare first few items in detail
    console.log('\n6. Comparing first few items in detail...');
    const itemsToCheck = Math.min(3, originalFile.items.length, reloadedFile.items.length);
    let itemsMatch = true;
    
    for (let i = 0; i < itemsToCheck; i++) {
      const originalItem = originalFile.items[i];
      const reloadedItem = reloadedFile.items[i];
      
      const fieldsMatch = 
        originalItem.id === reloadedItem.id &&
        originalItem.name === reloadedItem.name &&
        originalItem.level === reloadedItem.level &&
        originalItem.maxAtk === reloadedItem.maxAtk &&
        originalItem.minAtk === reloadedItem.minAtk &&
        originalItem.def === reloadedItem.def &&
        originalItem.desc === reloadedItem.desc;
      
      if (!fieldsMatch) {
        itemsMatch = false;
        console.log(`   ❌ Item ${i} mismatch:`);
        console.log(`      Original: ID=${originalItem.id}, Name="${originalItem.name}", Level=${originalItem.level}`);
        console.log(`      Reloaded: ID=${reloadedItem.id}, Name="${reloadedItem.name}", Level=${reloadedItem.level}`);
      } else {
        console.log(`   ✅ Item ${i}: ID=${originalItem.id}, Name="${originalItem.name}", Level=${originalItem.level}`);
      }
    }

    // Final result
    console.log('\n🎉 TEST RESULTS:');
    if (differentBytes === 0 && 
        reloadedFile.items.length === originalFile.items.length &&
        itemsMatch) {
      console.log('✅ PERFECT: Decrypted write functionality works perfectly!');
      console.log('   - Decrypted buffers are byte-for-byte identical');
      console.log('   - All data structures preserved');
      console.log('   - Write logic is correct');
      console.log('   - Ready to fix encryption and test modifications');
    } else if (differentBytes === 0) {
      console.log('✅ GOOD: Decrypted buffers are byte-for-byte identical');
      console.log('❌ BUT: Some data structure issues detected');
      console.log('   - Write logic is correct, but parsing may have issues');
    } else {
      console.log('❌ FAIL: Decrypted buffers are not identical');
      console.log(`   - Different bytes: ${differentBytes}`);
      console.log(`   - This indicates issues in write implementation`);
      
      if (differentBytes < 100) {
        console.log('   - Small number of differences - might be fixable');
      } else {
        console.log('   - Large number of differences - major issues in write logic');
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testDecryptedWrite().catch(console.error);
