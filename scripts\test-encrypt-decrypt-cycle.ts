#!/usr/bin/env tsx

/**
 * Test encrypt/decrypt cycle to verify algorithm is truly reversible
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testEncryptDecryptCycle() {
  console.log('🔄 Testing Encrypt/Decrypt Cycle\n');

  try {
    // Step 1: Load original encrypted file
    console.log('1. Loading original encrypted YBi.cfg file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(originalFilePath)) {
      console.log(`❌ Original file not found: ${originalFilePath}`);
      return;
    }

    const originalEncrypted = fs.readFileSync(originalFilePath);
    console.log(`   ✅ Loaded original encrypted file: ${originalEncrypted.length} bytes`);

    // Step 2: Decrypt original file
    console.log('\n2. Decrypting original file...');
    const YbiParserClass = YbiParser as any;
    const decrypted = YbiParserClass.cryptData(originalEncrypted.buffer);
    console.log(`   ✅ Decrypted file: ${decrypted.byteLength} bytes`);

    // Step 3: Re-encrypt the decrypted data
    console.log('\n3. Re-encrypting the decrypted data...');
    const reencrypted = YbiParserClass.cryptData(decrypted);
    console.log(`   ✅ Re-encrypted file: ${reencrypted.byteLength} bytes`);

    // Step 4: Compare original encrypted vs re-encrypted
    console.log('\n4. Comparing original encrypted vs re-encrypted...');
    const originalBytes = new Uint8Array(originalEncrypted);
    const reencryptedBytes = new Uint8Array(reencrypted);

    let identical = true;
    let differentBytes = 0;
    let firstDifferenceOffset = -1;

    const maxBytes = Math.min(originalBytes.length, reencryptedBytes.length);
    for (let i = 0; i < maxBytes; i++) {
      if (originalBytes[i] !== reencryptedBytes[i]) {
        if (firstDifferenceOffset === -1) {
          firstDifferenceOffset = i;
        }
        differentBytes++;
        identical = false;

        // Show first few differences
        if (differentBytes <= 10) {
          console.log(`   Difference at offset 0x${i.toString(16)}: 0x${originalBytes[i].toString(16)} → 0x${reencryptedBytes[i].toString(16)}`);
        }
      }
    }

    console.log(`\n📊 Comparison results:`);
    console.log(`   - Total bytes compared: ${maxBytes}`);
    console.log(`   - Different bytes: ${differentBytes}`);
    console.log(`   - Similarity: ${((maxBytes - differentBytes) / maxBytes * 100).toFixed(6)}%`);
    
    if (firstDifferenceOffset !== -1) {
      console.log(`   - First difference at offset: 0x${firstDifferenceOffset.toString(16)}`);
    }

    // Step 5: If identical, test decrypt again
    if (identical) {
      console.log('\n5. ✅ PERFECT! Re-encryption matches original. Testing decrypt again...');
      
      const decrypted2 = YbiParserClass.cryptData(reencrypted);
      
      // Compare first decryption vs second decryption
      const decrypted1Bytes = new Uint8Array(decrypted);
      const decrypted2Bytes = new Uint8Array(decrypted2);
      
      let decryptIdentical = true;
      let decryptDifferentBytes = 0;
      
      for (let i = 0; i < Math.min(decrypted1Bytes.length, decrypted2Bytes.length); i++) {
        if (decrypted1Bytes[i] !== decrypted2Bytes[i]) {
          decryptDifferentBytes++;
          decryptIdentical = false;
          
          if (decryptDifferentBytes <= 5) {
            console.log(`   Decrypt difference at offset 0x${i.toString(16)}: 0x${decrypted1Bytes[i].toString(16)} → 0x${decrypted2Bytes[i].toString(16)}`);
          }
        }
      }
      
      console.log(`   📊 Decrypt comparison:`);
      console.log(`      - Different bytes: ${decryptDifferentBytes}`);
      console.log(`      - Decrypt identical: ${decryptIdentical ? '✅ YES' : '❌ NO'}`);
      
      if (decryptIdentical) {
        console.log('\n🎉 ALGORITHM IS FULLY REVERSIBLE!');
        console.log('   - Original → Decrypt → Encrypt → Decrypt works perfectly');
        console.log('   - Ready to test item modification');
      } else {
        console.log('\n❌ ALGORITHM HAS ISSUES');
        console.log('   - Decrypt is not consistent');
      }
      
    } else {
      console.log('\n❌ ALGORITHM IS NOT REVERSIBLE');
      console.log('   - Re-encryption does not match original');
      console.log('   - Need to fix encryption algorithm');
      
      // Save files for debugging
      const reencryptedPath = path.join(process.cwd(), 'scripts', 'YBi_reencrypted_debug.cfg');
      const decryptedPath = path.join(process.cwd(), 'scripts', 'YBi_decrypted_debug.bin');
      
      fs.writeFileSync(reencryptedPath, Buffer.from(reencrypted));
      fs.writeFileSync(decryptedPath, Buffer.from(decrypted));
      
      console.log(`   💾 Saved for debugging:`);
      console.log(`      - Re-encrypted: ${reencryptedPath}`);
      console.log(`      - Decrypted: ${decryptedPath}`);
    }

    // Step 6: Test with small data to isolate the issue
    console.log('\n6. Testing with small data (16 bytes)...');
    const smallData = new ArrayBuffer(16);
    const smallView = new DataView(smallData);
    
    // Fill with pattern
    for (let i = 0; i < 16; i++) {
      smallView.setUint8(i, i + 1);
    }
    
    console.log('   Original small data:', Array.from(new Uint8Array(smallData)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
    
    const smallEncrypted = YbiParserClass.cryptData(smallData);
    console.log('   Encrypted small data:', Array.from(new Uint8Array(smallEncrypted)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
    
    const smallDecrypted = YbiParserClass.cryptData(smallEncrypted);
    console.log('   Decrypted small data:', Array.from(new Uint8Array(smallDecrypted)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
    
    const smallOriginalBytes = new Uint8Array(smallData);
    const smallDecryptedBytes = new Uint8Array(smallDecrypted);
    
    let smallIdentical = true;
    for (let i = 0; i < smallOriginalBytes.length; i++) {
      if (smallOriginalBytes[i] !== smallDecryptedBytes[i]) {
        smallIdentical = false;
        break;
      }
    }
    
    console.log(`   Small data test: ${smallIdentical ? '✅ REVERSIBLE' : '❌ NOT REVERSIBLE'}`);

    // Final result
    console.log('\n🎯 FINAL RESULT:');
    if (identical && smallIdentical) {
      console.log('✅ ENCRYPTION ALGORITHM IS WORKING PERFECTLY');
      console.log('   - Both large file and small data tests pass');
      console.log('   - Algorithm is fully reversible');
      console.log('   - Issue must be in item modification logic');
    } else {
      console.log('❌ ENCRYPTION ALGORITHM HAS ISSUES');
      console.log(`   - Large file test: ${identical ? 'PASS' : 'FAIL'}`);
      console.log(`   - Small data test: ${smallIdentical ? 'PASS' : 'FAIL'}`);
      console.log('   - Need to fix encryption before testing item modification');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testEncryptDecryptCycle().catch(console.error);
