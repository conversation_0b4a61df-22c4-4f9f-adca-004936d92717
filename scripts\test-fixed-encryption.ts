#!/usr/bin/env tsx

/**
 * Test fixed encryption algorithm
 * This script will:
 * 1. Test encryption/decryption with small data
 * 2. Test with actual YBi.cfg file
 * 3. Verify reversibility
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testFixedEncryption() {
  console.log('🧪 Testing Fixed Encryption Algorithm\n');

  try {
    // Test 1: Small data encryption/decryption
    console.log('1. Testing small data encryption/decryption...');
    const testData = new ArrayBuffer(16);
    const testView = new DataView(testData);
    
    // Write test pattern
    testView.setUint32(0, 0x12345678, true);
    testView.setUint32(4, 0x87654321, true);
    testView.setUint32(8, 0xABCDEF00, true);
    testView.setUint32(12, 0x11223344, true);
    
    console.log('   Original data:');
    const originalBytes = new Uint8Array(testData);
    let hexStr = '';
    for (let i = 0; i < originalBytes.length; i++) {
      hexStr += originalBytes[i].toString(16).padStart(2, '0') + ' ';
    }
    console.log('   ' + hexStr);
    
    // Encrypt
    const YbiParserClass = YbiParser as any;
    const encrypted = YbiParserClass.cryptData(testData);
    console.log('   Encrypted data:');
    const encryptedBytes = new Uint8Array(encrypted);
    hexStr = '';
    for (let i = 0; i < encryptedBytes.length; i++) {
      hexStr += encryptedBytes[i].toString(16).padStart(2, '0') + ' ';
    }
    console.log('   ' + hexStr);
    
    // Decrypt
    const decrypted = YbiParserClass.cryptData(encrypted);
    console.log('   Decrypted data:');
    const decryptedBytes = new Uint8Array(decrypted);
    hexStr = '';
    for (let i = 0; i < decryptedBytes.length; i++) {
      hexStr += decryptedBytes[i].toString(16).padStart(2, '0') + ' ';
    }
    console.log('   ' + hexStr);
    
    // Compare
    let identical = true;
    for (let i = 0; i < originalBytes.length; i++) {
      if (originalBytes[i] !== decryptedBytes[i]) {
        identical = false;
        break;
      }
    }
    
    console.log(`   Result: ${identical ? '✅ IDENTICAL' : '❌ DIFFERENT'}`);
    
    // Test 2: Non-4-byte-aligned data
    console.log('\n2. Testing non-4-byte-aligned data...');
    const oddData = new ArrayBuffer(15); // Not divisible by 4
    const oddView = new DataView(oddData);
    
    // Fill with pattern
    for (let i = 0; i < 15; i++) {
      oddView.setUint8(i, (i + 1) * 17);
    }
    
    const oddEncrypted = YbiParserClass.cryptData(oddData);
    const oddDecrypted = YbiParserClass.cryptData(oddEncrypted);
    
    const oddOriginal = new Uint8Array(oddData);
    const oddDecryptedBytes = new Uint8Array(oddDecrypted);
    
    let oddIdentical = true;
    for (let i = 0; i < oddOriginal.length; i++) {
      if (oddOriginal[i] !== oddDecryptedBytes[i]) {
        oddIdentical = false;
        console.log(`   Difference at byte ${i}: ${oddOriginal[i]} → ${oddDecryptedBytes[i]}`);
      }
    }
    
    console.log(`   Result: ${oddIdentical ? '✅ IDENTICAL' : '❌ DIFFERENT'}`);

    // Test 3: Real YBi.cfg file
    console.log('\n3. Testing with real YBi.cfg file...');
    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('   ⚠️  YBi.cfg not found, skipping real file test');
      return;
    }
    
    const originalFile = fs.readFileSync(ybiFilePath);
    console.log(`   Original file size: ${originalFile.length} bytes`);
    
    // Decrypt
    const decryptedFile = YbiParserClass.cryptData(originalFile.buffer);
    console.log(`   Decrypted file size: ${decryptedFile.byteLength} bytes`);
    
    // Re-encrypt
    const reencryptedFile = YbiParserClass.cryptData(decryptedFile);
    console.log(`   Re-encrypted file size: ${reencryptedFile.byteLength} bytes`);
    
    // Compare original vs re-encrypted
    const originalFileBytes = new Uint8Array(originalFile);
    const reencryptedFileBytes = new Uint8Array(reencryptedFile);
    
    let fileIdentical = true;
    let differentBytes = 0;
    let firstDifferenceOffset = -1;
    
    for (let i = 0; i < Math.min(originalFileBytes.length, reencryptedFileBytes.length); i++) {
      if (originalFileBytes[i] !== reencryptedFileBytes[i]) {
        if (firstDifferenceOffset === -1) {
          firstDifferenceOffset = i;
        }
        differentBytes++;
        fileIdentical = false;
        
        // Show first few differences
        if (differentBytes <= 5) {
          console.log(`   Difference at offset 0x${i.toString(16)}: 0x${originalFileBytes[i].toString(16)} → 0x${reencryptedFileBytes[i].toString(16)}`);
        }
      }
    }
    
    console.log(`   📊 File comparison results:`);
    console.log(`      - Total bytes: ${originalFileBytes.length}`);
    console.log(`      - Different bytes: ${differentBytes}`);
    console.log(`      - Similarity: ${((originalFileBytes.length - differentBytes) / originalFileBytes.length * 100).toFixed(6)}%`);
    
    if (firstDifferenceOffset !== -1) {
      console.log(`      - First difference at offset: 0x${firstDifferenceOffset.toString(16)}`);
    }
    
    console.log(`   Result: ${fileIdentical ? '✅ PERFECT - File encryption is reversible!' : '❌ FAILED - File encryption is not reversible'}`);
    
    // Test 4: Parse test if encryption works
    if (fileIdentical) {
      console.log('\n4. Testing file parsing with fixed encryption...');
      try {
        const parsedFile = YbiParser.parse(originalFile.buffer, 'YBi.cfg');
        console.log(`   ✅ Parsing successful:`);
        console.log(`      - Items: ${parsedFile.items.length}`);
        console.log(`      - Skills: ${parsedFile.skills.length}`);
        console.log(`      - Abilities: ${parsedFile.abilities.length}`);
        console.log(`      - Hero Titles: ${parsedFile.heroTitles.length}`);
        console.log(`      - NPCs: ${parsedFile.npcInfos.length}`);
        console.log(`      - Maps: ${parsedFile.mapInfos.length}`);
        
        // Test generation
        console.log('\n5. Testing file generation...');
        const regeneratedFile = YbiParser.generate(parsedFile);
        
        // Compare with original
        const regeneratedBytes = new Uint8Array(regeneratedFile);
        let genIdentical = true;
        let genDifferentBytes = 0;
        
        for (let i = 0; i < Math.min(originalFileBytes.length, regeneratedBytes.length); i++) {
          if (originalFileBytes[i] !== regeneratedBytes[i]) {
            genDifferentBytes++;
            genIdentical = false;
          }
        }
        
        console.log(`   📊 Generation comparison results:`);
        console.log(`      - Original size: ${originalFileBytes.length} bytes`);
        console.log(`      - Generated size: ${regeneratedBytes.length} bytes`);
        console.log(`      - Different bytes: ${genDifferentBytes}`);
        console.log(`      - Similarity: ${((Math.min(originalFileBytes.length, regeneratedBytes.length) - genDifferentBytes) / Math.min(originalFileBytes.length, regeneratedBytes.length) * 100).toFixed(6)}%`);
        
        console.log(`   Result: ${genIdentical ? '✅ PERFECT - File generation works!' : '❌ PARTIAL - File generation has issues'}`);
        
      } catch (error) {
        console.log(`   ❌ Parsing failed: ${error}`);
      }
    }

    // Final summary
    console.log('\n🎉 FINAL RESULTS:');
    console.log(`   - Small data encryption: ${identical ? '✅ WORKING' : '❌ BROKEN'}`);
    console.log(`   - Odd-sized data encryption: ${oddIdentical ? '✅ WORKING' : '❌ BROKEN'}`);
    console.log(`   - Real file encryption: ${fileIdentical ? '✅ WORKING' : '❌ BROKEN'}`);
    
    if (identical && oddIdentical && fileIdentical) {
      console.log('\n🎊 SUCCESS: Encryption algorithm is now working perfectly!');
      console.log('   - All tests passed');
      console.log('   - Ready for production use');
      console.log('   - Item modification should work end-to-end');
    } else {
      console.log('\n❌ ISSUES REMAIN: Some tests failed');
      console.log('   - Need further investigation');
      console.log('   - Check C++ implementation details');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testFixedEncryption().catch(console.error);
