#!/usr/bin/env tsx

/**
 * Test item modification functionality
 * This script will:
 * 1. Load a YBi.cfg file
 * 2. Parse it
 * 3. Modify first item's name and level
 * 4. Generate modified decrypted buffer
 * 5. Verify modifications are applied correctly
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testItemModification() {
  console.log('🧪 Testing Item Modification Functionality\n');

  try {
    // Step 1: Load original file
    console.log('1. Loading original YBi.cfg file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(originalFilePath)) {
      console.log(`❌ Original file not found: ${originalFilePath}`);
      console.log('Please place a YBi.cfg file in the scripts directory');
      return;
    }

    const originalBuffer = fs.readFileSync(originalFilePath);
    console.log(`   ✅ Loaded file: ${originalBuffer.length} bytes`);

    // Step 2: Parse original file
    console.log('\n2. Parsing original file...');
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    console.log(`   ✅ Parsed successfully:`);
    console.log(`      - Items: ${originalFile.items.length}`);
    console.log(`      - Has original decrypted buffer: ${originalFile.originalDecryptedBuffer ? 'YES' : 'NO'}`);

    // Step 3: Show original first item
    const firstItem = originalFile.items[0];
    console.log(`\n3. Original first item:`);
    console.log(`   ID: ${firstItem.id}`);
    console.log(`   Name: "${firstItem.name}"`);
    console.log(`   Level: ${firstItem.level}`);
    console.log(`   Max Attack: ${firstItem.maxAtk}`);
    console.log(`   Min Attack: ${firstItem.minAtk}`);
    console.log(`   Defense: ${firstItem.def}`);

    // Step 4: Modify first item
    console.log(`\n4. Modifying first item...`);
    const modifiedFile = { ...originalFile };
    modifiedFile.items = [...originalFile.items]; // Create copy of items array
    modifiedFile.items[0] = {
      ...firstItem,
      name: "MODIFIED ITEM",
      level: 99,
      maxAtk: 9999,
      minAtk: 8888,
      def: 7777
    };

    console.log(`   ✅ Modified first item:`);
    console.log(`   Name: "${modifiedFile.items[0].name}"`);
    console.log(`   Level: ${modifiedFile.items[0].level}`);
    console.log(`   Max Attack: ${modifiedFile.items[0].maxAtk}`);
    console.log(`   Min Attack: ${modifiedFile.items[0].minAtk}`);
    console.log(`   Defense: ${modifiedFile.items[0].def}`);

    // Step 5: Generate modified decrypted buffer
    console.log('\n5. Generating modified decrypted buffer...');
    const modifiedDecryptedBuffer = YbiParser.generate(modifiedFile);
    const modifiedFilePath = path.join(process.cwd(), 'scripts', 'YBi_modified_decrypted.bin');
    
    fs.writeFileSync(modifiedFilePath, Buffer.from(modifiedDecryptedBuffer));
    console.log(`   ✅ Saved modified decrypted file: ${modifiedFilePath}`);
    console.log(`      - Size: ${modifiedDecryptedBuffer.byteLength} bytes`);

    // Step 6: Parse the modified decrypted buffer to verify changes
    console.log('\n6. Parsing modified decrypted buffer to verify changes...');
    
    // We need to encrypt it first to parse it properly (but encryption is broken)
    // So let's manually check the bytes at the first item location
    const firstItemOffset = firstItem.offset;
    const modifiedView = new DataView(modifiedDecryptedBuffer);
    
    // Check ID (should be unchanged)
    const modifiedId = modifiedView.getUint32(firstItemOffset, true);
    console.log(`   ID: ${modifiedId} (should be ${firstItem.id})`);
    
    // Check name bytes
    const NAME_OFFSET = 0x08;
    const NAME_LENGTH = 0x40;
    const modifiedNameBytes = new Uint8Array(modifiedDecryptedBuffer, firstItemOffset + NAME_OFFSET, NAME_LENGTH);
    let modifiedName = '';
    for (let i = 0; i < NAME_LENGTH; i++) {
      if (modifiedNameBytes[i] === 0) break;
      modifiedName += String.fromCharCode(modifiedNameBytes[i]);
    }
    console.log(`   Name: "${modifiedName}" (should be "MODIFIED ITEM")`);
    
    // Check level
    const LEVEL_OFFSET = 0x4C;
    const modifiedLevel = modifiedView.getUint16(firstItemOffset + LEVEL_OFFSET, true);
    console.log(`   Level: ${modifiedLevel} (should be 99)`);
    
    // Check attacks
    const ATK_MAX_OFFSET = 0x54;
    const ATK_MIN_OFFSET = 0x56;
    const modifiedMaxAtk = modifiedView.getUint16(firstItemOffset + ATK_MAX_OFFSET, true);
    const modifiedMinAtk = modifiedView.getUint16(firstItemOffset + ATK_MIN_OFFSET, true);
    console.log(`   Max Attack: ${modifiedMaxAtk} (should be 9999)`);
    console.log(`   Min Attack: ${modifiedMinAtk} (should be 8888)`);
    
    // Check defense
    const DEF_OFFSET = 0x58;
    const modifiedDef = modifiedView.getUint16(firstItemOffset + DEF_OFFSET, true);
    console.log(`   Defense: ${modifiedDef} (should be 7777)`);

    // Step 7: Compare with original to see what changed
    console.log('\n7. Comparing with original decrypted buffer...');
    const originalDecryptedBuffer = originalFile.originalDecryptedBuffer!;
    const originalDecryptedBytes = new Uint8Array(originalDecryptedBuffer);
    const modifiedDecryptedBytes = new Uint8Array(modifiedDecryptedBuffer);
    
    let changedBytes = 0;
    let firstChangeOffset = -1;
    const maxBytesToCheck = Math.min(originalDecryptedBytes.length, modifiedDecryptedBytes.length);
    
    for (let i = 0; i < maxBytesToCheck; i++) {
      if (originalDecryptedBytes[i] !== modifiedDecryptedBytes[i]) {
        if (firstChangeOffset === -1) {
          firstChangeOffset = i;
        }
        changedBytes++;
        
        // Show first few changes for debugging
        if (changedBytes <= 10) {
          console.log(`   Change at offset 0x${i.toString(16)}: 0x${originalDecryptedBytes[i].toString(16)} → 0x${modifiedDecryptedBytes[i].toString(16)}`);
        }
      }
    }
    
    console.log(`   📊 Comparison results:`);
    console.log(`      - Total bytes checked: ${maxBytesToCheck}`);
    console.log(`      - Changed bytes: ${changedBytes}`);
    console.log(`      - First change at offset: 0x${firstChangeOffset.toString(16)} (${firstChangeOffset})`);
    console.log(`      - Expected first item offset: 0x${firstItemOffset.toString(16)} (${firstItemOffset})`);

    // Final result
    console.log('\n🎉 TEST RESULTS:');
    const idCorrect = modifiedId === firstItem.id;
    const nameCorrect = modifiedName === "MODIFIED ITEM";
    const levelCorrect = modifiedLevel === 99;
    const maxAtkCorrect = modifiedMaxAtk === 9999;
    const minAtkCorrect = modifiedMinAtk === 8888;
    const defCorrect = modifiedDef === 7777;
    
    if (idCorrect && nameCorrect && levelCorrect && maxAtkCorrect && minAtkCorrect && defCorrect) {
      console.log('✅ PERFECT: Item modification works perfectly!');
      console.log('   - All fields modified correctly');
      console.log('   - Write logic is working');
      console.log('   - Ready for encryption fix');
    } else {
      console.log('❌ PARTIAL: Some modifications failed');
      console.log(`   - ID correct: ${idCorrect}`);
      console.log(`   - Name correct: ${nameCorrect}`);
      console.log(`   - Level correct: ${levelCorrect}`);
      console.log(`   - Max Attack correct: ${maxAtkCorrect}`);
      console.log(`   - Min Attack correct: ${minAtkCorrect}`);
      console.log(`   - Defense correct: ${defCorrect}`);
    }

    if (changedBytes > 0) {
      console.log(`✅ Buffer modification detected: ${changedBytes} bytes changed`);
      if (firstChangeOffset >= firstItemOffset && firstChangeOffset < firstItemOffset + 0x354) {
        console.log(`✅ Changes are in expected item range`);
      } else {
        console.log(`⚠️  Changes outside expected item range`);
      }
    } else {
      console.log(`❌ No buffer changes detected - write logic may not be working`);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testItemModification().catch(console.error);
