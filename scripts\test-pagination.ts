/**
 * Test script for Advanced Pagination functionality
 * Run with: npx tsx scripts/test-pagination.ts
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';

async function testPagination() {
  console.log('🧪 Testing Advanced Pagination Functionality\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      console.log('   Place a YBi.cfg file there to test pagination with real data');
      return;
    }

    console.log('📁 Loading test file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    const parsedFile = YbiParser.parse(fileBuffer.buffer, 'YBi.cfg');

    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes`);
    console.log(`   Parser: ${parsedFile.parserConfig.name}`);
    console.log('');

    // Test pagination scenarios
    const testScenarios = [
      { name: 'Items', data: parsedFile.items, itemsPerPage: 50 },
      { name: 'Skills', data: parsedFile.skills, itemsPerPage: 25 },
      { name: 'NPCs', data: parsedFile.npcInfos, itemsPerPage: 100 },
      { name: 'Maps', data: parsedFile.mapInfos, itemsPerPage: 50 }
    ];

    testScenarios.forEach(scenario => {
      const totalItems = scenario.data.length;
      const totalPages = Math.ceil(totalItems / scenario.itemsPerPage);

      console.log(`📊 ${scenario.name} Pagination Test:`);
      console.log(`   Total Items: ${totalItems.toLocaleString()}`);
      console.log(`   Items per Page: ${scenario.itemsPerPage}`);
      console.log(`   Total Pages: ${totalPages.toLocaleString()}`);

      if (totalPages > 1) {
        // Test different page scenarios
        const testPages = [
          1, // First page
          Math.min(5, totalPages), // Page 5 or last page if less than 5
          Math.min(10, totalPages), // Page 10 or last page if less than 10
          Math.min(100, totalPages), // Page 100 or last page if less than 100
          totalPages // Last page
        ];

        testPages.forEach(page => {
          const startIndex = (page - 1) * scenario.itemsPerPage;
          const endIndex = Math.min(startIndex + scenario.itemsPerPage, totalItems);
          const itemsOnPage = endIndex - startIndex;

          console.log(`      Page ${page}: Items ${startIndex + 1}-${endIndex} (${itemsOnPage} items)`);
        });

        // Test quick jump scenarios (new order: small to large for backward, large to small for forward)
        console.log('   Quick Jump Tests:');

        // Forward jumps from page 1 (order: 100, 10, 5 - large to small)
        const forwardJumps = [100, 10, 5].filter(jump => jump < totalPages);
        forwardJumps.forEach(jump => {
          console.log(`      From page 1, jump +${jump} → page ${Math.min(1 + jump, totalPages)}`);
        });

        // Backward jumps from last page (order: 5, 10, 100 - small to large)
        const backwardJumps = [5, 10, 100].filter(jump => totalPages - jump > 0);
        backwardJumps.forEach(jump => {
          console.log(`      From page ${totalPages}, jump -${jump} → page ${Math.max(totalPages - jump, 1)}`);
        });

        // Test edge cases
        console.log('   Edge Cases:');
        console.log(`      Jump to page 0 → should go to page 1`);
        console.log(`      Jump to page ${totalPages + 100} → should go to page ${totalPages}`);
        console.log(`      Jump to negative page → should go to page 1`);
      } else {
        console.log('      Single page - no pagination needed');
      }

      console.log('');
    });

    // Test search + pagination scenario
    console.log('🔍 Search + Pagination Test:');
    const itemsWithSword = parsedFile.items.filter(item => 
      item.name.toLowerCase().includes('sword') || 
      item.name.toLowerCase().includes('kiếm') ||
      item.desc.toLowerCase().includes('sword') ||
      item.desc.toLowerCase().includes('kiếm')
    );

    console.log(`   Items containing "sword/kiếm": ${itemsWithSword.length}`);
    if (itemsWithSword.length > 50) {
      const searchPages = Math.ceil(itemsWithSword.length / 50);
      console.log(`   Search result pages (50 per page): ${searchPages}`);
      console.log(`   Page 1: Items 1-50`);
      console.log(`   Page ${searchPages}: Items ${(searchPages - 1) * 50 + 1}-${itemsWithSword.length}`);
    } else {
      console.log('   All search results fit on one page');
    }

    console.log('');

    // Performance test
    console.log('⚡ Performance Test:');
    const performanceTest = (dataSize: number, itemsPerPage: number) => {
      const start = performance.now();
      
      // Simulate pagination calculations
      const totalPages = Math.ceil(dataSize / itemsPerPage);
      const currentPage = Math.floor(totalPages / 2); // Middle page
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = Math.min(startIndex + itemsPerPage, dataSize);
      
      // Simulate quick jumps
      const jumpTargets = [1, currentPage + 5, currentPage + 10, currentPage + 100, totalPages];
      jumpTargets.forEach(target => {
        const clampedTarget = Math.max(1, Math.min(target, totalPages));
        const jumpStart = (clampedTarget - 1) * itemsPerPage;
        const jumpEnd = Math.min(jumpStart + itemsPerPage, dataSize);
      });
      
      const end = performance.now();
      return end - start;
    };

    const largeDataTime = performanceTest(100000, 50); // 100k items
    const smallDataTime = performanceTest(1000, 50); // 1k items

    console.log(`   Large dataset (100k items): ${largeDataTime.toFixed(2)}ms`);
    console.log(`   Small dataset (1k items): ${smallDataTime.toFixed(2)}ms`);

  } catch (error) {
    console.error(`❌ Error testing pagination: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Advanced Pagination testing completed!');
}

// Run the test
testPagination().catch(console.error);
