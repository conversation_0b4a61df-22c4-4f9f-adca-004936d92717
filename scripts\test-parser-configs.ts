/**
 * Test script for YBI Parser Configurations
 * Run with: npx tsx scripts/test-parser-configs.ts
 */

import { YBI_PARSER_CONFIGS, getDefaultParserConfig, getParserConfig, YbiParser } from '../src/lib/parsers/ybi-parser';

async function testParserConfigs() {
  console.log('🧪 Testing YBI Parser Configurations\n');

  // Test 1: List all available configs
  console.log('📋 Available Parser Configurations:');
  YBI_PARSER_CONFIGS.forEach((config, index) => {
    console.log(`   ${index + 1}. ${config.name} (${config.id})`);
    console.log(`      Description: ${config.description}`);
    console.log(`      Skill Format: ${config.skillFormat}`);
    console.log(`      NPC Format: ${config.npcFormat}`);
    console.log(`      Max NPCs: ${config.constants.maxNpcs.toLocaleString()}`);
    console.log(`      Skill Byte Length: ${config.constants.skillByteLength}`);
    console.log(`      NPC Byte Length: ${config.constants.npcByteLength}`);
    console.log('');
  });

  // Test 2: Test helper functions
  console.log('🔧 Testing Helper Functions:');
  
  const defaultConfig = getDefaultParserConfig();
  console.log(`   Default Config: ${defaultConfig.name} (${defaultConfig.id})`);
  
  const v23Config = getParserConfig('v23');
  console.log(`   V23 Config: ${v23Config?.name || 'Not found'}`);
  
  const invalidConfig = getParserConfig('invalid');
  console.log(`   Invalid Config: ${invalidConfig?.name || 'Not found'}`);
  console.log('');

  // Test 3: Test parser config differences
  console.log('🔍 Parser Configuration Differences:');
  console.log('   Config ID    | Skill Format | NPC Format | Max NPCs | Skill Bytes | NPC Bytes');
  console.log('   -------------|--------------|------------|----------|-------------|----------');
  
  YBI_PARSER_CONFIGS.forEach(config => {
    const skillFormat = config.skillFormat.padEnd(12);
    const npcFormat = config.npcFormat.padEnd(10);
    const maxNpcs = config.constants.maxNpcs.toString().padStart(8);
    const skillBytes = config.constants.skillByteLength.toString().padStart(11);
    const npcBytes = config.constants.npcByteLength.toString().padStart(9);
    
    console.log(`   ${config.id.padEnd(12)} | ${skillFormat} | ${npcFormat} | ${maxNpcs} | ${skillBytes} | ${npcBytes}`);
  });
  console.log('');

  // Test 4: Test auto-detection vs manual config (if test file exists)
  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (fs.existsSync(ybiFilePath)) {
      console.log('📁 Testing with real YBi.cfg file:');
      const fileBuffer = fs.readFileSync(ybiFilePath);
      console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes`);

      // Test auto-detection
      console.log('   🤖 Auto-detection result:');
      const autoDetectedFile = YbiParser.parse(fileBuffer.buffer, 'YBi.cfg');
      console.log(`      Parser Config: ${autoDetectedFile.parserConfig.name}`);
      console.log(`      Items: ${autoDetectedFile.items.length}`);
      console.log(`      Skills: ${autoDetectedFile.skills.length}`);
      console.log(`      NPCs: ${autoDetectedFile.npcInfos.length}`);
      console.log('');

      // Test each config manually
      console.log('   🔧 Manual config testing:');
      for (const config of YBI_PARSER_CONFIGS) {
        try {
          const manualFile = YbiParser.parseWithConfig(fileBuffer.buffer, config, 'YBi.cfg');
          console.log(`      ${config.name}:`);
          console.log(`         Items: ${manualFile.items.length}`);
          console.log(`         Skills: ${manualFile.skills.length}`);
          console.log(`         NPCs: ${manualFile.npcInfos.length}`);
          console.log(`         Maps: ${manualFile.mapInfos.length}`);
        } catch (error) {
          console.log(`      ${config.name}: ❌ Failed - ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } else {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      console.log('   Place a YBi.cfg file there to test parsing with different configs');
    }
  } catch (error) {
    console.log(`❌ Error testing with file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Parser configuration testing completed!');
}

// Run the test
testParserConfigs().catch(console.error);
