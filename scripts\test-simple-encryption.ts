#!/usr/bin/env tsx

/**
 * Test simple encryption with manual implementation
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';

function testSimpleEncryption() {
  console.log('🧪 Testing Simple Encryption\n');

  const SRC_POSITIONS = [
    26, 31, 17, 10, 30, 16, 24, 2,
    29, 8, 20, 15, 28, 11, 13,
    4, 19, 23, 0, 12, 14, 27,
    6, 18, 21, 3, 9, 7, 22,
    1, 25, 5
  ];

  function moveBit(src: number, oldLoc: number, newLoc: number): number {
    return ((src >> oldLoc) & 1) << newLoc;
  }

  function manualCrypt(value: number): number {
    let num = 0;
    for (let i = 0; i < SRC_POSITIONS.length; i++) {
      num |= moveBit(value, SRC_POSITIONS[i], i);
    }
    return num;
  }

  // Test with simple value
  const testValue = 0x12345678;
  console.log('Original:', '0x' + (testValue >>> 0).toString(16).padStart(8, '0'));

  // Manual encryption
  const encrypted1 = manualCrypt(testValue);
  console.log('Encrypted:', '0x' + (encrypted1 >>> 0).toString(16).padStart(8, '0'));

  // Manual decryption (should be same as encryption for bijective transformation)
  const decrypted1 = manualCrypt(encrypted1);
  console.log('Decrypted:', '0x' + (decrypted1 >>> 0).toString(16).padStart(8, '0'));

  console.log('Manual reversible:', testValue === decrypted1 ? '✅ YES' : '❌ NO');

  // Test with YbiParser function
  const testBuffer = new ArrayBuffer(4);
  const testView = new DataView(testBuffer);
  testView.setUint32(0, testValue, true);

  const YbiParserClass = YbiParser as any;
  
  const encrypted2Buffer = YbiParserClass.cryptData(testBuffer);
  const encrypted2View = new DataView(encrypted2Buffer);
  const encrypted2 = encrypted2View.getUint32(0, true);
  
  console.log('\nYbiParser encrypted:', '0x' + (encrypted2 >>> 0).toString(16).padStart(8, '0'));
  console.log('Manual vs YbiParser:', encrypted1 === encrypted2 ? '✅ MATCH' : '❌ DIFFERENT');

  const decrypted2Buffer = YbiParserClass.cryptData(encrypted2Buffer);
  const decrypted2View = new DataView(decrypted2Buffer);
  const decrypted2 = decrypted2View.getUint32(0, true);
  
  console.log('YbiParser decrypted:', '0x' + (decrypted2 >>> 0).toString(16).padStart(8, '0'));
  console.log('YbiParser reversible:', testValue === decrypted2 ? '✅ YES' : '❌ NO');

  // Debug the difference
  if (testValue !== decrypted2) {
    console.log('\n🔍 Debugging difference:');
    console.log('Original binary: ', (testValue >>> 0).toString(2).padStart(32, '0'));
    console.log('Decrypted binary:', (decrypted2 >>> 0).toString(2).padStart(32, '0'));
    
    for (let i = 0; i < 32; i++) {
      const origBit = (testValue >> i) & 1;
      const decBit = (decrypted2 >> i) & 1;
      if (origBit !== decBit) {
        console.log(`Bit ${i}: ${origBit} -> ${decBit}`);
      }
    }
  }

  // Test edge case: what if we have bytes that are not 4-byte aligned?
  console.log('\n🔍 Testing edge case - 5 bytes:');
  const edgeBuffer = new ArrayBuffer(5);
  const edgeView = new DataView(edgeBuffer);
  edgeView.setUint32(0, 0x12345678, true);
  edgeView.setUint8(4, 0xAB);

  console.log('Original 5 bytes:', Array.from(new Uint8Array(edgeBuffer)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));

  const edgeEncrypted = YbiParserClass.cryptData(edgeBuffer);
  console.log('Encrypted 5 bytes:', Array.from(new Uint8Array(edgeEncrypted)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));

  const edgeDecrypted = YbiParserClass.cryptData(edgeEncrypted);
  console.log('Decrypted 5 bytes:', Array.from(new Uint8Array(edgeDecrypted)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));

  const edgeMatch = Array.from(new Uint8Array(edgeBuffer)).every((b, i) => b === new Uint8Array(edgeDecrypted)[i]);
  console.log('Edge case reversible:', edgeMatch ? '✅ YES' : '❌ NO');
}

testSimpleEncryption();
