#!/usr/bin/env tsx

/**
 * Test single field modification to isolate the issue
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testSingleFieldModification() {
  console.log('🔧 Testing Single Field Modification\n');

  try {
    // Step 1: Load and parse original file
    console.log('1. Loading and parsing original file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(originalFilePath)) {
      console.log(`❌ Original file not found: ${originalFilePath}`);
      return;
    }

    const originalBuffer = fs.readFileSync(originalFilePath);
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    
    console.log(`   ✅ Parsed file: ${originalFile.items.length} items`);
    
    const firstItem = originalFile.items[0];
    console.log(`   📋 First item:`);
    console.log(`      - ID: ${firstItem.id}`);
    console.log(`      - Name: "${firstItem.name}"`);
    console.log(`      - Level: ${firstItem.level}`);
    console.log(`      - Max Attack: ${firstItem.maxAtk}`);
    console.log(`      - Offset: 0x${firstItem.offset.toString(16)}`);

    // Step 2: Test direct buffer modification (without using writeItem function)
    console.log('\n2. Testing direct buffer modification...');
    
    // Get original decrypted buffer
    const originalDecrypted = originalFile.originalDecryptedBuffer!;
    const testBuffer = originalDecrypted.slice(0); // Create copy
    const testView = new DataView(testBuffer);
    
    // Manually modify just the level field at the correct offset
    const LEVEL_OFFSET = 0x4C;
    const itemOffset = firstItem.offset;
    const levelFieldOffset = itemOffset + LEVEL_OFFSET;
    
    console.log(`   📍 Modifying level at offset: 0x${levelFieldOffset.toString(16)}`);
    console.log(`   📍 Original level value: ${testView.getUint16(levelFieldOffset, true)}`);
    
    // Set level to 99
    testView.setUint16(levelFieldOffset, 99, true);
    
    console.log(`   📍 New level value: ${testView.getUint16(levelFieldOffset, true)}`);

    // Step 3: Encrypt the modified buffer
    console.log('\n3. Encrypting modified buffer...');
    const YbiParserClass = YbiParser as any;
    const encryptedModified = YbiParserClass.cryptData(testBuffer);
    
    // Step 4: Parse the encrypted modified buffer
    console.log('\n4. Parsing encrypted modified buffer...');
    const modifiedFile = YbiParser.parse(encryptedModified, 'YBi.cfg');
    
    const modifiedFirstItem = modifiedFile.items[0];
    console.log(`   📋 Modified first item:`);
    console.log(`      - ID: ${modifiedFirstItem.id} (should be ${firstItem.id})`);
    console.log(`      - Name: "${modifiedFirstItem.name}" (should be "${firstItem.name}")`);
    console.log(`      - Level: ${modifiedFirstItem.level} (should be 99)`);
    console.log(`      - Max Attack: ${modifiedFirstItem.maxAtk} (should be ${firstItem.maxAtk})`);

    // Step 5: Check results
    console.log('\n5. Verification results:');
    const idCorrect = modifiedFirstItem.id === firstItem.id;
    const nameCorrect = modifiedFirstItem.name === firstItem.name;
    const levelCorrect = modifiedFirstItem.level === 99;
    const maxAtkCorrect = modifiedFirstItem.maxAtk === firstItem.maxAtk;
    
    console.log(`   - ID correct: ${idCorrect ? '✅' : '❌'}`);
    console.log(`   - Name correct: ${nameCorrect ? '✅' : '❌'}`);
    console.log(`   - Level correct: ${levelCorrect ? '✅' : '❌'}`);
    console.log(`   - Max Attack correct: ${maxAtkCorrect ? '✅' : '❌'}`);

    if (idCorrect && nameCorrect && levelCorrect && maxAtkCorrect) {
      console.log('\n🎉 DIRECT MODIFICATION WORKS!');
      console.log('   - Issue is in writeItemToDecryptedBuffer function');
      console.log('   - Direct buffer modification is the correct approach');
    } else {
      console.log('\n❌ DIRECT MODIFICATION FAILED');
      console.log('   - Issue may be in parsing or field offsets');
    }

    // Step 6: Test with writeItemToDecryptedBuffer function
    console.log('\n6. Testing with writeItemToDecryptedBuffer function...');
    
    // Create another copy and use the write function
    const testBuffer2 = originalDecrypted.slice(0);
    const testView2 = new DataView(testBuffer2);
    
    // Modify the item object
    const modifiedItem = { ...firstItem, level: 99 };
    
    // Use the write function
    const YbiParserClassPrivate = YbiParser as any;
    YbiParserClassPrivate.writeItemToDecryptedBuffer(testView2, firstItem.offset, modifiedItem);
    
    // Encrypt and parse
    const encryptedModified2 = YbiParserClass.cryptData(testBuffer2);
    const modifiedFile2 = YbiParser.parse(encryptedModified2, 'YBi.cfg');
    
    const modifiedFirstItem2 = modifiedFile2.items[0];
    console.log(`   📋 Write function result:`);
    console.log(`      - ID: ${modifiedFirstItem2.id} (should be ${firstItem.id})`);
    console.log(`      - Name: "${modifiedFirstItem2.name}" (should be "${firstItem.name}")`);
    console.log(`      - Level: ${modifiedFirstItem2.level} (should be 99)`);
    console.log(`      - Max Attack: ${modifiedFirstItem2.maxAtk} (should be ${firstItem.maxAtk})`);

    const id2Correct = modifiedFirstItem2.id === firstItem.id;
    const name2Correct = modifiedFirstItem2.name === firstItem.name;
    const level2Correct = modifiedFirstItem2.level === 99;
    const maxAtk2Correct = modifiedFirstItem2.maxAtk === firstItem.maxAtk;
    
    console.log(`   - ID correct: ${id2Correct ? '✅' : '❌'}`);
    console.log(`   - Name correct: ${name2Correct ? '✅' : '❌'}`);
    console.log(`   - Level correct: ${level2Correct ? '✅' : '❌'}`);
    console.log(`   - Max Attack correct: ${maxAtk2Correct ? '✅' : '❌'}`);

    // Step 7: Compare the two approaches
    console.log('\n7. Comparing approaches:');
    console.log(`   - Direct modification: ${idCorrect && nameCorrect && levelCorrect && maxAtkCorrect ? '✅ WORKS' : '❌ FAILS'}`);
    console.log(`   - Write function: ${id2Correct && name2Correct && level2Correct && maxAtk2Correct ? '✅ WORKS' : '❌ FAILS'}`);

    if ((idCorrect && nameCorrect && levelCorrect && maxAtkCorrect) && 
        !(id2Correct && name2Correct && level2Correct && maxAtk2Correct)) {
      console.log('\n🎯 CONCLUSION: writeItemToDecryptedBuffer function has bugs');
      console.log('   - Direct buffer modification works');
      console.log('   - Write function corrupts data');
      console.log('   - Need to fix or replace write function');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testSingleFieldModification().catch(console.error);
