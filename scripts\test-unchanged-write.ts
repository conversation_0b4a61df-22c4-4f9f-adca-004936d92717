#!/usr/bin/env tsx

/**
 * Test unchanged write functionality
 * This script will:
 * 1. Load a YBi.cfg file
 * 2. Parse it
 * 3. Generate without any modifications
 * 4. Compare byte-by-byte with original
 * 5. Verify 100% identical
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testUnchangedWrite() {
  console.log('🧪 Testing Unchanged Write Functionality\n');

  try {
    // Step 1: Load original file
    console.log('1. Loading original YBi.cfg file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(originalFilePath)) {
      console.log(`❌ Original file not found: ${originalFilePath}`);
      console.log('Please place a YBi.cfg file in the scripts directory');
      return;
    }

    const originalBuffer = fs.readFileSync(originalFilePath);
    console.log(`   ✅ Loaded file: ${originalBuffer.length} bytes`);

    // Step 2: Parse original file
    console.log('\n2. Parsing original file...');
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    console.log(`   ✅ Parsed successfully:`);
    console.log(`      - Items: ${originalFile.items.length}`);
    console.log(`      - Skills: ${originalFile.skills.length}`);
    console.log(`      - Abilities: ${originalFile.abilities.length}`);
    console.log(`      - Hero Titles: ${originalFile.heroTitles.length}`);
    console.log(`      - NPCs: ${originalFile.npcInfos.length}`);
    console.log(`      - Maps: ${originalFile.mapInfos.length}`);
    console.log(`      - Has original decrypted buffer: ${originalFile.originalDecryptedBuffer ? 'YES' : 'NO'}`);

    // Step 3: Generate without any modifications
    console.log('\n3. Generating file without modifications...');
    const regeneratedBuffer = YbiParser.generate(originalFile);
    const regeneratedFilePath = path.join(process.cwd(), 'scripts', 'YBi_unchanged.cfg');
    
    fs.writeFileSync(regeneratedFilePath, Buffer.from(regeneratedBuffer));
    console.log(`   ✅ Saved regenerated file: ${regeneratedFilePath}`);
    console.log(`      - Original size: ${originalBuffer.length} bytes`);
    console.log(`      - Regenerated size: ${regeneratedBuffer.byteLength} bytes`);
    console.log(`      - Size difference: ${regeneratedBuffer.byteLength - originalBuffer.length} bytes`);

    // Step 4: Compare byte by byte
    console.log('\n4. Comparing files byte by byte...');
    const originalBytes = new Uint8Array(originalBuffer);
    const regeneratedBytes = new Uint8Array(regeneratedBuffer);
    
    let differentBytes = 0;
    let firstDifferenceOffset = -1;
    const maxBytesToCheck = Math.min(originalBytes.length, regeneratedBytes.length);
    
    for (let i = 0; i < maxBytesToCheck; i++) {
      if (originalBytes[i] !== regeneratedBytes[i]) {
        if (firstDifferenceOffset === -1) {
          firstDifferenceOffset = i;
        }
        differentBytes++;
        
        // Show first few differences for debugging
        if (differentBytes <= 5) {
          console.log(`   Difference at offset 0x${i.toString(16)}: 0x${originalBytes[i].toString(16)} → 0x${regeneratedBytes[i].toString(16)}`);
        }
      }
    }
    
    console.log(`   📊 Comparison results:`);
    console.log(`      - Total bytes checked: ${maxBytesToCheck}`);
    console.log(`      - Different bytes: ${differentBytes}`);
    console.log(`      - Similarity: ${((maxBytesToCheck - differentBytes) / maxBytesToCheck * 100).toFixed(6)}%`);
    
    if (firstDifferenceOffset !== -1) {
      console.log(`      - First difference at offset: 0x${firstDifferenceOffset.toString(16)} (${firstDifferenceOffset})`);
    }

    // Step 5: Parse the regenerated file to verify structure
    console.log('\n5. Parsing regenerated file to verify structure...');
    const reloadedFile = YbiParser.parse(regeneratedBuffer, 'YBi_unchanged.cfg');
    console.log(`   ✅ Parsed regenerated file successfully:`);
    console.log(`      - Items: ${reloadedFile.items.length} (should be ${originalFile.items.length})`);
    console.log(`      - Skills: ${reloadedFile.skills.length} (should be ${originalFile.skills.length})`);
    console.log(`      - Abilities: ${reloadedFile.abilities.length} (should be ${originalFile.abilities.length})`);

    // Step 6: Compare first few items in detail
    console.log('\n6. Comparing first few items in detail...');
    const itemsToCheck = Math.min(3, originalFile.items.length, reloadedFile.items.length);
    let itemsMatch = true;
    
    for (let i = 0; i < itemsToCheck; i++) {
      const originalItem = originalFile.items[i];
      const reloadedItem = reloadedFile.items[i];
      
      const fieldsMatch = 
        originalItem.id === reloadedItem.id &&
        originalItem.name === reloadedItem.name &&
        originalItem.level === reloadedItem.level &&
        originalItem.maxAtk === reloadedItem.maxAtk &&
        originalItem.minAtk === reloadedItem.minAtk &&
        originalItem.def === reloadedItem.def &&
        originalItem.desc === reloadedItem.desc;
      
      if (!fieldsMatch) {
        itemsMatch = false;
        console.log(`   ❌ Item ${i} mismatch:`);
        console.log(`      Original: ID=${originalItem.id}, Name="${originalItem.name}", Level=${originalItem.level}`);
        console.log(`      Reloaded: ID=${reloadedItem.id}, Name="${reloadedItem.name}", Level=${reloadedItem.level}`);
      } else {
        console.log(`   ✅ Item ${i}: ID=${originalItem.id}, Name="${originalItem.name}", Level=${originalItem.level}`);
      }
    }

    // Final result
    console.log('\n🎉 TEST RESULTS:');
    if (differentBytes === 0 && 
        reloadedFile.items.length === originalFile.items.length &&
        itemsMatch) {
      console.log('✅ PERFECT: Unchanged write functionality works perfectly!');
      console.log('   - Files are byte-for-byte identical');
      console.log('   - All data structures preserved');
      console.log('   - Ready for modification testing');
    } else if (differentBytes === 0) {
      console.log('✅ GOOD: Files are byte-for-byte identical');
      console.log('❌ BUT: Some data structure issues detected');
    } else {
      console.log('❌ FAIL: Files are not identical');
      console.log(`   - Different bytes: ${differentBytes}`);
      console.log(`   - This indicates issues in write implementation`);
      
      if (differentBytes < 100) {
        console.log('   - Small number of differences - might be fixable');
      } else {
        console.log('   - Large number of differences - major issues');
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testUnchangedWrite().catch(console.error);
