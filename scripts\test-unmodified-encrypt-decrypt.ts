#!/usr/bin/env tsx

/**
 * Test encrypt/decrypt with unmodified data to isolate the issue
 */

import { YbiParser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testUnmodifiedEncryptDecrypt() {
  console.log('🔄 Testing Encrypt/Decrypt with Unmodified Data\n');

  try {
    // Step 1: Load and parse original file
    console.log('1. Loading original file...');
    const originalFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    const originalBuffer = fs.readFileSync(originalFilePath);
    const originalFile = YbiParser.parse(originalBuffer.buffer, 'YBi.cfg');
    
    const firstItem = originalFile.items[0];
    console.log(`   📋 First item from parsing:`);
    console.log(`      - ID: ${firstItem.id}`);
    console.log(`      - Level: ${firstItem.level}`);
    console.log(`      - Offset: 0x${firstItem.offset.toString(16)}`);

    // Step 2: Get original decrypted buffer WITHOUT modification
    console.log('\n2. Getting original decrypted buffer (no modification)...');
    const originalDecrypted = originalFile.originalDecryptedBuffer!;
    const originalView = new DataView(originalDecrypted);
    
    const itemOffset = firstItem.offset;
    const ID_OFFSET = 0x00;
    const LEVEL_OFFSET = 0x4C;
    
    const originalRawId = originalView.getUint32(itemOffset + ID_OFFSET, true);
    const originalRawLevel = originalView.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    console.log(`   📍 Original raw values:`);
    console.log(`      - ID: ${originalRawId}`);
    console.log(`      - Level: ${originalRawLevel}`);

    // Step 3: Encrypt the UNMODIFIED decrypted buffer
    console.log('\n3. Encrypting UNMODIFIED decrypted buffer...');
    const YbiParserClass = YbiParser as any;
    const encryptedUnmodified = YbiParserClass.cryptData(originalDecrypted);
    
    // Step 4: Decrypt again and check raw bytes
    console.log('\n4. Decrypting again and checking raw bytes...');
    const decryptedAgain = YbiParserClass.cryptData(encryptedUnmodified);
    const decryptedView = new DataView(decryptedAgain);
    
    const finalRawId = decryptedView.getUint32(itemOffset + ID_OFFSET, true);
    const finalRawLevel = decryptedView.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    console.log(`   📍 After encrypt/decrypt cycle (unmodified):`);
    console.log(`      - ID: ${finalRawId} (should be ${originalRawId})`);
    console.log(`      - Level: ${finalRawLevel} (should be ${originalRawLevel})`);

    // Step 5: Compare buffers byte by byte
    console.log('\n5. Comparing original vs final decrypted buffers...');
    const originalBytes = new Uint8Array(originalDecrypted);
    const finalBytes = new Uint8Array(decryptedAgain);
    
    let differences = 0;
    let firstDiff = -1;
    
    for (let i = 0; i < Math.min(originalBytes.length, finalBytes.length); i++) {
      if (originalBytes[i] !== finalBytes[i]) {
        if (firstDiff === -1) firstDiff = i;
        differences++;
        
        if (differences <= 10) {
          console.log(`   Diff at 0x${i.toString(16)}: 0x${originalBytes[i].toString(16)} → 0x${finalBytes[i].toString(16)}`);
        }
      }
    }
    
    console.log(`   📊 Buffer comparison (unmodified):`);
    console.log(`      - Total differences: ${differences}`);
    console.log(`      - Buffers identical: ${differences === 0 ? '✅' : '❌'}`);
    
    if (firstDiff !== -1) {
      console.log(`      - First difference at: 0x${firstDiff.toString(16)}`);
    }

    // Step 6: Parse the final encrypted buffer
    console.log('\n6. Parsing final encrypted buffer...');
    const finalParsed = YbiParser.parse(encryptedUnmodified, 'YBi.cfg');
    const finalFirstItem = finalParsed.items[0];
    
    console.log(`   📋 Parser results (unmodified):`);
    console.log(`      - ID: ${finalFirstItem.id} (should be ${firstItem.id})`);
    console.log(`      - Level: ${finalFirstItem.level} (should be ${firstItem.level})`);
    console.log(`      - Offset: 0x${finalFirstItem.offset.toString(16)}`);

    // Step 7: Now test with a TINY modification
    console.log('\n7. Testing with tiny modification (change 1 byte)...');
    const testBuffer = originalDecrypted.slice(0);
    const testView = new DataView(testBuffer);
    
    // Change just 1 byte - the level field
    testView.setUint16(itemOffset + LEVEL_OFFSET, 1, true); // Change level from 0 to 1
    
    console.log(`   📍 After tiny modification:`);
    const modifiedRawId = testView.getUint32(itemOffset + ID_OFFSET, true);
    const modifiedRawLevel = testView.getUint16(itemOffset + LEVEL_OFFSET, true);
    console.log(`      - ID: ${modifiedRawId} (should be ${originalRawId})`);
    console.log(`      - Level: ${modifiedRawLevel} (should be 1)`);

    // Encrypt and decrypt the modified buffer
    const encryptedModified = YbiParserClass.cryptData(testBuffer);
    const decryptedModified = YbiParserClass.cryptData(encryptedModified);
    const modifiedView = new DataView(decryptedModified);
    
    const finalModifiedId = modifiedView.getUint32(itemOffset + ID_OFFSET, true);
    const finalModifiedLevel = modifiedView.getUint16(itemOffset + LEVEL_OFFSET, true);
    
    console.log(`   📍 After encrypt/decrypt cycle (modified):`);
    console.log(`      - ID: ${finalModifiedId} (should be ${originalRawId})`);
    console.log(`      - Level: ${finalModifiedLevel} (should be 1)`);

    // Compare modified buffers
    const modifiedOriginalBytes = new Uint8Array(testBuffer);
    const modifiedFinalBytes = new Uint8Array(decryptedModified);
    
    let modifiedDifferences = 0;
    for (let i = 0; i < Math.min(modifiedOriginalBytes.length, modifiedFinalBytes.length); i++) {
      if (modifiedOriginalBytes[i] !== modifiedFinalBytes[i]) {
        modifiedDifferences++;
      }
    }
    
    console.log(`   📊 Modified buffer comparison:`);
    console.log(`      - Total differences: ${modifiedDifferences}`);
    console.log(`      - Modified buffers identical: ${modifiedDifferences === 0 ? '✅' : '❌'}`);

    // Final analysis
    console.log('\n🎯 FINAL ANALYSIS:');
    
    const unmodifiedWorks = (differences === 0);
    const modifiedWorks = (modifiedDifferences === 0);
    
    console.log(`   - Unmodified encrypt/decrypt works: ${unmodifiedWorks ? '✅' : '❌'}`);
    console.log(`   - Modified encrypt/decrypt works: ${modifiedWorks ? '✅' : '❌'}`);
    
    if (unmodifiedWorks && !modifiedWorks) {
      console.log('\n🎯 CONCLUSION: Encryption algorithm only works with original data');
      console.log('   - Algorithm may have checksums or validation that fails with modified data');
      console.log('   - Need to investigate encryption algorithm more deeply');
    } else if (!unmodifiedWorks) {
      console.log('\n🎯 CONCLUSION: Encryption algorithm is fundamentally broken');
      console.log('   - Even unmodified data gets corrupted');
      console.log('   - Algorithm implementation is incorrect');
    } else {
      console.log('\n🎯 CONCLUSION: Both work - issue is elsewhere');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testUnmodifiedEncryptDecrypt().catch(console.error);
