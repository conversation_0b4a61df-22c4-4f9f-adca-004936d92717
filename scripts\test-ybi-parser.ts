/**
 * Simple test script for YbiParser
 * Run with: npx tsx scripts/test-ybi-parser.ts
 */

import { YbiParser, YbiFile, YbiItem } from '../src/lib/parsers/ybi-parser';

// Helper function to create a mock encrypted buffer
function createMockEncryptedBuffer(): ArrayBuffer {
  // Create a larger test buffer to accommodate all sections
  const buffer = new ArrayBuffer(50 * 1024 * 1024); // 50MB should be enough
  const view = new DataView(buffer);
  
  // Write header (8 bytes)
  view.setUint32(0, 0x12345678, true); // Magic number
  view.setUint32(4, 0x87654321, true); // Unknown
  view.setUint16(8, 1, true); // Total items = 1
  
  // Write first item at offset 8 + 852 = 860
  const itemOffset1 = 8 + 852;
  view.setUint32(itemOffset1, 1001, true); // ID
  
  // Write item name at offset itemOffset1 + 8
  const nameBytes = new TextEncoder().encode('Test Item 1');
  const nameArray = new Uint8Array(buffer, itemOffset1 + 8, 64);
  nameArray.set(nameBytes);
  
  // Write some basic item properties
  view.setUint16(itemOffset1 + 0x4C, 10, true); // Level
  view.setUint8(itemOffset1 + 0x4E, 1); // Job level
  view.setUint16(itemOffset1 + 0x54, 100, true); // Max attack
  view.setUint16(itemOffset1 + 0x56, 80, true); // Min attack
  view.setUint16(itemOffset1 + 0x58, 50, true); // Defense
  
  // Write description
  const descBytes = new TextEncoder().encode('Test item description');
  const descArray = new Uint8Array(buffer, itemOffset1 + 0x9C, 256);
  descArray.set(descBytes);
  
  return buffer;
}

async function testYbiParser() {
  console.log('🧪 Testing YbiParser...\n');

  try {
    // Test 1: Encryption/Decryption
    console.log('1. Testing encryption/decryption...');
    const originalData = new ArrayBuffer(16);
    const view = new DataView(originalData);

    // Write some test data
    view.setUint32(0, 0x12345678, true);
    view.setUint32(4, 0x87654321, true);
    view.setUint32(8, 0xABCDEF00, true);
    view.setUint32(12, 0x11223344, true);

    // Encrypt
    const encrypted = (YbiParser as any).cryptData(originalData);

    // Decrypt (should be same as original since it's reversible)
    const decrypted = (YbiParser as any).cryptData(encrypted);

    // Compare original and decrypted
    const originalArray = new Uint8Array(originalData);
    const decryptedArray = new Uint8Array(decrypted);

    const isEqual = originalArray.every((byte, index) => byte === decryptedArray[index]);
    console.log(`   ✅ Encryption/Decryption: ${isEqual ? 'PASS' : 'FAIL'}`);

    // Test 2: String encoding/decoding
    console.log('\n2. Testing string encoding/decoding...');
    const testString = 'Hello World';
    const encoded = (YbiParser as any).encodeString(testString, 20);
    const decoded = (YbiParser as any).decodeString(encoded);
    console.log(`   Original: "${testString}"`);
    console.log(`   Decoded: "${decoded}"`);
    console.log(`   ✅ String encoding/decoding: ${testString === decoded ? 'PASS' : 'FAIL'}`);

    // Test 3: Parse real YBi.cfg file
    console.log('\n3. Testing real YBi.cfg file parsing...');

    // Read the real YBi.cfg file
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    console.log(`   Reading file: ${ybiFilePath}`);

    if (!fs.existsSync(ybiFilePath)) {
      console.log(`   ❌ File not found: ${ybiFilePath}`);
      return;
    }

    const fileBuffer = fs.readFileSync(ybiFilePath);
    console.log(`   File size: ${fileBuffer.length} bytes`);

    // Parse the file
    const parsedFile = YbiParser.parse(fileBuffer.buffer, 'YBi.cfg');
    
    console.log(`   File name: ${parsedFile.fileName}`);
    console.log(`   File size: ${parsedFile.fileSize} bytes`);
    console.log(`   Total items in header: ${parsedFile.header.totalItems}`);
    console.log(`   Parsed items: ${parsedFile.items.length}`);
    console.log(`   Skills: ${parsedFile.skills.length}`);
    console.log(`   Abilities: ${parsedFile.abilities.length}`);
    console.log(`   Hero Titles: ${parsedFile.heroTitles.length}`);
    console.log(`   NPCs: ${parsedFile.npcInfos.length}`);
    console.log(`   Maps: ${parsedFile.mapInfos.length}`);

    // Focus on skills debugging
    console.log(`\n   🔍 Skills Analysis:`);
    if (parsedFile.skills.length > 0) {
      console.log(`   First 10 skills:`);
      for (let i = 0; i < Math.min(10, parsedFile.skills.length); i++) {
        const skill = parsedFile.skills[i];
        console.log(`     [${i}] ID: ${skill.id}, Type: ${skill.type === 0 ? 'Label' : 'Skill'}, Name: "${skill.name}", Job: ${skill.job}, Offset: 0x${skill.offset.toString(16)}`);
      }

      // Check for problematic skills (very large IDs)
      const problematicSkills = parsedFile.skills.filter(s => s.id > 1000000);
      if (problematicSkills.length > 0) {
        console.log(`\n   ⚠️  Found ${problematicSkills.length} skills with suspicious large IDs:`);
        for (let i = 0; i < Math.min(5, problematicSkills.length); i++) {
          const skill = problematicSkills[i];
          console.log(`     ID: ${skill.id}, Type: ${skill.type === 0 ? 'Label' : 'Skill'}, Name: "${skill.name}", Offset: 0x${skill.offset.toString(16)}`);
        }

        // Debug: Check raw data at problematic offset
        const problematicSkill = problematicSkills[0];
        const offset = problematicSkill.offset;
        console.log(`\n   🔍 Raw data at offset 0x${offset.toString(16)}:`);
        const view = new DataView(fileBuffer.buffer);
        const bytes = [];
        for (let i = 0; i < 32; i++) {
          if (offset + i < view.buffer.byteLength) {
            bytes.push(view.getUint8(offset + i).toString(16).padStart(2, '0'));
          }
        }
        console.log(`     ${bytes.join(' ')}`);

        // Try to interpret as different data types
        console.log(`     As uint32 LE: 0x${view.getUint32(offset, true).toString(16)}`);
        console.log(`     As uint32 BE: 0x${view.getUint32(offset, false).toString(16)}`);

        // Check if this might be part of a string or other data
        const stringBytes = new Uint8Array(view.buffer, offset, 64);
        let str = '';
        for (let i = 0; i < stringBytes.length; i++) {
          if (stringBytes[i] === 0) break;
          if (stringBytes[i] >= 32 && stringBytes[i] <= 126) {
            str += String.fromCharCode(stringBytes[i]);
          } else {
            str += '.';
          }
        }
        console.log(`     As string: "${str}"`);
      }
    }

    if (parsedFile.items.length > 0) {
      const firstItem = parsedFile.items[0];
      console.log(`\n   First item details:`);
      console.log(`     ID: ${firstItem.id}`);
      console.log(`     Name: "${firstItem.name}"`);
      console.log(`     Level: ${firstItem.level}`);
      console.log(`     Job Level: ${firstItem.jobLevel}`);
      console.log(`     Max Attack: ${firstItem.maxAtk}`);
      console.log(`     Min Attack: ${firstItem.minAtk}`);
      console.log(`     Defense: ${firstItem.def}`);
    }

    console.log(`   ✅ File parsing: ${parsedFile.items.length > 0 ? 'PASS' : 'FAIL'}`);

    // Skip file generation test for now to focus on parsing
    console.log('\n4. Skipping file generation test - focusing on parsing accuracy');

    console.log('\n🎉 YbiParser testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testYbiParser();
