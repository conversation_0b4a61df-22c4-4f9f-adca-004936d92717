#!/usr/bin/env tsx

/**
 * Test with <PERSON><PERSON><PERSON>eader decrypted file
 */

import { Ybi<PERSON>arser } from '../src/lib/parsers/ybi-parser';
import * as fs from 'fs';
import * as path from 'path';

async function testYbiReaderFile() {
  console.log('🧪 Testing with YbiReader Decrypted File\n');

  try {
    // Load YbiReader decrypted file
    const decryptedFilePath = path.join(process.cwd(), 'scripts', 'Ybi_from_ybiReader.cfg.dec');
    
    if (!fs.existsSync(decryptedFilePath)) {
      console.log('❌ YbiReader decrypted file not found');
      return;
    }

    const decryptedBuffer = fs.readFileSync(decryptedFilePath);
    console.log(`✅ Loaded YbiReader decrypted file: ${decryptedBuffer.length} bytes`);

    // Try to encrypt it with our algorithm
    const YbiParserClass = YbiParser as any;
    const encrypted = YbiParserClass.cryptData(decryptedBuffer.buffer);
    console.log(`✅ Encrypted with our algorithm: ${encrypted.byteLength} bytes`);

    // Save encrypted file
    const encryptedFilePath = path.join(process.cwd(), 'scripts', 'Ybi_reencrypted_by_us.cfg');
    fs.writeFileSync(encryptedFilePath, Buffer.from(encrypted));
    console.log(`✅ Saved re-encrypted file: ${encryptedFilePath}`);

    // Try to decrypt it back
    const decrypted = YbiParserClass.cryptData(encrypted);
    console.log(`✅ Decrypted back: ${decrypted.byteLength} bytes`);

    // Compare with original decrypted
    const originalDecrypted = new Uint8Array(decryptedBuffer);
    const reDecrypted = new Uint8Array(decrypted);

    let identical = true;
    let differentBytes = 0;
    let firstDifferenceOffset = -1;

    for (let i = 0; i < Math.min(originalDecrypted.length, reDecrypted.length); i++) {
      if (originalDecrypted[i] !== reDecrypted[i]) {
        if (firstDifferenceOffset === -1) {
          firstDifferenceOffset = i;
        }
        differentBytes++;
        identical = false;

        // Show first few differences
        if (differentBytes <= 10) {
          console.log(`   Difference at offset 0x${i.toString(16)}: 0x${originalDecrypted[i].toString(16)} → 0x${reDecrypted[i].toString(16)}`);
        }
      }
    }

    console.log(`\n📊 Comparison results:`);
    console.log(`   - Total bytes: ${originalDecrypted.length}`);
    console.log(`   - Different bytes: ${differentBytes}`);
    console.log(`   - Similarity: ${((originalDecrypted.length - differentBytes) / originalDecrypted.length * 100).toFixed(6)}%`);

    if (firstDifferenceOffset !== -1) {
      console.log(`   - First difference at offset: 0x${firstDifferenceOffset.toString(16)}`);
    }

    console.log(`\n🎯 Result: ${identical ? '✅ PERFECT - Our algorithm is reversible with YbiReader files!' : '❌ FAILED - Our algorithm is not reversible'}`);

    // If it works, try to parse the original decrypted file
    if (identical) {
      console.log('\n🎉 SUCCESS! Now testing parsing...');
      
      // We need to create a fake encrypted file to parse
      // Since our parser expects encrypted input, we'll encrypt the decrypted file first
      const fakeEncrypted = YbiParserClass.cryptData(decryptedBuffer.buffer);
      
      try {
        const parsedFile = YbiParser.parse(fakeEncrypted, 'Ybi.cfg');
        console.log(`✅ Parsing successful:`);
        console.log(`   - Items: ${parsedFile.items.length}`);
        console.log(`   - Skills: ${parsedFile.skills.length}`);
        console.log(`   - Abilities: ${parsedFile.abilities.length}`);
        console.log(`   - Hero Titles: ${parsedFile.heroTitles.length}`);
        console.log(`   - NPCs: ${parsedFile.npcInfos.length}`);
        console.log(`   - Maps: ${parsedFile.mapInfos.length}`);

        // Show first few items
        console.log(`\n📋 First 3 items:`);
        for (let i = 0; i < Math.min(3, parsedFile.items.length); i++) {
          const item = parsedFile.items[i];
          console.log(`   ${i + 1}. ID: ${item.id}, Name: "${item.name}", Level: ${item.level}`);
        }

      } catch (error) {
        console.log(`❌ Parsing failed: ${error}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testYbiReaderFile().catch(console.error);
