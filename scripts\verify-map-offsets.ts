/**
 * Verify corrected Map offsets for different YBI versions
 * Run with: npx tsx scripts/verify-map-offsets.ts
 */

import { YBI_PARSER_CONFIGS, YbiParser } from '../src/lib/parsers/ybi-parser';

async function verifyMapOffsets() {
  console.log('✅ Verifying corrected Map offsets\n');

  try {
    const fs = await import('fs');
    const path = await import('path');

    const ybiFilePath = path.join(process.cwd(), 'scripts', 'YBi.cfg');
    
    if (!fs.existsSync(ybiFilePath)) {
      console.log('📁 No test YBi.cfg file found at scripts/YBi.cfg');
      return;
    }

    console.log('📁 Loading test file...');
    const fileBuffer = fs.readFileSync(ybiFilePath);
    console.log(`   File size: ${fileBuffer.length.toLocaleString()} bytes\n`);

    // Test each parser config with corrected offsets
    for (const config of YBI_PARSER_CONFIGS) {
      console.log(`🔍 Testing ${config.name} (${config.id}):`);
      
      try {
        const parsedFile = YbiParser.parseWithConfig(fileBuffer.buffer, config, 'test.cfg');
        
        console.log(`   ✅ Parsing successful!`);
        console.log(`   📊 Data counts:`);
        console.log(`      Items: ${parsedFile.items.length.toLocaleString()}`);
        console.log(`      Skills: ${parsedFile.skills.length.toLocaleString()}`);
        console.log(`      Abilities: ${parsedFile.abilities.length.toLocaleString()}`);
        console.log(`      Hero Titles: ${parsedFile.heroTitles.length.toLocaleString()}`);
        console.log(`      NPCs: ${parsedFile.npcInfos.length.toLocaleString()}`);
        console.log(`      Maps: ${parsedFile.mapInfos.length.toLocaleString()}`);

        // Analyze map data quality
        if (parsedFile.mapInfos.length > 0) {
          let validMaps = 0;
          let mapsWithNames = 0;
          let mapsWithCoords = 0;
          let mapsWithBGM = 0;
          
          const sampleMaps: any[] = [];
          
          for (let i = 0; i < Math.min(parsedFile.mapInfos.length, 100); i++) {
            const map = parsedFile.mapInfos[i];
            
            // Check validity
            if (map.id > 0 && map.id < 10000) validMaps++;
            if (map.name && map.name.trim().length > 0) mapsWithNames++;
            if (!isNaN(map.x) && !isNaN(map.y) && !isNaN(map.z) && 
                Math.abs(map.x) < 100000 && Math.abs(map.y) < 100000 && Math.abs(map.z) < 100000) {
              mapsWithCoords++;
            }
            if (map.bgm1 && map.bgm1.trim().length > 0) mapsWithBGM++;
            
            // Collect samples
            if (sampleMaps.length < 5 && (map.name.length > 0 || map.id > 0)) {
              sampleMaps.push({
                id: map.id,
                name: map.name,
                x: map.x,
                y: map.y,
                z: map.z,
                bgm1: map.bgm1
              });
            }
          }
          
          const testCount = Math.min(parsedFile.mapInfos.length, 100);
          console.log(`   📈 Map data quality (first ${testCount} maps):`);
          console.log(`      Valid IDs: ${validMaps}/${testCount} (${(validMaps/testCount*100).toFixed(1)}%)`);
          console.log(`      With Names: ${mapsWithNames}/${testCount} (${(mapsWithNames/testCount*100).toFixed(1)}%)`);
          console.log(`      Valid Coords: ${mapsWithCoords}/${testCount} (${(mapsWithCoords/testCount*100).toFixed(1)}%)`);
          console.log(`      With BGM: ${mapsWithBGM}/${testCount} (${(mapsWithBGM/testCount*100).toFixed(1)}%)`);
          
          if (sampleMaps.length > 0) {
            console.log(`   🎯 Sample maps:`);
            sampleMaps.forEach((map, idx) => {
              const coords = `(${map.x.toFixed(1)}, ${map.y.toFixed(1)}, ${map.z.toFixed(1)})`;
              const name = map.name.length > 0 ? `"${map.name}"` : '(no name)';
              const bgm = map.bgm1.length > 0 ? `BGM:"${map.bgm1}"` : '(no BGM)';
              console.log(`      ${idx + 1}. ID=${map.id}, Name=${name}, Pos=${coords}, ${bgm}`);
            });
          }
          
          // Overall assessment
          const overallScore = (validMaps + mapsWithNames + mapsWithCoords) / (testCount * 3) * 100;
          if (overallScore > 70) {
            console.log(`   🎉 Overall Quality: EXCELLENT (${overallScore.toFixed(1)}%)`);
          } else if (overallScore > 40) {
            console.log(`   ⚠️  Overall Quality: GOOD (${overallScore.toFixed(1)}%)`);
          } else if (overallScore > 20) {
            console.log(`   ⚠️  Overall Quality: POOR (${overallScore.toFixed(1)}%)`);
          } else {
            console.log(`   ❌ Overall Quality: BAD (${overallScore.toFixed(1)}%)`);
          }
        } else {
          console.log(`   ❌ No maps found`);
        }

      } catch (error) {
        console.log(`   ❌ Parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
      
      console.log('');
    }

    // Compare with auto-detection
    console.log('🤖 Comparing with auto-detection:');
    try {
      const autoFile = YbiParser.parse(fileBuffer.buffer, 'test.cfg');
      console.log(`   Auto-detected parser: ${autoFile.parserConfig.name}`);
      console.log(`   Auto-detected maps: ${autoFile.mapInfos.length.toLocaleString()}`);
      
      if (autoFile.mapInfos.length > 0) {
        const firstMap = autoFile.mapInfos[0];
        const lastMap = autoFile.mapInfos[autoFile.mapInfos.length - 1];
        console.log(`   First map: ID=${firstMap.id}, Name="${firstMap.name}"`);
        console.log(`   Last map: ID=${lastMap.id}, Name="${lastMap.name}"`);
      }
    } catch (error) {
      console.log(`   ❌ Auto-detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

  } catch (error) {
    console.error(`❌ Error verifying map offsets: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  console.log('\n✅ Map offset verification completed!');
}

// Run the verification
verifyMapOffsets().catch(console.error);
