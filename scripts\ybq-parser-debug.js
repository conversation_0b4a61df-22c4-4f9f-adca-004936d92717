// YBQ Parser Debug Script - So sánh với Go version
// Chạy script này trong Node.js để debug parser

const fs = require('fs');
const path = require('path');

/**
 * Encryption key array (CHÍNH XÁC THEO GO VERSION)
 */
const ENCRYPTION_KEY = (() => {
  const key = new Array(256);
  for (let i = 0; i < 256; i++) {
    // Chính xác theo Go version: ((uint(i>>4) & 1) | (uint(i>>2) & 0x18) | (uint(i>>1) & 0x40) | uint(2*((i&3)|(4*((i&4)|(2*(i&0xF8)))))))
    const part1 = (i >> 4) & 1;
    const part2 = (i >> 2) & 0x18;
    const part3 = (i >> 1) & 0x40;
    const part4 = 2 * ((i & 3) | (4 * ((i & 4) | (2 * (i & 0xF8)))));
    key[i] = (part1 | part2 | part3 | part4) & 0xFF; // FIXED: Ensure byte value
  }
  return key;
})();

/**
 * Text decoder/encoder for Windows-1252
 */
class Windows1252Codec {
  static decode(bytes) {
    return bytes.map(byte => String.fromCharCode(byte)).join('');
  }

  static encode(str) {
    const result = [];
    for (let i = 0; i < str.length; i++) {
      result.push(str.charCodeAt(i));
    }
    return result;
  }
}

/**
 * Decrypt data using encryption key
 */
function decrypt(data) {
  return data.map(byte => ENCRYPTION_KEY[byte]);
}

/**
 * Read LenData from byte array - CHÍNH XÁC THEO GO VERSION
 */
function getLen(data, pos) {
  const startOffset = pos.value;

  // Read until space (32) or newline (10)
  while (pos.value < data.length) {
    const byte = data[pos.value];
    if ((byte === 32 || byte === 10) && pos.value > startOffset) {
      const delimiter = byte;
      
      // Copy buffer from start to current position
      const buffer = [];
      for (let i = startOffset; i < pos.value; i++) {
        buffer.push(data[i]);
      }
      
      const rawString = Windows1252Codec.decode(buffer);
      const value = parseInt(rawString) || 0;
      
      pos.value++; // Skip delimiter
      
      // Handle multiple consecutive whitespace (suffix) - IMPORTANT!
      const suffixStart = pos.value;
      while (pos.value < data.length && (data[pos.value] === 32 || data[pos.value] === 10)) {
        pos.value++;
      }
      
      return {
        value,
        rawBytes: [...buffer],
        rawString,
        delimiter
      };
    }
    pos.value++;
  }

  // If we reach here, we've read to end of data
  const buffer = [];
  for (let i = startOffset; i < pos.value; i++) {
    buffer.push(data[i]);
  }

  return {
    value: 0,
    rawBytes: [...buffer],
    rawString: Windows1252Codec.decode(buffer),
    delimiter: 32
  };
}

/**
 * Test function để parse file YBQ theo Go version logic
 */
function testParseYbqFileGoStyle(filePath) {
  try {
    console.log(`\n=== Testing YBQ Parser với file: ${filePath} ===`);
    
    const data = fs.readFileSync(filePath);
    const bytes = Array.from(data);
    
    console.log(`File size: ${bytes.length} bytes`);
    console.log(`First 20 bytes: [${bytes.slice(0, 20).join(', ')}]`);
    
    let pos = 0;

    // Read sign (until space) - THEO GO VERSION
    const signBytes = [];
    while (pos < bytes.length && bytes[pos] !== 32) {
      signBytes.push(bytes[pos]);
      pos++;
    }
    const sign = Windows1252Codec.decode(signBytes);
    pos++; // Skip space
    console.log(`Sign: "${sign}", pos after sign: ${pos}`);

    // Read signEx (until \r\n) - THEO GO VERSION
    const signExBytes = [];
    while (pos < bytes.length - 1 && !(bytes[pos] === 13 && bytes[pos + 1] === 10)) {
      signExBytes.push(bytes[pos]);
      pos++;
    }
    const signEx = Windows1252Codec.decode(signExBytes);
    pos += 2; // Skip \r\n
    console.log(`SignEx: "${signEx}", pos after signEx: ${pos}`);

    // Read quest count - THEO GO VERSION
    console.log(`About to read quest count at position ${pos}`);
    console.log(`Next 20 bytes for quest count: [${bytes.slice(pos, pos + 20).join(', ')}]`);
    
    const questCountPos = { value: pos };
    const questCountData = getLen(bytes, questCountPos);
    const totalQuest = questCountData.value;
    pos = questCountPos.value;
    console.log(`Total quest count: ${totalQuest}, pos after count: ${pos}`);
    console.log(`Quest count raw data: [${questCountData.rawBytes?.join(', ')}], rawString: "${questCountData.rawString}", delimiter: ${questCountData.delimiter}`);

    // Read encrypted data
    const encrypted = bytes.slice(pos);
    const decrypted = decrypt(encrypted);
    console.log(`Encrypted data size: ${encrypted.length} bytes`);
    console.log(`Decrypted data size: ${decrypted.length} bytes`);
    console.log(`First 50 decrypted bytes: [${decrypted.slice(0, 50).join(', ')}]`);

    // Test parse first quest
    console.log(`\n=== Testing first quest parsing ===`);
    const questPos = { value: 0 };
    
    console.log(`Next 20 bytes at start of quest data: [${decrypted.slice(0, 20).join(', ')}]`);
    
    // Read quest ID
    const questIDData = getLen(decrypted, questPos);
    const questID = questIDData.value;
    
    console.log(`First QuestID: ${questID}, Position: ${questPos.value}`);
    console.log(`QuestID RawBytes: [${questIDData.rawBytes?.join(', ')}], RawString: "${questIDData.rawString}", Delimiter: ${questIDData.delimiter}`);
    
    if (questID > 0) {
      console.log(`✅ Successfully read first quest ID: ${questID}`);
      
      // Try to read quest name
      const questNameData = getLen(decrypted, questPos); // This should be length
      console.log(`Quest name length: ${questNameData.value}, pos: ${questPos.value}`);
      
      if (questNameData.value > 0 && questPos.value + questNameData.value <= decrypted.length) {
        const nameBytes = decrypted.slice(questPos.value, questPos.value + questNameData.value);
        const questName = Windows1252Codec.decode(nameBytes);
        questPos.value += questNameData.value;
        
        console.log(`Quest name: "${questName}"`);
        console.log(`✅ Successfully parsed first quest: ID=${questID}, Name="${questName}"`);
      } else {
        console.log(`❌ Invalid quest name length: ${questNameData.value}`);
      }
    } else {
      console.log(`❌ Invalid first quest ID: ${questID}`);
    }

    return {
      success: true,
      sign,
      signEx,
      totalQuest,
      encryptedSize: encrypted.length,
      decryptedSize: decrypted.length,
      firstQuestID: questID
    };
    
  } catch (error) {
    console.error('❌ Error parsing YBQ file:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Compare encryption keys
 */
function testEncryptionKey() {
  console.log('\n=== Testing Encryption Key ===');
  console.log(`First 10 key values: [${ENCRYPTION_KEY.slice(0, 10).join(', ')}]`);
  console.log(`Last 10 key values: [${ENCRYPTION_KEY.slice(-10).join(', ')}]`);
  
  // Test specific values that should match Go version
  const testValues = [0, 1, 2, 3, 4, 5, 255];
  console.log('Test specific key values:');
  testValues.forEach(i => {
    console.log(`  Key[${i}] = ${ENCRYPTION_KEY[i]}`);
  });
}

/**
 * Main test function
 */
function main() {
  console.log('YBQ Parser Debug Script');
  console.log('========================');
  
  // Test encryption key first
  testEncryptionKey();
  
  // Test with actual YBQ file (try both small and large files)
  const ybqFiles = [
    'E:\\YulgangDev\\golang\\YBQToolReloaded\\ybq-editor-go\\YBq.cfg', // Small file (21 quests)
    // Add path to large file here if available
  ];
  
  for (const ybqFilePath of ybqFiles) {
    if (fs.existsSync(ybqFilePath)) {
      const result = testParseYbqFileGoStyle(ybqFilePath);

      console.log('\n=== FINAL RESULT ===');
      console.log(JSON.stringify(result, null, 2));
      break; // Test first available file
    } else {
      console.log(`❌ YBQ file not found: ${ybqFilePath}`);
    }
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  testParseYbqFileGoStyle,
  testEncryptionKey,
  ENCRYPTION_KEY,
  decrypt,
  getLen
};
