import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ accountId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const accountId = encodeURIComponent(resolvedParams.accountId);
    const endpoint = `/api/accounts/${accountId}`;

    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'character:read'
      }
    );

    return result;
  });
}
