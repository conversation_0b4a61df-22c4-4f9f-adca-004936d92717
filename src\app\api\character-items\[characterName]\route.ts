import { NextRequest, NextResponse } from 'next/server';
import { getCharacterItems } from '@/lib/db-game';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ characterName: string }> }
) {
  try {
    const { characterName } = await params;
    
    if (!characterName) {
      return NextResponse.json(
        { success: false, message: 'Character name is required' },
        { status: 400 }
      );
    }

    const characterData = await getCharacterItems(decodeURIComponent(characterName));
    
    if (!characterData) {
      return NextResponse.json(
        { success: false, message: 'Character not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: characterData
    });
  } catch (error) {
    console.error('Error fetching character items:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
