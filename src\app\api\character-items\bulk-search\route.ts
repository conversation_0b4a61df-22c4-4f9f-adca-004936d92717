import { NextRequest, NextResponse } from 'next/server';
import { bulkSearchItems, BulkSearchFilters } from '@/lib/db-game';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const { searchType, searchValue, searchLocations } = body as BulkSearchFilters;
    
    if (!searchType || !searchValue || !searchLocations) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!['globalId', 'itemId', 'itemOption'].includes(searchType)) {
      return NextResponse.json(
        { success: false, message: 'Invalid search type' },
        { status: 400 }
      );
    }

    const searchValueNum = parseInt(searchValue);
    if (isNaN(searchValueNum)) {
      return NextResponse.json(
        { success: false, message: 'Search value must be a valid number' },
        { status: 400 }
      );
    }

    // Validate search locations
    if (!searchLocations.characters && !searchLocations.publicWarehouse && !searchLocations.privateWarehouse) {
      return NextResponse.json(
        { success: false, message: 'At least one search location must be selected' },
        { status: 400 }
      );
    }

    // Perform bulk search
    const result = await bulkSearchItems({
      searchType,
      searchValue,
      searchLocations
    });
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error in bulk search:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
