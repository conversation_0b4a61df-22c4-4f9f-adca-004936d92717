import { NextRequest, NextResponse } from 'next/server';
import { get<PERSON><PERSON>cters<PERSON><PERSON>, CharacterFilters, PaginationParams } from '@/lib/db-game';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse pagination params
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');
    
    // Validate pagination params
    if (page < 1 || pageSize < 1 || pageSize > 100) {
      return NextResponse.json(
        { success: false, message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    const pagination: PaginationParams = { page, pageSize };

    // Parse filters
    const filters: CharacterFilters = {};
    
    const name = searchParams.get('name');
    if (name) filters.fldName = name;
    
    const id = searchParams.get('id');
    if (id) filters.fldId = id;
    
    const job = searchParams.get('job');
    if (job) filters.fldJob = parseInt(job);
    
    const levelMin = searchParams.get('levelMin');
    const levelMax = searchParams.get('levelMax');
    if (levelMin || levelMax) {
      filters.fldLevel = {};
      if (levelMin) filters.fldLevel.min = parseInt(levelMin);
      if (levelMax) filters.fldLevel.max = parseInt(levelMax);
    }
    
    const jobLevelMin = searchParams.get('jobLevelMin');
    const jobLevelMax = searchParams.get('jobLevelMax');
    if (jobLevelMin || jobLevelMax) {
      filters.fldJobLevel = {};
      if (jobLevelMin) filters.fldJobLevel.min = parseInt(jobLevelMin);
      if (jobLevelMax) filters.fldJobLevel.max = parseInt(jobLevelMax);
    }
    
    const menowMin = searchParams.get('menowMin');
    const menowMax = searchParams.get('menowMax');
    if (menowMin || menowMax) {
      filters.fldMenow = {};
      if (menowMin) filters.fldMenow.min = parseInt(menowMin);
      if (menowMax) filters.fldMenow.max = parseInt(menowMax);
    }

    // Fetch data
    const result = await getCharactersList(pagination, filters);
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error fetching characters list:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
