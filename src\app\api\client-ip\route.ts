import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get client IP from various headers
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    
    let clientIp = '127.0.0.1';
    
    if (forwarded) {
      // x-forwarded-for can contain multiple IPs, take the first one
      clientIp = forwarded.split(',')[0].trim();
    } else if (realIp) {
      clientIp = realIp;
    } else if (cfConnectingIp) {
      clientIp = cfConnectingIp;
    }
    
    return NextResponse.json({ 
      ip: clientIp,
      headers: {
        'x-forwarded-for': forwarded,
        'x-real-ip': realIp,
        'cf-connecting-ip': cfConnectingIp
      }
    });
  } catch (error) {
    console.error('Error getting client IP:', error);
    return NextResponse.json({ 
      ip: '127.0.0.1',
      error: 'Failed to determine client IP'
    });
  }
}
