import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { BanPlayerRequest } from '@/types/gameserver';

// params is promise
export async function POST(request: NextRequest, { params }: { params: Promise<{ playerId: string }> }) {
  
  return handleApiRoute(async () => {
    const playerId = parseInt((await params).playerId);
    const data = await request.json();
    const requestData: BanPlayerRequest = {
      playerId,
      serverId: data.serverId,
      reason: data.reason,
      duration: data.duration,
      banType: data.banType,
    };

    if (!requestData.serverId) {
      throw new Error('serverId is required');
    }

    const endpoint = `/api/webadmin/gameserver/players/${playerId}/ban`;

    const result = await makeProxyRequest(endpoint, {
      method: 'POST',
      body: requestData,
      requiredPermission: 'players:ban',
    });

    return result;
  });
}