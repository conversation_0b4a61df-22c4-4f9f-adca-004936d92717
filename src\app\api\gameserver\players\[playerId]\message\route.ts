import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { SendMessageRequest } from '@/types/gameserver';

export async function POST(request: NextRequest, { params }: { params: Promise<{ playerId: string }> }) {
  const playerId = parseInt((await params).playerId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: SendMessageRequest = {
      playerId,
      serverId: body.serverId,
      message: body.message,
      messageType: body.messageType
    };

    if (!requestData.serverId) {
      throw new Error('serverId is required');
    }

    const endpoint = `/api/webadmin/gameserver/players/${playerId}/message`;

    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'players:message'
      }
    );

    return result;
  });
}
