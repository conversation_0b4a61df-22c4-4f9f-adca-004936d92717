import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { DisableNewConnectionsRequest, DisableNewConnectionsResponse } from '@/types/gameserver';

export async function POST(request: NextRequest, { params }: { params: Promise<{ serverId: string }> }) {
  return handleApiRoute(async () => {
    const serverId = parseInt((await params).serverId);
    const body = await request.json();

    const requestData: DisableNewConnectionsRequest = {
      serverId,
      clusterId: body.clusterId,
      disable: body.disable,
      reason: body.reason
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/disable-connections`;

    // Proxy request to game server
    const result = await makeProxyRequest<DisableNewConnectionsResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'servers:manage-connections'
      }
    );

    return result;
  });
}
