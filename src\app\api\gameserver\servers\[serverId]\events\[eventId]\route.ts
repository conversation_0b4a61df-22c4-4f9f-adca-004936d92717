import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { ManageEventRequest, ManageEventResponse } from '@/types/gameserver';

export async function POST(request: NextRequest, { params }: { params: Promise<{ serverId: string; eventId: string }> }) {
  return handleApiRoute(async () => {
    const serverId = parseInt((await params).serverId);
    const eventId = (await params).eventId;
    const body = await request.json();

    const requestData: ManageEventRequest = {
      serverId,
      clusterId: body.clusterId,
      eventId,
      action: body.action
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    if (!requestData.action || !['start', 'stop', 'pause', 'resume'].includes(requestData.action)) {
      throw new Error('Valid action is required (start, stop, pause, resume)');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/events/${eventId}`;

    // Proxy request to game server
    const result = await makeProxyRequest<ManageEventResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'servers:manage-events'
      }
    );

    return result;
  });
}
