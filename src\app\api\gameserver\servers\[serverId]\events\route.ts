import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { GetServerEventsRequest, GetServerEventsResponse } from '@/types/gameserver';

export async function POST(request: NextRequest, { params }: { params: Promise<{ serverId: string }> }) {
  return handleApiRoute(async () => {
    const serverId = parseInt((await params).serverId);
    const { searchParams } = new URL(request.url);
    
    const clusterId = searchParams.get('clusterId');
    const activeOnly = searchParams.get('activeOnly');

    const requestData: GetServerEventsRequest = {
      serverId,
      clusterId: clusterId ? parseInt(clusterId) : 0,
      activeOnly: activeOnly === 'true'
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const queryParams = new URLSearchParams();
    queryParams.append('clusterId', requestData.clusterId.toString());
    if (requestData.activeOnly !== undefined) {
      queryParams.append('activeOnly', requestData.activeOnly.toString());
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/events?${queryParams}`;

    // Proxy request to game server
    const result = await makeProxyRequest<GetServerEventsResponse>(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'servers:view-events'
      }
    );

    return result;
  });
}
