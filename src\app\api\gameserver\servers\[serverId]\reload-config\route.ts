import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { ReloadConfigRequest, ReloadConfigResponse } from '@/types/gameserver';

// params is a promise, so we can use await
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: ReloadConfigRequest = {
      serverId,
      clusterId: body.clusterId,
      configType: body.configType
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/reload-config`;

    // Proxy request to game server
    const result = await makeProxyRequest<ReloadConfigResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'server:update'
      }
    );

    return result;
  });
}
