import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { RestartGameServerRequest } from '@/types/gameserver';

export async function POST(request: NextRequest, { params }: { params: Promise<{ serverId: string }> }) {
  return handleApiRoute(async () => {
    const serverId = parseInt((await params).serverId);
    const body = await request.json();

    const requestData: RestartGameServerRequest = {
      serverId,
      clusterId: body.clusterId,
      graceful: body.graceful
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/restart`;

    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'servers:restart'
      }
    );

    return result;
  });
}
