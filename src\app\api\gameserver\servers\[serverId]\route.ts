import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';

export async function POST(request: NextRequest, { params }: { params: Promise<{ serverId: string }> }) {
  return handleApiRoute(async () => {
    const serverId = parseInt((await params).serverId);
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    if (!searchParams.get('clusterId')) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}${queryString ? `?${queryString}` : ''}`;

    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'servers:read'
      }
    );

    return result;
  });
}
