import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { SaveCharactersRequest, SaveCharactersResponse } from '@/types/gameserver';

export async function POST(request: NextRequest, { params }: { params: Promise<{ serverId: string }> }) {
  return handleApiRoute(async () => {
    const serverId = parseInt((await params).serverId);
    const body = await request.json();

    const requestData: SaveCharactersRequest = {
      serverId,
      clusterId: body.clusterId,
      forceAll: body.forceAll
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/save-characters`;

    // Proxy request to game server
    const result = await makeProxyRequest<SaveCharactersResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'servers:save-characters'
      }
    );

    return result;
  });
}
