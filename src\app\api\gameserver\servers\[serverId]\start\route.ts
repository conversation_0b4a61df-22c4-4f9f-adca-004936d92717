import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { StartGameServerRequest } from '@/types/gameserver';

export async function POST(request: NextRequest, { params }: { params: Promise<{ serverId: string }> }) {
  return handleApiRoute(async () => {
    const serverId = parseInt((await params).serverId);
    const body = await request.json();

    const requestData: StartGameServerRequest = {
      serverId,
      clusterId: body.clusterId,
      configOverrides: body.configOverrides
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/start`;

    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'servers:start'
      }
    );

    return result;
  });
}
