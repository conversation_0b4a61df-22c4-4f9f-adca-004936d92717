import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';

export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    const endpoint = `/api/webadmin/gameserver${queryString ? `?${queryString}` : ''}`;

    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'servers:read'
      }
    );

    return result;
  });
}
