// Quest Export API Endpoint
import { NextRequest, NextResponse } from 'next/server';
import { QuestExporter } from '@/lib/quest-exporter';
import { parseYbqFile } from '@/lib/ybq-parser';
import {
  QuestExportRequest,
  QuestExportResponse,
  QuestExportConfig
} from '@/types/quest-export';

/**
 * POST /api/quest/export
 * Export quest data to gameserver format
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as QuestExportRequest;

    // Validate request
    if (!body.config) {
      return NextResponse.json({
        success: false,
        message: 'Export config is required'
      } as QuestExportResponse, { status: 400 });
    }

    // Get YBQ data from session or request
    // Giả sử YBQ data được lưu trong session hoặc được gửi kèm request
    const ybqData = await getYbqDataFromSession(request);

    if (!ybqData) {
      return NextResponse.json({
        success: false,
        message: 'No quest data found. Please upload YBQ file first.'
      } as QuestExportResponse, { status: 400 });
    }

    // Create exporter with config
    const exporter = new QuestExporter(body.config);

    // Export quest data
    const exportData = exporter.exportQuestData(ybqData, body.questIds);

    // Convert to requested format
    let fileData: string;
    let fileName: string;
    let contentType: string;

    switch (body.format) {
      case 'json':
        fileData = Buffer.from(exporter.exportToJson(exportData)).toString('base64');
        fileName = `quest_export_${new Date().toISOString().split('T')[0]}.json`;
        contentType = 'application/json';
        break;

      case 'xml':
        fileData = Buffer.from(exporter.exportToXml(exportData)).toString('base64');
        fileName = `quest_export_${new Date().toISOString().split('T')[0]}.xml`;
        contentType = 'application/xml';
        break;

      case 'binary':
        // For binary format, we could serialize to a custom binary format
        fileData = Buffer.from(JSON.stringify(exportData)).toString('base64');
        fileName = `quest_export_${new Date().toISOString().split('T')[0]}.dat`;
        contentType = 'application/octet-stream';
        break;

      default:
        return NextResponse.json({
          success: false,
          message: 'Unsupported export format'
        } as QuestExportResponse, { status: 400 });
    }

    // Get export statistics
    const stats = exporter.getStats();

    const response: QuestExportResponse = {
      success: true,
      message: `Successfully exported ${stats.exportedQuests} quests in ${stats.processingTime}ms`,
      data: exportData,
      fileData,
      fileName
    };

    // Set appropriate headers for file download
    const nextResponse = NextResponse.json(response);
    nextResponse.headers.set('Content-Type', contentType);
    nextResponse.headers.set('Content-Disposition', `attachment; filename="${fileName}"`);

    return nextResponse;

  } catch (error) {
    console.error('Quest export error:', error);

    return NextResponse.json({
      success: false,
      message: `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    } as QuestExportResponse, { status: 500 });
  }
}

/**
 * GET /api/quest/export
 * Get export configuration options and available formats
 */
export async function GET(request: NextRequest) {
  try {
    const defaultConfig: QuestExportConfig = {
      includeUnknownFields: true,
      includeDialogs: true,
      includeCoordinates: true,
      minifyOutput: false,
      validateRequirements: true
    };

    const availableFormats = ['json', 'xml', 'binary'];

    const supportedRequirementTypes = [
      'item',
      'level',
      'job',
      'job_level',
      'ability_point',
      'faction',
      'guild'
    ];

    const supportedRewardTypes = [
      'item',
      'experience',
      'gold',
      'skill_point'
    ];

    return NextResponse.json({
      success: true,
      message: 'Export configuration retrieved successfully',
      data: {
        defaultConfig,
        availableFormats,
        supportedRequirementTypes,
        supportedRewardTypes,
        maxQuestsPerExport: 1000
      }
    });

  } catch (error) {
    console.error('Get export config error:', error);

    return NextResponse.json({
      success: false,
      message: `Failed to get export configuration: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 });
  }
}

/**
 * Helper function to get YBQ data from session or storage
 * This is a placeholder - implement based on your session management
 */
async function getYbqDataFromSession(request: NextRequest) {
  // TODO: Implement session/storage logic to retrieve YBQ data
  // This could be from:
  // 1. Session storage
  // 2. Database
  // 3. File system
  // 4. Request body (if data is sent with each request)

  // For now, return null - this needs to be implemented based on your architecture
  return null;
}