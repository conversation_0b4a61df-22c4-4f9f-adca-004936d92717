import { NextRequest, NextResponse } from 'next/server';
import { updateRole, deleteRole, UpdateRoleData } from '@/lib/actions/role-actions';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const roleId = (await params).id;
  try {
    const body = await request.json();
    const { name, description, level, permissions } = body;

    const updateData: UpdateRoleData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (level !== undefined) updateData.level = parseInt(level);
    if (permissions !== undefined) updateData.permissions = permissions;

    const result = await updateRole(roleId, updateData);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error updating role:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update role' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const roleId = (await params).id;
  try {
    const result = await deleteRole(roleId);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error deleting role:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete role' },
      { status: 500 }
    );
  }
}
