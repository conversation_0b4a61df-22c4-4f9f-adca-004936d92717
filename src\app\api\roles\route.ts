import { NextRequest, NextResponse } from 'next/server';
import { getRolesWithStats, createRole, getAvailablePermissions } from '@/lib/actions/role-actions';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (type === 'permissions') {
      const permissions = await getAvailablePermissions();
      return NextResponse.json({ success: true, data: permissions });
    }

    const roles = await getRolesWithStats();
    return NextResponse.json({ success: true, data: roles });
  } catch (error) {
    console.error('Error fetching roles:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch roles' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, level, permissions } = body;

    if (!name || !level) {
      return NextResponse.json(
        { success: false, message: 'Name and level are required' },
        { status: 400 }
      );
    }

    const result = await createRole({
      name,
      description,
      level: parseInt(level) as 1 | 2 | 3 | 4,
      permissions: permissions || []
    });

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error creating role:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create role' },
      { status: 500 }
    );
  }
}
