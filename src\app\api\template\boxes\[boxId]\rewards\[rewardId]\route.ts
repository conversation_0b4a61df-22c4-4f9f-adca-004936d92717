import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlOpen } from '@/../drizzle/public/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/template/boxes/[boxId]/rewards/[rewardId] - Get specific reward
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ boxId: string; rewardId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);
    const rewardId = parseInt(resolvedParams.rewardId);

    if (!boxId || !rewardId) {
      return {
        success: false,
        message: 'Invalid box ID or reward ID'
      };
    }

    const reward = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(and(
        eq(tblXwwlOpen.fldPid, boxId),
        eq(tblXwwlOpen.id, rewardId)
      ))
      .limit(1);

    if (reward.length === 0) {
      return {
        success: false,
        message: 'Reward not found'
      };
    }

    return {
      success: true,
      message: 'Reward loaded successfully',
      data: reward[0]
    };
  });
}

// PUT /api/template/boxes/[boxId]/rewards/[rewardId] - Update reward
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ boxId: string; rewardId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);
    const rewardId = parseInt(resolvedParams.rewardId);

    if (!boxId || !rewardId) {
      return {
        success: false,
        message: 'Invalid box ID or reward ID'
      };
    }

    const body = await request.json();

    const {
      fldPidx,
      fldNumber,
      fldNamex,
      fldPp,
      fldMagic1,
      fldMagic2,
      fldMagic3,
      fldMagic4,
      fldMagic5,
      fldFjThuctinh,
      fldFjTienhoa,
      fldFjTrungcapphuhon,
      fldBd,
      fldDays,
      comothongbao,
      sttHopEvent
    } = body;

    // Check if reward exists
    const existingReward = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(and(
        eq(tblXwwlOpen.fldPid, boxId),
        eq(tblXwwlOpen.id, rewardId)
      ))
      .limit(1);

    if (existingReward.length === 0) {
      return {
        success: false,
        message: 'Reward not found'
      };
    }

    // If fldPidx is being changed, check for conflicts within the same box
    if (fldPidx && fldPidx !== existingReward[0].fldPidx) {
      const conflictReward = await dbPublic
        .select()
        .from(tblXwwlOpen)
        .where(and(
          eq(tblXwwlOpen.fldPid, boxId),
          eq(tblXwwlOpen.fldPidx, fldPidx)
        ))
        .limit(1);

      if (conflictReward.length > 0) {
        return {
          success: false,
          message: 'Another reward with this item ID already exists in this box'
        };
      }
    }

    // Update reward
    await dbPublic
      .update(tblXwwlOpen)
      .set({
        ...(fldPidx !== undefined && { fldPidx }),
        ...(fldNumber !== undefined && { fldNumber }),
        ...(fldNamex !== undefined && { fldNamex }),
        ...(fldPp !== undefined && { fldPp }),
        ...(fldMagic1 !== undefined && { fldMagic1 }),
        ...(fldMagic2 !== undefined && { fldMagic2 }),
        ...(fldMagic3 !== undefined && { fldMagic3 }),
        ...(fldMagic4 !== undefined && { fldMagic4 }),
        ...(fldMagic5 !== undefined && { fldMagic5 }),
        ...(fldFjThuctinh !== undefined && { fldFjThuctinh }),
        ...(fldFjTienhoa !== undefined && { fldFjTienhoa }),
        ...(fldFjTrungcapphuhon !== undefined && { fldFjTrungcapphuhon }),
        ...(fldBd !== undefined && { fldBd }),
        ...(fldDays !== undefined && { fldDays }),
        ...(comothongbao !== undefined && { comothongbao }),
        ...(sttHopEvent !== undefined && { sttHopEvent })
      })
      .where(eq(tblXwwlOpen.id, rewardId));

    return {
      success: true,
      message: 'Reward updated successfully'
    };
  });
}

// DELETE /api/template/boxes/[boxId]/rewards/[rewardId] - Delete reward
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ boxId: string; rewardId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);
    const rewardId = parseInt(resolvedParams.rewardId);

    console.log('DELETE reward request:', { boxId, rewardId });

    if (!boxId || boxId <= 0) {
      return {
        success: false,
        message: 'Invalid box ID'
      };
    }

    if (!rewardId || rewardId <= 0) {
      return {
        success: false,
        message: 'Invalid reward ID'
      };
    }

    // Check if reward exists
    const existingReward = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(and(
        eq(tblXwwlOpen.fldPid, boxId),
        eq(tblXwwlOpen.id, rewardId)
      ))
      .limit(1);

    console.log('Existing reward found:', existingReward);

    if (existingReward.length === 0) {
      return {
        success: false,
        message: 'Reward not found'
      };
    }

    // Delete reward
    const deleteResult = await dbPublic
      .delete(tblXwwlOpen)
      .where(eq(tblXwwlOpen.id, rewardId));

    console.log('Delete result:', deleteResult);

    return {
      success: true,
      message: 'Reward deleted successfully'
    };
  });
}
