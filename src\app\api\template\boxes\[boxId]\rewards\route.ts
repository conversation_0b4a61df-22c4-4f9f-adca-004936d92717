import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlOpen } from '@/../drizzle/public/schema';
import { eq, and,  like, desc, asc } from 'drizzle-orm';

// GET /api/template/boxes/[boxId]/rewards - List rewards for a specific box
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ boxId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);

    if (!boxId) {
      return {
        success: false,
        message: 'Invalid box ID'
      };
    }

    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // Filter parameters
    const search = searchParams.get('search') || '';
    
    // Sorting parameters
    const sortBy = searchParams.get('sortBy') || 'fldPidx';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build where conditions
    const whereConditions = [eq(tblXwwlOpen.fldPid, boxId)];

    if (search && search.trim()) {
        whereConditions.push(like(tblXwwlOpen.fldNamex, `%${search}%`));
    }

    // Build order by clause
    let orderByColumn = tblXwwlOpen.fldPidx; // default
    if (sortBy && sortBy in tblXwwlOpen) {
      orderByColumn = tblXwwlOpen[sortBy as keyof typeof tblXwwlOpen] as any;
    }
    const orderBy = sortOrder === 'desc' ? desc(orderByColumn) : asc(orderByColumn);

    // Get total count for pagination
    const totalResult = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(and(...whereConditions));

    const total = totalResult.length;

    // Get rewards data
    const rewards = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    return {
      success: true,
      message: 'Box rewards loaded successfully',
      data: {
        rewards,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    };
  });
}

// POST /api/template/boxes/[boxId]/rewards - Add new reward to box
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ boxId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);

    if (!boxId) {
      return {
        success: false,
        message: 'Invalid box ID'
      };
    }

    const body = await request.json();
    
    const {
      fldPidx,
      fldNumber = 1,
      fldNamex,
      fldPp = 0,
      fldMagic1 = 0,
      fldMagic2 = 0,
      fldMagic3 = 0,
      fldMagic4 = 0,
      fldMagic5 = 0,
      fldFjThuctinh = 0,
      fldFjTienhoa = 0,
      fldFjTrungcapphuhon = 0,
      fldBd = 0,
      fldDays = 0,
      comothongbao = 0,
      sttHopEvent = 0
    } = body;

    if (!fldPidx || fldPidx <= 0) {
      return {
        success: false,
        message: 'fldPidx is required and must be greater than 0'
      };
    }

    // Check if this reward already exists for this box (by fldPidx)
    const existingReward = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(and(
        eq(tblXwwlOpen.fldPid, boxId),
        eq(tblXwwlOpen.fldPidx, fldPidx)
      ))
      .limit(1);

    if (existingReward.length > 0) {
      return {
        success: false,
        message: 'This reward item already exists for this box'
      };
    }

    // Get box name for the new reward
    const boxInfo = await dbPublic
      .select({ fldName: tblXwwlOpen.fldName })
      .from(tblXwwlOpen)
      .where(eq(tblXwwlOpen.fldPid, boxId))
      .limit(1);

    const boxName = boxInfo.length > 0 ? boxInfo[0].fldName : `Box ${boxId}`;

    // Insert new reward
    await dbPublic.insert(tblXwwlOpen).values({
      fldPid: boxId,
      fldPidx,
      fldNumber,
      fldName: boxName,
      fldNamex,
      fldPp,
      fldMagic1,
      fldMagic2,
      fldMagic3,
      fldMagic4,
      fldMagic5,
      fldFjThuctinh,
      fldFjTienhoa,
      fldFjTrungcapphuhon,
      fldBd,
      fldDays,
      comothongbao,
      sttHopEvent
    });

    return {
      success: true,
      message: 'Reward added successfully'
    };
  });
}
