import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlOpen } from '@/../drizzle/public/schema';
import { eq, sql } from 'drizzle-orm';

// GET /api/template/boxes/[boxId] - Get box info with aggregated data
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ boxId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);

    if (!boxId) {
      return {
        success: false,
        message: 'Invalid box ID'
      };
    }

    // Get box info with aggregated data
    const boxInfo = await dbPublic
      .select({
        fldPid: tblXwwlOpen.fldPid,
        fldName: sql<string>`MAX(${tblXwwlOpen.fldName})`.as('fldName'),
        rewardCount: sql<number>`COUNT(*)`.as('rewardCount'),
        totalPp: sql<number>`SUM(COALESCE(${tblXwwlOpen.fldPp}, 0))`.as('totalPp')
      })
      .from(tblXwwlOpen)
      .where(eq(tblXwwlOpen.fldPid, boxId))
      .groupBy(tblXwwlOpen.fldPid)
      .limit(1);

    if (boxInfo.length === 0) {
      return {
        success: false,
        message: 'Box not found'
      };
    }

    return {
      success: true,
      message: 'Box loaded successfully',
      data: boxInfo[0]
    };
  });
}

// PUT /api/template/boxes/[boxId] - Update box info
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ boxId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);

    if (!boxId) {
      return {
        success: false,
        message: 'Invalid box ID'
      };
    }

    const body = await request.json();
    const { fldName } = body;

    // Check if box exists
    const existingBox = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(eq(tblXwwlOpen.fldPid, boxId))
      .limit(1);

    if (existingBox.length === 0) {
      return {
        success: false,
        message: 'Box not found'
      };
    }

    // Update all records with this fldPid
    await dbPublic
      .update(tblXwwlOpen)
      .set({
        ...(fldName !== undefined && { fldName })
      })
      .where(eq(tblXwwlOpen.fldPid, boxId));

    return {
      success: true,
      message: 'Box updated successfully'
    };
  });
}

// DELETE /api/template/boxes/[boxId] - Delete box and all its rewards
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ boxId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const boxId = parseInt(resolvedParams.boxId);

    if (!boxId) {
      return {
        success: false,
        message: 'Invalid box ID'
      };
    }

    // Check if box exists
    const existingBox = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(eq(tblXwwlOpen.fldPid, boxId))
      .limit(1);

    if (existingBox.length === 0) {
      return {
        success: false,
        message: 'Box not found'
      };
    }

    // Delete all rewards for this box
    await dbPublic
      .delete(tblXwwlOpen)
      .where(eq(tblXwwlOpen.fldPid, boxId));

    return {
      success: true,
      message: 'Box and all its rewards deleted successfully'
    };
  });
}
