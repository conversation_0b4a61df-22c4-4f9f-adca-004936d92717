import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlOpen } from '@/../drizzle/public/schema';
import { eq, and, or, like, desc, asc, sql } from 'drizzle-orm';

// GET /api/template/boxes - List distinct boxes with reward counts and total PP
export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Filter parameters
    const search = searchParams.get('search') || '';
    const fldPid = searchParams.get('fldPid');
    
    // Sorting parameters
    const sortBy = searchParams.get('sortBy') || 'fldPid';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build where conditions for filtering
    const whereConditions = [];

    if (search) {
      whereConditions.push(
        or(
          like(tblXwwlOpen.fldName, `%${search}%`),
          eq(tblXwwlOpen.fldPid, parseInt(search) || -1)
        )
      );
    }

    if (fldPid) {
      whereConditions.push(eq(tblXwwlOpen.fldPid, parseInt(fldPid)));
    }

    // Get distinct boxes with aggregated data
    let query = dbPublic
      .select({
        fldPid: tblXwwlOpen.fldPid,
        fldName: sql<string>`MAX(${tblXwwlOpen.fldName})`.as('fldName'),
        rewardCount: sql<number>`COUNT(*)`.as('rewardCount'),
        totalPp: sql<number>`SUM(COALESCE(${tblXwwlOpen.fldPp}, 0))`.as('totalPp')
      })
      .from(tblXwwlOpen)
      .groupBy(tblXwwlOpen.fldPid);

    if (whereConditions.length > 0) {
       query.having(and(...whereConditions));
    }

    // Apply sorting
    const orderByColumn = sortBy === 'fldPid' ? tblXwwlOpen.fldPid : 
                         sortBy === 'rewardCount' ? sql`COUNT(*)` :
                         sortBy === 'totalPp' ? sql`SUM(COALESCE(${tblXwwlOpen.fldPp}, 0))` :
                         sql`MAX(${tblXwwlOpen.fldName})`;
    
    const orderBy = sortOrder === 'desc' ? desc(orderByColumn) : asc(orderByColumn);
    query.orderBy(orderBy);

    // Get total count for pagination
    const totalResult = await query;
    const total = totalResult.length;

    // Apply pagination
    const boxes = await query.limit(limit).offset(offset);

    return {
      success: true,
      message: 'Boxes loaded successfully',
      data: {
        boxes,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    };
  });
}

// POST /api/template/boxes - Create new box
export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    
    const {
      fldPid,
      fldName
    } = body;

    if (!fldPid || fldPid <= 0) {
      return {
        success: false,
        message: 'fldPid is required and must be greater than 0'
      };
    }

    // Check if box with this fldPid already exists
    const existingBox = await dbPublic
      .select()
      .from(tblXwwlOpen)
      .where(eq(tblXwwlOpen.fldPid, fldPid))
      .limit(1);

    if (existingBox.length > 0) {
      return {
        success: false,
        message: 'Box with this fldPid already exists'
      };
    }

    // Insert new box (we'll create an empty box entry that can have rewards added later)
    await dbPublic.insert(tblXwwlOpen).values({
      fldPid,
      fldName,
      fldPidx: 0, // Placeholder reward
      fldNumber: 0,
      fldNamex: 'Empty Box',
      fldPp: 0,
      fldMagic1: 0,
      fldMagic2: 0,
      fldMagic3: 0,
      fldMagic4: 0,
      fldMagic5: 0,
      fldFjThuctinh: 0,
      fldFjTienhoa: 0,
      fldFjTrungcapphuhon: 0,
      fldBd: 0,
      fldDays: 0,
      comothongbao: 0,
      sttHopEvent: 0
    });

    return {
      success: true,
      message: 'Box created successfully'
    };
  });
}
