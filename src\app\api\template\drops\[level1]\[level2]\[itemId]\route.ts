import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlDrop, tblXwwlItem } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { eq, and } from 'drizzle-orm';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ level1: string; level2: string; itemId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const level1 = parseInt(resolvedParams.level1);
    const level2 = parseInt(resolvedParams.level2);
    const itemId = parseInt(resolvedParams.itemId);

    if (!level1 || !level2 || !itemId) {
      return {
        success: false,
        message: 'Invalid parameters'
      };
    }

    // Get drop data with item details
    const dropData = await dbPublic
      .select({
        // Drop table fields
        fldLevel1: tblXwwlDrop.fldLevel1,
        fldLevel2: tblXwwlDrop.fldLevel2,
        fldPid: tblXwwlDrop.fldPid,
        fldName: tblXwwlDrop.fldName,
        fldMagic0: tblXwwlDrop.fldMagic0,
        fldMagic1: tblXwwlDrop.fldMagic1,
        fldMagic2: tblXwwlDrop.fldMagic2,
        fldMagic3: tblXwwlDrop.fldMagic3,
        fldMagic4: tblXwwlDrop.fldMagic4,
        fldSocapphuhon: tblXwwlDrop.fldSocapphuhon,
        fldTrungcapphuhon: tblXwwlDrop.fldTrungcapphuhon,
        fldTienhoa: tblXwwlDrop.fldTienhoa,
        fldKhoalai: tblXwwlDrop.fldKhoalai,
        fldPp: tblXwwlDrop.fldPp,
        fldSunx: tblXwwlDrop.fldSunx,
        comothongbao: tblXwwlDrop.comothongbao,
        fldDays: tblXwwlDrop.fldDays,
        // Item details
        itemName: tblXwwlItem.fldName,
        itemLevel: tblXwwlItem.fldLevel,
        itemReside1: tblXwwlItem.fldReside1,
        itemReside2: tblXwwlItem.fldReside2,
        itemSex: tblXwwlItem.fldSex,
        itemUpLevel: tblXwwlItem.fldUpLevel,
        itemRecycleMoney: tblXwwlItem.fldRecycleMoney,
        itemSaleMoney: tblXwwlItem.fldSaleMoney,
        itemQuestitem: tblXwwlItem.fldQuestitem,
        itemNj: tblXwwlItem.fldNj,
        itemDf: tblXwwlItem.fldDf,
        itemAt1: tblXwwlItem.fldAt1,
        itemAt2: tblXwwlItem.fldAt2,
        itemAp: tblXwwlItem.fldAp,
        itemJobLevel: tblXwwlItem.fldJobLevel,
        itemZx: tblXwwlItem.fldZx,
        itemEl: tblXwwlItem.fldEl,
        itemWx: tblXwwlItem.fldWx,
        itemWxjd: tblXwwlItem.fldWxjd,
        itemMoney: tblXwwlItem.fldMoney,
        itemWeight: tblXwwlItem.fldWeight,
        itemType: tblXwwlItem.fldType,
        itemNeedMoney: tblXwwlItem.fldNeedMoney,
        itemNeedFightexp: tblXwwlItem.fldNeedFightexp,
        itemMagic1: tblXwwlItem.fldMagic1,
        itemMagic2: tblXwwlItem.fldMagic2,
        itemMagic3: tblXwwlItem.fldMagic3,
        itemMagic4: tblXwwlItem.fldMagic4,
        itemMagic5: tblXwwlItem.fldMagic5,
        itemSide: tblXwwlItem.fldSide,
        itemSellType: tblXwwlItem.fldSellType,
        itemLock: tblXwwlItem.fldLock,
        itemSeries: tblXwwlItem.fldSeries,
        itemIntegration: tblXwwlItem.fldIntegration,
        itemDes: tblXwwlItem.fldDes,
        itemHeadWear: tblXwwlItem.fldHeadWear,
      })
      .from(tblXwwlDrop)
      .leftJoin(tblXwwlItem, eq(tblXwwlDrop.fldPid, tblXwwlItem.fldPid))
      .where(and(
        eq(tblXwwlDrop.fldLevel1, level1),
        eq(tblXwwlDrop.fldLevel2, level2),
        eq(tblXwwlDrop.fldPid, itemId)
      ))
      .limit(1);

    if (dropData.length === 0) {
      return {
        success: false,
        message: 'Drop item not found'
      };
    }

    return {
      success: true,
      data: dropData[0]
    };
  });
}
