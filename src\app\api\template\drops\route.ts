import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlDrop, tblXwwlItem } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { eq, and, like, gte, lte, or } from 'drizzle-orm';

// Get drops with pagination and filters
export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // Filters
    const search = searchParams.get('search');
    const minLevel = searchParams.get('minLevel');
    const maxLevel = searchParams.get('maxLevel');
    const itemId = searchParams.get('itemId');

    let query = dbPublic
      .select({
        fldLevel1: tblXwwlDrop.fldLevel1,
        fldLevel2: tblXwwlDrop.fldLevel2,
        fldPid: tblXwwlDrop.fldPid,
        fldName: tblXwwlDrop.fldName,
        fldMagic0: tblXwwlDrop.fldMagic0,
        fldMagic1: tblXwwlDrop.fldMagic1,
        fldMagic2: tblXwwlDrop.fldMagic2,
        fldMagic3: tblXwwlDrop.fldMagic3,
        fldMagic4: tblXwwlDrop.fldMagic4,
        fldSocapphuhon: tblXwwlDrop.fldSocapphuhon,
        fldTrungcapphuhon: tblXwwlDrop.fldTrungcapphuhon,
        fldTienhoa: tblXwwlDrop.fldTienhoa,
        fldKhoalai: tblXwwlDrop.fldKhoalai,
        fldPp: tblXwwlDrop.fldPp,
        fldSunx: tblXwwlDrop.fldSunx,
        comothongbao: tblXwwlDrop.comothongbao,
        fldDays: tblXwwlDrop.fldDays,
        // Item details
        itemName: tblXwwlItem.fldName,
        itemLevel: tblXwwlItem.fldLevel,
        itemReside1: tblXwwlItem.fldReside1,
        itemReside2: tblXwwlItem.fldReside2,
        itemType: tblXwwlItem.fldType,
      })
      .from(tblXwwlDrop)
      .leftJoin(tblXwwlItem, eq(tblXwwlDrop.fldPid, tblXwwlItem.fldPid));

    const conditions = [];

    // Apply filters
    if (search) {
      conditions.push(
        or(
          like(tblXwwlDrop.fldName, `%${search}%`),
          like(tblXwwlItem.fldName, `%${search}%`),
          eq(tblXwwlDrop.fldPid, parseInt(search) || 0)
        )
      );
    }

    if (minLevel) {
      conditions.push(gte(tblXwwlDrop.fldLevel1, parseInt(minLevel)));
    }

    if (maxLevel) {
      conditions.push(lte(tblXwwlDrop.fldLevel2, parseInt(maxLevel)));
    }

    if (itemId) {
      conditions.push(eq(tblXwwlDrop.fldPid, parseInt(itemId)));
    }

    if (conditions.length > 0) {
      query.where(and(...conditions));
    }

    // Get total count for pagination
    const totalQuery = dbPublic
      .select({ count: tblXwwlDrop.fldPid })
      .from(tblXwwlDrop)
      .leftJoin(tblXwwlItem, eq(tblXwwlDrop.fldPid, tblXwwlItem.fldPid));

    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }

    const [drops, totalResult] = await Promise.all([
      query.limit(limit).offset(offset),
      totalQuery
    ]);

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: {
        drops,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    };
  });
}

// Add new drop item
export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const {
      fldLevel1,
      fldLevel2,
      fldPid,
      fldName,
      fldMagic0 = 0,
      fldMagic1 = 0,
      fldMagic2 = 0,
      fldMagic3 = 0,
      fldMagic4 = 0,
      fldSocapphuhon = 0,
      fldTrungcapphuhon = 0,
      fldTienhoa = 0,
      fldKhoalai = 0,
      fldPp = 0,
      fldSunx = '',
      comothongbao = 0,
      fldDays = 0
    } = body;

    if (!fldLevel1 || !fldLevel2 || !fldPid || !fldPp) {
      return {
        success: false,
        message: 'Missing required fields: fldLevel1, fldLevel2, fldPid, fldPp'
      };
    }

    // Check if drop already exists for this level range and item
    const existingDrop = await dbPublic
      .select()
      .from(tblXwwlDrop)
      .where(and(
        eq(tblXwwlDrop.fldLevel1, fldLevel1),
        eq(tblXwwlDrop.fldLevel2, fldLevel2),
        eq(tblXwwlDrop.fldPid, fldPid)
      ))
      .limit(1);

    if (existingDrop.length > 0) {
      return {
        success: false,
        message: 'Drop item already exists for this level range'
      };
    }

    // Get item name if not provided
    let itemName = fldName;
    if (!itemName) {
      const item = await dbPublic
        .select({ fldName: tblXwwlItem.fldName })
        .from(tblXwwlItem)
        .where(eq(tblXwwlItem.fldPid, fldPid))
        .limit(1);
      
      if (item.length > 0) {
        itemName = item[0].fldName;
      }
    }

    await dbPublic.insert(tblXwwlDrop).values({
      fldLevel1,
      fldLevel2,
      fldPid,
      fldName: itemName,
      fldMagic0,
      fldMagic1,
      fldMagic2,
      fldMagic3,
      fldMagic4,
      fldSocapphuhon,
      fldTrungcapphuhon,
      fldTienhoa,
      fldKhoalai,
      fldPp,
      fldSunx,
      comothongbao,
      fldDays
    });

    return {
      success: true,
      message: 'Drop item added successfully'
    };
  });
}

// Update drop item
export async function PUT(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const {
      originalLevel1,
      originalLevel2,
      originalPid,
      fldLevel1,
      fldLevel2,
      fldPid,
      fldName,
      fldMagic0,
      fldMagic1,
      fldMagic2,
      fldMagic3,
      fldMagic4,
      fldSocapphuhon,
      fldTrungcapphuhon,
      fldTienhoa,
      fldKhoalai,
      fldPp,
      fldSunx,
      comothongbao,
      fldDays
    } = body;

    if (!originalLevel1 || !originalLevel2 || !originalPid) {
      return {
        success: false,
        message: 'Missing original identifiers'
      };
    }

    await dbPublic
      .update(tblXwwlDrop)
      .set({
        fldLevel1,
        fldLevel2,
        fldPid,
        fldName,
        fldMagic0,
        fldMagic1,
        fldMagic2,
        fldMagic3,
        fldMagic4,
        fldSocapphuhon,
        fldTrungcapphuhon,
        fldTienhoa,
        fldKhoalai,
        fldPp,
        fldSunx,
        comothongbao,
        fldDays
      })
      .where(and(
        eq(tblXwwlDrop.fldLevel1, originalLevel1),
        eq(tblXwwlDrop.fldLevel2, originalLevel2),
        eq(tblXwwlDrop.fldPid, originalPid)
      ));

    return {
      success: true,
      message: 'Drop item updated successfully'
    };
  });
}

// Delete drop item
export async function DELETE(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { fldLevel1, fldLevel2, fldPid } = body;

    if (!fldLevel1 || !fldLevel2 || !fldPid) {
      return {
        success: false,
        message: 'Missing required fields: fldLevel1, fldLevel2, fldPid'
      };
    }

    await dbPublic
      .delete(tblXwwlDrop)
      .where(and(
        eq(tblXwwlDrop.fldLevel1, fldLevel1),
        eq(tblXwwlDrop.fldLevel2, fldLevel2),
        eq(tblXwwlDrop.fldPid, fldPid)
      ));

    return {
      success: true,
      message: 'Drop item deleted successfully'
    };
  });
}
