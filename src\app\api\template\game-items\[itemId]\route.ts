import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlItem } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { eq } from 'drizzle-orm';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ itemId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const itemId = parseInt(resolvedParams.itemId);

    if (!itemId) {
      return {
        success: false,
        message: 'Invalid item ID'
      };
    }

    // Get item data
    const item = await dbPublic
      .select()
      .from(tblXwwlItem)
      .where(eq(tblXwwlItem.fldPid, itemId))
      .limit(1);

    if (item.length === 0) {
      return {
        success: false,
        message: 'Item not found'
      };
    }

    return {
      success: true,
      data: item[0]
    };
  });
}
