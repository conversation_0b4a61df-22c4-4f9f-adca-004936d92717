import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlItem } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { inArray, eq, and, like, gte, lte, or } from 'drizzle-orm';

// Get items with pagination and filters
export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);

    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // Filters
    const search = searchParams.get('search');
    const minLevel = searchParams.get('minLevel');
    const maxLevel = searchParams.get('maxLevel');
    const fldType = searchParams.get('fldType');
    const fldReside1 = searchParams.get('fldReside1');
    const fldReside2 = searchParams.get('fldReside2');

    const query = dbPublic.select().from(tblXwwlItem).orderBy(tblXwwlItem.fldPid);
    const conditions = [];

    // Apply filters
    if (search) {
      conditions.push(
        or(
          like(tblXwwlItem.fldName, `%${search}%`),
          eq(tblXwwlItem.fldPid, parseInt(search) || 0)
        )
      );
    }

    if (minLevel) {
      conditions.push(gte(tblXwwlItem.fldLevel, parseInt(minLevel)));
    }

    if (maxLevel) {
      conditions.push(lte(tblXwwlItem.fldLevel, parseInt(maxLevel)));
    }

    if (fldType) {
      conditions.push(eq(tblXwwlItem.fldType, parseInt(fldType)));
    }

    if (fldReside1) {
      conditions.push(eq(tblXwwlItem.fldReside1, parseInt(fldReside1)));
    }

    if (fldReside2) {
      conditions.push(eq(tblXwwlItem.fldReside2, parseInt(fldReside2)));
    }
    query.where(and(...conditions));
    // if (conditions.length > 0) {
    //   query = query.where(and(...conditions));
    // }

    // Get total count for pagination
    const totalQuery = dbPublic.select({ count: tblXwwlItem.fldPid }).from(tblXwwlItem);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }

    const [items, totalResult] = await Promise.all([
      query.limit(limit).offset(offset),
      totalQuery
    ]);

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: {
        items,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    };
  });
}

// Get template items by IDs or Add new item
export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();

    // Check if this is a request for getting items by IDs
    if (body.itemIds) {
      const { itemIds } = body;

      if (!itemIds || !Array.isArray(itemIds) || itemIds.length === 0) {
        return {
          success: false,
          message: 'Invalid itemIds array'
        };
      }

      // Get template items by IDs
      const items = await dbPublic
        .select()
        .from(tblXwwlItem)
        .where(inArray(tblXwwlItem.fldPid, itemIds));

      return {
        success: true,
        message: 'Template items loaded successfully',
        data: {
          items
        }
      };
    }

    // Otherwise, this is a request to add a new item
    const {
      fldPid,
      fldName,
      fldReside1 = 0,
      fldReside2 = 0,
      fldSex = 0,
      fldLevel = 0,
      fldUpLevel = 0,
      fldRecycleMoney = 0,
      fldSaleMoney = 0,
      fldQuestitem = 0,
      fldNj = 0,
      fldDf = 0,
      fldAt1 = 0,
      fldAt2 = 0,
      fldAp = 0,
      fldJobLevel = 0,
      fldZx = 0,
      fldEl = 0,
      fldWx = 0,
      fldWxjd = 0,
      fldMoney = 0,
      fldWeight = 0,
      fldType = 0,
      fldNeedMoney = 0,
      fldNeedFightexp = 0,
      fldMagic1 = 0,
      fldMagic2 = 0,
      fldMagic3 = 0,
      fldMagic4 = 0,
      fldMagic5 = 0,
      fldSide = 0,
      fldSellType = 0,
      fldLock = 0,
      fldSeries = 0,
      fldIntegration = 0,
      fldDes = '',
      fldHeadWear = 0
    } = body;

    if (!fldPid || !fldName) {
      return {
        success: false,
        message: 'Missing required fields: fldPid, fldName'
      };
    }

    // Check if item already exists
    const existingItem = await dbPublic
      .select()
      .from(tblXwwlItem)
      .where(eq(tblXwwlItem.fldPid, fldPid))
      .limit(1);

    if (existingItem.length > 0) {
      return {
        success: false,
        message: 'Item with this ID already exists'
      };
    }

    await dbPublic.insert(tblXwwlItem).values({
      fldPid,
      fldName,
      fldReside1,
      fldReside2,
      fldSex,
      fldLevel,
      fldUpLevel,
      fldRecycleMoney,
      fldSaleMoney,
      fldQuestitem,
      fldNj,
      fldDf,
      fldAt1,
      fldAt2,
      fldAp,
      fldJobLevel,
      fldZx,
      fldEl,
      fldWx,
      fldWxjd,
      fldMoney,
      fldWeight,
      fldType,
      fldNeedMoney,
      fldNeedFightexp,
      fldMagic1,
      fldMagic2,
      fldMagic3,
      fldMagic4,
      fldMagic5,
      fldSide,
      fldSellType,
      fldLock,
      fldSeries,
      fldIntegration,
      fldDes,
      fldHeadWear
    });

    return {
      success: true,
      message: 'Item added successfully'
    };
  });
}

// Update item
export async function PUT(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { originalPid, ...updateData } = body;

    if (!originalPid) {
      return {
        success: false,
        message: 'Missing original item ID'
      };
    }

    // Check if item exists
    const existingItem = await dbPublic
      .select()
      .from(tblXwwlItem)
      .where(eq(tblXwwlItem.fldPid, originalPid))
      .limit(1);

    if (existingItem.length === 0) {
      return {
        success: false,
        message: 'Item not found'
      };
    }

    // If changing ID, check if new ID already exists
    if (updateData.fldPid && updateData.fldPid !== originalPid) {
      const duplicateItem = await dbPublic
        .select()
        .from(tblXwwlItem)
        .where(eq(tblXwwlItem.fldPid, updateData.fldPid))
        .limit(1);

      if (duplicateItem.length > 0) {
        return {
          success: false,
          message: 'Item with new ID already exists'
        };
      }
    }

    await dbPublic
      .update(tblXwwlItem)
      .set(updateData)
      .where(eq(tblXwwlItem.fldPid, originalPid));

    return {
      success: true,
      message: 'Item updated successfully'
    };
  });
}

// Delete item
export async function DELETE(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { fldPid } = body;

    if (!fldPid) {
      return {
        success: false,
        message: 'Missing required field: fldPid'
      };
    }

    await dbPublic
      .delete(tblXwwlItem)
      .where(eq(tblXwwlItem.fldPid, fldPid));

    return {
      success: true,
      message: 'Item deleted successfully'
    };
  });
}
