import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblItemoption } from '@/../drizzle/public/schema';
import { eq } from 'drizzle-orm';

// GET /api/template/item-options/[id] - Get single item option by ID
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    if (!id) {
      return {
        success: false,
        message: 'Invalid item option ID'
      };
    }

    const itemOption = await dbPublic
      .select()
      .from(tblItemoption)
      .where(eq(tblItemoption.id, id))
      .limit(1);

    if (itemOption.length === 0) {
      return {
        success: false,
        message: 'Item option not found'
      };
    }

    return {
      success: true,
      message: 'Item option loaded successfully',
      data: itemOption[0]
    };
  });
}

// PUT /api/template/item-options/[id] - Update item option
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    if (!id) {
      return {
        success: false,
        message: 'Invalid item option ID'
      };
    }

    const body = await request.json();
    
    const {
      fldPid,
      fldName,
      bonusHp,
      bonusPercenthp,
      bonusMp,
      bonusPercentmp,
      bonusAtk,
      bonusPercentatk,
      bonusPercentdf,
      bonusDf,
      bonusPercentatkskill,
      bonusDefskill,
      bonusQigong,
      bonusDropgold,
      bonusExp,
      bonusLucky,
      bonusAccuracy,
      bonusEvasion,
      bonusDiemhoangkim,
      bonusAtkmonster,
      bonusDefmonster
    } = body;

    // Check if item option exists
    const existingItemOption = await dbPublic
      .select()
      .from(tblItemoption)
      .where(eq(tblItemoption.id, id))
      .limit(1);

    if (existingItemOption.length === 0) {
      return {
        success: false,
        message: 'Item option not found'
      };
    }

    // If fldPid is being changed, check for conflicts
    if (fldPid && fldPid !== existingItemOption[0].fldPid) {
      const conflictItemOption = await dbPublic
        .select()
        .from(tblItemoption)
        .where(eq(tblItemoption.fldPid, fldPid))
        .limit(1);

      if (conflictItemOption.length > 0) {
        return {
          success: false,
          message: 'Another item option with this fldPid already exists'
        };
      }
    }

    // Update item option
    await dbPublic
      .update(tblItemoption)
      .set({
        ...(fldPid !== undefined && { fldPid }),
        ...(fldName !== undefined && { fldName }),
        ...(bonusHp !== undefined && { bonusHp }),
        ...(bonusPercenthp !== undefined && { bonusPercenthp }),
        ...(bonusMp !== undefined && { bonusMp }),
        ...(bonusPercentmp !== undefined && { bonusPercentmp }),
        ...(bonusAtk !== undefined && { bonusAtk }),
        ...(bonusPercentatk !== undefined && { bonusPercentatk }),
        ...(bonusPercentdf !== undefined && { bonusPercentdf }),
        ...(bonusDf !== undefined && { bonusDf }),
        ...(bonusPercentatkskill !== undefined && { bonusPercentatkskill }),
        ...(bonusDefskill !== undefined && { bonusDefskill }),
        ...(bonusQigong !== undefined && { bonusQigong }),
        ...(bonusDropgold !== undefined && { bonusDropgold }),
        ...(bonusExp !== undefined && { bonusExp }),
        ...(bonusLucky !== undefined && { bonusLucky }),
        ...(bonusAccuracy !== undefined && { bonusAccuracy }),
        ...(bonusEvasion !== undefined && { bonusEvasion }),
        ...(bonusDiemhoangkim !== undefined && { bonusDiemhoangkim }),
        ...(bonusAtkmonster !== undefined && { bonusAtkmonster }),
        ...(bonusDefmonster !== undefined && { bonusDefmonster })
      })
      .where(eq(tblItemoption.id, id));

    return {
      success: true,
      message: 'Item option updated successfully'
    };
  });
}

// DELETE /api/template/item-options/[id] - Delete item option
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    if (!id) {
      return {
        success: false,
        message: 'Invalid item option ID'
      };
    }

    // Check if item option exists
    const existingItemOption = await dbPublic
      .select()
      .from(tblItemoption)
      .where(eq(tblItemoption.id, id))
      .limit(1);

    if (existingItemOption.length === 0) {
      return {
        success: false,
        message: 'Item option not found'
      };
    }

    // Delete item option
    await dbPublic
      .delete(tblItemoption)
      .where(eq(tblItemoption.id, id));

    return {
      success: true,
      message: 'Item option deleted successfully'
    };
  });
}
