import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblItemoption } from '@/../drizzle/public/schema';
import { eq, and, or, like, desc, asc } from 'drizzle-orm';

// GET /api/template/item-options - List item options with filtering, pagination, and sorting
export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Filter parameters
    const search = searchParams.get('search') || '';
    const fldPid = searchParams.get('fldPid');
    
    // Sorting parameters
    const sortBy = searchParams.get('sortBy') || 'id';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build where conditions
    const whereConditions = [];

    if (search) {
      whereConditions.push(
        or(
          like(tblItemoption.fldName, `%${search}%`),
          eq(tblItemoption.fldPid, parseInt(search) || -1)
        )
      );
    }

    if (fldPid) {
      whereConditions.push(eq(tblItemoption.fldPid, parseInt(fldPid)));
    }

    // Build order by clause
    const orderByColumn = tblItemoption.id;
    const orderBy = sortOrder === 'desc' ? desc(orderByColumn) : asc(orderByColumn);

    // Get total count for pagination
    let totalCountQuery = dbPublic
      .select()
      .from(tblItemoption);

    if (whereConditions.length > 0) {
      totalCountQuery.where(and(...whereConditions));
    }

    const totalResult = await totalCountQuery;
    const total = totalResult.length;

    // Get item options data
    let query = dbPublic
      .select()
      .from(tblItemoption)
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    if (whereConditions.length > 0) {
      query.where(and(...whereConditions));
    }

    const itemOptions = await query;

    return {
      success: true,
      message: 'Item options loaded successfully',
      data: {
        itemOptions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    };
  });
}

// POST /api/template/item-options - Create new item option
export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    
    const {
      fldPid,
      fldName,
      bonusHp = 0,
      bonusPercenthp = 0,
      bonusMp = 0,
      bonusPercentmp = 0,
      bonusAtk = 0,
      bonusPercentatk = 0,
      bonusPercentdf = 0,
      bonusDf = 0,
      bonusPercentatkskill = 0,
      bonusDefskill = 0,
      bonusQigong = 0,
      bonusDropgold = 0,
      bonusExp = 0,
      bonusLucky = 0,
      bonusAccuracy = 0,
      bonusEvasion = 0,
      bonusDiemhoangkim = 0,
      bonusAtkmonster = 0,
      bonusDefmonster = 0
    } = body;

    if (!fldPid || fldPid <= 0) {
      return {
        success: false,
        message: 'fldPid is required and must be greater than 0'
      };
    }

    // Check if item option with this fldPid already exists
    const existingItemOption = await dbPublic
      .select()
      .from(tblItemoption)
      .where(eq(tblItemoption.fldPid, fldPid))
      .limit(1);

    if (existingItemOption.length > 0) {
      return {
        success: false,
        message: 'Item option with this fldPid already exists'
      };
    }

    // Insert new item option
    await dbPublic.insert(tblItemoption).values({
      fldPid,
      fldName,
      bonusHp,
      bonusPercenthp,
      bonusMp,
      bonusPercentmp,
      bonusAtk,
      bonusPercentatk,
      bonusPercentdf,
      bonusDf,
      bonusPercentatkskill,
      bonusDefskill,
      bonusQigong,
      bonusDropgold,
      bonusExp,
      bonusLucky,
      bonusAccuracy,
      bonusEvasion,
      bonusDiemhoangkim,
      bonusAtkmonster,
      bonusDefmonster
    });

    return {
      success: true,
      message: 'Item option created successfully'
    };
  });
}
