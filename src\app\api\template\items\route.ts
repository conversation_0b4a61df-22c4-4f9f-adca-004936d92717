import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlSell } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { eq, and } from 'drizzle-orm';

// Add item to template
export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { fldNid, fldIndex, fldPid, fldMoney, fldMagic0, fldMagic1, fldMagic2, fldMagic3, fldMagic4, fldCanvohuan, fldDays, fldBd } = body;

    if (!fldNid || fldIndex === undefined || !fldPid || !fldMoney) {
      return {
        success: false,
        message: 'Missing required fields: fldNid, fldIndex, fldPid, fldMoney'
      };
    }

    // Check if slot is already occupied
    const existingItem = await dbPublic
      .select()
      .from(tblXwwlSell)
      .where(and(
        eq(tblXwwlSell.fldNid, fldNid),
        eq(tblXwwlSell.fldIndex, fldIndex)
      ))
      .limit(1);

    if (existingItem.length > 0) {
      // Update existing template item
      await dbPublic
        .update(tblXwwlSell)
        .set({
          fldPid,
          fldMoney,
          fldMagic0: fldMagic0 || 0,
          fldMagic1: fldMagic1 || 0,
          fldMagic2: fldMagic2 || 0,
          fldMagic3: fldMagic3 || 0,
          fldMagic4: fldMagic4 || 0,
          fldCanvohuan: fldCanvohuan || 0,
          fldDays: fldDays || 0,
          fldBd: fldBd || 0
        })
        .where(and(
          eq(tblXwwlSell.fldNid, fldNid),
          eq(tblXwwlSell.fldIndex, fldIndex)
        ));
    } else {
      // Insert new template item
      await dbPublic
        .insert(tblXwwlSell)
        .values({
          fldNid,
          fldIndex,
          fldPid,
          fldMoney,
          fldMagic0: fldMagic0 || 0,
          fldMagic1: fldMagic1 || 0,
          fldMagic2: fldMagic2 || 0,
          fldMagic3: fldMagic3 || 0,
          fldMagic4: fldMagic4 || 0,
          fldCanvohuan: fldCanvohuan || 0,
          fldDays: fldDays || 0,
          fldBd: fldBd || 0
        });
    }

    return {
      success: true,
      message: 'Template item added successfully'
    };
  });
}

// Remove item from template
export async function DELETE(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { fldNid, fldIndex } = body;

    if (!fldNid || fldIndex === undefined) {
      return {
        success: false,
        message: 'Missing required fields: fldNid, fldIndex'
      };
    }

    await dbPublic
      .delete(tblXwwlSell)
      .where(and(
        eq(tblXwwlSell.fldNid, fldNid),
        eq(tblXwwlSell.fldIndex, fldIndex)
      ));

    return {
      success: true,
      message: 'Template item removed successfully'
    };
  });
}

// Update item in template
export async function PUT(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { fldNid, fldIndex, fldPid, fldMoney, fldMagic0, fldMagic1, fldMagic2, fldMagic3, fldMagic4, fldCanvohuan, fldDays, fldBd } = body;

    if (!fldNid || fldIndex === undefined) {
      return {
        success: false,
        message: 'Missing required fields: fldNid, fldIndex'
      };
    }

    const updateData: any = {};
    if (fldPid !== undefined) updateData.fldPid = fldPid;
    if (fldMoney !== undefined) updateData.fldMoney = fldMoney;
    if (fldMagic0 !== undefined) updateData.fldMagic0 = fldMagic0;
    if (fldMagic1 !== undefined) updateData.fldMagic1 = fldMagic1;
    if (fldMagic2 !== undefined) updateData.fldMagic2 = fldMagic2;
    if (fldMagic3 !== undefined) updateData.fldMagic3 = fldMagic3;
    if (fldMagic4 !== undefined) updateData.fldMagic4 = fldMagic4;
    if (fldCanvohuan !== undefined) updateData.fldCanvohuan = fldCanvohuan;
    if (fldDays !== undefined) updateData.fldDays = fldDays;
    if (fldBd !== undefined) updateData.fldBd = fldBd;

    await dbPublic
      .update(tblXwwlSell)
      .set(updateData)
      .where(and(
        eq(tblXwwlSell.fldNid, fldNid),
        eq(tblXwwlSell.fldIndex, fldIndex)
      ));

    return {
      success: true,
      message: 'Template item updated successfully'
    };
  });
}
