import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlItem } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { like, and, gte, lte, eq, or } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Filters
    const name = searchParams.get('name');
    const fldPid = searchParams.get('fldPid');
    const fldReside1 = searchParams.get('fldReside1');
    const fldReside2 = searchParams.get('fldReside2');
    const minLevel = searchParams.get('minLevel');
    const maxLevel = searchParams.get('maxLevel');
    const job = searchParams.get('job');
    const minJobLevel = searchParams.get('minJobLevel');
    const maxJobLevel = searchParams.get('maxJobLevel');
    const fldType = searchParams.get('fldType');

    const query = dbPublic.select().from(tblXwwlItem);
    const conditions = [];

    // Apply filters
    if (name) {
      conditions.push(like(tblXwwlItem.fldName, `%${name}%`));
    }

    if (fldPid) {
      conditions.push(eq(tblXwwlItem.fldPid, parseInt(fldPid)));
    }

    if (fldReside1) {
      conditions.push(eq(tblXwwlItem.fldReside1, parseInt(fldReside1)));
    }

    if (fldReside2) {
      conditions.push(eq(tblXwwlItem.fldReside2, parseInt(fldReside2)));
    }

    if (minLevel) {
      conditions.push(gte(tblXwwlItem.fldLevel, parseInt(minLevel)));
    }

    if (maxLevel) {
      conditions.push(lte(tblXwwlItem.fldLevel, parseInt(maxLevel)));
    }

    if (job) {
      conditions.push(
        or(
          eq(tblXwwlItem.fldReside1, parseInt(job)),
          eq(tblXwwlItem.fldReside2, parseInt(job))
        )
      );
    }

    if (minJobLevel) {
      conditions.push(gte(tblXwwlItem.fldJobLevel, parseInt(minJobLevel)));
    }

    if (maxJobLevel) {
      conditions.push(lte(tblXwwlItem.fldJobLevel, parseInt(maxJobLevel)));
    }

    if (fldType) {
      conditions.push(eq(tblXwwlItem.fldType, parseInt(fldType)));
    }

    query.where(and(...conditions));

    // Get items with pagination
    const items = await query
      .limit(limit)
      .offset(offset)
      .orderBy(tblXwwlItem.fldName);

    // Get total count for pagination
    let countQuery = dbPublic.select({ count: tblXwwlItem.fldPid }).from(tblXwwlItem);
    if (conditions.length > 0) {
      countQuery.where(and(...conditions));
    }
    
    const totalResult = await countQuery;
    const total = totalResult.length;

    return {
      success: true,
      message: 'Template items loaded successfully',
      data: {
        items,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  });
}
