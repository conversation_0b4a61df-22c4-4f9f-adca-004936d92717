import { NextRequest, NextResponse } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMonsterSetBase } from '@/../drizzle/schema';
import { eq } from 'drizzle-orm';

// Update monster setbase
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ mapId: string; monsterId: string }> }
) {
  try {
    const { mapId, monsterId } = await params;
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['fldPid', 'fldX', 'fldY'];
    for (const field of requiredFields) {
      if (body[field] === undefined || body[field] === null) {
        return NextResponse.json(
          { 
            success: false, 
            message: `Missing required field: ${field}` 
          },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData = {
      fldPid: body.fldPid,
      fldMid: parseInt(mapId),
      fldX: body.fldX,
      fldY: body.fldY,
      fldZ: body.fldZ || 15,
      fldFace0: body.fldFace0 || 0,
      fldFace: body.fldFace || 0,
      fldHp: body.fldHp || 0,
      fldAt: body.fldAt || 0,
      fldDf: body.fldDf || 0,
      fldNpc: body.fldNpc || (body.fldPid < 10000 ? 1 : 0),
      fldNewtime: body.fldNewtime || 5,
      fldLevel: body.fldLevel || 1,
      fldExp: body.fldExp || 0,
      fldAuto: body.fldAuto || 0,
      fldBoss: body.fldBoss || 0,
      fldGold: body.fldGold || 0,
      fldAccuracy: body.fldAccuracy || 0,
      fldEvasion: body.fldEvasion || 0,
      fldQitemdrop: body.fldQitemdrop || 0,
      fldQdroppp: body.fldQdroppp || 0,
      fldFreedrop: body.fldFreedrop || 0,
      fldAmount: body.fldAmount || 1,
      fldAoe: body.fldAoe || 0,
      fldActive: body.fldActive !== undefined ? body.fldActive : 1
    };

    // Update monster setbase
    const result = await dbPublic
      .update(tblXwwlMonsterSetBase)
      .set(updateData)
      .where(eq(tblXwwlMonsterSetBase.fldIndex, parseInt(monsterId)))
      .returning();

    if (result.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Monster not found' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Monster updated successfully',
      data: result[0]
    });

  } catch (error) {
    console.error('Error updating monster:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to update monster',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Delete monster setbase
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ mapId: string; monsterId: string }> }
) {
  try {
    const { monsterId } = await params;

    // Delete monster setbase
    const result = await dbPublic
      .delete(tblXwwlMonsterSetBase)
      .where(eq(tblXwwlMonsterSetBase.fldIndex, parseInt(monsterId)))
      .returning();

    if (result.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Monster not found' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Monster deleted successfully',
      data: result[0]
    });

  } catch (error) {
    console.error('Error deleting monster:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to delete monster',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
