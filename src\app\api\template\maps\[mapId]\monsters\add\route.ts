import { NextRequest, NextResponse } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMonsterSetBase } from '@/../drizzle/schema';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ mapId: string }> }
) {
  try {
    const { mapId } = await params;
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['fldPid', 'fldX', 'fldY'];
    for (const field of requiredFields) {
      if (body[field] === undefined || body[field] === null) {
        return NextResponse.json(
          { 
            success: false, 
            message: `Missing required field: ${field}` 
          },
          { status: 400 }
        );
      }
    }

    // Prepare monster setbase data
    const monsterData = {
      fldPid: body.fldPid,
      fldMid: parseInt(mapId),
      fldX: body.fldX,
      fldY: body.fldY,
      fldZ: body.fldZ || 15,
      fldFace0: body.fldFace0 || 0,
      fldFace: body.fldFace || 0,
      fldHp: body.fldHp || 0,
      fldAt: body.fldAt || 0,
      fldDf: body.fldDf || 0,
      fldNpc: body.fldNpc || (body.fldPid < 10000 ? 1 : 0),
      fldNewtime: body.fldNewtime || 5,
      fldLevel: body.fldLevel || 1,
      fldExp: body.fldExp || 0,
      fldAuto: body.fldAuto || 0,
      fldBoss: body.fldBoss || 0,
      fldGold: body.fldGold || 0,
      fldAccuracy: body.fldAccuracy || 0,
      fldEvasion: body.fldEvasion || 0,
      fldQitemdrop: body.fldQitemdrop || 0,
      fldQdroppp: body.fldQdroppp || 0,
      fldFreedrop: body.fldFreedrop || 0,
      fldAmount: body.fldAmount || 1,
      fldAoe: body.fldAoe || 0,
      fldActive: body.fldActive !== undefined ? body.fldActive : 1
    };

    // Insert into monster setbase
    const result = await dbPublic
      .insert(tblXwwlMonsterSetBase)
      .values(monsterData)
      .returning();

    return NextResponse.json({
      success: true,
      message: 'Monster added successfully',
      data: result[0]
    });

  } catch (error) {
    console.error('Error adding monster:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to add monster',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
