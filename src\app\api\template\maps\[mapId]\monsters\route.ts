import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMonster, tblXwwlMonsterSetBase } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { eq } from 'drizzle-orm';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ mapId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const mapId = parseInt(resolvedParams.mapId);

    if (!mapId) {
      return {
        success: false,
        message: 'Invalid Map ID'
      };
    }

    // Get monster setbase data for this map
    const monsterSetbase = await dbPublic
      .select()
      .from(tblXwwlMonsterSetBase)
      .where(eq(tblXwwlMonsterSetBase.fldMid, mapId))
      .orderBy(tblXwwlMonsterSetBase.fldPid);

    // Get all unique NPC IDs from monster setbase
    // Use fldPid (the actual entity ID) instead of fldNpc (which seems to be a flag)
    const allNpcIds = [...new Set(monsterSetbase
      .filter(entity => entity.fldPid)
      .map(entity => entity.fldPid!))];



    let npcData: any[] = [];
    if (allNpcIds.length > 0) {
      // Query all NPCs at once
      const allNpcData = [];
      for (const npcId of allNpcIds) {
        const npcInfo = await dbPublic
          .select()
          .from(tblXwwlMonster)
          .where(eq(tblXwwlMonster.fldPid, npcId))
          .limit(1);
        if (npcInfo.length > 0) {
          allNpcData.push(npcInfo[0]);
        }
      }
      npcData = allNpcData;
    }

    // Create a map of NPC ID to NPC data for quick lookup
    const npcMap = new Map();
    npcData.forEach(npc => {
      npcMap.set(npc.fldPid, npc);
    });

    // Enhance monster setbase data with NPC information
    const enhancedMonsters = monsterSetbase.map((monster) => {
      // Use fldPid to get NPC info (the actual entity ID)
      const npcInfo = npcMap.get(monster.fldPid);

      return {
        ...monster,
        // Always use NPC table data for name and level
        fldName: npcInfo?.fldName || `${monster.fldPid}`,
        fldLevel: npcInfo?.fldLevel || 1,
        fldJob: npcInfo?.fldJob || 0,
        fldHp: npcInfo?.fldHp || 0,
        fldMp: npcInfo?.fldMp || 0,
        fldAttack: npcInfo?.fldAttack || 0,
        fldDefense: npcInfo?.fldDefense || 0,
        fldType: monster.fldPid && monster.fldPid < 10000 ? 'npc' : 'monster',
        fldNid: monster.fldPid, // For compatibility with frontend (use actual entity ID)
        fldRange: monster.fldAoe || 0,
        fldNum: monster.fldAmount || 1,
        // Additional NPC info
        npcData: npcInfo || null
      };
    });

    return {
      success: true,
      message: 'Map monster data loaded successfully',
      data: {
        mapId,
        monsters: enhancedMonsters,
        totalEntities: enhancedMonsters.length,
        npcCount: enhancedMonsters.filter(e => e.fldType === 'npc').length,
        monsterCount: enhancedMonsters.filter(e => e.fldType === 'monster').length
      }
    };
  });
}
