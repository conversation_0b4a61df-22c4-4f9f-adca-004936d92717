import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMap } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { like, or, eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = (page - 1) * limit;

    let query = dbPublic.select().from(tblXwwlMap);
    const conditions = [];

    // Apply search filter
    if (search) {
      conditions.push(
        or(
          like(tblXwwlMap.fldName, `%${search}%`),
          eq(tblXwwlMap.fldMid, parseInt(search) || 0)
        )
      );
    }

    // Apply conditions if any
    if (conditions.length > 0) {
      query.where(conditions[0]);
    }

    // Get maps with pagination
    const maps = await query
      .limit(limit)
      .offset(offset)
      .orderBy(tblXwwlMap.fldMid);

    // Get total count for pagination (simplified approach)
    const totalResult = await dbPublic.select().from(tblXwwlMap);
    let filteredTotal = totalResult;
    
    // Apply same filters for counting
    if (search) {
      filteredTotal = filteredTotal.filter(map => 
        (map.fldName && map.fldName.toLowerCase().includes(search.toLowerCase())) ||
        (map.fldMid && map.fldMid.toString().includes(search))
      );
    }
    
    const total = filteredTotal.length;

    return {
      success: true,
      message: 'Maps loaded successfully',
      data: {
        maps,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  });
}
