import { NextRequest, NextResponse } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMonster } from '@/../drizzle/schema';
import { asc } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '1000');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get all monsters from tblXwwlMonster
    const monsters = await dbPublic
      .select()
      .from(tblXwwlMonster)
      .orderBy(asc(tblXwwlMonster.fldPid))
      .limit(limit)
      .offset(offset);

    // Map the data to expected format
    const formattedMonsters = monsters.map(monster => ({
      fldPid: monster.fldPid,
      fldName: monster.fldName,
      fldLevel: monster.fldLevel,
      fldHp: monster.fldHp,
      fldMp: 0, // Default value since not in schema
      fldAttack: monster.fldAt,
      fldDefense: monster.fldDf,
      fldJob: 0, // Default value since not in schema
      fldExp: monster.fldExp,
      fldAuto: monster.fldAuto,
      fldBoss: monster.fldBoss
    }));

    return NextResponse.json({
      success: true,
      message: 'Monsters loaded successfully',
      data: formattedMonsters,
      total: formattedMonsters.length
    });

  } catch (error) {
    console.error('Error loading monsters:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to load monsters',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
