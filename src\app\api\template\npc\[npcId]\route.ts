import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMonster, tblXwwlSell } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { eq } from 'drizzle-orm';
import { TemplateData, TemplatePage, TEMPLATE_SLOTS_PER_PAGE } from '@/types/template';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ npcId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const npcId = parseInt(resolvedParams.npcId);

    if (!npcId) {
      return {
        success: false,
        message: 'Invalid NPC ID'
      };
    }

    // Get NPC data
    const npc = await dbPublic
      .select()
      .from(tblXwwlMonster)
      .where(eq(tblXwwlMonster.fldPid, npcId))
      .limit(1);

    if (npc.length === 0) {
      return {
        success: false,
        message: 'Template NPC not found'
      };
    }

    // Get template items for this NPC
    const templateItems = await dbPublic
      .select()
      .from(tblXwwlSell)
      .where(eq(tblXwwlSell.fldNid, npcId))
      .orderBy(tblXwwlSell.fldIndex);

    // Organize items into pages (1-based indexing)
    const pages: TemplatePage[] = [];
    const maxIndex = Math.max(...templateItems.map(item => item.fldIndex || 1), 1);
    const totalPages = Math.ceil(maxIndex / TEMPLATE_SLOTS_PER_PAGE) || 1;

    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      const startIndex = (pageNum - 1) * TEMPLATE_SLOTS_PER_PAGE + 1; // 1-based start
      const endIndex = pageNum * TEMPLATE_SLOTS_PER_PAGE; // 1-based end

      // Create array of 60 slots (null for empty slots)
      const pageItems = new Array(TEMPLATE_SLOTS_PER_PAGE).fill(null);

      // Fill in the actual items (convert 1-based DB index to 0-based array index)
      templateItems.forEach(item => {
        if (item.fldIndex !== null && item.fldIndex >= startIndex && item.fldIndex <= endIndex) {
          const slotIndex = item.fldIndex - startIndex; // Convert to 0-based array index
          pageItems[slotIndex] = item;
        }
      });

      pages.push({
        pageNumber: pageNum,
        items: pageItems
      });
    }

    const templateData: TemplateData = {
      npc: npc[0] as any,
      pages: pages as any,
      totalPages
    };

    return {
      success: true,
      message: 'Template data loaded successfully',
      data: templateData
    };
  });
}
