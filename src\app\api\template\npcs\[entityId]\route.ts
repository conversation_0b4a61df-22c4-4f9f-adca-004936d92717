import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMonster } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { eq } from 'drizzle-orm';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ entityId: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const entityId = parseInt(resolvedParams.entityId);

    if (!entityId) {
      return {
        success: false,
        message: 'Invalid entity ID'
      };
    }

    try {
      // Get NPC data
      const npc = await dbPublic
        .select()
        .from(tblXwwlMonster)
        .where(eq(tblXwwlMonster.fldPid, entityId))
        .limit(1);

      if (npc.length === 0) {
        return {
          success: false,
          message: 'NPC not found'
        };
      }

      return {
        success: true,
        data: npc[0]
      };
    } catch (error) {
      console.error('Error loading entity:', error);
      return {
        success: false,
        message: 'Failed to load entity'
      };
    }
  });
}
