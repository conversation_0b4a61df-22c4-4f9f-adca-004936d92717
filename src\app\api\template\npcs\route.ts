import { NextRequest } from 'next/server';
import { dbPublic } from '@/lib/db-public';
import { tblXwwlMonster } from '@/../drizzle/schema';
import { handleApiRoute } from '@/lib/proxy-utils';
import { like, or, eq, lt, and, gte, lte } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const maxId = parseInt(searchParams.get('maxId') || '999999');
    const minLevel = searchParams.get('minLevel');
    const maxLevel = searchParams.get('maxLevel');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    const query = dbPublic.select().from(tblXwwlMonster);
    const conditions = [];

    // Apply maxId filter (for shop NPCs with ID < 10000)
    if (maxId < 999999) {
      conditions.push(lt(tblXwwlMonster.fldPid, maxId));
    }

    // Apply search filter
    if (search) {
      conditions.push(
        or(
          like(tblXwwlMonster.fldName, `%${search}%`),
          eq(tblXwwlMonster.fldPid, parseInt(search) || 0)
        )
      );
    }

    // Apply level filters
    if (minLevel) {
      conditions.push(gte(tblXwwlMonster.fldLevel, parseInt(minLevel)));
    }

    if (maxLevel) {
      conditions.push(lte(tblXwwlMonster.fldLevel, parseInt(maxLevel)));
    }

    query.where(and(...conditions));

    // Add pagination
    const npcs = await query.limit(limit).offset(offset);

    // Get total count for pagination (simplified approach)
    const totalResult = await dbPublic.select().from(tblXwwlMonster).orderBy(tblXwwlMonster.fldPid);
    let filteredTotal = totalResult;

    // Apply same filters for counting
    if (maxId < 999999) {
      filteredTotal = filteredTotal.filter(npc => npc.fldPid && npc.fldPid < maxId);
    }
    if (search) {
      filteredTotal = filteredTotal.filter(npc =>
        (npc.fldName && npc.fldName.toLowerCase().includes(search.toLowerCase())) ||
        (npc.fldPid && npc.fldPid.toString().includes(search))
      );
    }

    const total = filteredTotal.length;

    return {
      success: true,
      message: 'Template NPCs loaded successfully',
      data: {
        npcs,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  });
}

// Add new NPC
export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();

    if (!body.fldPid || !body.fldName) {
      return {
        success: false,
        message: 'Missing required fields: fldPid, fldName'
      };
    }

    try {
      // Check if NPC already exists
      const existingNpc = await dbPublic
        .select()
        .from(tblXwwlMonster)
        .where(eq(tblXwwlMonster.fldPid, body.fldPid))
        .limit(1);

      if (existingNpc.length > 0) {
        return {
          success: false,
          message: 'NPC with this ID already exists'
        };
      }

      await dbPublic.insert(tblXwwlMonster).values({
        fldPid: body.fldPid,
        fldName: body.fldName,
        fldLevel: body.fldLevel || 1,
        fldHp: body.fldHp || 100,
        fldAt: body.fldAt || 10,
        fldDf: body.fldDf || 5,
        fldExp: body.fldExp || 10,
        fldBoss: body.fldBoss || 0,
        fldAuto: body.fldAuto || 0,
        fldNpc: body.fldNpc || 0,
        fldQuest: body.fldQuest || 0,
        fldQuestid: body.fldQuestid || 0,
        fldStages: body.fldStages || 0,
        fldQuestitem: body.fldQuestitem || 0,
        fldPp: body.fldPp || 0
      });

      return {
        success: true,
        message: 'NPC added successfully'
      };
    } catch (error) {
      console.error('Error adding entity:', error);
      return {
        success: false,
        message: 'Failed to add NPC'
      };
    }
  });
}

// Update NPC
export async function PUT(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { originalPid, ...updateData } = body;

    if (!originalPid) {
      return {
        success: false,
        message: 'Missing original entity ID'
      };
    }

    try {
      // Check if NPC exists
      const existingNpc = await dbPublic
        .select()
        .from(tblXwwlMonster)
        .where(eq(tblXwwlMonster.fldPid, originalPid))
        .limit(1);

      if (existingNpc.length === 0) {
        return {
          success: false,
          message: 'NPC not found'
        };
      }

      await dbPublic
        .update(tblXwwlMonster)
        .set(updateData)
        .where(eq(tblXwwlMonster.fldPid, originalPid));

      return {
        success: true,
        message: 'NPC updated successfully'
      };
    } catch (error) {
      console.error('Error updating entity:', error);
      return {
        success: false,
        message: 'Failed to update NPC'
      };
    }
  });
}

// Delete NPC
export async function DELETE(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    const { fldPid } = body;

    if (!fldPid) {
      return {
        success: false,
        message: 'Missing required field: fldPid'
      };
    }

    try {
      await dbPublic
        .delete(tblXwwlMonster)
        .where(eq(tblXwwlMonster.fldPid, fldPid));

      return {
        success: true,
        message: 'NPC deleted successfully'
      };
    } catch (error) {
      console.error('Error deleting entity:', error);
      return {
        success: false,
        message: 'Failed to delete NPC'
      };
    }
  });
}
