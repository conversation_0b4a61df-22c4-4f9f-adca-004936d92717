import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblPill } from '@/../drizzle/public/schema';
import { eq } from 'drizzle-orm';

// GET /api/pills/[id] - Get single pill by ID
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    if (!id) {
      return {
        success: false,
        message: 'Invalid pill ID'
      };
    }

    const pill = await dbPublic
      .select()
      .from(tblPill)
      .where(eq(tblPill.id, id))
      .limit(1);

    if (pill.length === 0) {
      return {
        success: false,
        message: 'Pill not found'
      };
    }

    return {
      success: true,
      message: 'Pill loaded successfully',
      data: pill[0]
    };
  });
}

// PUT /api/pills/[id] - Update pill
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    if (!id) {
      return {
        success: false,
        message: 'Invalid pill ID'
      };
    }

    const body = await request.json();
    
    const {
      pillId,
      pillName,
      levelUse,
      bonusHp,
      bonusHppercent,
      bonusMp,
      bonusMppercent,
      bonusAtk,
      bonusAtkpercent,
      bonusDf,
      bonusDfpercent,
      bonusEvasion,
      bonusEvapercent,
      bonusAccuracy,
      bonusAccupercent,
      bonusAtkskillpercent,
      bonusDfskill,
      bonusDfskillpercent,
      bonusAbilities,
      bonusLucky,
      bonusGoldpercent,
      bonusDroppercent,
      bonusExppercent,
      upgradeWeapon,
      upgradeArmor,
      pillTime,
      pillDays,
      publicPill,
      pillMerge,
      cantUse,
      onOff,
      hatchItem,
      bonusDiemhoangkim,
      tanghoa
    } = body;

    // Check if pill exists
    const existingPill = await dbPublic
      .select()
      .from(tblPill)
      .where(eq(tblPill.id, id))
      .limit(1);

    if (existingPill.length === 0) {
      return {
        success: false,
        message: 'Pill not found'
      };
    }

    // If pillId is being changed, check for conflicts
    if (pillId && pillId !== existingPill[0].pillId) {
      const conflictPill = await dbPublic
        .select()
        .from(tblPill)
        .where(eq(tblPill.pillId, pillId))
        .limit(1);

      if (conflictPill.length > 0) {
        return {
          success: false,
          message: 'Another pill with this pillId already exists'
        };
      }
    }

    // Update pill
    await dbPublic
      .update(tblPill)
      .set({
        ...(pillId !== undefined && { pillId }),
        ...(pillName !== undefined && { pillName }),
        ...(levelUse !== undefined && { levelUse }),
        ...(bonusHp !== undefined && { bonusHp }),
        ...(bonusHppercent !== undefined && { bonusHppercent }),
        ...(bonusMp !== undefined && { bonusMp }),
        ...(bonusMppercent !== undefined && { bonusMppercent }),
        ...(bonusAtk !== undefined && { bonusAtk }),
        ...(bonusAtkpercent !== undefined && { bonusAtkpercent }),
        ...(bonusDf !== undefined && { bonusDf }),
        ...(bonusDfpercent !== undefined && { bonusDfpercent }),
        ...(bonusEvasion !== undefined && { bonusEvasion }),
        ...(bonusEvapercent !== undefined && { bonusEvapercent }),
        ...(bonusAccuracy !== undefined && { bonusAccuracy }),
        ...(bonusAccupercent !== undefined && { bonusAccupercent }),
        ...(bonusAtkskillpercent !== undefined && { bonusAtkskillpercent }),
        ...(bonusDfskill !== undefined && { bonusDfskill }),
        ...(bonusDfskillpercent !== undefined && { bonusDfskillpercent }),
        ...(bonusAbilities !== undefined && { bonusAbilities }),
        ...(bonusLucky !== undefined && { bonusLucky }),
        ...(bonusGoldpercent !== undefined && { bonusGoldpercent }),
        ...(bonusDroppercent !== undefined && { bonusDroppercent }),
        ...(bonusExppercent !== undefined && { bonusExppercent }),
        ...(upgradeWeapon !== undefined && { upgradeWeapon }),
        ...(upgradeArmor !== undefined && { upgradeArmor }),
        ...(pillTime !== undefined && { pillTime }),
        ...(pillDays !== undefined && { pillDays }),
        ...(publicPill !== undefined && { publicPill }),
        ...(pillMerge !== undefined && { pillMerge }),
        ...(cantUse !== undefined && { cantUse }),
        ...(onOff !== undefined && { onOff }),
        ...(hatchItem !== undefined && { hatchItem }),
        ...(bonusDiemhoangkim !== undefined && { bonusDiemhoangkim }),
        ...(tanghoa !== undefined && { tanghoa })
      })
      .where(eq(tblPill.id, id));

    return {
      success: true,
      message: 'Pill updated successfully'
    };
  });
}

// DELETE /api/pills/[id] - Delete pill
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return handleApiRoute(async () => {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    if (!id) {
      return {
        success: false,
        message: 'Invalid pill ID'
      };
    }

    // Check if pill exists
    const existingPill = await dbPublic
      .select()
      .from(tblPill)
      .where(eq(tblPill.id, id))
      .limit(1);

    if (existingPill.length === 0) {
      return {
        success: false,
        message: 'Pill not found'
      };
    }

    // Delete pill
    await dbPublic
      .delete(tblPill)
      .where(eq(tblPill.id, id));

    return {
      success: true,
      message: 'Pill deleted successfully'
    };
  });
}
