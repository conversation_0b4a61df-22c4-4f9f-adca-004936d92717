import { NextRequest } from 'next/server';
import { handleApiRoute } from '@/lib/proxy-utils';
import { dbPublic } from '@/lib/db-public';
import { tblPill } from '@/../drizzle/public/schema';
import { eq, and, or, like, desc, asc } from 'drizzle-orm';

// GET /api/pills - List pills with filtering, pagination, and sorting
export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const { searchParams } = new URL(request.url);
    
    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Filter parameters
    const search = searchParams.get('search') || '';
    const pillId = searchParams.get('pillId');
    const levelUse = searchParams.get('levelUse');
    const onOff = searchParams.get('onOff');
    const publicPill = searchParams.get('publicPill');
    
    // Sorting parameters
    const sortBy = searchParams.get('sortBy') || 'id';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Build where conditions
    const whereConditions = [];

    if (search) {
      whereConditions.push(
        or(
          like(tblPill.pillName, `%${search}%`),
          eq(tblPill.pillId, parseInt(search) || -1)
        )
      );
    }

    if (pillId) {
      whereConditions.push(eq(tblPill.pillId, parseInt(pillId)));
    }

    if (levelUse) {
      whereConditions.push(like(tblPill.levelUse, `%${levelUse}%`));
    }

    if (onOff !== null && onOff !== undefined && onOff !== '') {
      whereConditions.push(eq(tblPill.onOff, parseInt(onOff)));
    }

    if (publicPill !== null && publicPill !== undefined && publicPill !== '') {
      whereConditions.push(eq(tblPill.publicPill, parseInt(publicPill)));
    }

    // Build order by clause
    const orderByColumn = tblPill.id;
    const orderBy = sortOrder === 'desc' ? desc(orderByColumn) : asc(orderByColumn);

    // Get total count for pagination
    let totalCountQuery = dbPublic
      .select()
      .from(tblPill);

    if (whereConditions.length > 0) {
       totalCountQuery.where(and(...whereConditions));
    }

    const totalResult = await totalCountQuery;
    const total = totalResult.length;

    // Get pills data
    let query = dbPublic
      .select()
      .from(tblPill)
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    if (whereConditions.length > 0) {
      query.where(and(...whereConditions));
    }

    const pills = await query;

    return {
      success: true,
      message: 'Pills loaded successfully',
      data: {
        pills,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    };
  });
}

// POST /api/pills - Create new pill
export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    
    const {
      pillId,
      pillName,
      levelUse,
      bonusHp = 0,
      bonusHppercent = 0,
      bonusMp = 0,
      bonusMppercent = 0,
      bonusAtk = 0,
      bonusAtkpercent = 0,
      bonusDf = 0,
      bonusDfpercent = 0,
      bonusEvasion = 0,
      bonusEvapercent = 0,
      bonusAccuracy = 0,
      bonusAccupercent = 0,
      bonusAtkskillpercent = 0,
      bonusDfskill = 0,
      bonusDfskillpercent = 0,
      bonusAbilities = 0,
      bonusLucky = 0,
      bonusGoldpercent = 0,
      bonusDroppercent = 0,
      bonusExppercent = 0,
      upgradeWeapon = 0,
      upgradeArmor = 0,
      pillTime = 0,
      pillDays = 0,
      publicPill = 0,
      pillMerge = 0,
      cantUse,
      onOff = 1,
      hatchItem = 0,
      bonusDiemhoangkim = 0,
      tanghoa = 0
    } = body;

    if (!pillId || pillId <= 0) {
      return {
        success: false,
        message: 'pillId is required and must be greater than 0'
      };
    }

    // Check if pill with this ID already exists
    const existingPill = await dbPublic
      .select()
      .from(tblPill)
      .where(eq(tblPill.pillId, pillId))
      .limit(1);

    if (existingPill.length > 0) {
      return {
        success: false,
        message: 'Pill with this ID already exists'
      };
    }

    // Insert new pill
    await dbPublic.insert(tblPill).values({
      pillId,
      pillName,
      levelUse,
      bonusHp,
      bonusHppercent,
      bonusMp,
      bonusMppercent,
      bonusAtk,
      bonusAtkpercent,
      bonusDf,
      bonusDfpercent,
      bonusEvasion,
      bonusEvapercent,
      bonusAccuracy,
      bonusAccupercent,
      bonusAtkskillpercent,
      bonusDfskill,
      bonusDfskillpercent,
      bonusAbilities,
      bonusLucky,
      bonusGoldpercent,
      bonusDroppercent,
      bonusExppercent,
      upgradeWeapon,
      upgradeArmor,
      pillTime,
      pillDays,
      publicPill,
      pillMerge,
      cantUse,
      onOff,
      hatchItem,
      bonusDiemhoangkim,
      tanghoa
    });

    return {
      success: true,
      message: 'Pill created successfully'
    };
  });
}
