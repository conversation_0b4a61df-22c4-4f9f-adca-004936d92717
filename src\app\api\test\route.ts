import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // const res = await auth.api.signUpEmail({
    //   email: "<EMAIL>",
    //   password: "admin123",
    //   name: "Administrator",
    //   isActive: true,
    //   roles: ["admin"]
    // });
    // const res = await auth.api.signUpEmail({
    //     body: {
    //         email: "<EMAIL>",
    //         password: "admin123",
    //         name: "Administrator",
    //         isActive: true,
    //     }
    // })
    // console.log({res});

    return NextResponse.json({ message: "API test route" });
  } catch (error) {
    console.error({error});
    return NextResponse.json({ message: "Error" });
  }
}
