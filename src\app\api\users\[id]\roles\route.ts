import { NextRequest, NextResponse } from 'next/server';
import { assignUserRole, removeUserRole } from '@/lib/actions/role-actions';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = (await params).id;
  try {
    const body = await request.json();
    const { roleId } = body;

    if (!roleId) {
      return NextResponse.json(
        { success: false, message: 'Role ID is required' },
        { status: 400 }
      );
    }

    const result = await assignUserRole(userId, roleId);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error assigning role:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to assign role' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = (await params).id;
  try {
    const { searchParams } = new URL(request.url);
    const roleId = searchParams.get('roleId');

    if (!roleId) {
      return NextResponse.json(
        { success: false, message: 'Role ID is required' },
        { status: 400 }
      );
    }

    const result = await removeUserRole(userId, roleId);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error removing role:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to remove role' },
      { status: 500 }
    );
  }
}
