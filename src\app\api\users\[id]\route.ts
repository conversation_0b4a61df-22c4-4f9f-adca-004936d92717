import { NextRequest, NextResponse } from 'next/server';
import { updateUser, deleteUser, toggleUserStatus, UpdateUserData } from '@/lib/actions/user-actions';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = (await params).id;
  try {
    const body = await request.json();
    const { name, email, password, isActive, roleIds } = body;

    const updateData: UpdateUserData = {};
    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (password !== undefined) updateData.password = password;
    if (isActive !== undefined) updateData.isActive = isActive;

    const result = await updateUser(userId, updateData);

    if (!result.success) {
      return NextResponse.json(result, { status: 400 });
    }

    // Update roles if provided
    if (roleIds !== undefined) {
      const { getUserWithRoles } = await import('@/lib/auth-utils');
      const { assignUserRole, removeUserRole } = await import('@/lib/actions/role-actions');

      // Get current user roles
      const currentUser = await getUserWithRoles(userId);
      if (currentUser) {
        const currentRoleIds = currentUser.roles.map(r => r.role.id);

        // Find roles to add and remove
        const rolesToAdd = roleIds.filter((id: string) => !currentRoleIds.includes(id));
        const rolesToRemove = currentRoleIds.filter(id => !roleIds.includes(id));

        // Add new roles
        for (const roleId of rolesToAdd) {
          try {
            await assignUserRole(userId, roleId);
          } catch (error) {
            console.error(`Error assigning role ${roleId}:`, error);
          }
        }

        // Remove old roles
        for (const roleId of rolesToRemove) {
          try {
            await removeUserRole(userId, roleId);
          } catch (error) {
            console.error(`Error removing role ${roleId}:`, error);
          }
        }
      }
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = (await params).id;
  try {
    const result = await deleteUser(userId);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete user' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const userId = (await params).id;
  try {
    const result = await toggleUserStatus(userId);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error toggling user status:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to toggle user status' },
      { status: 500 }
    );
  }
}
