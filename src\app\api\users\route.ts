import { NextRequest, NextResponse } from 'next/server';
import { getUsersWithRoles } from '@/lib/actions/user-actions';
import { auth } from '@/lib/auth';

export async function GET() {
  try {
    const users = await getUsersWithRoles();
    return NextResponse.json({ success: true, data: users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, password, isActive } = body;

    if (!name || !email || !password) {
      return NextResponse.json(
        { success: false, message: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    const result = await auth.api.signUpEmail({
      body: {
        name,
        email,
        password,
        isActive: isActive ?? true,
      }
    });

    if (result.token) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create user' },
      { status: 500 }
    );
  }
}
