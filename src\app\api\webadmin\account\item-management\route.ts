import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';

export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    // Parse request body
    const body = await request.json();
    
    const endpoint = '/api/webadmin/account/item-management';
    console.log(JSON.stringify(body));
    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'POST',
        body: JSON.stringify(body),
        headers: {
          'Content-Type': 'application/json',
        },
        requiredPermission: 'character:update'
      }
    );

    return result;
  });
}
