import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest, parseQueryParams } from '@/lib/proxy-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ characterName: string }> }
) {
  return handleApiRoute(async () => {
    const { characterName } = await params;
    const searchParams = parseQueryParams(request);
    
    const serverId = searchParams.get('serverId');
    const clusterId = searchParams.get('clusterId');
    
    // Validate required parameters
    if (!serverId) {
      throw new Error('serverId is required');
    }
    
    if (!clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/account/player/${encodeURIComponent(characterName)}${searchParams.toString() ? `?${searchParams}` : ''}`;

    // Proxy request to HeroLogin server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'character:read'
      }
    );

    return result;
  });
}
