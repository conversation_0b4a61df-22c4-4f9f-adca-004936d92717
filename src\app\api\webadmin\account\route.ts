import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest, parseQueryParams } from '@/lib/proxy-utils';

export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const searchParams = parseQueryParams(request);
    
    // Build query string for proxy request
    const queryString = new URLSearchParams();
    
    const page = searchParams.get('page');
    if (page) {
      queryString.append('page', page);
    }
    
    const pageSize = searchParams.get('pageSize');
    if (pageSize) {
      queryString.append('pageSize', pageSize);
    }
    
    const searchTerm = searchParams.get('searchTerm');
    if (searchTerm) {
      queryString.append('searchTerm', searchTerm);
    }
    
    const serverId = searchParams.get('serverId');
    if (serverId) {
      queryString.append('serverId', serverId);
    }
    
    const clusterId = searchParams.get('clusterId');
    if (clusterId) {
      queryString.append('clusterId', clusterId);
    }
    
    const onlineOnly = searchParams.get('onlineOnly');
    if (onlineOnly) {
      queryString.append('onlineOnly', onlineOnly);
    }
    
    const sortBy = searchParams.get('sortBy');
    if (sortBy) {
      queryString.append('sortBy', sortBy);
    }
    
    const sortOrder = searchParams.get('sortOrder');
    if (sortOrder) {
      queryString.append('sortOrder', sortOrder);
    }
    
    const includePartyInfo = searchParams.get('includePartyInfo');
    if (includePartyInfo) {
      queryString.append('includePartyInfo', includePartyInfo);
    }

    const endpoint = `/api/webadmin/account${queryString.toString() ? `?${queryString}` : ''}`;

    // Proxy request to game server
    const result = await makeProxyRequest(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'character:read'
      }
    );

    return result;
  });
}
