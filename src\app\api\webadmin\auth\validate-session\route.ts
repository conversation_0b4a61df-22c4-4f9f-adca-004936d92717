import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { SessionValidationRequest, SessionValidationResponse } from '@/types/gameserver';

export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();
    if (!body) {
      throw new Error('Invalid request body');
    }
    
    const requestData: SessionValidationRequest = {
      sessionToken: body.sessionToken,
      requiredPermission: body.requiredPermission,
      clientIP: body.clientIP
    };

    // Proxy request to game server
    const result = await makeProxyRequest<SessionValidationResponse>(
      '/api/webadmin/auth/validate-session',
      {
        method: 'POST',
        body: requestData
      }
    );

    return result;
  });
}
