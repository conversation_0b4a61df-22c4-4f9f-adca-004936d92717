import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { ReloadServerConfigRequest, ReloadServerConfigResponse } from '@/types/gameserver';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: ReloadServerConfigRequest = {
      serverId,
      category: body.category,
      restartIfNeeded: body.restartIfNeeded
    };

    const endpoint = `/api/webadmin/gameserver/${serverId}/config/reload`;

    // Proxy request to game server
    const result = await makeProxyRequest<ReloadServerConfigResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'server:config'
      }
    );

    return result;
  });
}
