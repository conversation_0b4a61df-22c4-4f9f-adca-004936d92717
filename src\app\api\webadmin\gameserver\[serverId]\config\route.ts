import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest, parseQueryParams } from '@/lib/proxy-utils';
import { GetServerConfigRequest, GetServerConfigResponse, UpdateServerConfigRequest, UpdateServerConfigResponse } from '@/types/gameserver';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const searchParams = parseQueryParams(request);
    
    const requestData: GetServerConfigRequest = {
      serverId
    };
    
    const category = searchParams.get('category');
    if (category) {
      requestData.category = category;
    }
    
    const configKey = searchParams.get('configKey');
    if (configKey) {
      requestData.configKey = configKey;
    }

    // Build query string for proxy request
    const queryString = new URLSearchParams();
    if (requestData.category) {
      queryString.append('category', requestData.category);
    }
    if (requestData.configKey) {
      queryString.append('configKey', requestData.configKey);
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/config${queryString.toString() ? `?${queryString}` : ''}`;

    // Proxy request to game server
    const result = await makeProxyRequest<GetServerConfigResponse>(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'server:config'
      }
    );

    return result;
  });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: UpdateServerConfigRequest = {
      serverId,
      configs: body.configUpdates,
    };

    if (!requestData.configs || Object.keys(requestData.configs).length === 0) {
      throw new Error('configs is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/config`;

    // Proxy request to game server
    const result = await makeProxyRequest<UpdateServerConfigResponse>(
      endpoint,
      {
        method: 'PUT',
        body: requestData,
        requiredPermission: 'server:config'
      }
    );

    return result;
  });
}
