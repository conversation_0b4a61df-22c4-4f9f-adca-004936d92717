import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { RestartGameServerRequest, RestartGameServerResponse } from '@/types/gameserver';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: RestartGameServerRequest = {
      serverId,
      clusterId: body.clusterId,
      graceful: body.graceful,
      // timeoutSeconds: body.timeoutSeconds
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/restart`;

    // Proxy request to game server
    const result = await makeProxyRequest<RestartGameServerResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'server:restart'
      }
    );

    return result;
  });
}
