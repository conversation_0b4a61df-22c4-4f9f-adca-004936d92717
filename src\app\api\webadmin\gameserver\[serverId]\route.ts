import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest, parseQueryParams } from '@/lib/proxy-utils';
import { GetServerStatusRequest, GetServerStatusResponse } from '@/types/gameserver';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const searchParams = parseQueryParams(request);
    
    const clusterId = searchParams.get('clusterId');
    if (!clusterId) {
      throw new Error('clusterId is required');
    }

    const requestData: GetServerStatusRequest = {
      serverId,
      clusterId: parseInt(clusterId)
    };

    // Build query string for proxy request
    const queryString = new URLSearchParams();
    queryString.append('clusterId', requestData.clusterId.toString());

    const endpoint = `/api/webadmin/gameserver/${serverId}?${queryString}`;

    // Proxy request to game server
    const result = await makeProxyRequest<GetServerStatusResponse>(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'server:read'
      }
    );

    return result;
  });
}
