import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { StartGameServerRequest, StartGameServerResponse } from '@/types/gameserver';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: StartGameServerRequest = {
      serverId,
      clusterId: body.clusterId,
      configOverrides: body.configOverrides
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/start`;

    // Proxy request to game server
    const result = await makeProxyRequest<StartGameServerResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'server:start'
      }
    );

    return result;
  });
}
