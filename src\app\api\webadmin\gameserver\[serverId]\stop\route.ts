import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { StopGameServerRequest, StopGameServerResponse } from '@/types/gameserver';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  const serverId = parseInt((await params).serverId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: StopGameServerRequest = {
      serverId,
      clusterId: body.clusterId,
      graceful: body.graceful,
      timeoutSeconds: body.timeoutSeconds
    };

    if (!requestData.clusterId) {
      throw new Error('clusterId is required');
    }

    const endpoint = `/api/webadmin/gameserver/${serverId}/stop`;

    // Proxy request to game server
    const result = await makeProxyRequest<StopGameServerResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'server:stop'
      }
    );

    return result;
  });
}
