import { NextRequest, NextResponse } from 'next/server';
import { validateSessionAndPermissions, parseQueryParams } from '@/lib/proxy-utils';
import { baseUrl } from '@/app/constant';

export async function GET(request: NextRequest) {
  try {
    // Validate session first
    const validation = await validateSessionAndPermissions('metrics:read');
    if (!validation.success) {
      return NextResponse.json(
        { success: false, message: validation.message || 'Authentication failed' },
        { status: 401 }
      );
    }

    const searchParams = parseQueryParams(request);

    const serverId = searchParams.get('serverId');
    if (!serverId) {
      return NextResponse.json(
        { success: false, message: 'serverId is required' },
        { status: 400 }
      );
    }

    // Build query string for proxy request
    const queryString = new URLSearchParams();
    queryString.append('serverId', serverId);

    const interval = searchParams.get('interval');
    if (interval) {
      queryString.append('interval', interval);
    }

    // Redirect to the actual streaming endpoint
    const streamUrl = `${baseUrl}/api/webadmin/gameserver/metrics/stream?${queryString}`;

    return NextResponse.redirect(streamUrl);
  } catch (error) {
    console.error('Stream metrics error:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}
