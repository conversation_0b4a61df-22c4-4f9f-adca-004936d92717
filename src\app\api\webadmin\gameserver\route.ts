import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest, parseQueryParams } from '@/lib/proxy-utils';
import { GetServerListRequest, GetServerListResponse } from '@/types/gameserver';

export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    try {
  const searchParams = parseQueryParams(request);
      
      const requestData: GetServerListRequest = {};
      
      const clusterId = searchParams.get('clusterId');
      if (clusterId) {
        requestData.clusterId = parseInt(clusterId);
      }
      
      const includeOffline = searchParams.get('includeOffline');
      if (includeOffline) {
        requestData.includeOffline = includeOffline === 'true';
      }

      // Build query string for proxy request
      const queryString = new URLSearchParams();
      if (requestData.clusterId !== undefined) {
        queryString.append('clusterId', requestData.clusterId.toString());
      }
      if (requestData.includeOffline !== undefined) {
        queryString.append('includeOffline', requestData.includeOffline.toString());
      }

      const endpoint = `/api/webadmin/gameserver${queryString.toString() ? `?${queryString}` : ''}`;

      // Proxy request to game server
      const result = await makeProxyRequest<GetServerListResponse>(
        endpoint,
        {
          method: 'GET',
          requiredPermission: 'server:read'
        }
      );
      // console.log('Game server list:', result);

      return result;
    }
    catch (error) {
      console.error('Error getting server list:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
        servers: []
      };
    }
    
  });
}
