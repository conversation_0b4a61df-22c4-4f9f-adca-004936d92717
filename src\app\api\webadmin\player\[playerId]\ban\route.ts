import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { BanPlayerRequest, BanPlayerResponse } from '@/types/gameserver';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ playerId: string }> }
) {
  const playerId = parseInt((await params).playerId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: BanPlayerRequest = {
      playerId,
      serverId: body.serverId,
      reason: body.reason,
      duration: body.duration,
      banType: body.banType
    };

    if (!requestData.serverId) {
      throw new Error('serverId is required');
    }

    const endpoint = `/api/webadmin/player/${playerId}/ban`;

    // Proxy request to game server
    const result = await makeProxyRequest<BanPlayerResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'players:ban'
      }
    );

    return result;
  });
}
