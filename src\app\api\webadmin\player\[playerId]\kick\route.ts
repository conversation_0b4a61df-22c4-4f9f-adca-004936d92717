import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { KickPlayerRequest, KickPlayerResponse } from '@/types/gameserver';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ playerId: string }> }
) {
  const playerId = parseInt((await params).playerId);
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: KickPlayerRequest = {
      playerId,
      serverId: body.serverId,
      reason: body.reason
    };

    if (!requestData.serverId) {
      throw new Error('serverId is required');
    }

    const endpoint = `/api/webadmin/player/${playerId}/kick`;

    // Proxy request to game server
    const result = await makeProxyRequest<KickPlayerResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'players:kick'
      }
    );

    return result;
  });
}
