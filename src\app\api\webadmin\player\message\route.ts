import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest } from '@/lib/proxy-utils';
import { SendMessageRequest, SendMessageResponse } from '@/types/gameserver';

export async function POST(request: NextRequest) {
  return handleApiRoute(async () => {
    const body = await request.json();

    const requestData: SendMessageRequest = {
      serverId: body.serverId,
      targetType: body.targetType,
      targetId: body.targetId,
      message: body.message,
      messageType: body.messageType
    };

    if (!requestData.serverId) {
      throw new Error('serverId is required');
    }

    if (!requestData.message) {
      throw new Error('message is required');
    }

    const endpoint = '/api/webadmin/player/message';

    // Proxy request to game server
    const result = await makeProxyRequest<SendMessageResponse>(
      endpoint,
      {
        method: 'POST',
        body: requestData,
        requiredPermission: 'players:message'
      }
    );

    return result;
  });
}
