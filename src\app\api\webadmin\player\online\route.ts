import { NextRequest } from 'next/server';
import { handleApiRoute, makeProxyRequest, parseQueryParams } from '@/lib/proxy-utils';
import { GetOnlinePlayersRequest, GetOnlinePlayersResponse } from '@/types/gameserver';

export async function GET(request: NextRequest) {
  return handleApiRoute(async () => {
    const searchParams = parseQueryParams(request);
    
    const requestData: GetOnlinePlayersRequest = {};
    
    const serverId = searchParams.get('serverId');
    if (serverId) {
      requestData.serverId = parseInt(serverId);
    }
    
    const clusterId = searchParams.get('clusterId');
    if (clusterId) {
      requestData.clusterId = parseInt(clusterId);
    }
    
    const page = searchParams.get('page');
    if (page) {
      requestData.page = parseInt(page);
    }
    
    const pageSize = searchParams.get('pageSize');
    if (pageSize) {
      requestData.pageSize = parseInt(pageSize);
    }
    
    const searchTerm = searchParams.get('searchTerm');
    if (searchTerm) {
      requestData.searchTerm = searchTerm;
    }

    // Build query string for proxy request
    const queryString = new URLSearchParams();
    if (requestData.serverId !== undefined) {
      queryString.append('serverId', requestData.serverId.toString());
    }
    if (requestData.clusterId !== undefined) {
      queryString.append('clusterId', requestData.clusterId.toString());
    }
    if (requestData.page !== undefined) {
      queryString.append('page', requestData.page.toString());
    }
    if (requestData.pageSize !== undefined) {
      queryString.append('pageSize', requestData.pageSize.toString());
    }
    if (requestData.searchTerm) {
      queryString.append('searchTerm', requestData.searchTerm);
    }

    const endpoint = `/api/webadmin/player/online${queryString.toString() ? `?${queryString}` : ''}`;

    // Proxy request to game server
    const result = await makeProxyRequest<GetOnlinePlayersResponse>(
      endpoint,
      {
        method: 'GET',
        requiredPermission: 'players:read'
      }
    );

    return result;
  });
}
