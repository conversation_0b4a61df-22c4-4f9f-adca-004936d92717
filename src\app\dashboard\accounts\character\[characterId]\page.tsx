import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import { CharacterDetails } from '@/components/accounts/character-details';

interface CharacterPageProps {
  params: Promise<{
    characterId: string;
  }>;
}

function CharacterDetailsSkeleton() {
  return (
    <div className="flex items-center justify-center p-8">
      <Loader2 className="h-8 w-8 animate-spin" />
      <span className="ml-2">Loading character details...</span>
    </div>
  );
}

export default async function CharacterPage({ params }: CharacterPageProps) {
  // Decode the characterId in case it's URL encoded
  const { characterId } = await params;

  return (
    <div className="mx-auto py-6 space-y-6 w-full">
      <div className="mb-4">
        <h1 className="text-2xl font-bold">Character Details</h1>
        <p className="text-muted-foreground">
          Viewing details for character: <span className="font-medium">{characterId}</span>
        </p>
      </div>
      <Suspense fallback={<CharacterDetailsSkeleton />}>
        <CharacterDetails characterId={characterId} />
      </Suspense>
    </div>
  );
}
