import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import { AccountsManager } from '@/components/accounts/accounts-manager';
import { auth } from '@/lib/auth';
import { headers } from 'next/headers';

export default async function AccountsPage() {
  const session = await auth.api.getSession({
    headers: await headers()
  })
  if (!session?.user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Không có quyền truy cập</h1>
          <p className="text-muted-foreground">Vui lòng đăng nhập để tiếp tục.</p>
        </div>
      </div>
    );
  }
  return (
    <div className="mx-auto py-6 space-y-6 w-full">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Account Management</h1>
          <p className="text-muted-foreground">
            Manage all player accounts and characters across game servers
          </p>
        </div>
      </div>

      <Suspense fallback={<AccountsSkeleton />}>
        <AccountsManager />
      </Suspense>
    </div>
  );
}

function AccountsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading accounts...</span>
      </div>
    </div>
  );
}