"use client";

import { useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Package, Shirt, Gem, Palette, Gift, Warehouse, Home, Search, X } from "lucide-react";
import { CharacterItemData } from "@/lib/db-game";
import { ItemInfo } from "@/types/player";
import ItemGrid from "../item-grid";
import { Button } from "@/components/ui/button";


export default function CharacterDetail({characterData} : { characterData: CharacterItemData | null }) {
  const [searchFilter, setSearchFilter] = useState("");

  if (!characterData) {
    return <div>Not found</div>;
  }
  const itemCategories = [
    {
      key: "wearItems",
      label: "Equipment",
      icon: Shirt,
      description: "Trang bị đang mặc (17 slots)",
      items: characterData.wearItems,
      color: "blue"
    },
    {
      key: "inventoryItems", 
      label: "Inventory",
      icon: Package,
      description: "Túi đồ chính",
      items: characterData.inventoryItems,
      color: "green"
    },
    {
      key: "questItems",
      label: "Quest Items", 
      icon: Package,
      description: "Vật phẩm nhiệm vụ (36 slots)",
      items: characterData.questItems,
      color: "yellow"
    },
    {
      key: "gemBagItems",
      label: "NTC Bag",
      icon: Gem,
      description: "Túi để NTC (6 slots)", 
      items: characterData.gemBagItems,
      color: "purple"
    },
    {
      key: "fashionItems",
      label: "Fashion",
      icon: Palette,
      description: "Túi áo choàng (60 slots)",
      items: characterData.fashionItems,
      color: "pink"
    },
    {
      key: "eventBagItems",
      label: "Event Bag",
      icon: Gift,
      description: "Túi vật phẩm event (24 slots)",
      items: characterData.eventBagItems,
      color: "orange"
    },
    {
      key: "publicWarehouse",
      label: "Public Warehouse",
      icon: Warehouse,
      description: "Thùng đồ chung (60 slots)",
      items: characterData.publicWarehouse,
      color: "gray"
    },
    {
      key: "privateWarehouse",
      label: "Private Warehouse", 
      icon: Home,
      description: "Thùng đồ riêng (60 slots)",
      items: characterData.privateWarehouse,
      color: "indigo"
    }
  ];

  // Filter function
  const filterItems = (items: (ItemInfo | null)[]): (ItemInfo | null)[] => {
    if (!searchFilter.trim()) return items;

    return items.map(item => {
      if (!item) return null;

      const searchTerm = searchFilter.toLowerCase();
      const itemIdMatch = item.itemId.toString().includes(searchTerm);
      const globalIdMatch = item.globalId.toString().includes(searchTerm);

      return (itemIdMatch || globalIdMatch) ? item : null;
    });
  };

  // Get filtered categories with item counts
  const filteredCategories = useMemo(() => {
    return itemCategories.map(category => {
      const filteredItems = filterItems(category.items);
      const itemCount = filteredItems.filter(item => item !== null).length;

      return {
        ...category,
        items: filteredItems,
        itemCount,
        originalCount: category.items.filter(item => item !== null).length
      };
    });
  }, [itemCategories, searchFilter]);

  // Total counts
  const totalFilteredItems = filteredCategories.reduce((sum, cat) => sum + cat.itemCount, 0);
  const totalOriginalItems = filteredCategories.reduce((sum, cat) => sum + cat.originalCount, 0);

  // Clear filter
  const clearFilter = () => {
    setSearchFilter("");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{characterData.characterName}</h2>
          <p className="text-muted-foreground">Character Item Management</p>
        </div>
      </div>

      {/* Search Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center">
            <Search className="h-4 w-4 mr-2" />
            Item Filter
          </CardTitle>
          <CardDescription>
            Search items by Item ID or Global ID
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Enter Item ID or Global ID..."
                value={searchFilter}
                onChange={(e) => setSearchFilter(e.target.value)}
                className="pl-10"
              />
            </div>

            {searchFilter && (
              <Button variant="outline" size="sm" onClick={clearFilter}>
                <X className="h-4 w-4 mr-2" />
                Clear
              </Button>
            )}

            <div className="text-sm text-muted-foreground">
              {searchFilter ? (
                <span>
                  Showing {totalFilteredItems} of {totalOriginalItems} items
                </span>
              ) : (
                <span>
                  Total: {totalOriginalItems} items
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Item Categories Sections */}
      <div className="space-y-6">
        {filteredCategories.map((category) => {
          // Skip categories with no items when filtering
          if (searchFilter && category.itemCount === 0) {
            return null;
          }

          const Icon = category.icon;

          return (
            <Card key={category.key}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Icon className="h-5 w-5" />
                    <span>{category.label}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {searchFilter && (
                      <Badge variant="secondary">
                        {category.itemCount} / {category.originalCount}
                      </Badge>
                    )}
                    {!searchFilter && (
                      <Badge variant="secondary">
                        {category.originalCount} items
                      </Badge>
                    )}
                  </div>
                </CardTitle>
                <CardDescription>{category.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ItemGrid
                  items={category.items}
                  storageType={category.key}
                  characterName={characterData.characterName}
                />
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* No results message */}
      {searchFilter && totalFilteredItems === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No items found</p>
              <p className="text-sm">
                No items match your search criteria: {searchFilter}
              </p>
              <Button variant="outline" onClick={clearFilter} className="mt-4">
                Clear filter
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

