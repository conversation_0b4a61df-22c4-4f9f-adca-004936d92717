"use server"

import { getCharacterItems } from "@/lib/db-game";
import CharacterDetail from "./characterDetail";

const  page = async ({ params }: { params: Promise<{ characterId: string }> }) => {
  const characterId = (await params).characterId;
  if (!characterId) {
    return (
      <div>Invalid character ID</div>
    )
  }
  const characterData = await getCharacterItems(characterId);
  return (
    <CharacterDetail characterData={characterData} />
  )
}

export default page