"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search, Trash2, Clock, Users, Package, Warehouse, Home, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { BulkSearchFilters, BulkSearchResponse } from "@/lib/db-game";
import OneItem from "@/components/accounts/one-item";
import { buildItemOption, buildMagicValue, getMagicTypeOptions, getAttributeTypeOptions } from "@/components/accounts/itemEdit/utils";

export default function BulkSearchPage() {
  const [searchFilters, setSearchFilters] = useState<BulkSearchFilters>({
    searchType: 'globalId',
    searchValue: '',
    searchLocations: {
      characters: true,
      publicWarehouse: true,
      privateWarehouse: true,
    }
  });

  // Advanced search options
  const [advancedMode, setAdvancedMode] = useState(false);
  const [itemOptionBuilder, setItemOptionBuilder] = useState({
    enhancement: 0,
    attributeType: 0,
    attributeLevel: 0,
    itemType: 2 // 1 for weapon, 2 for others
  });
  const [magicValueBuilder, setMagicValueBuilder] = useState({
    type: 0,
    value: 0,
    isStone: false
  });

  const [searchResults, setSearchResults] = useState<BulkSearchResponse | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  // Generate search value from builders
  const generateItemOptionValue = () => {
    return buildItemOption(
      itemOptionBuilder.enhancement,
      itemOptionBuilder.attributeType,
      itemOptionBuilder.attributeLevel,
      itemOptionBuilder.itemType
    );
  };

  const generateMagicValue = () => {
    return buildMagicValue(
      magicValueBuilder.type,
      magicValueBuilder.value,
      magicValueBuilder.isStone
    );
  };

  // Use generated value for search
  const useGeneratedValue = (type: 'itemOption' | 'magic') => {
    if (type === 'itemOption') {
      const value = generateItemOptionValue();
      setSearchFilters(prev => ({
        ...prev,
        searchType: 'itemOption',
        searchValue: value.toString()
      }));
    } else {
      const value = generateMagicValue();
      setSearchFilters(prev => ({
        ...prev,
        searchType: 'itemOption', // Magic values are stored in itemOption for search
        searchValue: value.toString()
      }));
    }
  };

  // Handle search
  const handleSearch = async () => {
    if (!searchFilters.searchValue.trim()) {
      toast.error("Please enter a search value");
      return;
    }

    if (!Object.values(searchFilters.searchLocations).some(Boolean)) {
      toast.error("Please select at least one search location");
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch('/api/character-items/bulk-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchFilters),
      });

      const result = await response.json();

      if (result.success) {
        setSearchResults(result.data);
        setSelectedItems(new Set());
        toast.success(`Found ${result.data.totalFound} items in ${result.data.searchSummary.charactersWithItems} characters`);
      } else {
        toast.error(result.message || 'Search failed');
      }
    } catch (error) {
      toast.error('Error performing search');
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle item selection
  const toggleItemSelection = (itemKey: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemKey)) {
      newSelected.delete(itemKey);
    } else {
      newSelected.add(itemKey);
    }
    setSelectedItems(newSelected);
  };

  // Select all items
  const selectAllItems = () => {
    if (!searchResults) return;

    const allKeys = searchResults.results.map((_, index) => index.toString());
    setSelectedItems(new Set(allKeys));
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedItems(new Set());
  };

  // Handle bulk delete (placeholder)
  const handleBulkDelete = () => {
    if (selectedItems.size === 0) {
      toast.error("No items selected");
      return;
    }

    toast.info(`Delete functionality for ${selectedItems.size} items will be implemented`);
  };

  // Get location icon
  const getLocationIcon = (location: string) => {
    switch (location) {
      case 'character':
        return <Users className="h-4 w-4" />;
      case 'publicWarehouse':
        return <Warehouse className="h-4 w-4" />;
      case 'privateWarehouse':
        return <Home className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  // Get location label
  const getLocationLabel = (location: string) => {
    switch (location) {
      case 'character':
        return 'Character';
      case 'publicWarehouse':
        return 'Public Warehouse';
      case 'privateWarehouse':
        return 'Private Warehouse';
      default:
        return location;
    }
  };

  // Get storage type label
  const getStorageTypeLabel = (storageType: string) => {
    const labels: Record<string, string> = {
      wearItems: 'Equipment',
      inventoryItems: 'Inventory',
      questItems: 'Quest Items',
      gemBagItems: 'Gem Bag',
      fashionItems: 'Fashion',
      eventBagItems: 'Event Bag',
      publicWarehouse: 'Public Warehouse',
      privateWarehouse: 'Private Warehouse',
    };
    return labels[storageType] || storageType;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Bulk Item Search</h1>
        <p className="text-muted-foreground">
          Search for items across all characters and warehouses
        </p>
      </div>

      {/* Search Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Search Filters
          </CardTitle>
          <CardDescription>
            Search for items by Global ID, Item ID, or Item Option across all storage locations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search Type and Value */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Search Type</Label>
              <Select
                value={searchFilters.searchType}
                onValueChange={(value: 'globalId' | 'itemId' | 'itemOption') =>
                  setSearchFilters(prev => ({ ...prev, searchType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="globalId">Global ID</SelectItem>
                  <SelectItem value="itemId">Item ID</SelectItem>
                  <SelectItem value="itemOption">Item Option</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Search Value</Label>
              <Input
                type="number"
                placeholder="Enter numeric value..."
                value={searchFilters.searchValue}
                onChange={(e) =>
                  setSearchFilters(prev => ({ ...prev, searchValue: e.target.value }))
                }
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
              />
            </div>
          </div>

          {/* Search Locations */}
          <div className="space-y-3">
            <Label>Search Locations</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="characters"
                  checked={searchFilters.searchLocations.characters}
                  onCheckedChange={(checked) =>
                    setSearchFilters(prev => ({
                      ...prev,
                      searchLocations: { ...prev.searchLocations, characters: checked as boolean }
                    }))
                  }
                />
                <Label htmlFor="characters" className="flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Characters
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="publicWarehouse"
                  checked={searchFilters.searchLocations.publicWarehouse}
                  onCheckedChange={(checked) =>
                    setSearchFilters(prev => ({
                      ...prev,
                      searchLocations: { ...prev.searchLocations, publicWarehouse: checked as boolean }
                    }))
                  }
                />
                <Label htmlFor="publicWarehouse" className="flex items-center">
                  <Warehouse className="h-4 w-4 mr-2" />
                  Public Warehouse
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="privateWarehouse"
                  checked={searchFilters.searchLocations.privateWarehouse}
                  onCheckedChange={(checked) =>
                    setSearchFilters(prev => ({
                      ...prev,
                      searchLocations: { ...prev.searchLocations, privateWarehouse: checked as boolean }
                    }))
                  }
                />
                <Label htmlFor="privateWarehouse" className="flex items-center">
                  <Home className="h-4 w-4 mr-2" />
                  Private Warehouse
                </Label>
              </div>
            </div>
          </div>

          {/* Advanced Search Toggle */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="advancedMode"
              checked={advancedMode}
              onCheckedChange={(checked) => setAdvancedMode(checked as boolean)}
            />
            <Label htmlFor="advancedMode">Advanced Search (Build Values)</Label>
          </div>

          {/* Advanced Search Options */}
          {advancedMode && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
              <h4 className="font-medium">Value Builders</h4>

              {/* Item Option Builder */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Item Option Builder</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div>
                    <Label className="text-xs">Enhancement (0-99)</Label>
                    <Input
                      type="number"
                      min="0"
                      max="99"
                      value={itemOptionBuilder.enhancement}
                      onChange={(e) => setItemOptionBuilder(prev => ({
                        ...prev,
                        enhancement: parseInt(e.target.value) || 0
                      }))}
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Attribute Type (0-6)</Label>
                    <Select
                      value={itemOptionBuilder.attributeType.toString()}
                      onValueChange={(value) => setItemOptionBuilder(prev => ({
                        ...prev,
                        attributeType: parseInt(value)
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {getAttributeTypeOptions().map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs">Attribute Level (0-9)</Label>
                    <Input
                      type="number"
                      min="0"
                      max="9"
                      value={itemOptionBuilder.attributeLevel}
                      onChange={(e) => setItemOptionBuilder(prev => ({
                        ...prev,
                        attributeLevel: parseInt(e.target.value) || 0
                      }))}
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Item Type</Label>
                    <Select
                      value={itemOptionBuilder.itemType.toString()}
                      onValueChange={(value) => setItemOptionBuilder(prev => ({
                        ...prev,
                        itemType: parseInt(value)
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">Weapon</SelectItem>
                        <SelectItem value="2">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => useGeneratedValue('itemOption')}
                  >
                    Use Item Option: {generateItemOptionValue()}
                  </Button>
                </div>
              </div>

              {/* Magic Value Builder */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Magic Value Builder</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <Label className="text-xs">Magic Type</Label>
                    <Select
                      value={magicValueBuilder.type.toString()}
                      onValueChange={(value) => setMagicValueBuilder(prev => ({
                        ...prev,
                        type: parseInt(value)
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">None</SelectItem>
                        {getMagicTypeOptions().map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs">Magic Value</Label>
                    <Input
                      type="number"
                      min="0"
                      value={magicValueBuilder.value}
                      onChange={(e) => setMagicValueBuilder(prev => ({
                        ...prev,
                        value: parseInt(e.target.value) || 0
                      }))}
                    />
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <Checkbox
                      id="isStone"
                      checked={magicValueBuilder.isStone}
                      onCheckedChange={(checked) => setMagicValueBuilder(prev => ({
                        ...prev,
                        isStone: checked as boolean
                      }))}
                    />
                    <Label htmlFor="isStone" className="text-xs">Is Stone Item</Label>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => useGeneratedValue('magic')}
                  >
                    Use Magic Value: {generateMagicValue()}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Search Button */}
          <div className="flex items-center space-x-4">
            <Button onClick={handleSearch} disabled={isSearching} className="min-w-32">
              {isSearching ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      {searchResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Search Results
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">
                  {searchResults.totalFound} items found
                </Badge>
                <Badge variant="outline">
                  {searchResults.searchSummary.charactersWithItems} characters
                </Badge>
                <Badge variant="outline">
                  {searchResults.searchSummary.searchTime}ms
                </Badge>
              </div>
            </CardTitle>
            <CardDescription>
              Items matching your search criteria across all selected locations
            </CardDescription>
          </CardHeader>
          <CardContent>
            {searchResults.totalFound === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">No items found</p>
                <p className="text-sm">
                  No items match your search criteria. Try adjusting your search parameters.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Bulk Actions */}
                <div className="flex items-center justify-between border-b pb-4">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={selectAllItems}
                      disabled={selectedItems.size === searchResults.totalFound}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearSelection}
                      disabled={selectedItems.size === 0}
                    >
                      Clear Selection
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleBulkDelete}
                      disabled={selectedItems.size === 0}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Selected ({selectedItems.size})
                    </Button>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {selectedItems.size} of {searchResults.totalFound} items selected
                  </div>
                </div>

                {/* Results Table */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">Select</TableHead>
                      <TableHead className="w-20">Item</TableHead>
                      <TableHead>Character</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Storage</TableHead>
                      <TableHead>Slot</TableHead>
                      <TableHead>Global ID</TableHead>
                      <TableHead>Item ID</TableHead>
                      <TableHead>Option</TableHead>
                      <TableHead>Quantity</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {searchResults.results.map((result, index) => {
                      const itemKey = index.toString();
                      const isSelected = selectedItems.has(itemKey);

                      return (
                        <TableRow key={itemKey} className={isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''}>
                          <TableCell>
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={() => toggleItemSelection(itemKey)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="w-12 h-12">
                              <OneItem
                                item={result.item}
                                bagType={0}
                                slotPosition={result.slotIndex}
                                type="offline"
                              />
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {result.characterName}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {getLocationIcon(result.location)}
                              <span className="ml-2">{getLocationLabel(result.location)}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {getStorageTypeLabel(result.storageType)}
                            </Badge>
                          </TableCell>
                          <TableCell>{result.slotIndex}</TableCell>
                          <TableCell className="font-mono text-xs">
                            {result.item.globalId}
                          </TableCell>
                          <TableCell className="font-mono text-xs">
                            {result.item.itemId}
                          </TableCell>
                          <TableCell className="font-mono text-xs">
                            {result.item.itemOption}
                          </TableCell>
                          <TableCell>{result.item.quantity}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}