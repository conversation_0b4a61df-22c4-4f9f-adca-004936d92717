"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, User, ChevronLeft, ChevronRight, Filter, X } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import {  CharacterListResponse } from "@/lib/db-game";
import Link from "next/link";
import { JOB_NAMES } from "@/types/template";

const jobColors: Record<number, string> = {
  0: "bg-red-500",
  1: "bg-blue-500",
  2: "bg-green-500",
  3: "bg-purple-500",
  4: "bg-yellow-500",
  5: "bg-gray-500"
};

export default function CharacterDataTable() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [data, setData] = useState<CharacterListResponse>({
    characters: [],
    totalCount: 0,
    totalPages: 0,
    currentPage: 1,
  });
  const [loading, setLoading] = useState(true);

  // Get current filters from URL
  const currentPage = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '20');
  const nameFilter = searchParams.get('name') || '';
  const idFilter = searchParams.get('id') || '';
  const jobFilter = searchParams.get('job') || '';
  const levelMin = searchParams.get('levelMin') || '';
  const levelMax = searchParams.get('levelMax') || '';
  const jobLevelMin = searchParams.get('jobLevelMin') || '';
  const jobLevelMax = searchParams.get('jobLevelMax') || '';

  // Local filter states
  const [filters, setFilters] = useState({
    name: nameFilter,
    id: idFilter,
    job: jobFilter,
    levelMin: levelMin,
    levelMax: levelMax,
    jobLevelMin: jobLevelMin,
    jobLevelMax: jobLevelMax,
  });

  // Update URL with new search params
  const updateSearchParams = (newParams: Record<string, string>) => {
    const params = new URLSearchParams(searchParams.toString());
    
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });

    // Reset to page 1 when filters change
    if (Object.keys(newParams).some(key => key !== 'page' && key !== 'pageSize')) {
      params.set('page', '1');
    }

    router.push(`?${params.toString()}`);
  };

  // Fetch data
  const fetchData = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: pageSize.toString(),
        ...(nameFilter && { name: nameFilter }),
        ...(idFilter && { id: idFilter }),
        ...(jobFilter && { job: jobFilter }),
        ...(levelMin && { levelMin: levelMin }),
        ...(levelMax && { levelMax: levelMax }),
        ...(jobLevelMin && { jobLevelMin: jobLevelMin }),
        ...(jobLevelMax && { jobLevelMax: jobLevelMax }),
      });

      const response = await fetch(`/api/character-items?${queryParams}`);
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        console.error('Failed to fetch characters:', result.message);
      }
    } catch (error) {
      console.error('Error fetching characters:', error);
    } finally {
      setLoading(false);
    }
  };

  // Sync local filters with URL params
  useEffect(() => {
    setFilters({
      name: nameFilter,
      id: idFilter,
      job: jobFilter,
      levelMin: levelMin,
      levelMax: levelMax,
      jobLevelMin: jobLevelMin,
      jobLevelMax: jobLevelMax,
    });
  }, [nameFilter, idFilter, jobFilter, levelMin, levelMax, jobLevelMin, jobLevelMax]);

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, nameFilter, idFilter, jobFilter, levelMin, levelMax, jobLevelMin, jobLevelMax]);

  // Apply filters
  const applyFilters = () => {
    updateSearchParams({
      name: filters.name,
      id: filters.id,
      job: filters.job,
      levelMin: filters.levelMin,
      levelMax: filters.levelMax,
      jobLevelMin: filters.jobLevelMin,
      jobLevelMax: filters.jobLevelMax,
    });
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      name: '',
      id: '',
      job: '',
      levelMin: '',
      levelMax: '',
      jobLevelMin: '',
      jobLevelMax: '',
    });
    updateSearchParams({
      name: '',
      id: '',
      job: '',
      levelMin: '',
      levelMax: '',
      jobLevelMin: '',
      jobLevelMax: '',
    });
  };

  // Pagination
  const goToPage = (page: number) => {
    updateSearchParams({ page: page.toString() });
  };

  const changePageSize = (newPageSize: string) => {
    updateSearchParams({ 
      pageSize: newPageSize,
      page: '1' // Reset to first page
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Characters</h3>
          <p className="text-sm text-muted-foreground">
            {data.totalCount} characters found
          </p>
        </div>
      </div>

      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </CardTitle>
          <CardDescription>Filter characters by various criteria</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Row 1: Basic Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Character Name</label>
                <Input
                  placeholder="Search by name..."
                  value={filters.name}
                  onChange={(e) => setFilters(prev => ({ ...prev, name: e.target.value }))}
                  className="h-9"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Character ID</label>
                <Input
                  placeholder="Search by ID..."
                  value={filters.id}
                  onChange={(e) => setFilters(prev => ({ ...prev, id: e.target.value }))}
                  className="h-9"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Job Class</label>
                <Select
                  value={filters.job || "all"}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, job: value === "all" ? "" : value }))}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="All jobs" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Jobs</SelectItem>
                    {Object.entries(JOB_NAMES).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Row 2: Level Filters */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Min Level</label>
                <Input
                  type="number"
                  placeholder="1"
                  value={filters.levelMin}
                  onChange={(e) => setFilters(prev => ({ ...prev, levelMin: e.target.value }))}
                  className="h-9"
                  min="1"
                  max="999"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Max Level</label>
                <Input
                  type="number"
                  placeholder="999"
                  value={filters.levelMax}
                  onChange={(e) => setFilters(prev => ({ ...prev, levelMax: e.target.value }))}
                  className="h-9"
                  min="1"
                  max="999"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Min Job Level</label>
                <Input
                  type="number"
                  placeholder="1"
                  value={filters.jobLevelMin}
                  onChange={(e) => setFilters(prev => ({ ...prev, jobLevelMin: e.target.value }))}
                  className="h-9"
                  min="1"
                  max="999"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Max Job Level</label>
                <Input
                  type="number"
                  placeholder="999"
                  value={filters.jobLevelMax}
                  onChange={(e) => setFilters(prev => ({ ...prev, jobLevelMax: e.target.value }))}
                  className="h-9"
                  min="1"
                  max="999"
                />
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between mt-6 pt-4 border-t">
            <div className="flex items-center space-x-3">
              <Button onClick={applyFilters} size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Search className="h-4 w-4 mr-2" />
                Apply Filters
              </Button>
              <Button variant="outline" onClick={clearFilters} size="sm">
                <X className="h-4 w-4 mr-2" />
                Clear All
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              {Object.values(filters).some(v => v) && (
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                    {Object.values(filters).filter(v => v).length} filter(s) active
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Character</TableHead>
                <TableHead>ID</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Job</TableHead>
                <TableHead>Job Level</TableHead>
                <TableHead>Money</TableHead>
                <TableHead>HP/MP</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                Array.from({ length: Math.min(pageSize, 5) }).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Skeleton className="h-8 w-8 rounded-full" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </TableCell>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-12" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-8" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-8 w-20" /></TableCell>
                  </TableRow>
                ))
              ) : data.characters.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    No characters found
                  </TableCell>
                </TableRow>
              ) : (
                data.characters.map((character, index) => (
                  <TableRow key={`${character.fldId}-${character.fldName}-${index}`} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className={`text-white text-xs ${jobColors[character.fldJob] || 'bg-gray-500'}`}>
                            <User className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{character.fldName}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-xs">{character.fldId}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">{character.fldLevel}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {JOB_NAMES[character.fldJob] || "Unknown"}
                      </Badge>
                    </TableCell>
                    <TableCell>{character.fldJobLevel}</TableCell>
                    <TableCell className="font-mono text-xs">{character.fldMoney}</TableCell>
                    <TableCell className="text-xs">
                      {character.fldHp}/{character.fldMp}
                    </TableCell>
                    <TableCell>
                     
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                      >
                       <Link href={`/dashboard/character-items/${character.fldName}`}>
                        View Items
                      </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Rows per page:</span>
          <Select value={pageSize.toString()} onValueChange={changePageSize}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            Page {data.currentPage} of {data.totalPages} ({data.totalCount} total)
          </span>
          
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => goToPage(data.currentPage - 1)}
              disabled={data.currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => goToPage(data.currentPage + 1)}
              disabled={data.currentPage >= data.totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
