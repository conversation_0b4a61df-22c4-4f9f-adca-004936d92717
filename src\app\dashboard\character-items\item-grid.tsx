"use client";

import { ItemInfo } from "@/types/player";
import OneItem from "@/components/accounts/one-item";

interface ItemGridProps {
  items: (ItemInfo | null)[];
  characterName: string;
  storageType: string;
}

export default function ItemGrid({ items, storageType }: ItemGridProps) {


  // Get storage type for API calls
  const getStorageTypeForAPI = () => {
    const storageMap: Record<string, number> = {
      wearItems: 0,
      inventoryItems: 1,
      questItems: 2,
      gemBagItems: 3,
      fashionItems: 4,
      eventBagItems: 5,
      publicWarehouse: 6,
      privateWarehouse: 7,
    };
    return storageMap[storageType] || 1;
  };

  return (
    <div className="space-y-4">
      {/* Item Grid */}
      <div className={`flex flex-wrap gap-2`}>
        {items.map((item, index) => (
          <div
            key={index}
            className="aspect-square border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg p-1 hover:border-blue-400 transition-colors cursor-pointer w-16 h-16"
          >
            {item ? (
              <div className="w-full h-full">
                <OneItem item={item} bagType={getStorageTypeForAPI()} slotPosition={0} type="offline" />
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                {index + 1}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Stats */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>
          Slots: {items.filter(item => item !== null).length} / {items.length}
        </span>
        <span>
          Empty: {items.filter(item => item === null).length}
        </span>
      </div>
    </div>
  );
}
