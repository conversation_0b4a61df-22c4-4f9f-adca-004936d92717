"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, ArrowRight, TestTube } from "lucide-react";
import Link from "next/link";
import CharacterDataTable from "./character-data-table";

export default function CharacterItemsPage() {

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Character Item Management</h1>
          <p className="text-muted-foreground">
            Quản lý vật phẩm của nhân vật trực tiếp từ database
          </p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="bulk-operations" disabled>
            Bulk Operations
          </TabsTrigger>
          <TabsTrigger value="analytics" disabled>
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <CharacterDataTable />
        </TabsContent>

        <TabsContent value="bulk-operations">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>
                Thao tác hàng loạt với vật phẩm
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center">
                      <Search className="h-4 w-4 mr-2" />
                      Bulk Item Search
                    </CardTitle>
                    <CardDescription>
                      Tìm kiếm vật phẩm từ tất cả nhân vật và kho đồ
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">
                      Tìm kiếm theo Global ID, Item ID, hoặc Item Option từ tất cả các nguồn lưu trữ.
                    </p>
                    <Link href="/dashboard/character-items/bulk">
                      <Button className="w-full">
                        <Search className="h-4 w-4 mr-2" />
                        Open Bulk Search
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center">
                      <TestTube className="h-4 w-4 mr-2" />
                      Search Pattern Test
                    </CardTitle>
                    <CardDescription>
                      Test và debug hex pattern matching
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">
                      Debug tool để test các phương pháp convert hex patterns khác nhau.
                    </p>
                    <Link href="/dashboard/character-items/test-search">
                      <Button variant="outline" className="w-full">
                        <TestTube className="h-4 w-4 mr-2" />
                        Open Test Tool
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>
                Thống kê và phân tích vật phẩm (Coming soon)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Feature đang được phát triển...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
