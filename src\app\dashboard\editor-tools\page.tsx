'use client';

import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Database,
  Settings,
  MessageSquare,
  Package,
  ArrowRightLeft
} from 'lucide-react';
import Link from 'next/link';
import { YbmsgEditor } from '@/components/editor/ybmsg-editor';
import { CfgEditor } from '@/components/editor/cfg-editor';
import { SetItemEditor } from '@/components/editor/setitem-editor';
import { YbiEditor } from '@/components/editor/ybi-editor';
import YbqEditor from '@/components/editors/ybq-editor';
import YbiMigration from '@/components/editors/ybi-migration';

export default function EditorToolsPage() {
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('tab') || 'ybmsg';

  const tabsData = [
    {
      id: 'ybmsg',
      label: 'Ybmsg Editor',
      icon: MessageSquare,
      description: 'Chỉnh sửa file ybmsg.cfg (message strings)',
      badge: 'Active'
    },
    {
      id: 'ybi',
      label: 'Ybi Editor',
      icon: FileText,
      description: 'Chỉnh sửa file ybi.cfg (items, skills, abilities, etc.)',
      badge: 'Active'
    },
    {
      id: 'ybq',
      label: 'Ybq Editor',
      icon: Database,
      description: 'Chỉnh sửa file ybq (quest data)',
      badge: 'Active'
    },
    {
      id: 'ybq-test',
      label: 'Ybq Parser Test',
      icon: Database,
      description: 'Test và debug YBQ parser',
      badge: 'Debug'
    },
    {
      id: 'cfg',
      label: 'Cfg Editor',
      icon: Settings,
      description: 'Chỉnh sửa các file config',
      badge: 'Active'
    },
    {
      id: 'setitem',
      label: 'SetItem Editor',
      icon: Package,
      description: 'Chỉnh sửa SetItem.cfg',
      badge: 'Active'
    },
    {
      id: 'ybi-migration',
      label: 'YBI Migration',
      icon: ArrowRightLeft,
      description: 'Migrate dữ liệu giữa các file YBI với config khác nhau',
      badge: 'New'
    }
  ];



  return (
    <div className="mx-auto py-6 space-y-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Editor Tools</h1>
          <p className="text-muted-foreground">
            Công cụ chỉnh sửa các file binary của game client
          </p>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center gap-2 border-b pb-4 overflow-x-auto">
        {tabsData.map((tab) => (
          <Link
            key={tab.id}
            href={`/dashboard/editor-tools?tab=${tab.id}`}
            className={`
              flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors whitespace-nowrap
              ${activeTab === tab.id 
                ? 'bg-primary text-primary-foreground border-primary' 
                : 'bg-background hover:bg-accent border-border'
              }
            `}
          >
            <tab.icon className="h-4 w-4" />
            {tab.label}
            {tab.badge && (
              <Badge 
                variant={tab.badge === 'Active' ? 'default' : 'secondary'} 
                className="text-xs"
              >
                {tab.badge}
              </Badge>
            )}
          </Link>
        ))}
      </div>

      {/* Content Area */}
      <div className="min-h-[600px]">
        {activeTab === 'ybmsg' ? (
          <YbmsgEditor />
        ) : activeTab === 'ybi' ? (
          <YbiEditor />
        ) : activeTab === 'ybq' ? (
          <YbqEditor />
        ) : activeTab === 'cfg' ? (
          <CfgEditor />
        ) : activeTab === 'setitem' ? (
          <SetItemEditor />
        ) : activeTab === 'ybi-migration' ? (
          <YbiMigration />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tabsData.filter(tab => tab.id !== 'ybmsg').map((tab) => (
              <Card key={tab.id} className="cursor-pointer hover:shadow-md transition-shadow opacity-60">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-muted">
                        <tab.icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{tab.label}</CardTitle>
                        <CardDescription className="text-sm">
                          {tab.description}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {tab.badge}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <tab.icon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      {tab.description} sẽ được phát triển trong phiên bản tiếp theo.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
