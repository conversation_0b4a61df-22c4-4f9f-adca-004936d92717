import { Suspense } from 'react';
import { ServerList } from '@/components/gameserver/server-list';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export default function GameServersPage() {
  return (
    <div className="mx-auto py-6 space-y-6 w-full">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Game Server Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage your game servers
          </p>
        </div>
      </div>

      <Suspense fallback={<ServerListSkeleton />}>
        <ServerList />
      </Suspense>
    </div>
  );
}

function ServerListSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="h-6 bg-gray-200 rounded w-32"></div>
              <div className="h-6 bg-gray-200 rounded w-16"></div>
            </div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, j) => (
                <div key={j}>
                  <div className="h-4 bg-gray-200 rounded w-12 mb-1"></div>
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                </div>
              ))}
            </div>

            <div className="h-4 bg-gray-200 rounded w-32"></div>

            <div className="flex gap-2 pt-2">
              <div className="h-8 bg-gray-200 rounded w-16"></div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
              <div className="h-8 bg-gray-200 rounded w-20"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
