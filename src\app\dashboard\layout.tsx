
import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import {
  SidebarInset,
} from "@/components/ui/sidebar"
import { SidebarProviderWrapper } from "@/components/sidebar-provider-wrapper"


import { auth } from "@/lib/auth"
import { headers } from "next/headers"

interface LayoutProps {
  children: React.ReactNode;
}

export default async function layout({children}: LayoutProps) {
  const session = await auth.api.getSession({
      headers: await headers()
    })

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Không có quyền truy cập</h1>
          <p className="text-muted-foreground">Vui lòng đăng nhập để tiếp tục.</p>
        </div>
      </div>
    );
  }

  return (
    <SidebarProviderWrapper>
      <AppSidebar />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 px-6 py-4 md:gap-6 md:py-6">
                {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProviderWrapper>
  )
}
