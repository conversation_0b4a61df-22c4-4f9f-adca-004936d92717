import { Suspense } from 'react';
import { OnlinePlayers } from '@/components/gameserver/online-players';
import { Loader2 } from 'lucide-react';

export default function PlayersPage() {
  return (
    <div className="mx-auto py-6 space-y-6 w-full">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Player Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage online players
          </p>
        </div>
      </div>

      <Suspense fallback={<PlayersSkeleton />}>
        <OnlinePlayers />
      </Suspense>
    </div>
  );
}

function PlayersSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading players...</span>
      </div>
    </div>
  );
}
