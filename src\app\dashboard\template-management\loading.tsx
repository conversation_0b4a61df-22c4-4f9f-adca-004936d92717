'use client';

import { <PERSON>, CardContent,  CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  Store,
  Package,
  Settings,
  ShoppingCart,
  Database,
  FileText,
  BarChart3,
  Shield,
  Zap
} from 'lucide-react';

export default function TemplateManagementLoading() {
  // Mock tab data matching the actual page structure
  const managementTabs = [
    {
      id: 'shop',
      label: 'Shop',
      icon: Store,
      description: 'Quản lý shop template (NPC có ID < 10000)',
      badge: 'Active'
    },
    {
      id: 'items',
      label: 'Items',
      icon: Package,
      description: 'Quản lý template vật phẩm',
      badge: 'Active'
    },
    {
      id: 'drops',
      label: 'Drop Tables',
      icon: Database,
      description: 'Quản lý template bảng rơi đồ',
      badge: 'Active'
    },
    {
      id: 'quests',
      label: 'Stones',
      icon: FileText,
      description: 'Quản lý template đá',
      badge: 'TODO'
    },
    {
      id: 'skills',
      label: 'Skills',
      icon: Zap,
      description: 'Quản lý template kỹ năng',
      badge: 'TODO'
    },
    {
      id: 'abilities',
      label: 'Abilities',
      icon: Shield,
      description: 'Quản lý template kĩ năng',
      badge: 'TODO'
    },
    {
      id: 'maps',
      label: 'Maps',
      icon: BarChart3,
      description: 'Quản lý template bản đồ',
      badge: 'Active'
    },
    {
      id: 'events',
      label: 'Events',
      icon: ShoppingCart,
      description: 'Quản lý template sự kiện',
      badge: 'TODO'
    },
    {
      id: 'configs',
      label: 'Configs',
      icon: Settings,
      description: 'Template cấu hình hệ thống',
      badge: 'TODO'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Template Management</h1>
        <p className="text-muted-foreground">
          Quản lý toàn bộ template data của game: NPC, vật phẩm, quái vật và các thành phần khác
        </p>
      </div>

      {/* Management Navigation */}
      <div className="space-y-4">
        {/* Tab Navigation Skeleton - Grid Layout */}
        <div className="grid grid-cols-6 lg:grid-cols-11 gap-2">
          {managementTabs.map((tab, index) => {
            const Icon = tab.icon;
            return (
              <div
                key={tab.id}
                className={`
                  flex flex-col items-center gap-1 p-3 rounded-lg border transition-all animate-pulse
                  ${index === 0
                    ? 'bg-primary/10 border-primary/20'
                    : 'bg-card border-border'
                  }
                `}
              >
                <Icon className="h-4 w-4 text-muted-foreground" />
                <Skeleton className="h-3 w-12" />
                <Badge
                  variant={tab.badge === 'Active' ? 'default' : 'secondary'}
                  className="text-xs px-1 py-0 opacity-60"
                >
                  {tab.badge}
                </Badge>
              </div>
            );
          })}
        </div>

        {/* Active Tab Content Skeleton */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Store className="h-5 w-5 text-muted-foreground" />
              <Skeleton className="h-6 w-24" />
              <Badge variant="default" className="opacity-60">
                <Skeleton className="h-3 w-12" />
              </Badge>
            </div>
            <Skeleton className="h-4 w-64 mt-2" />
          </CardHeader>
          <CardContent>
            {/* Dynamic Content Based on Tab Type */}
            <div className="space-y-6">
              {/* Shop/Items/Drops Layout - Two Column */}
              <div className="grid grid-cols-12 gap-6">
                {/* Left Column */}
                <div className="col-span-4">
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Store className="h-5 w-5 text-muted-foreground" />
                        <Skeleton className="h-5 w-24" />
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Search Skeleton */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-10 flex-1" />
                        <Skeleton className="h-10 w-10" />
                      </div>

                      {/* List Items Skeleton */}
                      <div className="space-y-2 h-[calc(100vh-400px)] overflow-y-auto">
                        {Array.from({ length: 8 }).map((_, i) => (
                          <div key={i} className="p-3 rounded-lg border animate-pulse">
                            <div className="flex items-center justify-between">
                              <div className="space-y-2">
                                <Skeleton className="h-5 w-32" />
                                <Skeleton className="h-3 w-20" />
                              </div>
                              <div className="flex items-center gap-2">
                                <Skeleton className="h-4 w-12" />
                                <Skeleton className="h-5 w-12 rounded-full" />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Right Column */}
                <div className="col-span-8">
                  <Card className="h-full">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          <Package className="h-5 w-5 text-muted-foreground" />
                          <Skeleton className="h-5 w-32" />
                        </CardTitle>
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-8 w-8" />
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-8 w-8" />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {/* Template Grid Skeleton */}
                      <div className="grid grid-cols-6 gap-2">
                        {Array.from({ length: 48 }).map((_, i) => (
                          <div key={i} className="aspect-square border rounded-lg p-2 animate-pulse">
                            <Skeleton className="w-full h-full rounded" />
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

    </div>
  );
}
