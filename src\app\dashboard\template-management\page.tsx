'use client';

import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Store,
  Package,
  Settings,
  ShoppingCart,
  Database,
  FileText,
  BarChart3,
  Wrench,
  Shield,
  Zap,
  Gift
} from 'lucide-react';
import Link from 'next/link';
import { ShopTemplateManager } from '@/components/template/shop-template-manager';
import { MapManager } from '@/components/template/map-manager';
import { DropTemplateManager } from '@/components/template/drop-template-manager';
import { ItemTemplateManager } from '@/components/template/item-template-manager';
import { default as PillTemplateManager } from '@/components/template/pill-template.manager';
import { default as ItemOptionTemplateManager } from '@/components/template/item-option-template-manager';
import { default as BoxTemplateManager } from '@/components/template/box-template-manager';

export default function TemplateManagementPage() {
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('tab') || 'shop';
  const searchId = searchParams.get('searchId') || '';

  const managementTabs = [
    {
      id: 'shop',
      label: 'Shop',
      icon: Store,
      description: 'Quản lý shop template (NPC có ID < 10000)',
      badge: 'Active'
    },
    {
      id: 'items',
      label: 'Items',
      icon: Package,
      description: 'Quản lý template vật phẩm',
      badge: 'Active'
    },
    {
      id: 'drops',
      label: 'Drop Tables',
      icon: Database,
      description: 'Quản lý template bảng rơi đồ',
      badge: 'Active'
    },
    {
      id: 'quests',
      label: 'Stones',
      icon: FileText,
      description: 'Quản lý template đá',
      badge: 'TODO'
    },
    {
      id: 'pill',
      label: 'Pills',
      icon: Zap,
      description: 'Quản lý template pill',
      badge: 'ACTIVE'
    },
    {
      id: 'item-options',
      label: 'Item Options',
      icon: Wrench,
      description: 'Quản lý template item options',
      badge: 'ACTIVE'
    },
    {
      id: 'boxes',
      label: 'Boxes',
      icon: Gift,
      description: 'Quản lý template boxes và rewards',
      badge: 'ACTIVE'
    },
    {
      id: 'abilities',
      label: 'Abilities',
      icon: Shield,
      description: 'Quản lý template kĩ năng',
      badge: 'TODO'
    },
    {
      id: 'maps',
      label: 'Maps',
      icon: BarChart3,
      description: 'Quản lý template bản đồ',
      badge: 'Active'
    },
    {
      id: 'events',
      label: 'Events',
      icon: ShoppingCart,
      description: 'Quản lý template sự kiện',
      badge: 'TODO'
    },
    {
      id: 'configs',
      label: 'Configs',
      icon: Settings,
      description: 'Template cấu hình hệ thống',
      badge: 'TODO'
    }
  ];

  const activeTabData = managementTabs.find(tab => tab.id === activeTab);



  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Template Management</h1>
        <p className="text-muted-foreground">
          Quản lý toàn bộ template data của game: NPC, vật phẩm, quái vật và các thành phần khác
        </p>
      </div>


      {/* Management Navigation */}
      <div className="space-y-4">
        {/* Tab Navigation */}
        <div className="grid grid-cols-6 lg:grid-cols-11 gap-2">
          {managementTabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            return (
              <Link
                key={tab.id}
                href={`/dashboard/template-management?tab=${tab.id}${searchId ? `&searchId=${searchId}` : ''}`}
                className={`
                  flex flex-col items-center gap-1 p-3 rounded-lg border transition-all
                  ${isActive
                    ? 'bg-primary text-primary-foreground border-primary'
                    : 'bg-card hover:bg-accent border-border hover:border-primary/50'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span className="text-xs hidden sm:block font-medium">{tab.label}</span>
                <Badge
                  variant={tab.badge === 'Active' ? 'default' : 'secondary'}
                  className="text-xs px-1 py-0"
                >
                  {tab.badge}
                </Badge>
              </Link>
            );
          })}
        </div>

        {/* Active Tab Content */}
        {activeTabData && (
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <activeTabData.icon className="h-5 w-5" />
                <CardTitle>{activeTabData.label}</CardTitle>
                <Badge variant={activeTabData.badge === 'Active' ? 'default' : 'secondary'}>
                  {activeTabData.badge}
                </Badge>
              </div>
              <CardDescription>{activeTabData.description}</CardDescription>
            </CardHeader>
            <CardContent>
              {activeTab === 'shop' ? (
                <ShopTemplateManager />
              ) : activeTab === 'pill' ? (
                <PillTemplateManager />
              ) : activeTab === 'item-options' ? (
                <ItemOptionTemplateManager />
              ) : activeTab === 'boxes' ? (
                <BoxTemplateManager />
              ) : activeTab === 'maps' ? (
                <MapManager />
              ) : activeTab === 'drops' ? (
                <DropTemplateManager />
              ) : activeTab === 'items' ? (
                <ItemTemplateManager />
              ) : (
                <div className="text-center py-12">
                  <activeTabData.icon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">TODO</h3>
                  <p className="text-muted-foreground">
                    {activeTabData.description} sẽ được phát triển trong phiên bản tiếp theo.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
