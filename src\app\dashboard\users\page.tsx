import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import { UserManager } from '@/components/users/user-manager';
import { PageGuard } from '@/components/auth/permission-guard';
import { PERMISSIONS } from '@/lib/auth-utils';

export default async function UsersPage() {
  return (
    <PageGuard
      permission={PERMISSIONS.USER_READ}
      noPermissionMessage="Bạn không có quyền truy cập trang quản lý người dùng."
    >
      <div className="mx-auto py-6 space-y-6 w-full">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Quản lý Người dùng</h1>
            <p className="text-muted-foreground">
              Quản lý tài khoản người dùng và phân quyền trong hệ thống
            </p>
          </div>
        </div>

        <Suspense fallback={<UsersSkeleton />}>
          <UserManager />
        </Suspense>
      </div>
    </PageGuard>
  );
}

function UsersSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    </div>
  );
}
