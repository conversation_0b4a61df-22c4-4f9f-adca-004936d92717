import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { AlertCircle } from "lucide-react";

export default function RegisterPage() {
  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-md">
        <Card className="overflow-hidden p-0">
          <CardContent className="p-8 text-center">
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-destructive/15 p-3">
                <AlertCircle className="h-6 w-6 text-destructive" />
              </div>
            </div>

            <h1 className="text-2xl font-bold mb-2">Đăng ký đã bị tắt</h1>
            <p className="text-muted-foreground mb-6">
              <PERSON><PERSON> thống không cho phép đăng ký tài khoản mới.
              Chỉ quản trị viên mới có thể tạo tài khoản cho người dùng.
            </p>

            <div className="space-y-3">
              <Button asChild className="w-full">
                <Link href="/login">
                  Đăng nhập
                </Link>
              </Button>

              <p className="text-sm text-muted-foreground">
                Cần tài khoản? Vui lòng liên hệ quản trị viên để được cấp quyền truy cập.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
