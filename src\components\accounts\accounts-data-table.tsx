'use client';

import * as React from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import {
  ArrowUpDown,
  ChevronDown,
  MoreHorizontal,
  Eye,
  Edit,
  UserX,
  Ban,
  MessageSquare,
  Navigation,
  Package,
  FileText
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { DetailedPlayer } from '@/types/gameserver';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface AccountsDataTableProps {
  data: DetailedPlayer[];
  loading?: boolean;
  onRefresh?: () => void;
  // Filter props
  searchTerm: string;
  onSearchTermChange: (value: string) => void;
  sortBy: string;
  onSortByChange: (value: string) => void;
  sortOrder: string;
  onSortOrderChange: (value: string) => void;
  pageSize: number;
  onPageSizeChange: (value: number) => void;
  onlineOnly: boolean;
  onOnlineOnlyChange: (value: boolean) => void;
  onSearch: () => void;
  onClearFilters: () => void;
}

export function AccountsDataTable({
  data,
  loading = false,
  onRefresh,
  searchTerm,
  onSearchTermChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrderChange,
  pageSize,
  onPageSizeChange,
  onlineOnly,
  onOnlineOnlyChange,
  onSearch,
  onClearFilters
}: AccountsDataTableProps) {
  const router = useRouter();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const columns: ColumnDef<DetailedPlayer>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'sessionId',
      header: 'Session ID',
      cell: ({ row }) => <div>{row.getValue('sessionId')}</div>,
    },
    {
      accessorKey: 'characterName',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Character
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const player = row.original;
        return (
          <Link
            href={`/dashboard/accounts/character/${player.characterName}?serverId=${player.serverId}&clusterId=${player.clusterId}`}
            className="font-medium text-center text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
          >
            {row.getValue('characterName')}
          </Link>
        );
      },
    },
    // {
    //   accessorKey: 'accountId',
    //   header: ({ column }) => {
    //     return (
    //       <Button
    //         variant="ghost"
    //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    //       >
    //         ID
    //         <ArrowUpDown className="ml-2 h-4 w-4" />
    //       </Button>
    //     );
    //   },
    //   cell: ({ row }) => <div className="lowercase">{row.getValue('accountId')}</div>,
    // },
    {
      accessorKey: 'level',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Level
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="text-center font-medium">
          {row.getValue('level')}
        </div>
      ),
    },
    {
      accessorKey: 'jobName',
      header: 'Job',
      cell: ({ row }) => {
        return (
          <Badge variant="outline">
            {row.getValue('jobName')} {row.original.jobLevel}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'jobLevel',
      header: () => {
        return null;
      },
      cell: () => null,
    },
    {
      id: 'position',
      header: 'Position (X,Y)',
      cell: ({ row }) => {
        const player = row.original;
        return (
          <div className="text-center text-sm">
            ({Math.round(player.posX)}, {Math.round(player.posY)})
          </div>
        );
      },
    },
    {
      accessorKey: 'mapName',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Map
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.getValue('mapName')}</div>,
    },
    {
      id: 'partyInfo',
      header: 'Party',
      cell: ({ row }) => {
        const player = row.original;
        if (!player.partyInfo) {
          return <div className="text-muted-foreground text-sm">No party</div>;
        }
        return (
          <div className="text-sm">
            <div className="font-medium">{player.partyInfo.leaderName}</div>
            <div className="text-muted-foreground">
              {player.partyInfo.memberCount}/{player.partyInfo.maxMembers} members
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'isOnline',
      header: 'Status',
      cell: ({ row }) => {
        const lobby = row.original.characterName  == '';
        const dead = row.original.isDead ;
        const offlevel = row.original.offLevel;
        return (
        <Badge variant={row.getValue('isOnline') ? 'default' : 'secondary'}>
          {lobby ? "Lobby" : (dead ? "Dead" : (offlevel ? "Offlevel" : "Online"))} - Cụm {row.original.clusterId} - Kênh {row.original.serverId}
        </Badge>
      )},
    },
    // {
    //   accessorKey: 'lastActivity',
    //   header: ({ column }) => {
    //     return (
    //       <Button
    //         variant="ghost"
    //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    //       >
    //         Last Activity
    //         <ArrowUpDown className="ml-2 h-4 w-4" />
    //       </Button>
    //     );
    //   },
    //   cell: ({ row }) => {
    //     const date = new Date(row.getValue('lastActivity'));
    //     return <div className="text-sm">{date.toLocaleString()}</div>;
    //   },
    // },
    {
      id: 'actions',
      header: 'Actions',
      enableHiding: false,
      cell: ({ row }) => {
        const player = row.original;

        return (
          <div className="flex items-center gap-1">
            {/* View Details */}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              title="View Details"
              onClick={() => {
                router.push(`/dashboard/accounts/character/${player.characterName}?serverId=${player.serverId+1}&clusterId=${player.clusterId}`);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>

            {/* Edit Account */}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              title="Edit Account"
              onClick={() => {
                // TODO: Implement edit account
                console.log('Edit account for:', player.accountId);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>

            {/* Kick Player */}
            {player.isOnline && (
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                title="Kick Player"
                onClick={() => {
                  // TODO: Implement kick player
                  console.log('Kick player:', player.characterName);
                }}
              >
                <UserX className="h-4 w-4" />
              </Button>
            )}

            {/* Ban Account */}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 text-red-600 hover:text-red-700"
              title="Ban Account"
              onClick={() => {
                // TODO: Implement ban account
                console.log('Ban account:', player.accountId);
              }}
            >
              <Ban className="h-4 w-4" />
            </Button>

            {/* Send Message */}
            {player.isOnline && (
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                title="Send Message"
                onClick={() => {
                  // TODO: Implement send message
                  console.log('Send message to:', player.characterName);
                }}
              >
                <MessageSquare className="h-4 w-4" />
              </Button>
            )}

            {/* Teleport To */}
            {player.isOnline && (
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                title="Teleport To"
                onClick={() => {
                  // TODO: Implement teleport
                  console.log('Teleport to:', player.characterName);
                }}
              >
                <Navigation className="h-4 w-4" />
              </Button>
            )}

            {/* View Inventory */}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              title="View Inventory"
              onClick={() => {
                // TODO: Implement view inventory
                console.log('View inventory for:', player.characterName);
              }}
            >
              <Package className="h-4 w-4" />
            </Button>

            {/* View Logs */}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              title="View Logs"
              onClick={() => {
                // TODO: Implement view logs
                console.log('View logs for:', player.accountId);
              }}
            >
              <FileText className="h-4 w-4" />
            </Button>

            {/* More Actions Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon" className="h-8 w-8">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>More Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => navigator.clipboard.writeText(player.accountId)}
                >
                  Copy Account ID
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => navigator.clipboard.writeText(player.characterName)}
                >
                  Copy Character Name
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  Reset Password
                </DropdownMenuItem>
                <DropdownMenuItem>
                  View Login History
                </DropdownMenuItem>
                <DropdownMenuItem>
                  View Purchase History
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      {/* Filters & Search - Compact Layout */}
      <div className="flex flex-wrap items-center gap-4 py-4 border-b">
        {/* Search */}
        <div className="flex items-center gap-2">
          <Input
            placeholder="Character name or account ID..."
            value={searchTerm}
            onChange={(e) => onSearchTermChange(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && onSearch()}
            className="w-64"
          />
        </div>

        {/* Sort By */}
        <div className="flex items-center gap-2">
          <Select value={sortBy} onValueChange={onSortByChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="characterName">Character Name</SelectItem>
              <SelectItem value="level">Level</SelectItem>
              <SelectItem value="job">Job</SelectItem>
              <SelectItem value="mapId">Map</SelectItem>
              <SelectItem value="lastActivity">Last Activity</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Sort Order */}
        <div className="flex items-center gap-2">
          <Select value={sortOrder} onValueChange={onSortOrderChange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Order" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="asc">Asc</SelectItem>
              <SelectItem value="desc">Desc</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Page Size */}
        <div className="flex items-center gap-2">
          <Select value={pageSize.toString()} onValueChange={(value) => onPageSizeChange(parseInt(value))}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
              <SelectItem value="200">200</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Online Only Switch */}
        <div className="flex items-center gap-2">
          <Switch
            id="onlineOnly"
            checked={onlineOnly}
            onCheckedChange={onOnlineOnlyChange}
          />
          <Label htmlFor="onlineOnly" className="text-sm">Online only</Label>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 ml-auto">
          <Button variant="outline" size="sm" onClick={onClearFilters}>
            Clear
          </Button>
          <Button size="sm" onClick={onSearch}>
            Search
          </Button>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {loading ? 'Loading...' : 'No results.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
