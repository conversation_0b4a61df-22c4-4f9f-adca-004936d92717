'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, UserCheck, UsersRound, Activity } from 'lucide-react';
import { toast } from 'sonner';
import { AccountsDataTable } from './accounts-data-table';
import { apiClientService } from '@/services/api-client.service';
import { DetailedPlayer } from '@/types/gameserver';

interface AccountsStats {
  totalPlayers: number;
  onlinePlayers: number;
  totalParties: number;
  totalAccounts: number;
}

export function AccountsManager() {
  const [players, setPlayers] = useState<DetailedPlayer[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<AccountsStats>({
    totalPlayers: 0,
    onlinePlayers: 0,
    totalParties: 0,
    totalAccounts: 0
  });

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [onlineOnly, setOnlineOnly] = useState(false);
  const [sortBy, setSortBy] = useState('characterName');
  const [sortOrder, setSortOrder] = useState('asc');
  const [pageSize, setPageSize] = useState(50);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const loadPlayers = useCallback(async () => {
    try {
      setLoading(true);

      const response = await apiClientService.getAllAccounts({
        page: currentPage,
        pageSize,
        searchTerm: searchTerm || undefined,
        onlineOnly,
        sortBy: sortBy as 'accountId' | 'level' | 'lastLogin' | 'characterName',
        sortOrder: sortOrder as 'asc' | 'desc'
      });

      if (response.success) {
        setPlayers(response.players);
        setTotalPages(response.totalPages);
        setStats({
          totalPlayers: response.totalCharacters,
          onlinePlayers: response.onlineCount,
          totalParties: 0,
          totalAccounts: response.totalCount
        });
      } else {
        toast.error('Failed to load accounts: ' + response.message);
      }
    } catch (error) {
      console.error('Error loading accounts:', error);
      toast.error('Failed to load accounts: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchTerm, onlineOnly, sortBy, sortOrder]);

  useEffect(() => {
    loadPlayers();
  }, [loadPlayers]);

  useEffect(() => {
    // Debounce search
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        loadPlayers();
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, currentPage, loadPlayers]);



  const handleRefresh = () => {
    loadPlayers();
  };

  const handleSearch = () => {
    if (currentPage === 1) {
      loadPlayers();
    } else {
      setCurrentPage(1);
    }
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setOnlineOnly(false);
    setSortBy('characterName');
    setSortOrder('asc');
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Players</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalPlayers?.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              All characters in database
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online Players</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats?.onlinePlayers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Currently connected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Parties</CardTitle>
            <UsersRound className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalParties.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Groups currently formed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Accounts</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalAccounts.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Registered accounts
            </p>
          </CardContent>
        </Card>
      </div>



      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Player Accounts</CardTitle>
          <CardDescription>
            Manage all player accounts and characters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AccountsDataTable
            data={players}
            loading={loading}
            onRefresh={handleRefresh}
            searchTerm={searchTerm}
            onSearchTermChange={setSearchTerm}
            sortBy={sortBy}
            onSortByChange={setSortBy}
            sortOrder={sortOrder}
            onSortOrderChange={setSortOrder}
            pageSize={pageSize}
            onPageSizeChange={setPageSize}
            onlineOnly={onlineOnly}
            onOnlineOnlyChange={setOnlineOnly}
            onSearch={handleSearch}
            onClearFilters={handleClearFilters}
          />
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages} ({stats?.totalPlayers} total players)
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1 || loading}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages || loading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
