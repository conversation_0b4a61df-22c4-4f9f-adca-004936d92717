"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  User,
  Heart,
  Eye,
  MapPin,
  MessageSquare,
  Ban,
  UserX,
  RefreshCw,
  Settings,
  AlertCircle,
  Pin,
} from "lucide-react";
import { toast } from "sonner";
import { formatNumber } from "@/lib/utils";
import { DetailedPlayerInfoFull } from "@/types/player";
import CharacterEquipment from "./character-equipment";

interface CharacterDetailsProps {
  characterId: string;
}


export function CharacterDetails({ characterId }: CharacterDetailsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [character, setCharacter] = useState<DetailedPlayerInfoFull | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  const loadCharacterDetails = useCallback(async () => {
    try {
      setLoading(true);
      setError("");

      // Get serverId and clusterId from URL params
      const serverId = searchParams.get("serverId");
      const clusterId = searchParams.get("clusterId");
      const characterName = characterId;

      if (!serverId || !clusterId) {
        toast.error("Missing serverId or clusterId parameters");
        return;
      }

      console.log(
        `Loading character details for: ${characterName} on server ${clusterId}-${serverId}`
      );

      // Call the API route
      const queryParams = new URLSearchParams({
        serverId,
        clusterId,
      });

      const response = await fetch(
        `/api/webadmin/account/player/${encodeURIComponent(
          characterName
        )}?${queryParams}`
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data = await response.json();

      if (data.success && data.player) {
        setCharacter(data.player);
        toast.success(`Character ${characterName} loaded successfully`);
      } else {
        const errorMsg = data.message || "Character not found";
        setError(errorMsg);
        toast.error(errorMsg);
      }
    } catch (error) {
      console.error("Error loading character details:", error);
      const errorMsg =
        error instanceof Error
          ? error.message
          : "Failed to load character details";
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setLoading(false);
    }
  }, [characterId, searchParams]);

  useEffect(() => {
    loadCharacterDetails();
  }, [loadCharacterDetails]);

  const handleAction = async (action: string) => {
    try {
      toast.info(`Executing ${action} for ${character?.characterName}`);
      // TODO: Implement actual actions
    } catch (_error) {
      console.log(_error)
      toast.error(`Failed to execute ${action}`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading character details...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <div className="flex items-center justify-center mb-4">
          <AlertCircle className="h-8 w-8 text-red-500" />
        </div>
        <p className="text-muted-foreground mb-2">Failed to load character</p>
        <p className="text-sm text-red-600 mb-4">{error}</p>
        <div className="flex gap-2 justify-center">
          <Button onClick={loadCharacterDetails} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  if (!character) {
    return (
      <div className="text-center p-8">
        <p className="text-muted-foreground">Character not found</p>
        <Button onClick={() => router.back()} className="mt-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Back Button and Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Accounts
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <User className="h-8 w-8" />
              {character.characterName}
            </h1>
            <p className="text-muted-foreground">
              Level {character.level} {character.job} •{" "}
              {character.isOnline ? "Online" : "Offline"}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAction("view")}
          >
            <Pin className="h-4 w-4 mr-2" />
            Move
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAction("message")}
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Message
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAction("kick")}
          >
            <UserX className="h-4 w-4 mr-2" />
            Kick
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAction("ban")}
          >
            <Ban className="h-4 w-4 mr-2" />
            Ban
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAction("settings")}
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6">
        {/* Left Column - Character Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-red-500" />
                Basic Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">HP</span>
                    <span className="font-medium text-red-500">
                      {character.hp.toLocaleString()} /{" "}
                      {character.maxHp.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">MP</span>
                    <span className="font-medium text-blue-500">
                      {character.mp.toLocaleString()} /{" "}
                      {character.maxMp.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">SP</span>
                    <span className="font-medium text-yellow-500">
                      {character.sp.toLocaleString()} /{" "}
                      {character.maxSp.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">EXP</span>
                    <span className="font-medium text-green-500">
                      {formatNumber(character.experience)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Level</span>
                    <span className="font-medium">{character.level}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Job</span>
                    <span className="font-medium">{character.jobName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Job Level
                    </span>
                    <span className="font-medium">{character.jobLevel}</span>
                  </div>
                  {/* Atk/Def/MAtk/Mdef,Evasion,Accuracy */}
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Attack
                    </span>
                    <span className="font-medium">{character.atk}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Defense
                    </span>
                    <span className="font-medium">{character.def}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Magic Attack
                    </span>
                    <span className="font-medium">{character.matk}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Magic Defense
                    </span>
                    <span className="font-medium">{character.mdef}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Evasion
                    </span>
                    <span className="font-medium">{character.eva}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Accuracy
                    </span>
                    <span className="font-medium">{character.acc}</span>
                  </div>
                </div>
                <div className="space-y-2"></div>
              </div>
              <Separator />
              <div className="grid grid-cols-1 gap-4">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Wu Xun</span>
                  <span className="font-medium">
                    {character.wuXun.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Qigong Point
                  </span>
                  <span className="font-medium">
                    {character.qigongPoint.toLocaleString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location & Social */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-blue-500" />
                Location & Social
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Map ID</span>
                  <span className="font-medium">{character.mapName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Position
                  </span>
                  <span className="font-medium">
                    ({character.posX}, {character.posY})
                  </span>
                </div>
              </div>
              <Separator />
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Server</span>
                  <span className="font-medium">
                    {character.clusterId}-{character.serverId}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Session ID
                  </span>
                  <span className="font-medium text-blue-600">
                    {character.sessionId}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    IP Address
                  </span>
                  <span className="font-medium text-gray-600">
                    {character.ipAddress}
                  </span>
                </div>
                {character.partyInfo && (
                  <>
                    <Separator />
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Party
                      </span>
                      <span className="font-medium text-purple-600">
                        {character.partyInfo.teamName}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Members
                      </span>
                      <span className="font-medium">
                        {character.partyInfo.memberCount}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Leader
                      </span>
                      <span
                        className={`font-medium ${
                          character.partyInfo.isLeader
                            ? "text-yellow-600"
                            : "text-gray-600"
                        }`}
                      >
                        {character.partyInfo.isLeader ? "Yes" : "No"}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Session & Activity Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-blue-500" />
                Session & Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Login Time
                  </span>
                  <span className="font-medium text-sm">
                    {new Date(character.loginTime).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Last Activity
                  </span>
                  <span className="font-medium text-sm">
                    {new Date(character.lastActivity).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Fight EXP
                  </span>
                  <span className="font-medium">
                    {character.fightExp.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">GM Mode</span>
                  <span
                    className={`font-medium ${
                      character.gmMode ? "text-red-600" : "text-green-600"
                    }`}
                  >
                    {character.gmMode ? "Enabled" : "Disabled"}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Equipment & Inventory */}
        <div className="lg:col-span-2 xl:col-span-3 2xl:col-span-4">
          <CharacterEquipment  {...character} />
        </div>
      </div>
    </div>
  );
}

