import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { DetailedPlayerInfoFull, ItemInfo, SkillInfo } from "@/types/player";
import OneItem from "./one-item";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Crown, Shield, Star, Zap } from "lucide-react";
import { useState } from "react";

// Equipment slot mapping - thường các game có index cố định cho từng slot
const EQUIPMENT_SLOTS = {
  ARMOR: 0,
  LEFT_GLOVE: 1,
  RIGHT_GLOVE: 2,
  WEAPON: 3,
  BOOTS: 4,
  INNER_ARMOR: 5,
  NECKLACE: 6,
  LEFT_EARRING: 7,
  RIGHT_EARRING: 8,
  LEFT_RING: 9,
  RIGHT_RING: 10,
  COSTUME: 11,
  SUB_WEAPON: 12,
  GUILD_ARMOR: 13,
  SPIRIT_BEAST: 14,
  PEARL: 15,
  DIVINE_BEAST: 16,
} as const;

interface EquipmentSlotProps {
  title: string;
  item?: ItemInfo | null; // ItemInfo from character.equipmentItems
  slotIndex?: number; // Index in equipmentItems array
  icon?: string;
  color?: string;
  empty?: boolean;
  disabled?: boolean; // For disabled slots in sub equipment
  type?: number; // 0 = equipment, 1 = inventory, 2 = shared storage, 3 = private storage
}

interface AntiAbility {
  abilityId: number;
  abilityName: string;
  currentLevel: number;
  maxLevel: number;
  points: number;
  abilityType: number;
}


interface SkillCardProps {
  skill: SkillInfo | AntiAbility;
  type: "skill" | "ability" | "anti-ability";
}

// Type guard functions
function isSkillInfo(skill: SkillInfo | AntiAbility): skill is SkillInfo {
  return 'skillId' in skill && 'skillName' in skill;
}

function isAntiAbility(skill: SkillInfo | AntiAbility): skill is AntiAbility {
  return 'abilityId' in skill && 'abilityName' in skill;
}

function SkillCard({ skill, type }: SkillCardProps) {
  const getTypeIcon = () => {
    switch (type) {
      case "skill":
        if (isSkillInfo(skill)) {
          return (
            <div
              className="h-8 w-8"
              style={{
                background: `url(https://one.chamthoi.com/mugong/${skill.skillId}.jpg) no-repeat center center`,
                backgroundSize: "cover",
              }}
            ></div>
          );
        }
        break;
      case "ability":
      case "anti-ability":
        if (isAntiAbility(skill)) {
          return (
            <div
              className="h-8 w-8"
              style={{
                background: `url(https://one.chamthoi.com/ability/${skill.abilityId}.jpg) no-repeat center center`,
                backgroundSize: "cover",
              }}
            ></div>
          );
        }
        break;
      default:
        return <Star className="h-4 w-4 text-gray-500" />;
    }
    return <Star className="h-4 w-4 text-gray-500" />;
  };

  // Safe property access using type guards
  // const skillId = isSkillInfo(skill) ? skill.skillId : skill.abilityId;
  const skillName = isSkillInfo(skill) ? skill.skillName : skill.abilityName;
  const currentLevel = skill.currentLevel;
  // const maxLevel = skill.maxLevel || 1;
  // const experience = skill.experience || 0;
  // const points = skill.points || 0;

  return (
 <div className="flex items-center gap-2 mb-2 relative h-8 w-8">
  {getTypeIcon()}
  <span
    className="text-md font-bold left-1 rounded-full absolute"
    style={{
      color: "white", // Chữ trắng
      opacity: 0.8, // Độ trong suốt nhẹ
      textShadow: "0 0 12px rgba(0, 0, 0, 0.9), 0 0 16px rgba(0, 0, 0, 0.7)", // Bóng đen, tăng độ lan tỏa
      zIndex: 10, // Đảm bảo nổi lên trên
    }}
    title={skillName}
  >
    {currentLevel}
  </span>
</div>
  );
}

function EquipmentSlot({
  title,
  item,
  slotIndex: slotIndex,
  icon,
  color,
  empty = false,
  disabled = false,
}: EquipmentSlotProps) {
  // If we have an item, use OneItem component
  if (item && slotIndex != null) {
    return <OneItem item={item} bagType={0} slotPosition={slotIndex} type="online" />
  }
  // Otherwise show empty slot
  return (
    <div
      className={`aspect-square border-2 border-dashed rounded flex items-center justify-center transition-colors max-w-16 max-h-16 w-full h-full ${
        disabled
          ? "border-gray-200 bg-gray-100 cursor-not-allowed opacity-50"
          : "border-gray-300 bg-gray-50 hover:bg-gray-100 cursor-pointer"
      }`}
      title={disabled ? `${title} (Disabled)` : title}
    >
      {!empty && icon && color && !disabled && (
        <div
          className={`w-full h-full bg-gradient-to-br ${color} rounded flex items-center justify-center`}
        >
          <span className="text-xs font-bold text-white">{icon}</span>
        </div>
      )}
      {disabled && (
        <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
          <span className="text-xs text-gray-400">×</span>
        </div>
      )}
    </div>
  );
}


export default  function CharacterEquipment( character: DetailedPlayerInfoFull | null) {
    const [activeTab, setActiveTab] = useState("inventory");
    const [equipmentTab, setEquipmentTab] = useState("main-equipment");
    const getEquipmentItem = (slotIndex: number,  type : 'wear' | 'wear2' | 'wear3' = 'wear') => {
      var bags = type === 'wear' ? character?.wearItems : type === 'wear2' ? character?.subWearItems : character?.thirdWearItems;
      if (!bags || !Array.isArray(bags))
        return null;
  
      // Try to find by slotIndex first
      let item = bags[slotIndex];
  
      // If not found and we have items, try to get by array index as fallback
      if (!item && bags[slotIndex]) {
        item = bags[slotIndex];
      }
  
      return item || undefined;
    };
    console.log(character)
  return <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
    {/* Equipment Section */}
    <div className="xl:col-span-1 gap-6 grid">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Equipment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            value={equipmentTab}
            onValueChange={setEquipmentTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="main-equipment">
                Main Equipment
              </TabsTrigger>
              <TabsTrigger value="sub-equipment">
                Sub Equipment
              </TabsTrigger>
              <TabsTrigger value="dragon-pearl">
                Dragon Pearl
              </TabsTrigger>
            </TabsList>

            {/* Tab 1: Main Equipment */}
            <TabsContent value="main-equipment" className="mt-6">
              <div className="space-y-3">
                {/* Row 1: Weapon, Left Earring, Necklace, Right Earring, Sub Weapon */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Weapon"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.WEAPON)}
                    icon="W"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.WEAPON}
                    color="from-red-500 to-red-600" />
                  <EquipmentSlot
                    title="Left Earring"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.LEFT_EARRING
                    )}
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.LEFT_EARRING}
                    icon="EL"
                    color="from-pink-400 to-pink-500" />
                  <EquipmentSlot
                    title="Necklace"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.NECKLACE}
                    item={getEquipmentItem(EQUIPMENT_SLOTS.NECKLACE)}
                    icon="N"
                    color="from-yellow-400 to-yellow-500" />
                  <EquipmentSlot
                    title="Right Earring"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.RIGHT_EARRING
                    )}
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.RIGHT_EARRING}
                    icon="ER"
                    color="from-pink-400 to-pink-500" />
                  <EquipmentSlot
                    title="Sub Weapon"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.SUB_WEAPON)}
                    icon="SW"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.SUB_WEAPON}
                    color="from-orange-500 to-orange-600" />
                </div>

                {/* Row 2: Inner Armor, Left Ring, Armor, Right Ring, Guild Armor */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Inner Armor"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.INNER_ARMOR)}
                    icon="IA"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.INNER_ARMOR}
                    color="from-blue-400 to-blue-500" />
                  <EquipmentSlot
                    title="Left Ring"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.LEFT_RING)}
                    icon="RL"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.LEFT_RING}
                    color="from-purple-400 to-purple-500" />
                  <EquipmentSlot
                    title="Armor"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.ARMOR)}
                    icon="A"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.ARMOR}
                    color="from-indigo-500 to-indigo-600" />
                  <EquipmentSlot
                    title="Right Ring"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.RIGHT_RING)}
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.RIGHT_RING}
                    icon="RR"
                    color="from-purple-400 to-purple-500" />
                  <EquipmentSlot
                    title="Guild Armor"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.GUILD_ARMOR)}
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.GUILD_ARMOR}
                    icon="GA"
                    color="from-green-500 to-green-600" />
                </div>

                {/* Row 3: Left Glove, Boots, Right Glove (centered) */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Left Glove"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.LEFT_GLOVE)}
                    icon="GL"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.LEFT_GLOVE}
                    color="from-teal-400 to-teal-500" />
                  <div></div>
                  <EquipmentSlot
                    title="Boots"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.BOOTS)}
                    icon="B"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.BOOTS}
                    color="from-amber-500 to-amber-600" />
                  <div></div>
                  <EquipmentSlot
                    title="Right Glove"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.RIGHT_GLOVE)}
                    icon="GR"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.RIGHT_GLOVE}
                    color="from-teal-400 to-teal-500" />
                </div>

                {/* Row 4: Costume, Pearl, Divine Beast, Spirit Beast */}
                <div className="grid grid-cols-4 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Costume"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.COSTUME)}
                    icon="C"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.COSTUME}
                    color="from-violet-500 to-violet-600" />
                  <EquipmentSlot
                    title="Pearl"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.PEARL)}
                    icon="P"
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.PEARL}
                    color="from-cyan-400 to-cyan-500" />
                  <EquipmentSlot
                    title="Divine Beast"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.DIVINE_BEAST
                    )}
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.DIVINE_BEAST}
                    icon="DB"
                    color="from-emerald-500 to-emerald-600" />
                  <EquipmentSlot
                    title="Spirit Beast"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.SPIRIT_BEAST
                    )}
                    type={0}
                    slotIndex={EQUIPMENT_SLOTS.SPIRIT_BEAST}
                    icon="SB"
                    color="from-lime-500 to-lime-600" />
                </div>
              </div>
            </TabsContent>

            {/* Tab 2: Sub Equipment (16 slots, disable SPIRIT_BEAST, PEARL, DIVINE_BEAST) */}
            <TabsContent value="sub-equipment" className="mt-6">
              <div className="space-y-3">
                {/* Row 1: Weapon, Inner Armor, Armor, Left Glove, Right Glove */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Sub Weapon"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.WEAPON , 'wear2')}
                    icon="W"
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.WEAPON}
                    color="from-red-500 to-red-600" />
                  <EquipmentSlot
                    title="Sub Inner Armor"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.INNER_ARMOR , 'wear2'
                    )}
                    icon="IA"
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.INNER_ARMOR}
                    color="from-blue-400 to-blue-500" />
                  <EquipmentSlot
                    title="Sub Armor"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.ARMOR , 'wear2')}
                    icon="A"
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.ARMOR}
                    color="from-indigo-500 to-indigo-600" />
                  <EquipmentSlot
                    title="Sub Left Glove"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.LEFT_GLOVE , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.LEFT_GLOVE}
                    icon="GL"
                    color="from-teal-400 to-teal-500" />
                  <EquipmentSlot
                    title="Sub Right Glove"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.RIGHT_GLOVE , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.RIGHT_GLOVE}
                    icon="GR"
                    color="from-teal-400 to-teal-500" />
                </div>

                {/* Row 2: Boots, Necklace, Left Earring, Right Earring, Left Ring */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Sub Boots"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.BOOTS , 'wear2')}
                    icon="B"
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.BOOTS}
                    color="from-amber-500 to-amber-600" />
                  <EquipmentSlot
                    title="Sub Necklace"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.NECKLACE , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.NECKLACE}
                    icon="N"
                    color="from-yellow-400 to-yellow-500" />
                  <EquipmentSlot
                    title="Sub Left Earring"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.LEFT_EARRING , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.LEFT_EARRING}
                    icon="EL"
                    color="from-pink-400 to-pink-500" />
                  <EquipmentSlot
                    title="Sub Right Earring"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.RIGHT_EARRING , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.RIGHT_EARRING}
                    icon="ER"
                    color="from-pink-400 to-pink-500" />
                  <EquipmentSlot
                    title="Sub Left Ring"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.LEFT_RING , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.LEFT_RING}
                    icon="RL"
                    color="from-purple-400 to-purple-500" />
                </div>

                {/* Row 3: Right Ring, Costume, Sub Weapon, Guild Armor, Spirit Beast (disabled) */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Sub Right Ring"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.RIGHT_RING , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.RIGHT_RING}
                    icon="RR"
                    color="from-purple-400 to-purple-500" />
                  <EquipmentSlot
                    title="Sub Costume"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.COSTUME , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.COSTUME}
                    icon="C"
                    color="from-violet-500 to-violet-600" />
                  <EquipmentSlot
                    title="Sub Sub Weapon"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.SUB_WEAPON , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.SUB_WEAPON}
                    icon="SW"
                    color="from-orange-500 to-orange-600" />
                  <EquipmentSlot
                    title="Sub Guild Armor"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.GUILD_ARMOR , 'wear2'
                    )}
                    type={5}
                    slotIndex={EQUIPMENT_SLOTS.GUILD_ARMOR}
                    icon="GA"
                    color="from-green-500 to-green-600" />
                  <EquipmentSlot title="Spirit Beast" disabled={true} />
                </div>

                {/* Row 4: Pearl (disabled), Divine Beast (disabled) */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot title="Pearl" disabled={true} />
                  <EquipmentSlot title="Divine Beast" disabled={true} />
                  <div></div>
                  <div></div>
                  <div></div>
                </div>
              </div>
            </TabsContent>

            {/* Tab 3: Dragon Pearl (only Weapon, Inner Armor, Armor, Gloves, Boots - disable others) */}
            <TabsContent value="dragon-pearl" className="mt-6">
              <div className="space-y-3">
                {/* Row 1: Weapon, Inner Armor, Armor, Left Glove, Right Glove */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Weapon Dragon Pearl"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.WEAPON , 'wear3')}
                    icon="WP"
                    type={6}
                    slotIndex={EQUIPMENT_SLOTS.WEAPON}
                    color="from-red-700 to-red-800" />
                  <EquipmentSlot
                    title="Inner Armor Dragon Pearl"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.INNER_ARMOR , 'wear3'
                    )}
                    icon="IAP"
                    type={6}
                    slotIndex={EQUIPMENT_SLOTS.INNER_ARMOR}
                    color="from-blue-700 to-blue-800" />
                  <EquipmentSlot
                    title="Armor Dragon Pearl"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.ARMOR , 'wear3')}
                    icon="AP"
                    type={6}
                    slotIndex={EQUIPMENT_SLOTS.ARMOR}
                    color="from-indigo-700 to-indigo-800" />
                  <EquipmentSlot
                    title="Left Glove Dragon Pearl"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.LEFT_GLOVE , 'wear3'
                    )}
                    icon="GLP"
                    type={6}
                    slotIndex={EQUIPMENT_SLOTS.LEFT_GLOVE}
                    color="from-teal-700 to-teal-800" />
                  <EquipmentSlot
                    title="Right Glove Dragon Pearl"
                    item={getEquipmentItem(
                      EQUIPMENT_SLOTS.RIGHT_GLOVE , 'wear3'
                    )}
                    icon="GRP"
                    type={6}
                    slotIndex={EQUIPMENT_SLOTS.RIGHT_GLOVE}
                    color="from-teal-700 to-teal-800" />
                </div>

                {/* Row 2: Boots and disabled slots */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot
                    title="Boots Dragon Pearl"
                    item={getEquipmentItem(EQUIPMENT_SLOTS.BOOTS , 'wear3')}
                    icon="BP"
                    type={6}
                    slotIndex={EQUIPMENT_SLOTS.BOOTS}
                    color="from-amber-700 to-amber-800" />
                  <EquipmentSlot title="Necklace" disabled={true} />
                  <EquipmentSlot title="Left Earring" disabled={true} />
                  <EquipmentSlot
                    title="Right Earring"
                    disabled={true} />
                  <EquipmentSlot title="Left Ring" disabled={true} />
                </div>

                {/* Row 3: More disabled slots */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot title="Right Ring" disabled={true} />
                  <EquipmentSlot title="Costume" disabled={true} />
                  <EquipmentSlot title="Sub Weapon" disabled={true} />
                  <EquipmentSlot title="Guild Armor" disabled={true} />
                  <EquipmentSlot title="Spirit Beast" disabled={true} />
                </div>

                {/* Row 4: Final disabled slots */}
                <div className="grid grid-cols-5 gap-3 justify-items-center">
                  <EquipmentSlot title="Pearl" disabled={true} />
                  <EquipmentSlot title="Divine Beast" disabled={true} />
                  <div></div>
                  <div></div>
                  <div></div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Skills, Abilities & Anti-Abilities Section */}
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-500" />
            Inventory & Storage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="inventory">Inventory</TabsTrigger>
              <TabsTrigger value="shared-storage">
                Shared Storage
              </TabsTrigger>
              <TabsTrigger value="private-storage">
                Private Storage
              </TabsTrigger>
            </TabsList>

            <TabsContent value="inventory" className="mt-6">
              <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10 gap-2 min-h-[400px] justify-items-center">
                {character?.inventoryItems.map(
                  (item: ItemInfo, i: number) => (
                    <OneItem key={i} item={item} bagType={1} slotPosition={i} type="online" /> // bagType = 1 for inventory
                  )
                )}
              </div>
            </TabsContent>

            <TabsContent value="shared-storage" className="mt-6">
              <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10 gap-2 min-h-[400px] justify-items-center">
                {character?.publicWarehouse.map(
                  (item: ItemInfo, i: number) => (
                    <OneItem key={i} item={item} bagType={2} slotPosition={i}  type="online"/> // bagType = 2 for shared storage
                  )
                )}
              </div>
            </TabsContent>

            <TabsContent value="private-storage" className="mt-6">
              <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10 gap-2 min-h-[400px] justify-items-center">
                {character?.personalWarehouse.map(
                  (item: ItemInfo, i: number) => (
                    <OneItem key={i} item={item} bagType={3} slotPosition={i} type="online" /> // bagType = 3 for private storage
                  )
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>

    {/* Inventory & Storage Section */}
    <div className="xl:col-span-1">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-purple-500" />
            Skills & Abilities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="skills" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="skills">Skills</TabsTrigger>
              <TabsTrigger value="abilities">Abilities</TabsTrigger>
              <TabsTrigger value="anti-abilities">
                Anti-Abilities
              </TabsTrigger>
            </TabsList>

            {/* Skills Tab */}
            <TabsContent value="skills" className="mt-6">
              <div className="flex flex-row gap-2 flex-wrap">
                {character?.skills?.map((skill: SkillInfo, index: number) => (
                  <SkillCard key={index} skill={skill} type="skill" />
                ))}
              </div>
            </TabsContent>

            {/* Abilities Tab */}
            <TabsContent value="abilities" className="mt-6">
              <div className="flex flex-row gap-2 flex-wrap">
                {character?.abilities?.map(
                  (ability: AntiAbility, index: number) => (
                    <SkillCard
                      key={index}
                      skill={ability}
                      type="ability" />
                  )
                )}
              </div>
              <div className="flex flex-row gap-2 flex-wrap">
                {character?.ascAbilities?.map(
                  (ability: AntiAbility, index: number) => (
                    <SkillCard
                      key={index}
                      skill={ability}
                      type="ability" />
                  )
                )}
              </div>
            </TabsContent>

            {/* Anti-Abilities Tab */}
            <TabsContent value="anti-abilities" className="mt-6">
              <div className="flex flex-row gap-2 flex-wrap">
                {character?.antiAbilities?.map(
                  (antiAbility: AntiAbility, index: number) => (
                    <SkillCard
                      key={index}
                      skill={antiAbility}
                      type="anti-ability" />
                  )
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  </div>;
}
