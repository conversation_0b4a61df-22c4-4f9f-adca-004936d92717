"use client";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useSearchParams, usePathname } from "next/navigation";
import { ItemInfo } from "@/types/player";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/lib/items";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Edit, Save, X } from "lucide-react";
import {
  BasicInfoEdit,
  ItemOptionEdit,
  MagicOptionsEdit,
  AdditionalInfoEdit,
  buildItemOption,
  buildMagicValue,
  isSpiritBeast,
  getSpiritBeastItemOptionFromMagic,
  itemTemplate,
} from "./itemEdit";


interface ItemDetailsPopupProps {
  item: ItemInfo;
  isOpen: boolean;
  onClose: (open: boolean) => void;
  // Additional props for item management
  characterName?: string;
  serverId?: number;
  clusterId?: number;
  channelId?: number;
  bagType?: number;
  slotPosition?: number;
  onSave: (editedItem: ItemInfo, originalItem: ItemInfo, characterInfo: any, actionType: string, deliveryMethod: string, createNewSeries: boolean) => Promise<boolean | undefined>;
  isSubmitting?: boolean;
}

const ItemDetailsPopup: React.FC<ItemDetailsPopupProps> = ({
  item,
  isOpen,
  onClose,
  serverId,
  clusterId,
  bagType = 1,
  slotPosition = 0,
  onSave,
  isSubmitting: externalIsSubmitting = false
}) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // Extract info from URL if not provided via props
  const getCharacterInfoFromUrl = () => {
    // Extract character name from pathname: /dashboard/accounts/character/[characterName]
    const pathParts = pathname.split('/');
    const charNameFromPath = pathParts[pathParts.length - 1] ;

    // Extract serverId and clusterId from search params
    const serverIdFromUrl = searchParams.get('serverId');
    const clusterIdFromUrl = searchParams.get('clusterId');

    return {
      characterName: charNameFromPath,
      serverId: serverId ?? (serverIdFromUrl ? parseInt(serverIdFromUrl) : 1),
      clusterId: clusterId ?? (clusterIdFromUrl ? parseInt(clusterIdFromUrl) : 1),
      channelId: serverId ?? (serverIdFromUrl ? parseInt(serverIdFromUrl) : 1)
    };
  };

  const characterInfo = getCharacterInfoFromUrl();

  const magicHandler = useMemo(() => new MagicHandler(), []);
  const [magicOptions, setMagicOptions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Edit mode states
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedItem, setEditedItem] = useState<ItemInfo>(item);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [createNewSeries, setCreateNewSeries] = useState(false);
  const [itemValidation, setItemValidation] = useState<{
    isValid: boolean;
    itemData: itemTemplate | null;
    isLoading: boolean;
  }>({ isValid: false, itemData: null, isLoading: false });

  // Item management states
  const [deliveryMethod, setDeliveryMethod] = useState<'mail' | 'direct'>('mail');
  const [actionType, setActionType] = useState<'create' | 'edit'>('create');
  const isSubmitting = externalIsSubmitting;

  // Local validateItemId function
  const validateItemIdLocal = async (itemId: number) => {
    if (itemId <= 0) {
      setItemValidation({ isValid: false, itemData: null, isLoading: false });
      return;
    }

    setItemValidation(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await fetch(`/api/template/game-items/${itemId}`);
      const result = await response.json();

      if (result.success && result.data) {
        setItemValidation({
          isValid: true,
          itemData: result.data,
          isLoading: false
        });
      } else {
        setItemValidation({
          isValid: false,
          itemData: null,
          isLoading: false
        });
      }
    } catch (error) {
      console.error('Error validating item:', error);
      setItemValidation({
        isValid: false,
        itemData: null,
        isLoading: false
      });
    }
  };

  // Reset edited item when item prop changes
  useEffect(() => {
    setEditedItem(item);
    setIsEditMode(false);
    setErrors({});
    setCreateNewSeries(false);
    setItemValidation({ isValid: true, itemData: null, isLoading: false });
  }, [item]);

  // Toggle edit mode
  const toggleEditMode = () => {
    if (isEditMode) {
      // Reset to original item when canceling edit
      setEditedItem(item);
      setErrors({});
      setCreateNewSeries(false);
    }
    setIsEditMode(!isEditMode);
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      console.warn("❌ Cannot save: Validation errors exist", errors);
      return;
    }

    try {
      const success = await onSave(
        editedItem,
        item,
        characterInfo,
        actionType,
        deliveryMethod,
        createNewSeries
      );

      if (success) {
        setIsEditMode(false);
        onClose(false);
      }
    } catch (error) {
      console.error("Error saving item:", error);
    }
  };



  // Handle itemOption changes
  const handleItemOptionChange = (enhancement: number, attributeType: number, attributeLevel: number) => {
    // Determine type based on fldReside2: 4 = weapon (type 1), others = type 2
    const type = itemValidation.itemData?.fldReside2 === 4 ? 1 : 2;
    attributeLevel = attributeLevel < 0 ? 0 : attributeLevel > 9 ? 9 : attributeLevel;
    attributeType = attributeType < 0 ? 0 : attributeType > 6 ? 6 : attributeType;
    const newItemOption = buildItemOption(enhancement, attributeType, attributeLevel, type);
    setEditedItem(prev => ({
      ...prev,
      itemOption: newItemOption
    }));
  };

  // Handle magic changes
  const handleMagicChange = (magicField: 'itemMagic1' | 'itemMagic2' | 'itemMagic3' | 'itemMagic4', type: number, value: number) => {
    // Check if this is a stone item
    const isStone = itemValidation.itemData?.fldReside2 === 16;

    const newMagicValue = buildMagicValue(type, value, isStone);

    setEditedItem(prev => {
      const newItem = {
        ...prev,
        [magicField]: newMagicValue
      };

      // Special handling for spirit beast itemMagic1 -> itemOption
      if (isSpiritBeast(prev.itemId) && magicField === 'itemMagic1') {
        const spiritBeastOption = getSpiritBeastItemOptionFromMagic(newMagicValue);
        newItem.itemOption = spiritBeastOption;
      }

      return newItem;
    });
  };

  // Handle input changes
  const handleInputChange = (field: keyof ItemInfo, value: number) => {
    // Validate the field
    const error = validateField(field, value);

    if (error) {
      setErrors(prev => ({
        ...prev,
        [field]: error
      }));
    } else {
      // Clear error if validation passes
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Update the value regardless of validation (for real-time feedback)
    setEditedItem(prev => ({
      ...prev,
      [field]: value
    }));

    // Special handling for itemId - validate against database
    if (field === 'itemId' && !error) {
      validateItemIdLocal(value);
    }
  };

  useEffect(() => {
    if (item.itemId) {
      validateItemIdLocal(item.itemId);
    }
  }, [item.itemId]);

  // Validation functions
  const validateField = (field: keyof ItemInfo, value: number): string | null => {
    switch (field) {
      case 'itemId':
        if (value <= 0) return 'Item ID phải lớn hơn 0';
        if (value > 9999999999) return 'Item ID quá lớn';
        break;
      case 'quantity':
        if (value < 0) return 'Số lượng không thể âm';
        if (value > 999999) return 'Số lượng quá lớn';
        break;
      case 'quality':
        if (value < 0 || value > 4) return 'Chất lượng phải từ 0-4';
        break;
      case 'mediumSoul':
        if (value < 0 || value > 51) return 'Medium Soul phải từ 0-51';
        break;
      case 'beast':
        if (value < 0) return 'Beast ID không thể âm';
        break;
      case 'lowSoul':
      case 'day1':
      case 'day2':
        if (value < 0) return `${field} không thể âm`;
        break;
      case 'globalId':
        if (value < 0) return 'Global ID không thể âm';
        break;
    }
    return null;
  };

  // Parse magic options using different methods based on value
  const getMagicOptions = useCallback(async () => {
    const options: string[] = [];
    const magics = [
      item.itemMagic1?.toString() || "0",
      item.itemMagic2?.toString() || "0",
      item.itemMagic3?.toString() || "0",
      item.itemMagic4?.toString() || "0",
    ];

    // Use getOptionStringEnhanced for better parsing
    try {
      const enhancedOptions = await magicHandler.getOptionStringEnhanced(
        magics[0],
        magics[1],
        magics[2],
        magics[3]
      );
      if (enhancedOptions) {
        return enhancedOptions
          .split("<br/>")
          .filter((opt) => opt.trim() !== "");
      }
    } catch (error) {
      console.warn(
        "Error parsing enhanced options, falling back to basic parsing:",
        error
      );
    }

    // Fallback to individual parsing
    for (const magic of [
      item.itemMagic1,
      item.itemMagic2,
      item.itemMagic3,
      item.itemMagic4,
    ]) {
      if (magic && magic !== 0) {
        const option = magicHandler.magic(magic);
        if (option) options.push(option);
      }
    }

    return options;
  }, [item.itemMagic1, item.itemMagic2, item.itemMagic3, item.itemMagic4, magicHandler]);

  // Get medium soul info
  const getMediumSoulInfo = () => {
    if (!item.mediumSoul || item.mediumSoul === 0) return null;
    return magicHandler.getMediumSoul(item.mediumSoul);
  };

  // Get quality info
  const getQualityText = () => {
    const qualityMap: { [key: number]: string } = {
      0: "Thông thường",
      1: "Cao cấp",
      2: "Quý hiếm",
      3: "Huyền thoại",
      4: "Thần thoại",
    };
    return qualityMap[item.quality] || "Không xác định";
  };

  // Get beast info with enhancement details
  const getBeastInfo = () => {
    if (!item.beast || item.beast === 0) return null;

    const beastType = magicHandler.getLinhThu(item.beast);
    if (!beastType) return null;

    // Check if this is a spirit beast with enhancement
    const isLinhThuItem =
      item.itemId >= 1000001170 && item.itemId <= 1000001181;
    if (isLinhThuItem && item.itemOption) {
      const enhancementLevel = item.quality || 0; // Assuming quality represents enhancement level
      const enhancementDetails = magicHandler.getLinhThuOption(
        enhancementLevel,
        item.itemId,
        item.itemOption
      );
      return {
        type: beastType,
        enhancement: enhancementDetails,
      };
    }

    return { type: beastType };
  };

  // Check if item is a cloak/cape that might have special options
  const getCloakInfo = () => {
    // This is a simplified check - you might need to adjust based on actual cloak item IDs
    const isCloakItem = item.itemId >= 1000000000 && item.itemId <= 1000001000; // Adjust range as needed
    if (isCloakItem && item.quality > 0) {
      return magicHandler.getAoChoangOption(item.quality);
    }
    return null;
  };

  const mediumSoulInfo = getMediumSoulInfo();
  const beastInfo = getBeastInfo();
  const cloakInfo = getCloakInfo();

  // Load magic options when popup opens
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      getMagicOptions().then((options) => {
        setMagicOptions(options);
        setIsLoading(false);
      });
    }
  }, [isOpen, item, getMagicOptions]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-xl lg:min-w-6xl max-h-[90vh] overflow-scroll p-0">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 text-white p-6 rounded-t-lg">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="text-lg font-semibold text-white">
                {isEditMode ? "Chỉnh sửa vật phẩm" : "Thông tin vật phẩm"}
              </DialogTitle>
              {isEditMode && (
                <div className="text-xs text-blue-100 mt-2">
                  <p><strong>Character:</strong> {characterInfo.characterName}</p>
                  <p><strong>Server:</strong> {characterInfo.serverId} | <strong>Cluster:</strong> {characterInfo.clusterId} | <strong>Channel:</strong> {characterInfo.channelId}</p>
                  <p><strong>Bag:</strong> {bagType} | <strong>Slot:</strong> {slotPosition}</p>
                </div>
              )}
              <Button
                onClick={toggleEditMode}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 transition-colors"
              >
                {isEditMode ? (
                  <>
                    <X className="w-4 h-4 mr-2" />
                    Hủy
                  </>
                ) : (
                  <>
                    <Edit className="w-4 h-4 mr-2" />
                    Chỉnh sửa
                  </>
                )}
              </Button>
            </div>
          </DialogHeader>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {/* Edit Mode Content */}
          {isEditMode  && (
            <>
              <BasicInfoEdit
                editedItem={editedItem}
                itemValidation={itemValidation}
                errors={errors}
                onItemChange={handleInputChange}
              />

              {/* Action Type and Delivery Method */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-3 flex items-center">
                  <span className="w-2 h-2 bg-yellow-500 dark:bg-yellow-400 rounded-full mr-2"></span>
                  Cài đặt gửi item
                </h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 block">
                      Hành động
                    </label>
                    <select
                      value={actionType}
                      onChange={(e) => setActionType(e.target.value as 'create' | 'edit')}
                      className="w-full p-2 border border-yellow-300 dark:border-yellow-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    >
                      <option value="create">Tạo mới</option>
                      <option value="edit">Chỉnh sửa slot hiện tại</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 block">
                      Phương thức gửi
                    </label>
                    <select
                      value={deliveryMethod}
                      onChange={(e) => setDeliveryMethod(e.target.value as 'mail' | 'direct')}
                      className="w-full p-2 border border-yellow-300 dark:border-yellow-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    >
                      <option value="mail">Gửi qua thư</option>
                      <option value="direct">Gửi trực tiếp vào túi</option>
                    </select>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Item Image and Basic Info - View Mode Only */}
          {!isEditMode && (
            <div className="flex items-center space-x-4 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <div
                className="w-20 h-20 rounded-lg border-2 border-gray-300 dark:border-gray-600 flex items-center justify-center shadow-md bg-white dark:bg-gray-700"
                style={{
                  backgroundImage: `url(https://one.chamthoi.com/item/${item.itemId}.jpg)`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              />
              <div className="flex-1">
                <p className="font-bold text-gray-900 dark:text-gray-100 text-lg">
                  ID: {item.itemId}
                </p>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    <span className="font-medium">Số lượng:</span> {item.quantity}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    <span className="font-medium">Chất lượng:</span>
                    <span
                      className={`ml-1 px-2 py-1 rounded text-xs font-medium ${
                        item.quality === 3
                          ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                          : item.quality === 2
                          ? "bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200"
                          : item.quality === 1
                          ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                          : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                      }`}
                    >
                      {getQualityText()}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          )}
          {/* ItemOption Section */}
          {isEditMode ? (
            <ItemOptionEdit
              editedItem={editedItem}
              itemValidation={itemValidation}
              errors={errors}
              onItemOptionChange={handleItemOptionChange}
            />
          ) : (
            item?.itemOption !== 0 && (
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800 gap-2 flex flex-col">
                <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-3 flex items-center">
                  <span className="w-2 h-2 bg-orange-500 dark:bg-orange-400 rounded-full mr-2"></span>
                    Option ID: {item.itemOption}
                </h4>
                <div className="text-sm text-orange-700 dark:text-orange-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-orange-100 dark:border-orange-700">
                  Cường hóa: {magicHandler.getCuongHoa(item.itemOption)}
                </div>
                {magicHandler.getThuocTinh(item.itemOption).id && (
                  <div className="text-sm text-orange-700 dark:text-orange-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-orange-100 dark:border-orange-700">
                    Thuộc tính: {magicHandler.getThuocTinh(item.itemOption).id} -{" "}
                    {magicHandler.getThuocTinh(item.itemOption).level}
                  </div>
                )}
              </div>
            )
          )}

          {/* Magic Options */}
          {isEditMode ? (
            <MagicOptionsEdit
              editedItem={editedItem}
              itemValidation={itemValidation}
              errors={errors}
              onMagicChange={handleMagicChange}
            />
          ) : (
            <div className="bg-gray-900 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                <span className="w-2 h-2 bg-gray-900 dark:bg-blue-400 rounded-full mr-2"></span>
                Thuộc tính ma pháp
              </h4>
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 dark:border-blue-400"></div>
                  <p className="text-sm text-blue-600 dark:text-blue-300">
                    Đang tải...
                  </p>
                </div>
              ) : magicOptions.length > 0 ? (
                <div className="space-y-2">
                  {magicOptions.map((option, index) => (
                    <div
                      key={index}
                      className="text-sm text-blue-700 dark:text-blue-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-blue-100 dark:border-blue-700"
                      dangerouslySetInnerHTML={{ __html: option }}
                    />
                  ))}
                </div>
              ) : (
                <p className="text-sm text-blue-600 dark:text-blue-300 italic">
                  Không có thuộc tính ma pháp
                </p>
              )}
            </div>
          )}

          {/* Additional Info Sections */}
          {isEditMode ? (
            <>
              <AdditionalInfoEdit
                editedItem={editedItem}
                errors={errors}
                onItemChange={handleInputChange}
                onCreateNewSeriesChange={setCreateNewSeries}
              />
            </>
          ) : (
            <>
              {/* Medium Soul */}
              {mediumSoulInfo && (
                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                  <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
                    <span className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full mr-2"></span>
                    Trung hồn
                  </h4>
                  <div className="text-sm text-purple-700 dark:text-purple-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-purple-100 dark:border-purple-700">
                    {typeof mediumSoulInfo === "object"
                      ? `${mediumSoulInfo.label} ${mediumSoulInfo.level}${
                          mediumSoulInfo.percent ? "%" : ""
                        }`
                      : mediumSoulInfo}
                  </div>
                </div>
              )}

              {/* Beast Info */}
              {beastInfo && (
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                  <h4 className="font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                    <span className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></span>
                    Linh thú
                  </h4>
                  <div className="text-sm text-green-700 dark:text-green-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-green-100 dark:border-green-700">
                    <p className="font-medium">
                      {typeof beastInfo === "object" ? beastInfo.type : beastInfo}
                    </p>
                    {typeof beastInfo === "object" && beastInfo.enhancement && (
                      <div
                        className="mt-2 text-xs text-green-600 dark:text-green-300 border-t border-green-200 dark:border-green-700 pt-2"
                        dangerouslySetInnerHTML={{ __html: beastInfo.enhancement }}
                      />
                    )}
                  </div>
                </div>
              )}

              {/* Cloak Enhancement Info */}
              {cloakInfo && (
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                  <h4 className="font-semibold text-red-900 dark:text-red-100 mb-3 flex items-center">
                    <span className="w-2 h-2 bg-red-500 dark:bg-red-400 rounded-full mr-2"></span>
                    Cường hóa áo choàng
                  </h4>
                  <div
                    className="text-sm text-red-700 dark:text-red-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-red-100 dark:border-red-700"
                    dangerouslySetInnerHTML={{ __html: cloakInfo }}
                  />
                </div>
              )}

              {/* Additional Info */}
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <span className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full mr-2"></span>
                  Thông tin khác
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="bg-white dark:bg-gray-700 px-3 py-2 rounded-md shadow-sm border border-gray-100 dark:border-gray-600">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Global ID:
                    </span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {item.globalId}
                    </span>
                  </div>
                  <div className="bg-white dark:bg-gray-700 px-3 py-2 rounded-md shadow-sm border border-gray-100 dark:border-gray-600">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Low Soul:
                    </span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {item.lowSoul}
                    </span>
                  </div>
                  <div className="bg-white dark:bg-gray-700 px-3 py-2 rounded-md shadow-sm border border-gray-100 dark:border-gray-600">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Day 1:
                    </span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {item.day1}
                    </span>
                  </div>
                  <div className="bg-white dark:bg-gray-700 px-3 py-2 rounded-md shadow-sm border border-gray-100 dark:border-gray-600">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Day 2:
                    </span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {item.day2}
                    </span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 px-6 pb-6 mt-4">
          {isEditMode ? (
            <>
              <Button
                onClick={toggleEditMode}
                variant="outline"
                className="border-gray-300 dark:border-gray-600"
              >
                <X className="w-4 h-4 mr-2" />
                Hủy
              </Button>
              <Button
                onClick={handleSaveChanges}
                disabled={Object.keys(errors).length > 0 || !itemValidation.isValid || itemValidation.isLoading || isSubmitting || !characterInfo.characterName.trim()}
                className={`${
                  Object.keys(errors).length > 0 || !itemValidation.isValid || itemValidation.isLoading || isSubmitting || !characterInfo.characterName.trim()
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 dark:from-green-600 dark:to-green-700 dark:hover:from-green-700 dark:hover:to-green-800"
                }`}
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting
                  ? "Đang gửi..."
                  : itemValidation.isLoading
                    ? "Đang kiểm tra..."
                    : !itemValidation.isValid
                      ? "Item không hợp lệ"
                      : !characterInfo.characterName.trim()
                        ? "Thiếu tên nhân vật"
                        : Object.keys(errors).length > 0
                          ? `Có ${Object.keys(errors).length} lỗi`
                          : actionType === 'create'
                            ? `Tạo & ${deliveryMethod === 'mail' ? 'Gửi mail' : 'Gửi trực tiếp'}`
                            : `Chỉnh sửa item`
                }
              </Button>
            </>
          ) : (
            <Button
              onClick={() => onClose(false)}
              className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 dark:from-gray-600 dark:to-gray-700 dark:hover:from-gray-700 dark:hover:to-gray-800"
            >
              Đóng
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ItemDetailsPopup;
