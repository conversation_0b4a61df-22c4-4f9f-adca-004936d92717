"use client";
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AdditionalInfoEditProps } from "./types";
import { MagicHandler } from "@/lib/items";
import { calculateDaysRemaining, generateDayValues } from "./utils";

const AdditionalInfoEdit: React.FC<AdditionalInfoEditProps> = ({
  editedItem,
  errors,
  onItemChange,
  onCreateNewSeriesChange
}) => {
  const magicHandler = new MagicHandler();
  const [createNewSeries, setCreateNewSeries] = useState(false);

  // Calculate current days remaining from day1 and day2
  const daysRemaining = calculateDaysRemaining(editedItem.day1, editedItem.day2);

  // Handler for days remaining change
  const handleDaysRemainingChange = (days: number) => {
    const { day1, day2 } = generateDayValues(days);
    onItemChange('day1', day1);
    onItemChange('day2', day2);
  };

  return (
    <div className="space-y-4">
      {/* Medium Soul */}
      <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
        <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
          <span className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full mr-2"></span>
          Chỉnh sửa Phụ hồn
        </h4>
        
        <div className="space-y-3">
          <div>
            <Label htmlFor="mediumSoul" className="text-sm font-medium text-purple-800 dark:text-purple-200">
              Medium Soul (0-51)
            </Label>
            <Input
              id="mediumSoul"
              type="number"
              value={editedItem.mediumSoul}
              onChange={(e) => onItemChange('mediumSoul', parseInt(e.target.value) || 0)}
              className={`mt-1 ${errors.mediumSoul ? 'border-red-500 focus:border-red-500' : ''}`}
              min="0"
              max="51"
            />
            {errors.mediumSoul && (
              <p className="text-red-500 text-xs mt-1">{errors.mediumSoul}</p>
            )}
          </div>
          {editedItem.mediumSoul !== 0 && (
            <div className="text-sm text-purple-700 dark:text-purple-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-purple-100 dark:border-purple-700">
              Preview: {(() => {
                const soulInfo = magicHandler.getMediumSoul(editedItem.mediumSoul);
                return typeof soulInfo === "object"
                  ? `${soulInfo.label} ${soulInfo.level}${soulInfo.percent ? "%" : ""}`
                  : soulInfo || "Không hợp lệ";
              })()}
            </div>
          )}
           <div>
            <Label htmlFor="lowSoul" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Low Soul
            </Label>
            <Input
              id="lowSoul"
              type="number"
              value={editedItem.lowSoul}
              onChange={(e) => onItemChange('lowSoul', parseInt(e.target.value) || 0)}
              className={`mt-1 ${errors.lowSoul ? 'border-red-500 focus:border-red-500' : ''}`}
              min="0"
            />
            {errors.lowSoul && (
              <p className="text-red-500 text-xs mt-1">{errors.lowSoul}</p>
            )}
          </div>
        </div>
      </div>

      {/* Beast Info */}
      <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
        <h4 className="font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
          <span className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></span>
          Chỉnh sửa Linh thú
        </h4>
        
        <div className="space-y-3">
          <div>
            <Label htmlFor="beast" className="text-sm font-medium text-green-800 dark:text-green-200">
              Beast ID
            </Label>
            <Input
              id="beast"
              type="number"
              value={editedItem.beast}
              onChange={(e) => onItemChange('beast', parseInt(e.target.value) || 0)}
              className={`mt-1 ${errors.beast ? 'border-red-500 focus:border-red-500' : ''}`}
              min="0"
            />
            {errors.beast && (
              <p className="text-red-500 text-xs mt-1">{errors.beast}</p>
            )}
          </div>
          {editedItem.beast !== 0 && (
            <div className="text-sm text-green-700 dark:text-green-200 bg-white dark:bg-gray-800 px-3 py-2 rounded-md shadow-sm border border-green-100 dark:border-green-700">
              Preview: {magicHandler.getLinhThu(editedItem.beast) || "Không hợp lệ"}
            </div>
          )}
        </div>
      </div>

      {/* Additional Info */}
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
          <span className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full mr-2"></span>
          Chỉnh sửa Thông tin khác
        </h4>
        
        <div className="grid grid-cols-2 gap-3">
          <div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="createNewSeries"
                checked={createNewSeries}
                onCheckedChange={(checked) => {
                  const newValue = checked as boolean;
                  setCreateNewSeries(newValue);
                  onCreateNewSeriesChange?.(newValue);
                }}
              />
              <Label htmlFor="createNewSeries" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Tạo series mới khi lưu
              </Label>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Nếu được chọn, hệ thống sẽ tạo series mới cho item này
            </p>
          </div>
          
         
          
          <div>
            <Label htmlFor="daysRemaining" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Số ngày còn lại
            </Label>
            <Input
              id="daysRemaining"
              type="number"
              value={daysRemaining}
              onChange={(e) => handleDaysRemainingChange(parseInt(e.target.value) || 0)}
              className="mt-1"
              min="0"
              max="365"
              placeholder="0 = không hết hạn"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Hệ thống sẽ tự động tính Day1 và Day2 dựa trên số ngày này
            </p>
          </div>

          <div className="text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-2 rounded">
            <p><strong>Debug info:</strong></p>
            <p>Day1: {editedItem.day1}</p>
            <p>Day2: {editedItem.day2}</p>
            <p>Ngày còn lại: {daysRemaining} ngày</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdditionalInfoEdit;
