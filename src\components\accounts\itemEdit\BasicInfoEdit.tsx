"use client";
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BasicInfoEditProps } from "./types";
import { getQualityOptions, getItemType, getItemTypeSpecificOptions } from "./utils";

const BasicInfoEdit: React.FC<BasicInfoEditProps> = ({
  editedItem,
  itemValidation,
  errors,
  onItemChange
}) => {
  return (
    <div className="space-y-4">
      {/* Item Validation Status */}
      <div className={`p-3 rounded-lg border ${
        itemValidation.isLoading 
          ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
          : itemValidation.isValid 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
      }`}>
        <div className="flex items-center space-x-2">
          <span className={`w-2 h-2 rounded-full ${
            itemValidation.isLoading 
              ? 'bg-yellow-500 dark:bg-yellow-400'
              : itemValidation.isValid 
                ? 'bg-green-500 dark:bg-green-400'
                : 'bg-red-500 dark:bg-red-400'
          }`}></span>
          <span className={`text-sm font-medium ${
            itemValidation.isLoading 
              ? 'text-yellow-800 dark:text-yellow-200'
              : itemValidation.isValid 
                ? 'text-green-800 dark:text-green-200'
                : 'text-red-800 dark:text-red-200'
          }`}>
            {itemValidation.isLoading 
              ? 'Đang kiểm tra item...'
              : itemValidation.isValid 
                ? `Item hợp lệ: ${itemValidation.itemData?.fldName || 'Unknown'}`
                : 'Item không tồn tại trong database'
            }
          </span>
        </div>
      </div>

      {/* Item Type Info */}
      {itemValidation.isValid && itemValidation.itemData && (
        <div className="bg-indigo-50 dark:bg-indigo-900/20 p-3 rounded-lg border border-indigo-200 dark:border-indigo-800">
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-indigo-500 dark:bg-indigo-400 rounded-full"></span>
            <span className="text-sm font-medium text-indigo-800 dark:text-indigo-200">
              Loại vật phẩm: {getItemTypeSpecificOptions(getItemType(editedItem.itemId, itemValidation.itemData?.fldReside2 || 0)).title}
            </span>
          </div>
          <p className="text-xs text-indigo-600 dark:text-indigo-300 mt-1">
            {getItemTypeSpecificOptions(getItemType(editedItem.itemId, itemValidation.itemData?.fldReside2 || 0)).description}
          </p>
        </div>
      )}

      {/* Basic Info Form */}
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
          <span className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full mr-2"></span>
          Thông tin cơ bản
        </h4>
        
        <div className="space-y-3 flex flex-row gap-4">
          <div
                className="w-20 h-20 rounded-lg border-2 border-gray-300 dark:border-gray-600 flex items-center justify-center shadow-md bg-white dark:bg-gray-700"
                style={{
                  backgroundImage: `url(https://one.chamthoi.com/item/${editedItem.itemId}.jpg)`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              />
          <div>
            <Label htmlFor="itemId" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Item ID
            </Label>
            <Input
              id="itemId"
              type="number"
              value={editedItem.itemId}
              onChange={(e) => onItemChange('itemId', parseInt(e.target.value) || 0)}
              className={`mt-1 ${errors.itemId ? 'border-red-500 focus:border-red-500' : ''}`}
              placeholder="Nhập Item ID"
            />
            {errors.itemId && (
              <p className="text-red-500 text-xs mt-1">{errors.itemId}</p>
            )}
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="quantity" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Số lượng
              </Label>
              <Input
                id="quantity"
                type="number"
                value={editedItem.quantity}
                onChange={(e) => onItemChange('quantity', parseInt(e.target.value) || 0)}
                className={`mt-1 ${errors.quantity ? 'border-red-500 focus:border-red-500' : ''}`}
                min="0"
              />
              {errors.quantity && (
                <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
              )}
            </div>
            
            <div>
              <Label htmlFor="quality" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Chất lượng
              </Label>
              <Select
                value={!editedItem.quality ? "0" : editedItem.quality.toString()}
                onValueChange={(value) => onItemChange('quality', parseInt(value))}
              >
                <SelectTrigger className={`mt-1 ${errors.quality ? 'border-red-500 focus:border-red-500' : ''}`}>
                  <SelectValue placeholder="Chọn chất lượng" />
                </SelectTrigger>
                <SelectContent>
                  {getQualityOptions().map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.quality && (
                <p className="text-red-500 text-xs mt-1">{errors.quality}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoEdit;
