"use client";
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ItemOptionEditProps } from "./types";
import { parseItemOption, getAttributeTypeOptions } from "./utils";

const ItemOptionEdit: React.FC<ItemOptionEditProps> = ({
  editedItem,
  itemValidation,
  errors,
  onItemOptionChange
}) => {
  const { enhancement, attributeType, attributeLevel } = parseItemOption(editedItem.itemOption);

  // Check if item supports attributes (fldReside2 = 1 or 4)
  const supportsAttributes = itemValidation.itemData?.fldReside2 === 1 || itemValidation.itemData?.fldReside2 === 4;
  const handleEnhancementChange = (newEnhancement: number) => {
    onItemOptionChange(newEnhancement, attributeType, attributeLevel);
  };

  const handleAttributeTypeChange = (newAttributeType: number) => {
    onItemOptionChange(enhancement, newAttributeType, attributeLevel);
  };

  const handleAttributeLevelChange = (newAttributeLevel: number) => {
    onItemOptionChange(enhancement, attributeType, newAttributeLevel);
  };

  return (
    <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
      <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-3 flex items-center">
        <span className="w-2 h-2 bg-orange-500 dark:bg-orange-400 rounded-full mr-2"></span>
        Chỉnh sửa Option
      </h4>
      
      <div className="space-y-3">
        <div className={`grid gap-3 ${supportsAttributes ? 'grid-cols-3' : 'grid-cols-1'}`}>
          <div>
            <Label htmlFor="enhancement" className="text-sm font-medium text-orange-800 dark:text-orange-200">
              Cường hóa (0-99)
            </Label>
            <Input
              id="enhancement"
              type="number"
              value={enhancement}
              onChange={(e) => handleEnhancementChange(parseInt(e.target.value) || 0)}
              className={`mt-1 ${errors.itemOption ? 'border-red-500 focus:border-red-500' : ''}`}
              min="0"
              max="99"
            />
          </div>

          {supportsAttributes && (
            <>
              <div>
                <Label htmlFor="attributeType" className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  Thuộc tính
                </Label>
                <Select
                  value={attributeType.toString()}
                  onValueChange={(value) => handleAttributeTypeChange(parseInt(value))}
                >
                  <SelectTrigger className={`mt-1 ${errors.itemOption ? 'border-red-500 focus:border-red-500' : ''}`}>
                    <SelectValue placeholder="Chọn thuộc tính" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Không có</SelectItem>
                    {getAttributeTypeOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="attributeLevel" className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  Cấp độ (0-9)
                </Label>
                <Input
                  id="attributeLevel"
                  type="number"
                  value={attributeLevel}
                  onChange={(e) => handleAttributeLevelChange(parseInt(e.target.value) || 0)}
                  className={`mt-1 ${errors.itemOption ? 'border-red-500 focus:border-red-500' : ''}`}
                  min="0"
                  max="9"
                />
              </div>
            </>
          )}
        </div>
        
        {errors.itemOption && (
          <p className="text-red-500 text-xs mt-1">{errors.itemOption}</p>
        )}
        
        <div className="text-xs text-orange-600 dark:text-orange-300 bg-white dark:bg-gray-800 px-3 py-2 rounded-md border border-orange-100 dark:border-orange-700">
          Preview Option ID: {editedItem.itemOption}
        </div>
      </div>
    </div>
  );
};

export default ItemOptionEdit;
