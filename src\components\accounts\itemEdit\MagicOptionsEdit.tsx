"use client";
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X } from "lucide-react";
import { MagicOptionsEditProps } from "./types";
import { 
  parseMagicValue, 
  getMagicTypeOptions, 
  getItemType, 
  isSpiritBeast, 
  getSpiritBeastOptions,
  getSpiritBeastItemOptionFromMagic 
} from "./utils";

const MagicOptionsEdit: React.FC<MagicOptionsEditProps> = ({
  editedItem,
  itemValidation,
  errors,
  onMagicChange
}) => {
  const [syncMagic, setSyncMagic] = useState(false);
  const isStone = itemValidation.itemData?.fldReside2 === 16;
  const itemType = getItemType(editedItem.itemId, itemValidation.itemData?.fldReside2!);

  // Handle magic change with sync option
  const handleMagicChange = (magicField: 'itemMagic1' | 'itemMagic2' | 'itemMagic3' | 'itemMagic4', type: number, value: number) => {
    if (syncMagic) {
      // Sync all magic fields
      onMagicChange('itemMagic1', type, value);
      onMagicChange('itemMagic2', type, value);
      onMagicChange('itemMagic3', type, value);
      onMagicChange('itemMagic4', type, value);
    } else {
      // Only change the specific field
      onMagicChange(magicField, type, value);
    }
  };

  // Clear specific magic field
  const handleClearMagic = (magicField: 'itemMagic1' | 'itemMagic2' | 'itemMagic3' | 'itemMagic4') => {
    onMagicChange(magicField, 0, 0);
  };

  // Special handler for spirit beast magic change
  const handleSpiritBeastMagicChange = (magicValue: number) => {
    // For spirit beast, we need to update both magic1 and itemOption
    // This is a special case that bypasses normal magic change logic
    onMagicChange('itemMagic1', 0, magicValue); // Pass the full value as 'value' parameter
  };

  const renderMagicSection = (
    magicField: 'itemMagic1' | 'itemMagic2' | 'itemMagic3' | 'itemMagic4',
    magicValue: number,
    title: string
  ) => {
    const { type, value } = parseMagicValue(magicValue, isStone);

    // Special handling for Spirit Beast Magic 1
    if (isSpiritBeast(editedItem.itemId) && magicField === 'itemMagic1') {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-md border border-blue-100 dark:border-blue-700">
          <Label className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 block">
            {title} (Hình ảnh thần thú)
          </Label>
          <div className="space-y-3">
            <Select
              value={magicValue.toString()}
              onValueChange={(value) => {
                const magicVal = parseInt(value);
                handleSpiritBeastMagicChange(magicVal);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn hình ảnh thần thú" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">Thông thường</SelectItem>
                {getSpiritBeastOptions().map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="text-xs text-blue-600 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
              ItemOption sẽ tự động cập nhật: {getSpiritBeastItemOptionFromMagic(magicValue)}
            </div>
          </div>
        </div>
      );
    }

    // Normal magic UI
    return (
      <div className="bg-white dark:bg-gray-800 p-3 rounded-md border border-blue-100 dark:border-blue-700">
        <div className="flex items-center justify-between mb-2">
          <Label className="text-sm font-medium text-blue-800 dark:text-blue-200">
            {title}
          </Label>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => handleClearMagic(magicField)}
            className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        <div className="grid grid-cols-2 gap-3">
          <Select
            value={type.toString()}
            onValueChange={(val) => {
              const newType = parseInt(val);
              handleMagicChange(magicField, newType, value);
            }}
          >
            <SelectTrigger className={errors[magicField] ? 'border-red-500 focus:border-red-500' : ''}>
              <SelectValue placeholder="Chọn loại magic" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">Không có</SelectItem>
              {getMagicTypeOptions(itemType).map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Input
            type="number"
            value={value}
            onChange={(e) => {
              const newValue = parseInt(e.target.value) || 0;
              handleMagicChange(magicField, type, newValue);
            }}
            className={errors[magicField] ? 'border-red-500 focus:border-red-500' : ''}
            placeholder="Giá trị"
            min="0"
          />
        </div>
        {errors[magicField] && (
          <p className="text-red-500 text-xs mt-1">{errors[magicField]}</p>
        )}
        {magicValue !== 0 && (
          <div className="mt-2 text-xs text-blue-600 dark:text-blue-300">
            Preview: {magicValue}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
      <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
        <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mr-2"></span>
        Chỉnh sửa Thuộc tính
      </h4>

      {/* Sync Magic Checkbox */}
      <div className="flex items-center space-x-2 mb-4">
        <Checkbox
          id="syncMagic"
          checked={syncMagic}
          onCheckedChange={(checked) => setSyncMagic(checked as boolean)}
        />
        <Label htmlFor="syncMagic" className="text-sm text-blue-800 dark:text-blue-200">
          Đồng bộ tất cả magic khi thay đổi
        </Label>
      </div>

      <div className="space-y-4">
        {renderMagicSection('itemMagic1', editedItem.itemMagic1, 'Magic 1')}
        {renderMagicSection('itemMagic2', editedItem.itemMagic2, 'Magic 2')}
        {renderMagicSection('itemMagic3', editedItem.itemMagic3, 'Magic 3')}
        {renderMagicSection('itemMagic4', editedItem.itemMagic4, 'Magic 4')}
      </div>
    </div>
  );
};

export default MagicOptionsEdit;
