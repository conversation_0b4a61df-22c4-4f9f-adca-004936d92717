import { ItemInfo } from "@/types/player";
import { tblXwwlItem } from '@/../drizzle/schema';
export type itemTemplate = typeof tblXwwlItem.$inferSelect

export interface ItemValidation {
  isValid: boolean;
  itemData: itemTemplate | null;
  isLoading: boolean;
}

export interface ItemEditProps {
  editedItem: ItemInfo;
  originalItem: ItemInfo;
  itemValidation: ItemValidation;
  errors: Record<string, string>;
  onItemChange: (field: keyof ItemInfo, value: number) => void;
  onItemOptionChange: (enhancement: number, attributeType: number, attributeLevel: number) => void;
  onMagicChange: (magicField: 'itemMagic1' | 'itemMagic2' | 'itemMagic3' | 'itemMagic4', type: number, value: number) => void;
}

export interface BasicInfoEditProps {
  editedItem: ItemInfo;
  itemValidation: ItemValidation;
  errors: Record<string, string>;
  onItemChange: (field: keyof ItemInfo, value: number) => void;
}

export interface ItemOptionEditProps {
  editedItem: ItemInfo;
  itemValidation: ItemValidation;
  errors: Record<string, string>;
  onItemOptionChange: (enhancement: number, attributeType: number, attributeLevel: number) => void;
}

export interface MagicOptionsEditProps {
  editedItem: ItemInfo;
  itemValidation: ItemValidation;
  errors: Record<string, string>;
  onMagicChange: (magicField: 'itemMagic1' | 'itemMagic2' | 'itemMagic3' | 'itemMagic4', type: number, value: number) => void;
}

export interface AdditionalInfoEditProps {
  editedItem: ItemInfo;
  errors: Record<string, string>;
  onItemChange: (field: keyof ItemInfo, value: number) => void;
  onCreateNewSeriesChange?: (createNew: boolean) => void;
}

export type ItemType = 
  | 'weapon' 
  | 'armor' 
  | 'gloves' 
  | 'boots' 
  | 'inner_armor' 
  | 'necklace' 
  | 'earring' 
  | 'ring' 
  | 'cloak' 
  | 'arrow' 
  | 'guild_armor' 
  | 'pet' 
  | 'spirit_beast' 
  | 'stone' 
  | 'fire_dragon_weapon' 
  | 'fire_dragon_armor' 
  | 'fire_dragon_inner' 
  | 'fire_dragon_gloves' 
  | 'fire_dragon_boots' 
  | 'normal';

export interface ItemTypeInfo {
  title: string;
  color: string;
  specialMagicTypes: number[];
  description: string;
}

export interface MagicOption {
  value: number;
  label: string;
  category?: string;
}

export interface QualityOption {
  value: number;
  label: string;
}

export interface AttributeOption {
  value: number;
  label: string;
}

export interface SpiritBeastOption {
  value: number;
  label: string;
}
