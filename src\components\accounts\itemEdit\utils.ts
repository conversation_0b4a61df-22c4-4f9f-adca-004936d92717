import { ItemType, ItemTypeInfo, MagicOption, QualityOption, AttributeOption, SpiritBeastOption } from './types';

// Parse itemOption to get enhancement and attribute
export const parseItemOption = (itemOption: number) => {
  if (itemOption === 0) return { enhancement: 0, attributeType: 0, attributeLevel: 0 };

  let enhancement = 0;
  let attributeType = 0;
  let attributeLevel = 0;

  // Cường hóa ở 2 số cuối
  enhancement = itemOption % 100;

  // Kiểm tra có thuộc tính không (>= 1 tỷ)
  if (itemOption >= 1_000_000_000) {
    // attributeType ở hàng ngàn
    attributeType = Math.floor((itemOption % 10_000) / 1_000);
    // attributeLevel ở hàng trăm
    attributeLevel = Math.floor((itemOption % 1_000) / 100);
  }

  return { enhancement, attributeType, attributeLevel };
};

// Build itemOption from enhancement and attribute
// Vu khi la 1, con lai la 2
// enhancement là cường hóa, attributeType là loại thuộc tính, attributeLevel là cấp độ thuộc tính, type là loại item
export const buildItemOption = (
  enhancement: number,
  attributeType: number,
  attributeLevel: number,
  type: number = 2
) => {
  // Base option = 0
  let option = 0;

  // Cường hóa
  if (enhancement > 0) {
    // type ở hàng chục triệu
    option += type * 10_000_000;
    // enhancement ở 2 số cuối
    option += enhancement % 100;
  }

  // Thuộc tính
  if (attributeType > 0 || attributeLevel > 0) {
    // Base option thuộc tính
    option += 1_000_000_000;
    // attributeType ở hàng ngàn
    option += attributeType * 1_000;
    // attributeLevel ở hàng trăm
    option += attributeLevel * 100;
  }

  return option;
};

// Parse magic value to get type and value
export const parseMagicValue = (magic: number, isStone: boolean = false) => {
  if (magic === 0) return { type: 0, value: 0 };
  
  if (isStone) {
    // Đá có ít số 0 hơn - chỉ chia cho 1000 thay vì 100000
    const type = Math.floor(magic / 100000);
    const value = magic - type * 100000;
    return { type, value };
  }
  
  const type = Math.floor(magic / 10000000);
  const value = magic - type * 10000000;
  return { type, value };
};

// Build magic value from type and value
export const buildMagicValue = (type: number, value: number, isStone: boolean = false) => {
  if (type === 0) return 0;

  if (isStone) {
    // Đá có ít số 0 hơn
    return type * 100000 + value;
  }

  return type * 10000000 + value;
};

// Detect item type based on fldReside2 from database
// Day1/Day2 helper functions
export const calculateDaysRemaining = (day1: number, day2: number): number => {
  if (day1 === 0 || day2 === 0) return 0;

  // Base time: 1970-01-01 08:00:00 (UTC+8)
  const baseTime = new Date(1970, 0, 1, 8, 0, 0);
  const currentTime = new Date();
  const currentSeconds = Math.floor((currentTime.getTime() - baseTime.getTime()) / 1000);

  // If Day1 > Day2, item expires when current time > Day1
  if (day1 > day2) {
    const remainingSeconds = day1 - currentSeconds;
    return Math.max(0, Math.ceil(remainingSeconds / (24 * 60 * 60))); // Convert to days
  }

  return 0; // Item doesn't expire or already expired
};

export const generateDayValues = (daysRemaining: number): { day1: number, day2: number } => {
  if (daysRemaining <= 0) {
    return { day1: 0, day2: 0 };
  }

  // Base time: 1970-01-01 08:00:00 (UTC+8)
  const baseTime = new Date(1970, 0, 1, 8, 0, 0);
  const currentTime = new Date();
  const currentSeconds = Math.floor((currentTime.getTime() - baseTime.getTime()) / 1000);

  // Day1 = current time + days remaining (in seconds)
  const day1 = currentSeconds + (daysRemaining * 24 * 60 * 60);

  // Day2 = current time (so Day1 > Day2 for expiration logic)
  const day2 = currentSeconds;

  return { day1, day2 };
};

export const getItemType = (itemId: number, fldReside2?: number): ItemType => {
  // Special cases based on itemId
  if (itemId >= 1000001170 && itemId <= 1000001175) {
    return 'spirit_beast'; // Thần thú
  }
  
  if (itemId >= 1000001176 && itemId <= 1000001181) {
    return 'pet'; // Linh thú
  }
  
  // Use fldReside2 if available
  if (fldReside2 !== undefined) {
    switch (fldReside2) {
      case 1: return 'armor'; // áo
      case 2: return 'gloves'; // tay
      case 4: return 'weapon'; // vũ khí
      case 5: return 'boots'; // giầy
      case 6: return 'inner_armor'; // nội giáp
      case 7: return 'necklace'; // dây chuyền
      case 8: return 'earring'; // khuyên tai
      case 10: return 'ring'; // nhẫn
      case 12: return 'cloak'; // áo choàng costume
      case 13: return 'arrow'; // mũi tên
      case 14: return 'guild_armor'; // áo bang
      case 15: return 'pet'; // linh thú
      case 16: return 'stone'; // đá
      case 32: return 'fire_dragon_weapon'; // hỏa long châu vũ khí
      case 33: return 'fire_dragon_armor'; // hỏa long châu áo
      case 34: return 'fire_dragon_inner'; // hỏa long châu giáp
      case 35: return 'fire_dragon_gloves'; // hỏa long châu tay
      case 36: return 'fire_dragon_boots'; // hỏa long châu giày
      default: return 'normal';
    }
  }
  
  return 'normal';
};

// Get item type specific options
export const getItemTypeSpecificOptions = (itemType: ItemType): ItemTypeInfo => {
  switch (itemType) {
    case 'weapon':
    case 'fire_dragon_weapon':
      return {
        title: itemType === 'fire_dragon_weapon' ? 'Hỏa Long Châu Vũ Khí' : 'Vũ khí',
        color: 'red',
        specialMagicTypes: [1, 7, 8], // Attack related magic types
        description: 'Vũ khí có thể có các thuộc tính tấn công đặc biệt'
      };
    case 'armor':
    case 'fire_dragon_armor':
      return {
        title: itemType === 'fire_dragon_armor' ? 'Hỏa Long Châu Áo' : 'Áo',
        color: 'blue',
        specialMagicTypes: [2, 3, 11], // Defense related magic types
        description: 'Áo chủ yếu tăng phòng thủ và sinh mệnh'
      };
    case 'inner_armor':
    case 'fire_dragon_inner':
      return {
        title: itemType === 'fire_dragon_inner' ? 'Hỏa Long Châu Giáp' : 'Nội giáp',
        color: 'blue',
        specialMagicTypes: [2, 3, 4], // Defense and internal power
        description: 'Nội giáp tăng phòng thủ và nội công'
      };
    case 'gloves':
    case 'fire_dragon_gloves':
      return {
        title: itemType === 'fire_dragon_gloves' ? 'Hỏa Long Châu Tay' : 'Găng tay',
        color: 'green',
        specialMagicTypes: [1, 5, 6], // Attack, accuracy, evasion
        description: 'Găng tay tăng tấn công và độ chính xác'
      };
    case 'boots':
    case 'fire_dragon_boots':
      return {
        title: itemType === 'fire_dragon_boots' ? 'Hỏa Long Châu Giày' : 'Giày',
        color: 'brown',
        specialMagicTypes: [6, 3], // Evasion and HP
        description: 'Giày tăng né tránh và sinh mệnh'
      };
    case 'necklace':
      return {
        title: 'Dây chuyền',
        color: 'yellow',
        specialMagicTypes: [1, 4, 5, 6], // Basic stats
        description: 'Dây chuyền tăng các thuộc tính cơ bản'
      };
    case 'earring':
      return {
        title: 'Khuyên tai',
        color: 'pink',
        specialMagicTypes: [4, 5, 6], // Internal stats
        description: 'Khuyên tai tăng nội công và độ chính xác'
      };
    case 'ring':
      return {
        title: 'Nhẫn',
        color: 'purple',
        specialMagicTypes: [1, 2, 4], // Balanced stats
        description: 'Nhẫn tăng các thuộc tính cân bằng'
      };
    case 'cloak':
      return {
        title: 'Áo choàng',
        color: 'purple',
        specialMagicTypes: [9, 12, 13, 15], // Special effects
        description: 'Áo choàng có các hiệu ứng đặc biệt'
      };
    case 'arrow':
      return {
        title: 'Mũi tên',
        color: 'orange',
        specialMagicTypes: [1, 7], // Attack related
        description: 'Mũi tên tăng sát thương'
      };
    case 'guild_armor':
      return {
        title: 'Áo bang',
        color: 'gold',
        specialMagicTypes: [2, 3, 12], // Defense and special
        description: 'Áo bang có thuộc tính đặc biệt cho bang hội'
      };
    case 'pet':
      return {
        title: 'Linh thú',
        color: 'green',
        specialMagicTypes: [],
        description: 'Linh thú có thể được cường hóa'
      };
    case 'spirit_beast':
      return {
        title: 'Thần thú',
        color: 'gold',
        specialMagicTypes: [],
        description: 'Thần thú có thuộc tính đặc biệt và hình ảnh'
      };
    case 'stone':
      return {
        title: 'Đá',
        color: 'gray',
        specialMagicTypes: [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 15], // All types for stones
        description: 'Đá có thể mang nhiều loại thuộc tính khác nhau'
      };
    default:
      return {
        title: 'Vật phẩm thông thường',
        color: 'gray',
        specialMagicTypes: [],
        description: 'Vật phẩm thông thường'
      };
  }
};

// Helper function to get magic type options
export const getMagicTypeOptions = (itemType?: ItemType): MagicOption[] => {
  const allOptions: MagicOption[] = [
    { value: 1, label: "Công lực", category: "basic" },
    { value: 2, label: "Lực phòng ngự", category: "basic" },
    { value: 3, label: "Sinh mệnh", category: "basic" },
    { value: 4, label: "Nội công", category: "basic" },
    { value: 5, label: "Chính xác", category: "basic" },
    { value: 6, label: "Né tránh", category: "basic" },
    { value: 7, label: "Công lực võ công", category: "weapon" },
    { value: 8, label: "Khí công", category: "weapon" },
    { value: 9, label: "Tỉ lệ may mắn", category: "accessory" },
    { value: 11, label: "Phòng Ngự võ công", category: "armor" },
    { value: 12, label: "Tỉ lệ nhận tiền", category: "accessory" },
    { value: 13, label: "Giảm tổn thất EXP", category: "accessory" },
    { value: 15, label: "Tăng điểm kinh nghiệm", category: "accessory" }
  ];

  if (!itemType) {
    return allOptions.map(opt => ({ value: opt.value, label: opt.label }));
  }

  const typeSpecific = getItemTypeSpecificOptions(itemType);
  const recommendedOptions = allOptions.filter(opt => 
    opt.category === "basic" || typeSpecific.specialMagicTypes.includes(opt.value)
  );

  return allOptions.map(opt => ({
    value: opt.value,
    label: opt.label + (recommendedOptions.includes(opt) ? " ⭐" : "")
  }));
};

// Helper function to get attribute type options
export const getAttributeTypeOptions = (): AttributeOption[] => [
  { value: 1, label: "Thuộc tính Hoả" },
  { value: 2, label: "Thuộc tính Thuỷ" },
  { value: 3, label: "Thuộc tính Phong" },
  { value: 4, label: "Thuộc tính Nội" },
  { value: 5, label: "Thuộc tính Ngoại" },
  { value: 6, label: "Thuộc tính Độc" }
];

// Helper function to get quality options
export const getQualityOptions = (): QualityOption[] => [
  { value: 0, label: "Thông thường" },
  { value: 1, label: "Cao cấp" },
  { value: 2, label: "Quý hiếm" },
  { value: 3, label: "Huyền thoại" },
  { value: 4, label: "Thần thoại" }
];

// Spirit Beast helper functions
export const getSpiritBeastOptions = (): SpiritBeastOption[] => [
  { value: 220000001, label: "Cao cấp" },
  { value: 220000002, label: "Quý hiếm" },
  { value: 220000003, label: "Huyền thoại" },
  { value: 220000004, label: "Thần 1" },
  { value: 220000005, label: "Thần 2" },
  { value: 220000006, label: "Thần 3" },
  { value: 220000007, label: "Thần 4" }
];

export const getSpiritBeastOptionFromMagic = (magic: number) => {
  const options = getSpiritBeastOptions();
  return options.find(opt => opt.value === magic) || { value: 0, label: "Thông thường" };
};

export const getSpiritBeastItemOptionFromMagic = (magic: number) => {
  // Số cuối của itemMagic1 sẽ được thêm vào itemOption
  if (magic >= 220000001 && magic <= 220000007) {
    return magic - 220000000; // Lấy số cuối (1-7)
  }
  return 0;
};

export const isSpiritBeast = (itemId: number) => {
  return itemId >= 1000001170 && itemId <= 1000001175;
};

// Validate itemId against database
export const validateItemId = async (itemId: number) => {
  if (itemId <= 0) {
    return { isValid: false, itemData: null, isLoading: false };
  }

  try {
    const response = await fetch(`/api/template/game-items/${itemId}`);
    const result = await response.json();

    if (result.success && result.data) {
      return {
        isValid: true,
        itemData: result.data,
        isLoading: false
      };
    } else {
      return {
        isValid: false,
        itemData: null,
        isLoading: false
      };
    }
  } catch (error) {
    console.error('Error validating item:', error);
    return {
      isValid: false,
      itemData: null,
      isLoading: false
    };
  }
};
