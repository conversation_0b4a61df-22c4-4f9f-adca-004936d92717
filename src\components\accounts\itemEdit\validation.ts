import { ItemInfo } from "@/types/player";

// Validation functions
export const validateField = (field: keyof ItemInfo, value: number): string | null => {
  switch (field) {
    case 'itemId':
      if (value <= 0) return 'Item ID phải lớn hơn 0';
      if (value > 9999999999) return 'Item ID quá lớn';
      break;
    case 'quantity':
      if (value <= 0) return 'Số lượng phải lớn hơn 0';
      if (value > 999999) return 'Số lượng quá lớn';
      break;
    case 'quality':
      if (value < 0 || value > 4) return 'Chất lượng phải từ 0-4';
      break;
    case 'mediumSoul':
      if (value < 0 || value > 51) return 'Medium Soul phải từ 0-51';
      break;
    case 'beast':
      if (value < 0) return 'Beast ID không thể âm';
      break;
    case 'lowSoul':
      if (value < 0 || value > 10) return 'Low Soul phải từ 0-10';
      break;
    case 'globalId':
      if (value < 0) return 'Global ID không thể âm';
      break;
  }
  return null;
};

export const validateItemOption = (enhancement: number, attributeType: number, attributeLevel: number): string | null => {
  if (enhancement < 0 || enhancement > 99) return 'Cường hóa phải từ 0-99';
  if (attributeType < 0 || attributeType > 6) return 'Loại thuộc tính phải từ 0-6';
  if (attributeLevel < 0 || attributeLevel > 9) return 'Cấp độ thuộc tính phải từ 0-9';
  return null;
};

export const validateMagicValue = (type: number, value: number): string | null => {
  if (type < 0 || type > 15) return 'Loại magic không hợp lệ';
  if (value < 0) return 'Giá trị magic không thể âm';
  if (value > 99999) return 'Giá trị magic quá lớn';
  return null;
};

// Hook for managing validation state
export const useValidation = () => {
  const validateAll = (item: ItemInfo): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    // Validate basic fields
    Object.keys(item).forEach(key => {
      const field = key as keyof ItemInfo;
      const value = item[field];
      if (typeof value === 'number') {
        const error = validateField(field, value);
        if (error) {
          errors[field] = error;
        }
      }
    });
    
    return errors;
  };

  return {
    validateField,
    validateItemOption,
    validateMagicValue,
    validateAll
  };
};
