import { ItemInfo } from "@/types/player";
import React, { useState } from "react";
import ItemDetailsPopup from "./item-details-popup";
import { toast } from "sonner";

const OneItem = ({ item , bagType = 0, slotPosition = 0 , type }: { item: ItemInfo; bagType: number; slotPosition: number; type: 'online' | 'offline' }) => {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleItemClick = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = (open: boolean) => {
    setIsPopupOpen(open);
  };

   const handleOfflineEditItem = async (editedItem: ItemInfo, originalItem: ItemInfo, characterInfo: any, actionType: string, deliveryMethod: string, createNewSeries: boolean): Promise<boolean | undefined> => {
      toast.info("<PERSON>ứ<PERSON> năng này chưa được hỗ trợ");
      return false;
   };
  // Handle save item changes
  const handleSendItem = async (editedItem: ItemInfo, originalItem: ItemInfo, characterInfo: any, actionType: string, deliveryMethod: string, createNewSeries: boolean): Promise<boolean | undefined> => {
    // Check if any changes were made
    const changes = Object.keys(editedItem).reduce((acc, key) => {
      const k = key as keyof ItemInfo;
      if (editedItem[k] !== originalItem[k]) {
        acc[k] = { from: originalItem[k], to: editedItem[k] };
      }
      return acc;
    }, {} as Record<string, any>);

    if (Object.keys(changes).length === 0) {
      console.log("ℹ️ No changes made to item");
      setIsPopupOpen(false);
      return;
    }

    if (!characterInfo.characterName.trim()) {
      toast.error("Tên nhân vật không được để trống");
      return;
    }

    setIsSubmitting(true);

    try {
      const requestData = {
        characterName: characterInfo.characterName,
        serverId: characterInfo.serverId,
        clusterId: characterInfo.clusterId,
        channelId: characterInfo.serverId,
        actionType: actionType,
        deliveryMethod: deliveryMethod,
        bagType: actionType === 'edit' ? bagType : undefined,
        slotPosition: actionType === 'edit' ? slotPosition : undefined,
        createNewSeries: createNewSeries,
        itemInfo: {
          itemId: editedItem.itemId,
          quantity: editedItem.quantity,
          magic0: editedItem.itemOption,
          magic1: editedItem.itemMagic1,
          magic2: editedItem.itemMagic2,
          magic3: editedItem.itemMagic3,
          magic4: editedItem.itemMagic4,
          lowSoul: editedItem.lowSoul,
          mediumSoul: editedItem.mediumSoul,
          tuLinh: editedItem.beast,
          day1: editedItem.day1,
          day2: editedItem.day2,
          tienHoa: editedItem.quality,
        }
      };

      console.log("🚀 Submitting item management request:", requestData);

      const response = await fetch('/api/webadmin/account/item-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(`Thành công! ${result.message || 'Item đã được xử lý thành công'}`);
        setIsPopupOpen(false);
        return true;
      } else {
        // Handle validation errors
        if (result.details && result.details.errors) {
          const validationErrors = Object.entries(result.details.errors)
            .map(([field, messages]) => {
              const fieldName = field.replace('ItemInfo.', '');
              const errorMessages = Array.isArray(messages) ? messages.join(', ') : messages;
              return `${fieldName}: ${errorMessages}`;
            })
            .join('\n');

          toast.error(`Lỗi validation:\n${validationErrors}`);
        } else {
          toast.error(`Lỗi: ${result.message || 'Có lỗi xảy ra khi xử lý yêu cầu'}`);
        }
        return false;
      }
    } catch {
      toast.error('Có lỗi xảy ra khi gửi yêu cầu. Vui lòng thử lại.');
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <>
      <div
        className="aspect-square border-2 border-dashed border-gray-300 rounded bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer flex items-center justify-center max-w-16 max-h-16 w-full h-full"
        title={`${item.itemId}`}
        onClick={handleItemClick}
      >
        <div
          className="w-full h-full rounded flex items-center justify-center"
          style={{
            backgroundImage: `url(https://one.chamthoi.com/item/${item.itemId}.jpg)`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
        </div>
      </div>

      <ItemDetailsPopup
        item={item}
        isOpen={isPopupOpen}
        onClose={handleClosePopup}
        bagType={bagType}
        slotPosition={slotPosition}
        onSave={type === 'online' ? handleSendItem : handleOfflineEditItem}
        isSubmitting={isSubmitting}
      />
    </>
  );
};

export default OneItem;
