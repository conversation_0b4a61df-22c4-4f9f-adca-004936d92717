'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { LogOut, User, Settings, Loader2 } from 'lucide-react';
import { IconLogout } from '@tabler/icons-react';
import { toast } from 'sonner';
import { authClient } from '@/lib/auth-client';

interface LogoutButtonProps {
  user?: {
    id: string;
    name: string;
    email: string;
    image?: string | null;
  } | null;
  variant?: 'button' | 'dropdown' | 'menu-item';
  className?: string;
}

export function LogoutButton({ user, variant = 'menu-item', className }: LogoutButtonProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      setLoading(true);

      // Sử dụng better-auth client để logout
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            toast.success('Đăng xuất thành công');
            router.push('/login');
            router.refresh();
          },
          onError: (ctx) => {
            console.error('Logout error:', ctx.error);
            toast.error('Có lỗi xảy ra khi đăng xuất');
            // Fallback: redirect anyway
            router.push('/login');
            router.refresh();
          }
        }
      });
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Có lỗi xảy ra khi đăng xuất');
      // Fallback: redirect anyway
      router.push('/login');
      router.refresh();
    } finally {
      setLoading(false);
    }
  };

  // Menu item variant (original)
  if (variant === 'menu-item') {
    return (
      <DropdownMenuItem onClick={handleLogout} className="text-red-600" disabled={loading}>
        {loading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <IconLogout />
        )}
        Đăng xuất
      </DropdownMenuItem>
    );
  }

  // Button variant
  if (variant === 'button') {
    return (
      <Button
        variant="outline"
        onClick={handleLogout}
        disabled={loading}
        className={className}
      >
        {loading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <LogOut className="mr-2 h-4 w-4" />
        )}
        Đăng xuất
      </Button>
    );
  }

  // Dropdown variant (user menu)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className={`relative h-8 w-8 rounded-full ${className}`}>
          <Avatar className="h-8 w-8">
            <AvatarImage src={user?.image || undefined} alt={user?.name || 'User'} />
            <AvatarFallback>
              {user?.name ? user.name.charAt(0).toUpperCase() : 'U'}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {user?.name || 'Người dùng'}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email || ''}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => router.push('/dashboard/profile')}>
          <User className="mr-2 h-4 w-4" />
          <span>Hồ sơ</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => router.push('/dashboard/settings')}>
          <Settings className="mr-2 h-4 w-4" />
          <span>Cài đặt</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout} disabled={loading} className="text-red-600">
          {loading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <LogOut className="mr-2 h-4 w-4" />
          )}
          <span>Đăng xuất</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
