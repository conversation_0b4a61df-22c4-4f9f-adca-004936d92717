import type { Permission } from '@/lib/auth-utils';
import { ReactNode } from 'react';
import { auth } from '@/lib/auth';
import { headers } from 'next/headers';
import { userHasPermission } from '@/lib/auth-utils';

interface PermissionGuardProps {
  permission: Permission;
  children: ReactNode;
  fallback?: ReactNode;
  showFallback?: boolean;
}

export async function PermissionGuard({
  permission,
  children,
  fallback = null,
  showFallback = false
}: PermissionGuardProps) {
  const session = await auth.api.getSession({
    headers: await headers()
  });

  if (!session?.user) {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }
    return null;
  }

  const hasPermission = await userHasPermission(session.user.id, permission);

  if (!hasPermission) {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }
    return null;
  }

  return <>{children}</>;
}

// Component để hiển thị thông báo không có quyền
export function NoPermissionMessage({ message }: { message?: string }) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="rounded-full bg-muted p-3 mb-4">
        <svg
          className="h-6 w-6 text-muted-foreground"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10"
          />
        </svg>
      </div>
      <h3 className="text-lg font-semibold mb-2">Không có quyền truy cập</h3>
      <p className="text-muted-foreground text-sm max-w-md">
        {message || "Bạn không có quyền truy cập vào tính năng này. Vui lòng liên hệ quản trị viên nếu bạn cần quyền truy cập."}
      </p>
    </div>
  );
}

// Component để bảo vệ toàn bộ page
interface PageGuardProps {
  permission: Permission;
  children: ReactNode;
  noPermissionMessage?: string;
}

export async function PageGuard({ permission, children, noPermissionMessage }: PageGuardProps) {
  return (
    <PermissionGuard
      permission={permission}
      fallback={<NoPermissionMessage message={noPermissionMessage} />}
      showFallback={true}
    >
      {children}
    </PermissionGuard>
  );
}
