'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Settings, 
  LogOut, 
  Shield, 
  Loader2,
  ChevronDown 
} from 'lucide-react';
import { toast } from 'sonner';
import { authClient } from '@/lib/auth-client';

interface UserMenuProps {
  user: {
    id: string;
    name: string;
    email: string;
    image?: string | null;
  };
  role?: {
    id: string;
    name: string;
    level: number;
    description?: string | null;
  } | null;
  variant?: 'avatar' | 'button';
  className?: string;
}

export function UserMenu({ user, role, variant = 'avatar', className }: UserMenuProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      setLoading(true);
      
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            toast.success('Đăng xuất thành công');
            router.push('/login');
            router.refresh();
          },
          onError: (ctx) => {
            console.error('Logout error:', ctx.error);
            toast.error('Có lỗi xảy ra khi đăng xuất');
            router.push('/login');
            router.refresh();
          }
        }
      });
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Có lỗi xảy ra khi đăng xuất');
      router.push('/login');
      router.refresh();
    } finally {
      setLoading(false);
    }
  };

  const getRoleBadgeVariant = (level: number) => {
    switch (level) {
      case 1: return 'destructive' as const;
      case 2: return 'default' as const;
      case 3: return 'secondary' as const;
      case 4: return 'outline' as const;
      default: return 'outline' as const;
    }
  };

  const getRoleDisplayName = (roleName: string) => {
    const roleMap: Record<string, string> = {
      'admin': 'Quản trị viên',
      'manager': 'Quản lý',
      'moderator': 'Điều hành viên',
      'editor': 'Biên tập viên',
    };
    return roleMap[roleName.toLowerCase()] || roleName;
  };

  const TriggerButton = variant === 'button' ? (
    <Button variant="ghost" className={`flex items-center gap-2 ${className}`}>
      <Avatar className="h-6 w-6">
        <AvatarImage src={user.image || undefined} alt={user.name} />
        <AvatarFallback className="text-xs">
          {user.name.charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      <span className="hidden sm:inline-block">{user.name}</span>
      <ChevronDown className="h-4 w-4" />
    </Button>
  ) : (
    <Button variant="ghost" className={`relative h-8 w-8 rounded-full ${className}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={user.image || undefined} alt={user.name} />
        <AvatarFallback>
          {user.name.charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>
    </Button>
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {TriggerButton}
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {user.name}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
            {role && (
              <Badge 
                variant={getRoleBadgeVariant(role.level)} 
                className="text-xs w-fit mt-1"
              >
                {getRoleDisplayName(role.name)}
              </Badge>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => router.push('/dashboard/profile')}>
          <User className="mr-2 h-4 w-4" />
          <span>Hồ sơ cá nhân</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => router.push('/dashboard/settings')}>
          <Settings className="mr-2 h-4 w-4" />
          <span>Cài đặt</span>
        </DropdownMenuItem>
        {role && (
          <DropdownMenuItem onClick={() => router.push('/dashboard/permissions')}>
            <Shield className="mr-2 h-4 w-4" />
            <span>Quyền hạn</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={handleLogout} 
          disabled={loading} 
          className="text-red-600 focus:text-red-600"
        >
          {loading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <LogOut className="mr-2 h-4 w-4" />
          )}
          <span>Đăng xuất</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
