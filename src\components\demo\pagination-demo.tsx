import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Search, Shuffle, RotateCcw } from 'lucide-react';

// Generate demo data
const generateDemoData = (count: number) => {
  const categories = ['Weapon', 'Armor', 'Accessory', 'Consumable', 'Material', 'Quest Item'];
  const rarities = ['Common', 'Uncommon', 'Rare', 'Epic', 'Legendary'];
  
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `Item ${i + 1}`,
    category: categories[Math.floor(Math.random() * categories.length)],
    rarity: rarities[Math.floor(Math.random() * rarities.length)],
    level: Math.floor(Math.random() * 100) + 1,
    price: Math.floor(Math.random() * 10000) + 100,
    description: `This is a demo item #${i + 1} for testing pagination functionality.`
  }));
};

export function PaginationDemo() {
  const [demoData] = useState(() => generateDemoData(1000)); // 1000 demo items
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter data based on search
  const filteredData = demoData.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.rarity.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination calculations
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleRandomPage = () => {
    const randomPage = Math.floor(Math.random() * totalPages) + 1;
    setCurrentPage(randomPage);
  };

  const handleReset = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'bg-gray-100 text-gray-800';
      case 'Uncommon': return 'bg-green-100 text-green-800';
      case 'Rare': return 'bg-blue-100 text-blue-800';
      case 'Epic': return 'bg-purple-100 text-purple-800';
      case 'Legendary': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced Pagination Demo</CardTitle>
          <CardDescription>
            Demo với 1,000 items để test các tính năng pagination nâng cao
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            {/* Search */}
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search items..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-8"
              />
            </div>

            {/* Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRandomPage}
                disabled={totalPages <= 1}
              >
                <Shuffle className="h-4 w-4 mr-2" />
                Random Page
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>

            {/* Top Pagination */}
            {totalPages > 1 && (
              <AdvancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                totalItems={filteredData.length}
                className="flex-shrink-0"
              />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Data Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Demo Items
            <Badge variant="outline">
              {filteredData.length} items
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {currentData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentData.map((item) => (
                <div
                  key={item.id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-medium">{item.name}</h3>
                    <Badge className={getRarityColor(item.rarity)}>
                      {item.rarity}
                    </Badge>
                  </div>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div>Category: {item.category}</div>
                    <div>Level: {item.level}</div>
                    <div>Price: {item.price.toLocaleString()} gold</div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm ? 'No matching items found' : 'No items available'}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bottom Pagination */}
      {totalPages > 1 && (
        <AdvancedPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          itemsPerPage={itemsPerPage}
          totalItems={filteredData.length}
        />
      )}

      {/* Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Pagination Stats</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold">{demoData.length.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Total Items</div>
            </div>
            <div>
              <div className="text-2xl font-bold">{filteredData.length.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Filtered Items</div>
            </div>
            <div>
              <div className="text-2xl font-bold">{totalPages}</div>
              <div className="text-sm text-muted-foreground">Total Pages</div>
            </div>
            <div>
              <div className="text-2xl font-bold">{currentPage}</div>
              <div className="text-sm text-muted-foreground">Current Page</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
