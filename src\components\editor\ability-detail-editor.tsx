import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Save, RotateCcw, Star } from 'lucide-react';
import { YbiAbility } from '@/lib/parsers/ybi-parser';

interface AbilityDetailEditorProps {
  ability: YbiAbility | null;
  onSave: (ability: YbiAbility) => void;
  onMarkAsEdited: (abilityId: string) => void;
  isEdited: boolean;
}

export function AbilityDetailEditor({ ability, onSave, onMarkAsEdited, isEdited }: AbilityDetailEditorProps) {
  const [editedAbility, setEditedAbility] = useState<YbiAbility | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (ability) {
      setEditedAbility({ ...ability });
      setHasChanges(false);
    }
  }, [ability]);

  const handleFieldChange = (field: keyof YbiAbility, value: any) => {
    if (!editedAbility) return;

    const newAbility = { ...editedAbility, [field]: value };
    setEditedAbility(newAbility);
    setHasChanges(true);
  };

  const handleSave = () => {
    if (!editedAbility || !hasChanges) return;

    onSave(editedAbility);
    onMarkAsEdited(editedAbility.id.toString());
    setHasChanges(false);
  };

  const handleReset = () => {
    if (ability) {
      setEditedAbility({ ...ability });
      setHasChanges(false);
    }
  };

  const getAbilityTypeLabel = (type: number) => {
    const types: { [key: number]: string } = {
      0: 'Passive Ability',
      1: 'Active Ability',
      2: 'Toggle Ability',
      3: 'Aura Ability',
    };
    return types[type] || `Type ${type}`;
  };

  if (!ability || !editedAbility) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-muted-foreground">
            <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Chọn một ability từ danh sách để chỉnh sửa</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Ability #{editedAbility.id}
              {isEdited && <Badge variant="secondary">Đã chỉnh sửa</Badge>}
              {hasChanges && <Badge variant="destructive">Chưa lưu</Badge>}
            </CardTitle>
            <CardDescription>
              Chỉnh sửa thông tin chi tiết của ability
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <Save className="h-4 w-4 mr-2" />
              Lưu
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="id">ID</Label>
                  <Input
                    id="id"
                    type="number"
                    value={editedAbility.id}
                    onChange={(e) => handleFieldChange('id', parseInt(e.target.value) || 0)}
                  />
                </div>
                
                {/* <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Input
                    id="type"
                    type="number"
                    value={editedAbility.type}
                    onChange={(e) => handleFieldChange('type', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    {getAbilityTypeLabel(editedAbility.type)}
                  </p>
                </div> */}
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Tên</Label>
                <Input
                  id="name"
                  value={editedAbility.name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="desc">Mô tả</Label>
                <Textarea
                  id="desc"
                  value={editedAbility.desc}
                  onChange={(e) => handleFieldChange('desc', e.target.value)}
                  rows={3}
                />
              </div>
            </div>

            <Separator />

            {/* Requirements */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Yêu cầu</h3>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="job">Job</Label>
                  <Input
                    id="job"
                    type="number"
                    value={editedAbility.job}
                    onChange={(e) => handleFieldChange('job', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="jobLevel">Job Level</Label>
                  <Input
                    id="jobLevel"
                    type="number"
                    value={editedAbility.jobLevel}
                    onChange={(e) => handleFieldChange('jobLevel', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Input
                    id="level"
                    type="number"
                    value={editedAbility.level}
                    onChange={(e) => handleFieldChange('level', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_44">U_44</Label>
                  <Input
                    id="u_44"
                    type="number"
                    value={editedAbility.u_44}
                    onChange={(e) => handleFieldChange('u_44', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Per Level Stats */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thống kê theo Level</h3>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_1_perLevel">O1 per Level</Label>
                  <Input
                    id="o_1_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_1_perLevel}
                    onChange={(e) => handleFieldChange('o_1_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_2_perLevel">O2 per Level</Label>
                  <Input
                    id="o_2_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_2_perLevel}
                    onChange={(e) => handleFieldChange('o_2_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_3_perLevel">O3 per Level</Label>
                  <Input
                    id="o_3_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_3_perLevel}
                    onChange={(e) => handleFieldChange('o_3_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_4_perLevel">O4 per Level</Label>
                  <Input
                    id="o_4_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_4_perLevel}
                    onChange={(e) => handleFieldChange('o_4_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_5_perLevel">O5 per Level</Label>
                  <Input
                    id="o_5_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_5_perLevel}
                    onChange={(e) => handleFieldChange('o_5_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_6_perLevel">O6 per Level</Label>
                  <Input
                    id="o_6_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_6_perLevel}
                    onChange={(e) => handleFieldChange('o_6_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_7_perLevel">O7 per Level</Label>
                  <Input
                    id="o_7_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_7_perLevel}
                    onChange={(e) => handleFieldChange('o_7_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_8_perLevel">O8 per Level</Label>
                  <Input
                    id="o_8_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_8_perLevel}
                    onChange={(e) => handleFieldChange('o_8_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_9_perLevel">O9 per Level</Label>
                  <Input
                    id="o_9_perLevel"
                    type="number"
                    step="0.01"
                    value={editedAbility.o_9_perLevel}
                    onChange={(e) => handleFieldChange('o_9_perLevel', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* O Strings */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">O Strings & Prefixes</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_1">O1</Label>
                  <Input
                    id="o_1"
                    value={editedAbility.o_1}
                    onChange={(e) => handleFieldChange('o_1', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_1_prefix">O1 Prefix</Label>
                  <Input
                    id="o_1_prefix"
                    value={editedAbility.o_1_prefix}
                    onChange={(e) => handleFieldChange('o_1_prefix', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_2">O2</Label>
                  <Input
                    id="o_2"
                    value={editedAbility.o_2}
                    onChange={(e) => handleFieldChange('o_2', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_2_prefix">O2 Prefix</Label>
                  <Input
                    id="o_2_prefix"
                    value={editedAbility.o_2_prefix}
                    onChange={(e) => handleFieldChange('o_2_prefix', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_3">O3</Label>
                  <Input
                    id="o_3"
                    value={editedAbility.o_3}
                    onChange={(e) => handleFieldChange('o_3', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_3_prefix">O3 Prefix</Label>
                  <Input
                    id="o_3_prefix"
                    value={editedAbility.o_3_prefix}
                    onChange={(e) => handleFieldChange('o_3_prefix', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_4">O4</Label>
                  <Input
                    id="o_4"
                    value={editedAbility.o_4}
                    onChange={(e) => handleFieldChange('o_4', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_4_prefix">O4 Prefix</Label>
                  <Input
                    id="o_4_prefix"
                    value={editedAbility.o_4_prefix}
                    onChange={(e) => handleFieldChange('o_4_prefix', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="o_5">O5</Label>
                  <Input
                    id="o_5"
                    value={editedAbility.o_5}
                    onChange={(e) => handleFieldChange('o_5', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="o_5_prefix">O5 Prefix</Label>
                  <Input
                    id="o_5_prefix"
                    value={editedAbility.o_5_prefix}
                    onChange={(e) => handleFieldChange('o_5_prefix', e.target.value)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Additional Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thuộc tính khác</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_5a">U_5A</Label>
                  <Input
                    id="u_5a"
                    type="number"
                    value={editedAbility.u_5a}
                    onChange={(e) => handleFieldChange('u_5a', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_6a">U_6A</Label>
                  <Input
                    id="u_6a"
                    type="number"
                    value={editedAbility.u_6a}
                    onChange={(e) => handleFieldChange('u_6a', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="offset">Offset</Label>
                <Input
                  id="offset"
                  type="number"
                  value={editedAbility.offset || 0}
                  onChange={(e) => handleFieldChange('offset', parseInt(e.target.value) || 0)}
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>

            <Separator />

            {/* Tips */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Ghi chú</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Type 0: Passive Ability (Kỹ năng thụ động)</li>
                  <li>• Type 1: Active Ability (Kỹ năng chủ động)</li>
                  <li>• Type 2: Toggle Ability (Kỹ năng bật/tắt)</li>
                  <li>• Type 3: Aura Ability (Kỹ năng hào quang)</li>
                  <li>• Job: ID nghề nghiệp yêu cầu</li>
                  <li>• Level: Level tối thiểu để học ability</li>
                  <li>• Attack: Sức mạnh của ability</li>
                </ul>
              </div>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
