import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Search, Star } from 'lucide-react';
import { YbiAbility } from '@/lib/parsers/ybi-parser';
import { displayText } from '@/lib/text-converter';

interface AbilityListProps {
  abilities: YbiAbility[];
  selectedAbilityId: string | null;
  onSelectAbility: (ability: YbiAbility) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedItems: Set<string>;
  editedCount: number;
}

export function AbilityList({
  abilities,
  selectedAbilityId,
  onSelectAbility,
  searchTerm,
  onSearch<PERSON>hange,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedItems,
  editedCount
}: AbilityListProps) {
  // Filter abilities based on search
  const filteredAbilities = abilities.filter(ability =>
    ability.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ability.desc.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ability.id.toString().includes(searchTerm)
  );

  // Pagination
  const totalPages = Math.ceil(filteredAbilities.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentAbilities = filteredAbilities.slice(startIndex, endIndex);

  const getAbilityTypeColor = (level: number) => {
    if (level <= 10) return 'secondary';
    if (level <= 30) return 'default';
    if (level <= 50) return 'outline';
    return 'destructive';
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Abilities ({filteredAbilities.length.toLocaleString()})
          </div>
          {editedCount > 0 && (
            <Badge variant="secondary">
              {editedCount} đã chỉnh sửa
            </Badge>
          )}
        </CardTitle>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm abilities..."
            value={searchTerm}
            onChange={(e) => {
              onSearchChange(e.target.value);
              onPageChange(1); // Reset to first page when searching
            }}
            className="pl-8"
          />
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <AdvancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
            itemsPerPage={itemsPerPage}
            totalItems={filteredAbilities.length}
          />
        )}
      </CardHeader>

      <CardContent className="p-0">
        {/* <ScrollArea className="h-[calc(100vh-180px)]"> */}
          <div className="space-y-2 p-3 h-[calc(100vh-180px)] overflow-y-auto">
            {currentAbilities.length > 0 ? (
              currentAbilities.map((ability) => (
                <div
                  key={ability.id}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedAbilityId === ability.id.toString() ? 'bg-accent border-primary' : 'border-border'}
                  `}
                  onClick={() => onSelectAbility(ability)}
                >
                  <div className="flex items-start gap-3">
                    {/* Ability Icon */}
                    <div className="flex-shrink-0">
                      <img
                        src={`http://one.chamthoi.com/ability/${ability.id}.jpg`}
                        alt={`Ability ${ability.id}`}
                        className="w-8 h-8 rounded border bg-muted object-cover"
                        onError={(e) => {
                          // Fallback to a default ability icon
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjRkVGM0M3IiByeD0iNCIvPgo8cGF0aCBkPSJNMTYgNkwxOC41IDEzSDEzLjVMMTYgNloiIGZpbGw9IiNGNTlFMEIiLz4KPHBhdGggZD0iTTE2IDI2TDEzLjUgMTlIMTguNUwxNiAyNloiIGZpbGw9IiNGNTlFMEIiLz4KPHBhdGggZD0iTTYgMTZMMTMgMTMuNVYxOC41TDYgMTZaIiBmaWxsPSIjRjU5RTBCIi8+CjxwYXRoIGQ9Ik0yNiAxNkwxOSAxMy41VjE4LjVMMjYgMTZaIiBmaWxsPSIjRjU5RTBCIi8+CjxjaXJjbGUgY3g9IjE2IiBjeT0iMTYiIHI9IjIiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+';
                        }}
                      />
                    </div>

                    {/* Ability Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">#{ability.id}</span>
                        <Badge variant={getAbilityTypeColor(ability.level) as any} className="text-xs px-1 py-0">
                          Lv.{ability.level}
                        </Badge>
                        {editedItems.has(ability.id.toString()) && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            Đã sửa
                          </Badge>
                        )}
                      </div>
                      
                      {/* Ability Name - with overflow hidden */}
                      <h4 className="text-sm font-medium text-foreground mb-1 overflow-hidden">
                        <span className="block truncate" title={displayText(ability.name, '(Không có tên)')}>
                          {displayText(ability.name, '(Không có tên)')}
                        </span>
                      </h4>

                      {/* Ability Description - with overflow hidden */}
                      <p className="text-xs text-muted-foreground mb-2 overflow-hidden">
                        <span className="block truncate" title={displayText(ability.desc, '(Không có mô tả)')}>
                          {displayText(ability.desc, '(Không có mô tả)')}
                        </span>
                      </p>

                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? (
                  <div>
                    <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không tìm thấy ability nào với từ khóa {searchTerm}</p>
                  </div>
                ) : (
                  <div>
                    <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không có abilities</p>
                  </div>
                )}
              </div>
            )}
          </div>
        {/* </ScrollArea> */}

        {/* Bottom Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredAbilities.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
