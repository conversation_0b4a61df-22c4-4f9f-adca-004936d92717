'use client';

import { useState, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Settings,
  Upload,
  Save,
  Plus,
  Trash2,
  Search,
  RefreshCw,
  Info,
  ChevronLeft,
  ChevronRight,
  Undo
} from 'lucide-react';
import { toast } from 'sonner';
import { CfgParser, CfgFile, CfgRow, HeaderSize, LanguageCodePage } from '@/lib/parsers/cfg-parser';

export function CfgEditor() {
  const [cfgFile, setCfgFile] = useState<CfgFile | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);
  
  // Inline editing states
  const [editedRows, setEditedRows] = useState<Map<number, CfgRow>>(new Map());
  const [originalRows, setOriginalRows] = useState<Map<number, CfgRow>>(new Map());
  
  // Settings
  const [headerSize, setHeaderSize] = useState<HeaderSize>(HeaderSize.LARGE);
  const [languageCodePage, setLanguageCodePage] = useState<LanguageCodePage>(LanguageCodePage.Vietnamese);
  
  // Dialogs
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingRow, setDeletingRow] = useState<CfgRow | null>(null);
  const [newRow, setNewRow] = useState<Partial<CfgRow>>({});

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle inline editing
  const handleInlineEdit = (row: CfgRow, columnIndex: number, value: string) => {
    // Store original row if not already stored
    setOriginalRows(prev => {
      if (!prev.has(row.id)) {
        const newMap = new Map(prev);
        newMap.set(row.id, { ...row, columns: [...row.columns] });
        return newMap;
      }
      return prev;
    });

    const updatedRow = { 
      ...row, 
      columns: row.columns.map((col, idx) => idx === columnIndex ? value : col)
    };
    
    // Update edited rows map
    setEditedRows(prev => {
      const newMap = new Map(prev);
      newMap.set(row.id, updatedRow);
      return newMap;
    });
    
    // Update the main file data
    if (cfgFile) {
      const updatedRows = cfgFile.rows.map(r => 
        r.id === row.id ? updatedRow : r
      );
      
      setCfgFile({
        ...cfgFile,
        rows: updatedRows
      });
    }
    
    setHasUnsavedChanges(true);
  };

  // Handle revert row to original state
  const handleRevertRow = (row: CfgRow) => {
    const originalRow = originalRows.get(row.id);
    if (!originalRow) return;

    // Remove from edited rows
    setEditedRows(prev => {
      const newMap = new Map(prev);
      newMap.delete(row.id);
      return newMap;
    });

    // Remove from original rows
    setOriginalRows(prev => {
      const newMap = new Map(prev);
      newMap.delete(row.id);
      return newMap;
    });

    // Update the main file data
    if (cfgFile) {
      const updatedRows = cfgFile.rows.map(r => 
        r.id === row.id ? originalRow : r
      );
      
      setCfgFile({
        ...cfgFile,
        rows: updatedRows
      });
    }

    // Update unsaved changes state
    const remainingEdits = editedRows.size - 1;
    setHasUnsavedChanges(remainingEdits > 0);
    
    toast.success('Đã khôi phục row về trạng thái gốc');
  };

  // Get edited rows count
  const getEditedCount = () => editedRows.size;

  // Reset edited rows
  const resetEditedRows = () => {
    setEditedRows(new Map());
    setOriginalRows(new Map());
    setHasUnsavedChanges(false);
  };

  // Handle file selection
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    try {
      const buffer = await file.arrayBuffer();
      
      // Auto-detect header size
      const detectedHeaderSize = CfgParser.detectHeaderSize(buffer);
      setHeaderSize(detectedHeaderSize);
      
      // Validate file
      const validation = CfgParser.validate(buffer, detectedHeaderSize);
      if (!validation.valid) {
        toast.error(`File không hợp lệ: ${validation.error}`);
        return;
      }

      // Parse file
      const parsedFile = CfgParser.parse(buffer, file.name, detectedHeaderSize);
      setCfgFile(parsedFile);
      resetEditedRows();
      setCurrentPage(1);
      
      toast.success(`Đã tải file ${file.name} thành công (${parsedFile.rows.length} rows, ${parsedFile.header.tableHeaderLength} columns)`);
      
      // Debug info
      console.log('Parsed cfg file:', {
        fileName: file.name,
        fileSize: buffer.byteLength,
        header: parsedFile.header,
        rowCount: parsedFile.rows.length,
        columnCount: parsedFile.header.tableHeaderLength,
        headerSize: detectedHeaderSize,
        firstRow: parsedFile.rows[0]
      });
    } catch (error) {
      console.error('Error parsing file:', error);
      toast.error(`Lỗi khi đọc file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, []);

  // Handle open file button
  const handleOpenFile = () => {
    fileInputRef.current?.click();
  };

  // Handle save file
  const handleSaveFile = useCallback(() => {
    if (!cfgFile) return;

    try {
      // Debug: Log header info before saving
      console.log('Saving cfg file with header:', {
        headerSize: cfgFile.header.headerSize,
        tableHeaderLength: cfgFile.header.tableHeaderLength,
        totalRows: cfgFile.rows.length,
        hasOriginalHeader: !!cfgFile.header.originalHeaderBytes
      });

      const buffer = CfgParser.generate(cfgFile);
      
      const blob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = cfgFile.fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      resetEditedRows();
      toast.success('Đã lưu file thành công');
    } catch (error) {
      console.error('Error saving file:', error);
      toast.error(`Lỗi khi lưu file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [cfgFile]);

  // Filter rows based on search
  const filteredRows = cfgFile?.rows.filter(row => 
    row.id.toString().includes(searchTerm) ||
    row.columns.some(col => col.toLowerCase().includes(searchTerm.toLowerCase()))
  ) || [];

  // Pagination calculations
  const totalPages = Math.ceil(filteredRows.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedRows = filteredRows.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle search with page reset
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Cfg Editor
            {hasUnsavedChanges && (
              <Badge variant="destructive" className="ml-2">
                {getEditedCount()} rows edited
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 flex-wrap">
            <Button onClick={handleOpenFile} variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Mở File
            </Button>
            
            {cfgFile && (
              <>
                <Button onClick={handleSaveFile} disabled={!hasUnsavedChanges}>
                  <Save className="h-4 w-4 mr-2" />
                  Lưu File
                </Button>
                
                <Button onClick={() => setIsAddDialogOpen(true)} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm Row
                </Button>
              </>
            )}

            {/* Settings */}
            <div className="flex items-center gap-2">
              <Label className="text-sm">Header Size:</Label>
              <Select value={headerSize.toString()} onValueChange={(value) => setHeaderSize(parseInt(value) as HeaderSize)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={HeaderSize.SMALL.toString()}>8</SelectItem>
                  <SelectItem value={HeaderSize.MEDIUM.toString()}>12</SelectItem>
                  <SelectItem value={HeaderSize.LARGE.toString()}>16</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Label className="text-sm">Language:</Label>
              <Select value={languageCodePage.toString()} onValueChange={(value) => setLanguageCodePage(parseInt(value) as LanguageCodePage)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={LanguageCodePage.Vietnamese.toString()}>Vietnamese</SelectItem>
                  <SelectItem value={LanguageCodePage.Korean.toString()}>Korean</SelectItem>
                  <SelectItem value={LanguageCodePage.Chinese.toString()}>Chinese</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {loading && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                Đang xử lý...
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept=".cfg"
            onChange={handleFileSelect}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* File Info */}
      {cfgFile && (
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Thông tin File
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-6 gap-4">
                <div>
                  <Label className="text-sm font-medium">Tên File</Label>
                  <p className="text-sm">{cfgFile.fileName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Kích thước</Label>
                  <p className="text-sm">{(cfgFile.fileSize / 1024).toFixed(2)} KB</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Header Size</Label>
                  <p className="text-sm">{cfgFile.header.headerSize} bytes</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Columns</Label>
                  <p className="text-sm">{cfgFile.header.tableHeaderLength}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Total Rows</Label>
                  <p className="text-sm">{cfgFile.rows.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Format Specification
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Encryption:</span>
                  <span className="font-mono">XOR with 147-byte key</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Header:</span>
                  <span className="font-mono">{cfgFile.header.headerSize} bytes (preserved)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Table Header:</span>
                  <span className="font-mono">4 bytes (column count)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Row Format:</span>
                  <span className="font-mono">Length + Data per column</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Encoding:</span>
                  <span className="font-mono">Latin1</span>
                </div>
              </div>
            </CardContent>
          </Card> */}
        </div>
      )}

      {/* Table */}
      {cfgFile && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                Table Data ({filteredRows.length} rows)
                {totalPages > 1 && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    - Page {currentPage} of {totalPages}
                  </span>
                )}
              </CardTitle>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm kiếm..."
                      value={searchTerm}
                      onChange={(e) => handleSearchChange(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>

                  {totalPages > 1 && (
                    <div className="flex items-center gap-1">
                      <Label className="text-xs text-muted-foreground">Page:</Label>
                      <Input
                        type="number"
                        min="1"
                        max={totalPages}
                        value={currentPage}
                        onChange={(e) => {
                          const page = parseInt(e.target.value);
                          if (page >= 1 && page <= totalPages) {
                            handlePageChange(page);
                          }
                        }}
                        className="w-16 h-8 text-xs"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="border rounded-lg overflow-hidden">
              {/* Fixed Header */}
              <div className="border-b bg-muted/50 sticky top-0 z-10">
                <div className="flex gap-2 p-3 text-sm font-medium">
                  <div className="w-16 text-center">Row</div>
                  {Array.from({ length: cfgFile.header.tableHeaderLength }, (_, i) => (
                    <div key={i} className="flex-1 min-w-32">Column {i + 1}</div>
                  ))}
                  <div className="w-20 text-center">Action</div>
                </div>
              </div>

              {/* Scrollable Body */}
              <div className="max-h-[calc(100vh-450px)] min-h-[400px] overflow-y-auto">
                <div className="divide-y">
                  {paginatedRows.map((row) => {
                    const isEdited = editedRows.has(row.id);
                    const hasOriginal = originalRows.has(row.id);

                    return (
                      <div
                        key={row.id}
                        className={`flex gap-2 p-3 hover:bg-muted/50 transition-colors ${
                          isEdited ? 'bg-yellow-50 dark:bg-yellow-950/20' : ''
                        }`}
                      >
                        {/* Row ID */}
                        <div className="w-16 flex items-center justify-center">
                          <span className="font-mono text-sm">{row.id}</span>
                        </div>

                        {/* Columns */}
                        {row.columns.map((column, colIndex) => (
                          <div key={colIndex} className="flex-1 min-w-32 flex items-center">
                            <Input
                              value={column}
                              onChange={(e) => handleInlineEdit(row, colIndex, e.target.value)}
                              className="border-0 bg-transparent p-0 h-auto text-sm focus-visible:ring-1 w-full"
                              placeholder={`Column ${colIndex + 1}...`}
                              title={`Row ${row.id}, Column ${colIndex + 1}: ${column}`}
                            />
                          </div>
                        ))}

                        {/* Action Column */}
                        <div className="w-20 flex items-center justify-center">
                          {hasOriginal ? (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRevertRow(row)}
                              className="h-8 w-8 p-0"
                              title="Khôi phục về trạng thái gốc"
                            >
                              <Undo className="h-3 w-3" />
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setDeletingRow(row);
                                setIsDeleteDialogOpen(true);
                              }}
                              className="h-8 w-8 p-0"
                              title="Xóa row"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {paginatedRows.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchTerm ? 'Không tìm thấy row nào' : 'Chưa có row nào'}
                  </div>
                )}
              </div>
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-4 py-3 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>
                    {startIndex + 1}-{Math.min(endIndex, filteredRows.length)} của {filteredRows.length} rows
                  </span>
                  {getEditedCount() > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {getEditedCount()} đã sửa
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Trước
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          size="sm"
                          variant={currentPage === pageNum ? "default" : "outline"}
                          onClick={() => handlePageChange(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                  >
                    Sau
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
      
      {/* No file loaded state */}
      {!cfgFile && !loading && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardContent className="text-center py-12">
              <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Chưa có file nào được tải</h3>
              <p className="text-muted-foreground mb-4">
                Chọn file .cfg để bắt đầu chỉnh sửa
              </p>
              <Button onClick={handleOpenFile}>
                <Upload className="h-4 w-4 mr-2" />
                Mở File
              </Button>
            </CardContent>
          </Card>

          {/* <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Hướng dẫn sử dụng
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <h4 className="font-medium mb-1">File Format:</h4>
                <p className="text-muted-foreground">
                  Cfg files chứa table data được mã hóa XOR.
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-1">Cấu trúc:</h4>
                <ul className="text-muted-foreground space-y-1 ml-4">
                  <li>• Header: 8/12/16 bytes</li>
                  <li>• Table header: 4 bytes (số columns)</li>
                  <li>• Rows: Length + Data per column</li>
                  <li>• Encryption: XOR với key 147 bytes</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-1">Chức năng:</h4>
                <ul className="text-muted-foreground space-y-1 ml-4">
                  <li>• Xem và chỉnh sửa table data</li>
                  <li>• Thêm/xóa rows</li>
                  <li>• Tìm kiếm theo nội dung</li>
                  <li>• Auto-detect header size</li>
                </ul>
              </div>
            </CardContent>
          </Card> */}
        </div>
      )}
    </div>
  );
}
