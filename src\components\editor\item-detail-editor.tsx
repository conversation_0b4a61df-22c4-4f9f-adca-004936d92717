import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Save, RotateCcw, Package } from 'lucide-react';
import { YbiItem } from '@/lib/parsers/ybi-parser';
import { displayText } from '@/lib/text-converter';

interface ItemDetailEditorProps {
  item: YbiItem | null;
  onSave: (item: YbiItem) => void;
  onMarkAsEdited: (itemId: string) => void;
  isEdited: boolean;
}

export function ItemDetailEditor({ item, onSave, onMarkAsEdited, isEdited }: ItemDetailEditorProps) {
  const [editedItem, setEditedItem] = useState<YbiItem | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Helper functions for better display
  const getItemTypeLabel = (type: number) => {
    const types: { [key: number]: string } = {
      0: 'Weapon',
      1: 'Armor',
      2: 'Accessory',
      3: 'Consumable',
      4: 'Material',
      5: 'Quest Item',
      6: 'Special',
    };
    return types[type] || `Type ${type}`;
  };

  const getSexLabel = (sex: number) => {
    switch (sex) {
      case 0: return 'All';
      case 1: return 'Male Only';
      case 2: return 'Female Only';
      default: return `Sex ${sex}`;
    }
  };

  const getLockStatusLabel = (lock: number) => {
    return lock === 0 ? 'Unlocked' : 'Locked';
  };

  useEffect(() => {
    if (item) {
      setEditedItem({ ...item });
      setHasChanges(false);
    }
  }, [item]);

  const handleFieldChange = (field: keyof YbiItem, value: any) => {
    if (!editedItem) return;

    const newItem = { ...editedItem, [field]: value };
    setEditedItem(newItem);
    setHasChanges(true);
  };

  const handleSave = () => {
    if (!editedItem || !hasChanges) return;

    onSave(editedItem);
    onMarkAsEdited(editedItem.id.toString());
    setHasChanges(false);
  };

  const handleReset = () => {
    if (item) {
      setEditedItem({ ...item });
      setHasChanges(false);
    }
  };

  if (!item || !editedItem) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Chọn một item từ danh sách để chỉnh sửa</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Item #{editedItem.id}
              {isEdited && <Badge variant="secondary">Đã chỉnh sửa</Badge>}
              {hasChanges && <Badge variant="destructive">Chưa lưu</Badge>}
            </CardTitle>
            <CardDescription>
              Chỉnh sửa thông tin chi tiết của item
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <Save className="h-4 w-4 mr-2" />
              Lưu
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="id">ID</Label>
                  <Input
                    id="id"
                    type="number"
                    value={editedItem.id}
                    onChange={(e) => handleFieldChange('id', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="itemType">Item Type</Label>
                  <Input
                    id="itemType"
                    type="number"
                    value={editedItem.itemType}
                    onChange={(e) => handleFieldChange('itemType', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    {getItemTypeLabel(editedItem.itemType)}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Tên</Label>
                <Input
                  id="name"
                  value={editedItem.name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                  placeholder="Nhập tên item..."
                />
                <p className="text-xs text-muted-foreground">
                  Hiển thị: {displayText(editedItem.name, '(Chưa có tên)')}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="desc">Mô tả</Label>
                <Textarea
                  id="desc"
                  value={editedItem.desc}
                  onChange={(e) => handleFieldChange('desc', e.target.value)}
                  rows={3}
                  placeholder="Nhập mô tả item..."
                />
                <p className="text-xs text-muted-foreground">
                  Hiển thị: {displayText(editedItem.desc, '(Chưa có mô tả)')}
                </p>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Input
                    id="level"
                    type="number"
                    value={editedItem.level}
                    onChange={(e) => handleFieldChange('level', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="jobLevel">Job Level</Label>
                  <Input
                    id="jobLevel"
                    type="number"
                    value={editedItem.jobLevel}
                    onChange={(e) => handleFieldChange('jobLevel', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sex">Sex Requirement</Label>
                  <Input
                    id="sex"
                    type="number"
                    value={editedItem.sex}
                    onChange={(e) => handleFieldChange('sex', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    {getSexLabel(editedItem.sex)}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="zx">ZX</Label>
                  <Input
                    id="zx"
                    type="number"
                    value={editedItem.zx}
                    onChange={(e) => handleFieldChange('zx', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reside1">Reside 1</Label>
                  <Input
                    id="reside1"
                    type="number"
                    value={editedItem.reside1}
                    onChange={(e) => handleFieldChange('reside1', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reside2">Reside 2</Label>
                  <Input
                    id="reside2"
                    type="number"
                    value={editedItem.reside2}
                    onChange={(e) => handleFieldChange('reside2', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

            </div>

            <Separator />

            {/* Combat Stats */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thống kê chiến đấu</h3>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxAtk">Max Attack</Label>
                  <Input
                    id="maxAtk"
                    type="number"
                    value={editedItem.maxAtk}
                    onChange={(e) => handleFieldChange('maxAtk', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minAtk">Min Attack</Label>
                  <Input
                    id="minAtk"
                    type="number"
                    value={editedItem.minAtk}
                    onChange={(e) => handleFieldChange('minAtk', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="def">Defense</Label>
                  <Input
                    id="def"
                    type="number"
                    value={editedItem.def}
                    onChange={(e) => handleFieldChange('def', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shield">Shield</Label>
                  <Input
                    id="shield"
                    type="number"
                    value={editedItem.shield}
                    onChange={(e) => handleFieldChange('shield', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="setBonusDamage">Set Bonus Damage</Label>
                  <Input
                    id="setBonusDamage"
                    type="number"
                    value={editedItem.setBonusDamage}
                    onChange={(e) => handleFieldChange('setBonusDamage', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Economy & Resources */}
            <div className="space-y-4">
              {/* <h3 className="text-lg font-semibold">Kinh tế & Tài nguyên</h3> */}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gold">Gold (Giá bán)</Label>
                  <Input
                    id="gold"
                    type="number"
                    value={editedItem.gold}
                    onChange={(e) => handleFieldChange('gold', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    {editedItem.gold.toLocaleString()} gold
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="recycleGold">Recycle Gold</Label>
                  <Input
                    id="recycleGold"
                    type="number"
                    value={editedItem.recycleGold}
                    onChange={(e) => handleFieldChange('recycleGold', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    {editedItem.recycleGold.toLocaleString()} gold khi recycle
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="nj">NJ (Nội Công) ?</Label>
                  <Input
                    id="nj"
                    type="number"
                    value={editedItem.nj}
                    onChange={(e) => handleFieldChange('nj', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="el">EL (Uy Lực) ?</Label>
                  <Input
                    id="el"
                    type="number"
                    value={editedItem.el}
                    onChange={(e) => handleFieldChange('el', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight">Weight</Label>
                  <Input
                    id="weight"
                    type="number"
                    value={editedItem.weight}
                    onChange={(e) => handleFieldChange('weight', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="wx">WX</Label>
                  <Input
                    id="wx"
                    type="number"
                    value={editedItem.wx}
                    onChange={(e) => handleFieldChange('wx', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="wxjd">WXJD</Label>
                  <Input
                    id="wxjd"
                    type="number"
                    value={editedItem.wxjd}
                    onChange={(e) => handleFieldChange('wxjd', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="qg_sll">QG SLL</Label>
                  <Input
                    id="qg_sll"
                    type="number"
                    value={editedItem.qg_sll}
                    onChange={(e) => handleFieldChange('qg_sll', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* System Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thuộc tính hệ thống</h3>

              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="lock">Lock Status</Label>
                  <Input
                    id="lock"
                    type="number"
                    value={editedItem.lock}
                    onChange={(e) => handleFieldChange('lock', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    0 = Unlocked, 1 = Locked
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="offset">Offset (Read-only)</Label>
                  <Input
                    id="offset"
                    type="number"
                    value={editedItem.offset || 0}
                    disabled
                    className="bg-muted"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Unknown Fields */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Unknown Fields</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Các trường chưa được xác định chức năng. Chỉnh sửa cẩn thận.
              </p>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="unknown1">Unknown 1</Label>
                  <Input
                    id="unknown1"
                    type="number"
                    value={editedItem.unknown1}
                    onChange={(e) => handleFieldChange('unknown1', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unknown2">Unknown 2</Label>
                  <Input
                    id="unknown2"
                    type="number"
                    value={editedItem.unknown2}
                    onChange={(e) => handleFieldChange('unknown2', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unknown3">Unknown 3</Label>
                  <Input
                    id="unknown3"
                    type="number"
                    value={editedItem.unknown3}
                    onChange={(e) => handleFieldChange('unknown3', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unknown4">Unknown 4</Label>
                  <Input
                    id="unknown4"
                    type="number"
                    value={editedItem.unknown4}
                    onChange={(e) => handleFieldChange('unknown4', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="unknown5">Unknown 5</Label>
                  <Input
                    id="unknown5"
                    type="number"
                    value={editedItem.unknown5}
                    onChange={(e) => handleFieldChange('unknown5', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unknown6">Unknown 6</Label>
                  <Input
                    id="unknown6"
                    type="number"
                    value={editedItem.unknown6}
                    onChange={(e) => handleFieldChange('unknown6', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unknown7">Unknown 7</Label>
                  <Input
                    id="unknown7"
                    type="number"
                    value={editedItem.unknown7}
                    onChange={(e) => handleFieldChange('unknown7', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Tips & Documentation */}
            {/* <div className="space-y-4">
              <h3 className="text-lg font-semibold">Ghi chú & Hướng dẫn</h3>
              <div className="bg-muted/50 p-4 rounded-lg space-y-3">
                <div>
                  <h4 className="font-medium text-sm mb-2">Item Types:</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• 0: Weapon (Vũ khí)</li>
                    <li>• 1: Armor (Giáp)</li>
                    <li>• 2: Accessory (Phụ kiện)</li>
                    <li>• 3: Consumable (Tiêu hao)</li>
                    <li>• 4: Material (Nguyên liệu)</li>
                    <li>• 5: Quest Item (Vật phẩm nhiệm vụ)</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-sm mb-2">Combat Stats:</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Max/Min Attack: Sát thương tối đa/tối thiểu</li>
                    <li>• Defense: Phòng thủ</li>
                    <li>• Shield: Khiên (magic defense)</li>
                    <li>• Set Bonus Damage: Sát thương bonus khi đủ set</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-sm mb-2">Resources:</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Gold: Giá bán cho NPC</li>
                    <li>• Recycle Gold: Tiền nhận khi recycle</li>
                    <li>• NJ: Nội Công (Internal Power)</li>
                    <li>• EL: Uy Lực (Prestige/Reputation)</li>
                    <li>• Weight: Trọng lượng trong inventory</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-sm mb-2">System:</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Sex: 0=All, 1=Male Only, 2=Female Only</li>
                    <li>• Lock: 0=Unlocked, 1=Locked (không thể trade)</li>
                    <li>• Job Level: Level nghề nghiệp yêu cầu</li>
                    <li>• Unknown fields: Chưa xác định chức năng, chỉnh sửa cẩn thận</li>
                  </ul>
                </div>
              </div>
            </div> */}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
