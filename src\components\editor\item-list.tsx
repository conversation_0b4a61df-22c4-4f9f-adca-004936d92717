import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import {  Package } from 'lucide-react';
import { YbiItem } from '@/lib/parsers/ybi-parser';
import { displayText } from '@/lib/text-converter';

interface ItemListProps {
  items: YbiItem[];
  selectedItemId: string | null;
  onSelectItem: (item: YbiItem) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedItems: Set<string>;
  editedCount: number;
}

export function ItemList({
  items,
  selectedItemId,
  onSelectItem,
  searchTerm,
  onSearchChange,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedItems,
  editedCount
}: ItemListProps) {
  // Filter items based on search
  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.desc.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.id.toString().includes(searchTerm)
  );

  // Pagination
  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredItems.slice(startIndex, endIndex);

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <Package className="h-4 w-4" />
          Danh sách Items
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0 w-full">
        {/* <ScrollArea className="h-[calc(100vh-180px)]"> */}
          <div className="space-y-2 p-3 h-[calc(100vh-180px)] overflow-y-auto">
            {currentItems.length > 0 ? (
              currentItems.map((item) => (
                <div
                  key={item.id}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedItemId === item.id.toString() ? 'bg-accent border-primary' : 'border-border'}
                  `}
                  onClick={() => onSelectItem(item)}
                >
                  <div className="flex items-start gap-3">
                    {/* Item Icon */}
                    <div className="flex-shrink-0">
                      <img
                        src={`http://one.chamthoi.com/item/${item.id}.jpg`}
                        alt={`Item ${item.id}`}
                        className="w-8 h-8 rounded border bg-muted object-cover"
                        onError={(e) => {
                          // Fallback to a default icon if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNiA4QzEyLjY4NjMgOCAxMCAxMC42ODYzIDEwIDE0VjE4QzEwIDIxLjMxMzcgMTIuNjg2MyAyNCAxNiAyNEMxOS4zMTM3IDI0IDIyIDIxLjMxMzcgMjIgMThWMTRDMjIgMTAuNjg2MyAxOS4zMTM3IDggMTYgOFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                        }}
                      />
                    </div>

                    {/* Item Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">#{item.id}</span>
                        {editedItems.has(item.id.toString()) && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            Đã sửa
                          </Badge>
                        )}
                      </div>

                      {/* Item Name - with overflow hidden */}
                      <h4 className="text-sm font-medium text-foreground mb-1 overflow-hidden">
                        <span className="block truncate" title={displayText(item.name, '(Không có tên)')}>
                          {displayText(item.name, '(Không có tên)')}
                        </span>
                      </h4>

                      {/* Item Description - with overflow hidden */}
                      <p className="text-xs text-muted-foreground mb-2 overflow-hidden">
                        <span className="block truncate" title={displayText(item.desc, '(Không có mô tả)')}>
                          {displayText(item.desc, '(Không có mô tả)')}
                        </span>
                      </p>

                      {/* Item Stats - Compact layout */}
                      <div className="flex flex-wrap gap-x-3 gap-y-1 text-xs text-muted-foreground">
                        <span>Lv.{item.level}</span>
                        <span>R2:{item.reside2}</span>
                        <span>R1:{item.reside1}</span>
                        <span>ZX:{item.zx}</span>
                        <span>Sex:{item.sex}</span>
                        <span className="text-amber-600 font-medium">
                          {item.gold.toLocaleString()}g
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? (
                  <div>
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không tìm thấy item nào với từ khóa {searchTerm}</p>
                  </div>
                ) : (
                  <div>
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không có items</p>
                  </div>
                )}
              </div>
            )}
          </div>
        {/* </ScrollArea> */}

        {/* Bottom Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredItems.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
