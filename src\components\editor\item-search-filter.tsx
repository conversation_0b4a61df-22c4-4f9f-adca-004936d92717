import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Search, Package, Filter, X } from 'lucide-react';
import { YbiItem } from '@/lib/parsers/ybi-parser';

interface ItemSearchFilterProps {
  items: YbiItem[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedCount: number;
  filteredItems: YbiItem[];
  // Filter states
  levelFilter: string;
  onLevelFilterChange: (level: string) => void;
  typeFilter: string;
  onTypeFilterChange: (type: string) => void;
  reside1Filter: string;
  onReside1FilterChange: (reside1: string) => void;
  reside2Filter: string;
  onReside2FilterChange: (reside2: string) => void;
  jobLevelFilter: string;
  onJobLevelFilterChange: (jobLevel: string) => void;
  sexFilter: string;
  onSexFilterChange: (sex: string) => void;
  zxFilter: string;
  onZxFilterChange: (zx: string) => void;
  onClearFilters: () => void;
}

export function ItemSearchFilter({
  items,
  searchTerm,
  onSearchChange,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedCount,
  filteredItems,
  levelFilter,
  onLevelFilterChange,
  typeFilter,
  onTypeFilterChange,
  reside1Filter,
  onReside1FilterChange,
  reside2Filter,
  onReside2FilterChange,
  jobLevelFilter,
  onJobLevelFilterChange,
  sexFilter,
  onSexFilterChange,
  zxFilter,
  onZxFilterChange,
  onClearFilters
}: ItemSearchFilterProps) {
  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);

  // Reside1 - Job/Class requirements
  const getReside1Label = (reside1: number) => {
    const reside1Map: { [key: number]: string } = {
      1: 'Đao',
      2: 'Kiếm',
      3: 'Thương',
      4: 'Cung',
      5: 'Đại phu (Healer)',
      7: 'Ninja',
      8: 'Cầm Sư',
      9: 'Quyền Sư',
      13: 'Hàn Bảo Quân',
      14: 'Đàm Hoa Liên',
      17: 'Mai Liễu Chân',
      18: 'Tử Hào',
      19: 'Thần Nữ',
    };
    return reside1Map[reside1] || `Reside1 ${reside1}`;
  };

  // Reside2 - Equipment type
  const getReside2Label = (reside2: number) => {
    const reside2Map: { [key: number]: string } = {
      0: 'Khác',
      1: 'Trang phục',
      2: 'Giáp tay',
      3: 'Vũ khí',
      4: 'Chân',
      5: 'Nội giáp',
      6: 'Dây chuyền',
      7: 'Bông tai',
      8: 'Nhẫn',
      9: 'Áo choàng',
      10: 'Các loại đá',
      11: 'Phúc vận phù',
      20: 'Mũi tên',
      21: 'Áo bang',
      22: 'Pet linh thú',
    };
    return reside2Map[reside2] || `Reside2 ${reside2}`;
  };

  // ZX - Faction
  const getZxLabel = (zx: number) => {
    const zxMap: { [key: number]: string } = {
      0: 'Trung lập',
      1: 'Chính phái',
      2: 'Tà phái',
    };
    return zxMap[zx] || `Faction ${zx}`;
  };

  // Sex
  const getSexLabel = (sex: number) => {
    const sexMap: { [key: number]: string } = {
      0: 'Tất cả',
      1: 'Nam',
      2: 'Nữ',
    };
    return sexMap[sex] || `Sex ${sex}`;
  };

  // Get unique values from data
  const uniqueTypes = Array.from(new Set(items.map(item => item.itemType))).sort((a, b) => a - b);
  const uniqueReside1 = Array.from(new Set(items.map(item => item.reside1))).sort((a, b) => a - b);
  const uniqueReside2 = Array.from(new Set(items.map(item => item.reside2))).sort((a, b) => a - b);
  const uniqueJobLevels = Array.from(new Set(items.map(item => item.jobLevel))).sort((a, b) => a - b);
  const uniqueSex = Array.from(new Set(items.map(item => item.sex))).sort((a, b) => a - b);
  const uniqueZx = Array.from(new Set(items.map(item => item.zx))).sort((a, b) => a - b);

  // Get level ranges
  const levelRanges = [
    { value: 'all', label: 'Tất cả level' },
    { value: '1-10', label: 'Level 1-10' },
    { value: '11-30', label: 'Level 11-30' },
    { value: '31-50', label: 'Level 31-50' },
    { value: '51-70', label: 'Level 51-70' },
    { value: '71-90', label: 'Level 71-90' },
    { value: '91+', label: 'Level 91+' },
  ];

  const hasActiveFilters = searchTerm ||
    (levelFilter && levelFilter !== 'all') ||
    (typeFilter && typeFilter !== 'all') ||
    (reside1Filter && reside1Filter !== 'all') ||
    (reside2Filter && reside2Filter !== 'all') ||
    (jobLevelFilter && jobLevelFilter !== 'all') ||
    (sexFilter && sexFilter !== 'all') ||
    (zxFilter && zxFilter !== 'all');

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Items ({filteredItems.length.toLocaleString()} / {items.length.toLocaleString()})
            {editedCount > 0 && (
              <Badge variant="secondary">
                {editedCount} đã chỉnh sửa
              </Badge>
            )}
          </div>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
              className="h-8"
            >
              <X className="h-4 w-4 mr-2" />
              Xóa bộ lọc
            </Button>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm theo tên, mô tả hoặc ID..."
            value={searchTerm}
            onChange={(e) => {
              onSearchChange(e.target.value);
              onPageChange(1);
            }}
            className="pl-8"
          />
        </div>

        {/* Filters Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-3">
          {/* Level Filter */}
          <div>
            <Select value={levelFilter || "all"} onValueChange={(value) => {
              onLevelFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                {levelRanges.map((range) => (
                  <SelectItem key={range.value || "all"} value={range.value || "all"}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Reside2 Filter - Equipment Type */}
          <div>
            <Select value={reside2Filter || "all"} onValueChange={(value) => {
              onReside2FilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Loại trang bị" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                {uniqueReside2.map((reside2) => (
                  <SelectItem key={reside2} value={reside2.toString()}>
                    {getReside2Label(reside2)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Reside1 Filter - Job/Class */}
          <div>
            <Select value={reside1Filter || "all"} onValueChange={(value) => {
              onReside1FilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Nghề nghiệp" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả nghề</SelectItem>
                {uniqueReside1.map((reside1) => (
                  <SelectItem key={reside1} value={reside1.toString()}>
                    {getReside1Label(reside1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* ZX Filter - Faction */}
          <div>
            <Select value={zxFilter || "all"} onValueChange={(value) => {
              onZxFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Phe phái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả phe</SelectItem>
                {uniqueZx.map((zx) => (
                  <SelectItem key={zx} value={zx.toString()}>
                    {getZxLabel(zx)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sex Filter */}
          <div>
            <Select value={sexFilter || "all"} onValueChange={(value) => {
              onSexFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Giới tính" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                {uniqueSex.map((sex) => (
                  <SelectItem key={sex} value={sex.toString()}>
                    {getSexLabel(sex)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Job Level Filter */}
          <div>
            <Select value={jobLevelFilter || "all"} onValueChange={(value) => {
              onJobLevelFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Job Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả job level</SelectItem>
                {uniqueJobLevels.map((jobLevel) => (
                  <SelectItem key={jobLevel} value={jobLevel.toString()}>
                    Job Level {jobLevel}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Type Filter */}
          <div>
            <Select value={typeFilter || "all"} onValueChange={(value) => {
              onTypeFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Item Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả type</SelectItem>
                {uniqueTypes.map((type) => (
                  <SelectItem key={type} value={type.toString()}>
                    Type {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2">
            {searchTerm && (
              <Badge variant="secondary" className="gap-1">
                <Search className="h-3 w-3" />
                {searchTerm}
              </Badge>
            )}
            {levelFilter && levelFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                Level: {levelFilter}
              </Badge>
            )}
            {reside2Filter && reside2Filter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                {getReside2Label(parseInt(reside2Filter))}
              </Badge>
            )}
            {reside1Filter && reside1Filter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                {getReside1Label(parseInt(reside1Filter))}
              </Badge>
            )}
            {zxFilter && zxFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                {getZxLabel(parseInt(zxFilter))}
              </Badge>
            )}
            {sexFilter && sexFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                {getSexLabel(parseInt(sexFilter))}
              </Badge>
            )}
            {jobLevelFilter && jobLevelFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                Job Lv.{jobLevelFilter}
              </Badge>
            )}
            {typeFilter && typeFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                Type: {typeFilter}
              </Badge>
            )}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredItems.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
