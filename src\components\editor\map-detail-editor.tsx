import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Save, RotateCcw, Map } from 'lucide-react';
import { YbiMapInfo } from '@/lib/parsers/ybi-parser';

interface MapDetailEditorProps {
  map: YbiMapInfo | null;
  onSave: (map: YbiMapInfo) => void;
  onMarkAsEdited: (mapId: string) => void;
  isEdited: boolean;
}

export function MapDetailEditor({ map, onSave, onMarkAsEdited, isEdited }: MapDetailEditorProps) {
  const [editedMap, setEditedMap] = useState<YbiMapInfo | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (map) {
      setEditedMap({ ...map });
      setHasChanges(false);
    }
  }, [map]);

  const handleFieldChange = (field: keyof YbiMapInfo, value: any) => {
    if (!editedMap) return;

    const newMap = { ...editedMap, [field]: value };
    setEditedMap(newMap);
    setHasChanges(true);
  };

  const handleSave = () => {
    if (!editedMap || !hasChanges) return;

    onSave(editedMap);
    onMarkAsEdited(editedMap.id.toString());
    setHasChanges(false);
  };

  const handleReset = () => {
    if (map) {
      setEditedMap({ ...map });
      setHasChanges(false);
    }
  };

  const getMapTypeLabel = (id: number) => {
    if (id >= 1000) return 'Special Area (Dungeons, Events)';
    if (id >= 800) return 'High Level Area';
    if (id >= 300) return 'Mid Level Area';
    return 'Starting Area';
  };

  if (!map || !editedMap) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-muted-foreground">
            <Map className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Chọn một map từ danh sách để chỉnh sửa</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Map className="h-5 w-5" />
              Map #{editedMap.id}
              {isEdited && <Badge variant="secondary">Đã chỉnh sửa</Badge>}
              {hasChanges && <Badge variant="destructive">Chưa lưu</Badge>}
            </CardTitle>
            <CardDescription>
              Chỉnh sửa thông tin chi tiết của map
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <Save className="h-4 w-4 mr-2" />
              Lưu
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>
              
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="id">ID</Label>
                  <Input
                    id="id"
                    type="number"
                    value={editedMap.id}
                    onChange={(e) => handleFieldChange('id', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    {getMapTypeLabel(editedMap.id)}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">Tên Map</Label>
                  <Input
                    id="name"
                    value={editedMap.name}
                    onChange={(e) => handleFieldChange('name', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_5c">U_5C</Label>
                  <Input
                    id="u_5c"
                    type="number"
                    value={editedMap.u_5c}
                    onChange={(e) => handleFieldChange('u_5c', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Position */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Tọa độ</h3>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="x">X Coordinate</Label>
                  <Input
                    id="x"
                    type="number"
                    step="0.1"
                    value={editedMap.x}
                    onChange={(e) => handleFieldChange('x', parseFloat(e.target.value) || 0)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="y">Y Coordinate</Label>
                  <Input
                    id="y"
                    type="number"
                    step="0.1"
                    value={editedMap.y}
                    onChange={(e) => handleFieldChange('y', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="z">Z Coordinate</Label>
                  <Input
                    id="z"
                    type="number"
                    step="0.1"
                    value={editedMap.z}
                    onChange={(e) => handleFieldChange('z', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="bg-muted/50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <strong>Vị trí 1:</strong> ({editedMap.x.toFixed(2)}, {editedMap.y.toFixed(2)}, {editedMap.z.toFixed(2)})
                </p>
              </div>

              {/* Second Coordinates */}
              <h4 className="text-md font-medium">Tọa độ thứ 2</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="x_2">X_2 Coordinate</Label>
                  <Input
                    id="x_2"
                    type="number"
                    step="0.1"
                    value={editedMap.x_2}
                    onChange={(e) => handleFieldChange('x_2', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="y_2">Y_2 Coordinate</Label>
                  <Input
                    id="y_2"
                    type="number"
                    step="0.1"
                    value={editedMap.y_2}
                    onChange={(e) => handleFieldChange('y_2', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="z_2">Z_2 Coordinate</Label>
                  <Input
                    id="z_2"
                    type="number"
                    step="0.1"
                    value={editedMap.z_2}
                    onChange={(e) => handleFieldChange('z_2', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="bg-muted/50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <strong>Vị trí 2:</strong> ({editedMap.x_2.toFixed(2)}, {editedMap.y_2.toFixed(2)}, {editedMap.z_2.toFixed(2)})
                </p>
              </div>
            </div>

            <Separator />

            {/* Audio */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">BGM Settings</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bgm1">BGM 1</Label>
                  <Input
                    id="bgm1"
                    value={editedMap.bgm1}
                    onChange={(e) => handleFieldChange('bgm1', e.target.value)}
                    placeholder="bgm01"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bgm2">BGM 2</Label>
                  <Input
                    id="bgm2"
                    value={editedMap.bgm2}
                    onChange={(e) => handleFieldChange('bgm2', e.target.value)}
                    placeholder="bgm02"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_168">U_168 (BGM3)</Label>
                  <Input
                    id="u_168"
                    value={editedMap.u_168}
                    onChange={(e) => handleFieldChange('u_168', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_1e8">U_1E8 (BGM4)</Label>
                  <Input
                    id="u_1e8"
                    value={editedMap.u_1e8}
                    onChange={(e) => handleFieldChange('u_1e8', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_268">U_268 (BGM5)</Label>
                  <Input
                    id="u_268"
                    value={editedMap.u_268}
                    onChange={(e) => handleFieldChange('u_268', e.target.value)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Additional Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thuộc tính khác</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="offset">Offset</Label>
                  <Input
                    id="offset"
                    type="number"
                    value={editedMap.offset || 0}
                    onChange={(e) => handleFieldChange('offset', parseInt(e.target.value) || 0)}
                    disabled
                    className="bg-muted"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Tips */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Ghi chú</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• ID 1-299: Starting Areas (màu xám)</li>
                  <li>• ID 300-799: Mid Level Areas (màu vàng)</li>
                  <li>• ID 800-999: High Level Areas (màu xanh)</li>
                  <li>• ID 1000+: Special Areas/Dungeons (màu đỏ)</li>
                  <li>• BGM files thường có format: bgm01, bgm02, etc.</li>
                  <li>• Coordinates (X,Y,Z) xác định vị trí spawn trong game</li>
                </ul>
              </div>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
