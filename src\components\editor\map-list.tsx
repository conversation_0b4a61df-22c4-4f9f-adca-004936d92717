import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Search, Map } from 'lucide-react';
import { YbiMapInfo } from '@/lib/parsers/ybi-parser';
import { displayText } from '@/lib/text-converter';

interface MapListProps {
  maps: YbiMapInfo[];
  selectedMapId: string | null;
  onSelectMap: (map: YbiMapInfo) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedItems: Set<string>;
  editedCount: number;
}

export function MapList({
  maps,
  selectedMapId,
  onSelectMap,
  searchTerm,
  onSearchChange,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedItems,
  editedCount
}: MapListProps) {
  // Filter maps based on search
  const filteredMaps = maps.filter(map =>
    map.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    map.bgm1.toLowerCase().includes(searchTerm.toLowerCase()) ||
    map.bgm2.toLowerCase().includes(searchTerm.toLowerCase()) ||
    map.id.toString().includes(searchTerm)
  );

  // Pagination
  const totalPages = Math.ceil(filteredMaps.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentMaps = filteredMaps.slice(startIndex, endIndex);

  const getMapTypeColor = (id: number) => {
    if (id >= 1000) return 'destructive'; // Special maps
    if (id >= 800) return 'default'; // High level areas
    if (id >= 300) return 'outline'; // Mid level areas
    return 'secondary'; // Starting areas
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Map className="h-5 w-5" />
            Maps ({filteredMaps.length.toLocaleString()})
          </div>
          {editedCount > 0 && (
            <Badge variant="secondary">
              {editedCount} đã chỉnh sửa
            </Badge>
          )}
        </CardTitle>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm maps..."
            value={searchTerm}
            onChange={(e) => {
              onSearchChange(e.target.value);
              onPageChange(1); // Reset to first page when searching
            }}
            className="pl-8"
          />
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <AdvancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
            itemsPerPage={itemsPerPage}
            totalItems={filteredMaps.length}
          />
        )}
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-280px)]">
          <div className="space-y-1 p-4">
            {currentMaps.length > 0 ? (
              currentMaps.map((map) => (
                <div
                  key={map.id}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedMapId === map.id.toString() ? 'bg-accent border-primary' : 'border-border'}
                  `}
                  onClick={() => onSelectMap(map)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">#{map.id}</span>
                        <Badge variant={getMapTypeColor(map.id) as any} className="text-xs">
                          {map.id >= 1000 ? 'Special' : map.id >= 800 ? 'High' : map.id >= 300 ? 'Mid' : 'Start'}
                        </Badge>
                        {editedItems.has(map.id.toString()) && (
                          <Badge variant="outline" className="text-xs">
                            Đã sửa
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm font-medium truncate">
                        {displayText(map.name, '(Không có tên)')}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        BGM: {map.bgm1 || 'None'} / {map.bgm2 || 'None'}
                      </p>
                    </div>
                    <div className="text-right text-xs text-muted-foreground">
                      <div>X: {map.x.toFixed(1)}</div>
                      <div>Y: {map.y.toFixed(1)}</div>
                      <div>Z: {map.z.toFixed(1)}</div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? (
                  <div>
                    <Map className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không tìm thấy map nào với từ khóa "{searchTerm}"</p>
                  </div>
                ) : (
                  <div>
                    <Map className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không có maps</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Bottom Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredMaps.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
