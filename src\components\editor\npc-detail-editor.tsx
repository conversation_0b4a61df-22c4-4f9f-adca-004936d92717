import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Save, RotateCcw, Users } from 'lucide-react';
import { YbiNpcInfo } from '@/lib/parsers/ybi-parser';

interface NpcDetailEditorProps {
  npc: YbiNpcInfo | null;
  onSave: (npc: YbiNpcInfo) => void;
  onMarkAsEdited: (npcId: string) => void;
  isEdited: boolean;
}

export function NpcDetailEditor({ npc, onSave, onMarkAsEdited, isEdited }: NpcDetailEditorProps) {
  const [editedNpc, setEditedNpc] = useState<YbiNpcInfo | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (npc) {
      setEditedNpc({ ...npc });
      setHasChanges(false);
    }
  }, [npc]);

  const handleFieldChange = (field: keyof YbiNpcInfo, value: any) => {
    if (!editedNpc) return;

    const newNpc = { ...editedNpc, [field]: value };
    setEditedNpc(newNpc);
    setHasChanges(true);
  };

  const handleSave = () => {
    if (!editedNpc || !hasChanges) return;

    onSave(editedNpc);
    onMarkAsEdited(editedNpc.id.toString());
    setHasChanges(false);
  };

  const handleReset = () => {
    if (npc) {
      setEditedNpc({ ...npc });
      setHasChanges(false);
    }
  };

  const getNpcTypeLabel = (level: number) => {
    if (level <= 10) return 'Newbie Monster';
    if (level <= 30) return 'Normal Monster';
    if (level <= 50) return 'Elite Monster';
    return 'Boss Monster';
  };

  if (!npc || !editedNpc) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Chọn một NPC từ danh sách để chỉnh sửa</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              NPC #{editedNpc.id}
              {isEdited && <Badge variant="secondary">Đã chỉnh sửa</Badge>}
              {hasChanges && <Badge variant="destructive">Chưa lưu</Badge>}
            </CardTitle>
            <CardDescription>
              Chỉnh sửa thông tin chi tiết của NPC
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <Save className="h-4 w-4 mr-2" />
              Lưu
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="id">ID</Label>
                  <Input
                    id="id"
                    type="number"
                    value={editedNpc.id}
                    onChange={(e) => handleFieldChange('id', parseInt(e.target.value) || 0)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Input
                    id="level"
                    type="number"
                    value={editedNpc.level}
                    onChange={(e) => handleFieldChange('level', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-xs text-muted-foreground">
                    {getNpcTypeLabel(editedNpc.level)}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Tên</Label>
                <Input
                  id="name"
                  value={editedNpc.name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="desc">Mô tả chính</Label>
                <Textarea
                  id="desc"
                  value={editedNpc.desc}
                  onChange={(e) => handleFieldChange('desc', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="hp">HP (Health Points)</Label>
                <Input
                  id="hp"
                  type="number"
                  value={editedNpc.hp}
                  onChange={(e) => handleFieldChange('hp', parseInt(e.target.value) || 0)}
                />
                <p className="text-xs text-muted-foreground">
                  {editedNpc.hp.toLocaleString()} HP
                </p>
              </div>
            </div>

            <Separator />

            {/* Menu System - Very Important */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Menu System (Quan trọng)</h3>
              <p className="text-sm text-muted-foreground">
                4 menu này rất quan trọng cho chức năng của NPC
              </p>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="menu1">Menu 1</Label>
                  <Input
                    id="menu1"
                    type="number"
                    value={editedNpc.menu1}
                    onChange={(e) => handleFieldChange('menu1', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="menu2">Menu 2</Label>
                  <Input
                    id="menu2"
                    type="number"
                    value={editedNpc.menu2}
                    onChange={(e) => handleFieldChange('menu2', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="menu3">Menu 3</Label>
                  <Input
                    id="menu3"
                    type="number"
                    value={editedNpc.menu3}
                    onChange={(e) => handleFieldChange('menu3', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="menu4">Menu 4</Label>
                  <Input
                    id="menu4"
                    type="number"
                    value={editedNpc.menu4}
                    onChange={(e) => handleFieldChange('menu4', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Additional Descriptions */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Mô tả bổ sung</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="desc2">Desc 2</Label>
                  <Textarea
                    id="desc2"
                    value={editedNpc.desc2}
                    onChange={(e) => handleFieldChange('desc2', e.target.value)}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="desc3">Desc 3</Label>
                  <Textarea
                    id="desc3"
                    value={editedNpc.desc3}
                    onChange={(e) => handleFieldChange('desc3', e.target.value)}
                    rows={2}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="desc4">Desc 4</Label>
                  <Textarea
                    id="desc4"
                    value={editedNpc.desc4}
                    onChange={(e) => handleFieldChange('desc4', e.target.value)}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="desc5">Desc 5</Label>
                  <Textarea
                    id="desc5"
                    value={editedNpc.desc5}
                    onChange={(e) => handleFieldChange('desc5', e.target.value)}
                    rows={2}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="desc6">Desc 6</Label>
                  <Textarea
                    id="desc6"
                    value={editedNpc.desc6}
                    onChange={(e) => handleFieldChange('desc6', e.target.value)}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="desc7">Desc 7</Label>
                  <Textarea
                    id="desc7"
                    value={editedNpc.desc7}
                    onChange={(e) => handleFieldChange('desc7', e.target.value)}
                    rows={2}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="desc8">Desc 8</Label>
                  <Textarea
                    id="desc8"
                    value={editedNpc.desc8}
                    onChange={(e) => handleFieldChange('desc8', e.target.value)}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="desc9">Desc 9</Label>
                  <Textarea
                    id="desc9"
                    value={editedNpc.desc9}
                    onChange={(e) => handleFieldChange('desc9', e.target.value)}
                    rows={2}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="desc10">Desc 10</Label>
                  <Textarea
                    id="desc10"
                    value={editedNpc.desc10}
                    onChange={(e) => handleFieldChange('desc10', e.target.value)}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="desc11">Desc 11</Label>
                  <Textarea
                    id="desc11"
                    value={editedNpc.desc11}
                    onChange={(e) => handleFieldChange('desc11', e.target.value)}
                    rows={2}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Unknown Fields */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Unknown Fields</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Các trường chưa xác định chức năng. Chỉnh sửa cẩn thận.
              </p>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_474">U_474</Label>
                  <Input
                    id="u_474"
                    type="number"
                    value={editedNpc.u_474}
                    onChange={(e) => handleFieldChange('u_474', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_478">U_478</Label>
                  <Input
                    id="u_478"
                    type="number"
                    value={editedNpc.u_478}
                    onChange={(e) => handleFieldChange('u_478', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_47c">U_47C</Label>
                  <Input
                    id="u_47c"
                    type="number"
                    value={editedNpc.u_47c}
                    onChange={(e) => handleFieldChange('u_47c', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_480">U_480</Label>
                  <Input
                    id="u_480"
                    type="number"
                    value={editedNpc.u_480}
                    onChange={(e) => handleFieldChange('u_480', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_4f4">U_4F4</Label>
                  <Input
                    id="u_4f4"
                    type="number"
                    value={editedNpc.u_4f4}
                    onChange={(e) => handleFieldChange('u_4f4', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_4f8">U_4F8</Label>
                  <Input
                    id="u_4f8"
                    type="number"
                    value={editedNpc.u_4f8}
                    onChange={(e) => handleFieldChange('u_4f8', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_4fc">U_4FC</Label>
                  <Input
                    id="u_4fc"
                    type="number"
                    value={editedNpc.u_4fc}
                    onChange={(e) => handleFieldChange('u_4fc', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_500">U_500</Label>
                  <Input
                    id="u_500"
                    type="number"
                    value={editedNpc.u_500}
                    onChange={(e) => handleFieldChange('u_500', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_504">U_504</Label>
                  <Input
                    id="u_504"
                    type="number"
                    value={editedNpc.u_504}
                    onChange={(e) => handleFieldChange('u_504', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_508">U_508</Label>
                  <Input
                    id="u_508"
                    type="number"
                    value={editedNpc.u_508}
                    onChange={(e) => handleFieldChange('u_508', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_50c">U_50C</Label>
                  <Input
                    id="u_50c"
                    type="number"
                    value={editedNpc.u_50c}
                    onChange={(e) => handleFieldChange('u_50c', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_510">U_510</Label>
                  <Input
                    id="u_510"
                    type="number"
                    value={editedNpc.u_510}
                    onChange={(e) => handleFieldChange('u_510', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_514">U_514</Label>
                  <Input
                    id="u_514"
                    type="number"
                    value={editedNpc.u_514}
                    onChange={(e) => handleFieldChange('u_514', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_518">U_518</Label>
                  <Input
                    id="u_518"
                    type="number"
                    value={editedNpc.u_518}
                    onChange={(e) => handleFieldChange('u_518', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Additional Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thuộc tính khác</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="offset">Offset</Label>
                  <Input
                    id="offset"
                    type="number"
                    value={editedNpc.offset || 0}
                    onChange={(e) => handleFieldChange('offset', parseInt(e.target.value) || 0)}
                    disabled
                    className="bg-muted"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Tips */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Ghi chú</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Level 1-10: Newbie Monster (màu xám)</li>
                  <li>• Level 11-30: Normal Monster (màu xanh)</li>
                  <li>• Level 31-50: Elite Monster (màu vàng)</li>
                  <li>• Level 51+: Boss Monster (màu đỏ)</li>
                </ul>
              </div>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
