import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Search, Users } from 'lucide-react';
import { YbiNpcInfo } from '@/lib/parsers/ybi-parser';
import { displayText } from '@/lib/text-converter';

interface NpcListProps {
  npcs: YbiNpcInfo[];
  selectedNpcId: string | null;
  onSelectNpc: (npc: YbiNpcInfo) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedItems: Set<string>;
  editedCount: number;
}

export function NpcList({
  npcs,
  selectedNpcId,
  onSelectNpc,
  searchTerm,
  onSearchChange,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedItems,
  editedCount
}: NpcListProps) {
  // Filter NPCs based on search
  const filteredNpcs = npcs.filter(npc =>
    npc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    npc.desc.toLowerCase().includes(searchTerm.toLowerCase()) ||
    npc.id.toString().includes(searchTerm)
  );

  // Pagination
  const totalPages = Math.ceil(filteredNpcs.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentNpcs = filteredNpcs.slice(startIndex, endIndex);

  const getNpcTypeColor = (level: number) => {
    if (level <= 10) return 'secondary';
    if (level <= 30) return 'default';
    if (level <= 50) return 'outline';
    return 'destructive';
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            NPCs ({filteredNpcs.length.toLocaleString()})
          </div>
          {editedCount > 0 && (
            <Badge variant="secondary">
              {editedCount} đã chỉnh sửa
            </Badge>
          )}
        </CardTitle>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm NPCs..."
            value={searchTerm}
            onChange={(e) => {
              onSearchChange(e.target.value);
              onPageChange(1); // Reset to first page when searching
            }}
            className="pl-8"
          />
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <AdvancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
            itemsPerPage={itemsPerPage}
            totalItems={filteredNpcs.length}
          />
        )}
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-280px)]">
          <div className="space-y-1 p-4">
            {currentNpcs.length > 0 ? (
              currentNpcs.map((npc) => (
                <div
                  key={npc.id}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedNpcId === npc.id.toString() ? 'bg-accent border-primary' : 'border-border'}
                  `}
                  onClick={() => onSelectNpc(npc)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">#{npc.id}</span>
                        <Badge variant={getNpcTypeColor(npc.level) as any} className="text-xs">
                          Lv.{npc.level}
                        </Badge>
                        {editedItems.has(npc.id.toString()) && (
                          <Badge variant="outline" className="text-xs">
                            Đã sửa
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm font-medium truncate">
                        {displayText(npc.name, '(Không có tên)')}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {displayText(npc.desc, '(Không có mô tả)')}
                      </p>
                    </div>
                    <div className="text-right text-xs text-muted-foreground">
                      <div>HP: {npc.hp.toLocaleString()}</div>
                      <div>Lv.{npc.level}</div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? (
                  <div>
                    <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không tìm thấy NPC nào với từ khóa {searchTerm}</p>
                  </div>
                ) : (
                  <div>
                    <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không có NPCs</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Bottom Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredNpcs.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
