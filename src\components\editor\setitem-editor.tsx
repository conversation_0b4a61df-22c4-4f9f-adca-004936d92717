'use client';

import { useState, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Package,
  Upload,
  Save,
  Plus,
  Trash2,
  Search,
  RefreshCw,
  Info,
  ChevronLeft,
  ChevronRight,
  Undo,
} from 'lucide-react';
import { toast } from 'sonner';
import { SetItemParser, SetItemFile, SetItem, SetOption, SetItemOption } from '@/lib/parsers/setitem-parser';

export function SetItemEditor() {
  const [setItemFile, setSetItemFile] = useState<SetItemFile | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [activeTab, setActiveTab] = useState<'setitems' | 'setoptions'>('setitems');
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);
  
  // Inline editing states
  const [editedSetItems, setEditedSetItems] = useState<Map<number, SetItem>>(new Map());
  const [editedSetOptions, setEditedSetOptions] = useState<Map<number, SetOption>>(new Map());
  const [originalSetItems, setOriginalSetItems] = useState<Map<number, SetItem>>(new Map());
  const [originalSetOptions, setOriginalSetOptions] = useState<Map<number, SetOption>>(new Map());
  
  // Dialogs
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingItem, setDeletingItem] = useState<SetItem | SetOption | null>(null);
  const [newItem, setNewItem] = useState<Partial<SetItem | SetOption>>({});

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle inline editing for SetItems
  const handleSetItemEdit = (setItem: SetItem, field: keyof SetItem, value: any) => {
    // Store original if not already stored
    setOriginalSetItems(prev => {
      if (!prev.has(setItem.id)) {
        const newMap = new Map(prev);
        newMap.set(setItem.id, { ...setItem, options: [...setItem.options] });
        return newMap;
      }
      return prev;
    });

    const updatedSetItem = { ...setItem, [field]: value };
    
    // Update edited map
    setEditedSetItems(prev => {
      const newMap = new Map(prev);
      newMap.set(setItem.id, updatedSetItem);
      return newMap;
    });
    
    // Update main file data
    if (setItemFile) {
      const updatedSetItems = setItemFile.setItems.map(item => 
        item.id === setItem.id ? updatedSetItem : item
      );
      
      setSetItemFile({
        ...setItemFile,
        setItems: updatedSetItems
      });
    }
    
    setHasUnsavedChanges(true);
  };

  // Handle inline editing for SetOptions
  const handleSetOptionEdit = (setOption: SetOption, field: keyof SetOption, value: any) => {
    // Store original if not already stored
    setOriginalSetOptions(prev => {
      if (!prev.has(setOption.id)) {
        const newMap = new Map(prev);
        newMap.set(setOption.id, { ...setOption });
        return newMap;
      }
      return prev;
    });

    const updatedSetOption = { ...setOption, [field]: value };
    
    // Update edited map
    setEditedSetOptions(prev => {
      const newMap = new Map(prev);
      newMap.set(setOption.id, updatedSetOption);
      return newMap;
    });
    
    // Update main file data
    if (setItemFile) {
      const updatedSetOptions = setItemFile.setOptions.map(option => 
        option.id === setOption.id ? updatedSetOption : option
      );
      
      setSetItemFile({
        ...setItemFile,
        setOptions: updatedSetOptions
      });
    }
    
    setHasUnsavedChanges(true);
  };

  // Handle SetItem option editing
  const handleSetItemOptionEdit = (setItem: SetItem, optionIndex: number, field: keyof SetItemOption, value: any) => {
    const updatedOptions = [...setItem.options];
    updatedOptions[optionIndex] = { ...updatedOptions[optionIndex], [field]: value };
    
    handleSetItemEdit(setItem, 'options', updatedOptions);
  };

  // Add new option to SetItem
  const addSetItemOption = (setItem: SetItem) => {
    const newOption: SetItemOption = { id: 0, value: 0 };
    const updatedOptions = [...setItem.options, newOption];
    handleSetItemEdit(setItem, 'options', updatedOptions);
  };

  // Remove option from SetItem
  const removeSetItemOption = (setItem: SetItem, optionIndex: number) => {
    const updatedOptions = setItem.options.filter((_, index) => index !== optionIndex);
    handleSetItemEdit(setItem, 'options', updatedOptions);
  };

  // Handle revert to original state
  const handleRevertSetItem = (setItem: SetItem) => {
    const original = originalSetItems.get(setItem.id);
    if (!original) return;

    // Remove from edited maps
    setEditedSetItems(prev => {
      const newMap = new Map(prev);
      newMap.delete(setItem.id);
      return newMap;
    });

    setOriginalSetItems(prev => {
      const newMap = new Map(prev);
      newMap.delete(setItem.id);
      return newMap;
    });

    // Update main file data
    if (setItemFile) {
      const updatedSetItems = setItemFile.setItems.map(item => 
        item.id === setItem.id ? original : item
      );
      
      setSetItemFile({
        ...setItemFile,
        setItems: updatedSetItems
      });
    }

    updateUnsavedChangesState();
    toast.success('Đã khôi phục SetItem về trạng thái gốc');
  };

  const handleRevertSetOption = (setOption: SetOption) => {
    const original = originalSetOptions.get(setOption.id);
    if (!original) return;

    // Remove from edited maps
    setEditedSetOptions(prev => {
      const newMap = new Map(prev);
      newMap.delete(setOption.id);
      return newMap;
    });

    setOriginalSetOptions(prev => {
      const newMap = new Map(prev);
      newMap.delete(setOption.id);
      return newMap;
    });

    // Update main file data
    if (setItemFile) {
      const updatedSetOptions = setItemFile.setOptions.map(option => 
        option.id === setOption.id ? original : option
      );
      
      setSetItemFile({
        ...setItemFile,
        setOptions: updatedSetOptions
      });
    }

    updateUnsavedChangesState();
    toast.success('Đã khôi phục SetOption về trạng thái gốc');
  };

  // Update unsaved changes state
  const updateUnsavedChangesState = () => {
    const totalEdits = editedSetItems.size + editedSetOptions.size - 2; // -2 because we just removed one
    setHasUnsavedChanges(totalEdits > 0);
  };

  // Get edited count
  const getEditedCount = () => editedSetItems.size + editedSetOptions.size;

  // Reset edited state
  const resetEditedState = () => {
    setEditedSetItems(new Map());
    setEditedSetOptions(new Map());
    setOriginalSetItems(new Map());
    setOriginalSetOptions(new Map());
    setHasUnsavedChanges(false);
  };

  // Handle file selection
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    try {
      const buffer = await file.arrayBuffer();
      
      // Validate file
      const validation = SetItemParser.validate(buffer);
      if (!validation.valid) {
        toast.error(`File không hợp lệ: ${validation.error}`);
        return;
      }

      // Parse file
      const parsedFile = SetItemParser.parse(buffer, file.name);
      setSetItemFile(parsedFile);
      resetEditedState();
      setCurrentPage(1);
      
      toast.success(`Đã tải file ${file.name} thành công (${parsedFile.setItems.length} SetItems, ${parsedFile.setOptions.length} SetOptions)`);
      
      // Debug info
      console.log('Parsed SetItem file:', {
        fileName: file.name,
        fileSize: buffer.byteLength,
        setItemCount: parsedFile.setItems.length,
        setOptionCount: parsedFile.setOptions.length,
        firstSetItem: parsedFile.setItems[0],
        firstSetOption: parsedFile.setOptions[0]
      });
    } catch (error) {
      console.error('Error parsing file:', error);
      toast.error(`Lỗi khi đọc file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, []);

  // Handle open file button
  const handleOpenFile = () => {
    fileInputRef.current?.click();
  };

  // Handle save file
  const handleSaveFile = useCallback(() => {
    if (!setItemFile) return;

    try {
      console.log('Saving SetItem file:', {
        setItemCount: setItemFile.setItems.length,
        setOptionCount: setItemFile.setOptions.length,
        editedCount: getEditedCount()
      });

      const buffer = SetItemParser.generate(setItemFile);
      
      const blob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = setItemFile.fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      resetEditedState();
      toast.success('Đã lưu file thành công');
    } catch (error) {
      console.error('Error saving file:', error);
      toast.error(`Lỗi khi lưu file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [setItemFile]);

  // Get current data based on active tab
  const getCurrentData = () => {
    if (!setItemFile) return [];
    
    if (activeTab === 'setitems') {
      return setItemFile.setItems.filter(item => 
        item.id.toString().includes(searchTerm) ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else {
      return setItemFile.setOptions.filter(option => 
        option.id.toString().includes(searchTerm) ||
        option.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
  };

  // Pagination calculations
  const currentData = getCurrentData();
  const totalPages = Math.ceil(currentData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = currentData.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle search with page reset
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab as 'setitems' | 'setoptions');
    setCurrentPage(1);
    setSearchTerm('');
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            SetItem Editor
            {hasUnsavedChanges && (
              <Badge variant="destructive" className="ml-2">
                {getEditedCount()} items edited
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 flex-wrap">
            <Button onClick={handleOpenFile} variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Mở File
            </Button>
            
            {setItemFile && (
              <>
                <Button onClick={handleSaveFile} disabled={!hasUnsavedChanges}>
                  <Save className="h-4 w-4 mr-2" />
                  Lưu File
                </Button>
                
                <Button onClick={() => setIsAddDialogOpen(true)} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm {activeTab === 'setitems' ? 'SetItem' : 'SetOption'}
                </Button>
              </>
            )}

            {loading && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                Đang xử lý...
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept=".cfg"
            onChange={handleFileSelect}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* File Info */}
      {setItemFile && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Thông tin File
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-6 gap-4">
              <div>
                <Label className="text-sm font-medium">Tên File</Label>
                <p className="text-sm">{setItemFile.fileName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Kích thước</Label>
                <p className="text-sm">{(setItemFile.fileSize / 1024).toFixed(2)} KB</p>
              </div>
              <div>
                <Label className="text-sm font-medium">SetItems</Label>
                <p className="text-sm">{setItemFile.setItems.length}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">SetOptions</Label>
                <p className="text-sm">{setItemFile.setOptions.length}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">SetItem Size</Label>
                <p className="text-sm">3828 bytes</p>
              </div>
              <div>
                <Label className="text-sm font-medium">SetOption Size</Label>
                <p className="text-sm">68 bytes</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      {setItemFile && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                {activeTab === 'setitems' ? 'SetItems' : 'SetOptions'} ({currentData.length})
                {totalPages > 1 && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    - Page {currentPage} of {totalPages}
                  </span>
                )}
              </CardTitle>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm ID hoặc tên..."
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>

                {totalPages > 1 && (
                  <div className="flex items-center gap-1">
                    <Label className="text-xs text-muted-foreground">Page:</Label>
                    <Input
                      type="number"
                      min="1"
                      max={totalPages}
                      value={currentPage}
                      onChange={(e) => {
                        const page = parseInt(e.target.value);
                        if (page >= 1 && page <= totalPages) {
                          handlePageChange(page);
                        }
                      }}
                      className="w-16 h-8 text-xs"
                    />
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={handleTabChange}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="setitems">SetItems ({setItemFile.setItems.length})</TabsTrigger>
                <TabsTrigger value="setoptions">SetOptions ({setItemFile.setOptions.length})</TabsTrigger>
              </TabsList>

              <TabsContent value="setitems" className="mt-0">
                <div className="border rounded-lg overflow-hidden">
                  {/* SetItems Header */}
                  <div className="border-b bg-muted/50 sticky top-0 z-10">
                    <div className="grid grid-cols-12 gap-2 p-3 text-sm font-medium">
                      <div className="col-span-1 text-center">ID</div>
                      <div className="col-span-3">Name</div>
                      <div className="col-span-1 text-center">Part1</div>
                      <div className="col-span-1 text-center">Part2</div>
                      <div className="col-span-1 text-center">Part3</div>
                      <div className="col-span-1 text-center">Part4</div>
                      <div className="col-span-1 text-center">Part5</div>
                      <div className="col-span-1 text-center">Part6</div>
                      <div className="col-span-1 text-center">Options</div>
                      <div className="col-span-1 text-center">Action</div>
                    </div>
                  </div>

                  {/* SetItems Body */}
                  <div className="max-h-[calc(100vh-450px)] min-h-[400px] overflow-y-auto">
                    <div className="divide-y">
                      {(paginatedData as SetItem[]).map((setItem) => {
                        const isEdited = editedSetItems.has(setItem.id);
                        const hasOriginal = originalSetItems.has(setItem.id);

                        return (
                          <div
                            key={setItem.id}
                            className={`grid grid-cols-12 gap-2 p-3 hover:bg-muted/50 transition-colors ${
                              isEdited ? 'bg-yellow-50 dark:bg-yellow-950/20' : ''
                            }`}
                          >
                            {/* ID */}
                            <div className="col-span-1 flex items-center justify-center">
                              <Input
                                type="number"
                                value={setItem.id}
                                onChange={(e) => handleSetItemEdit(setItem, 'id', parseInt(e.target.value) || 0)}
                                className="border-0 bg-transparent p-0 h-auto text-xs text-center w-full focus-visible:ring-1"
                              />
                            </div>

                            {/* Name */}
                            <div className="col-span-3 flex items-center">
                              <Input
                                value={setItem.name}
                                onChange={(e) => handleSetItemEdit(setItem, 'name', e.target.value)}
                                className="border-0 bg-transparent p-0 h-auto text-sm focus-visible:ring-1 w-full"
                                placeholder="SetItem name..."
                              />
                            </div>

                            {/* Parts */}
                            {(['part1', 'part2', 'part3', 'part4', 'part5', 'part6'] as const).map((part) => (
                              <div key={part} className="col-span-1 flex items-center justify-center">
                                <Input
                                  type="number"
                                  value={setItem[part]}
                                  onChange={(e) => handleSetItemEdit(setItem, part, parseInt(e.target.value) || 0)}
                                  className="border-0 bg-transparent p-0 h-auto text-xs text-center w-full focus-visible:ring-1"
                                />
                              </div>
                            ))}

                            {/* Options Count */}
                            <div className="col-span-1 flex items-center justify-center">
                              <Badge variant="secondary" className="text-xs">
                                {setItem.options.length}
                              </Badge>
                            </div>

                            {/* Action */}
                            <div className="col-span-1 flex items-center justify-center">
                              {hasOriginal ? (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleRevertSetItem(setItem)}
                                  className="h-8 w-8 p-0"
                                  title="Khôi phục về trạng thái gốc"
                                >
                                  <Undo className="h-3 w-3" />
                                </Button>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    setDeletingItem(setItem);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                  className="h-8 w-8 p-0"
                                  title="Xóa SetItem"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {paginatedData.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        {searchTerm ? 'Không tìm thấy SetItem nào' : 'Chưa có SetItem nào'}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="setoptions" className="mt-0">
                <div className="border rounded-lg overflow-hidden">
                  {/* SetOptions Header */}
                  <div className="border-b bg-muted/50 sticky top-0 z-10">
                    <div className="grid grid-cols-4 gap-2 p-3 text-sm font-medium">
                      <div className="col-span-1 text-center">ID</div>
                      <div className="col-span-2">Name</div>
                      <div className="col-span-1 text-center">Action</div>
                    </div>
                  </div>

                  {/* SetOptions Body */}
                  <div className="max-h-[calc(100vh-450px)] min-h-[400px] overflow-y-auto">
                    <div className="divide-y">
                      {(paginatedData as SetOption[]).map((setOption) => {
                        const isEdited = editedSetOptions.has(setOption.id);
                        const hasOriginal = originalSetOptions.has(setOption.id);

                        return (
                          <div
                            key={setOption.id}
                            className={`grid grid-cols-4 gap-2 p-3 hover:bg-muted/50 transition-colors ${
                              isEdited ? 'bg-yellow-50 dark:bg-yellow-950/20' : ''
                            }`}
                          >
                            {/* ID */}
                            <div className="col-span-1 flex items-center justify-center">
                              <Input
                                type="number"
                                value={setOption.id}
                                onChange={(e) => handleSetOptionEdit(setOption, 'id', parseInt(e.target.value) || 0)}
                                className="border-0 bg-transparent p-0 h-auto text-xs text-center w-full focus-visible:ring-1"
                              />
                            </div>

                            {/* Name */}
                            <div className="col-span-2 flex items-center">
                              <Input
                                value={setOption.name}
                                onChange={(e) => handleSetOptionEdit(setOption, 'name', e.target.value)}
                                className="border-0 bg-transparent p-0 h-auto text-sm focus-visible:ring-1 w-full"
                                placeholder="SetOption name..."
                              />
                            </div>

                            {/* Action */}
                            <div className="col-span-1 flex items-center justify-center">
                              {hasOriginal ? (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleRevertSetOption(setOption)}
                                  className="h-8 w-8 p-0"
                                  title="Khôi phục về trạng thái gốc"
                                >
                                  <Undo className="h-3 w-3" />
                                </Button>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    setDeletingItem(setOption);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                  className="h-8 w-8 p-0"
                                  title="Xóa SetOption"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {paginatedData.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        {searchTerm ? 'Không tìm thấy SetOption nào' : 'Chưa có SetOption nào'}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-4 py-3 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>
                    {startIndex + 1}-{Math.min(endIndex, currentData.length)} của {currentData.length} items
                  </span>
                  {getEditedCount() > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {getEditedCount()} đã sửa
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Trước
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          size="sm"
                          variant={currentPage === pageNum ? "default" : "outline"}
                          onClick={() => handlePageChange(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                  >
                    Sau
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
      
      {/* No file loaded state */}
      {!setItemFile && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Chưa có file nào được tải</h3>
            <p className="text-muted-foreground mb-4">
              Chọn file SetItem.cfg để bắt đầu chỉnh sửa
            </p>
            <Button onClick={handleOpenFile}>
              <Upload className="h-4 w-4 mr-2" />
              Mở File
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
