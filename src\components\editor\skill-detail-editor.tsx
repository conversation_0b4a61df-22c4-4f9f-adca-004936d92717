import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Save, RotateCcw, Zap } from 'lucide-react';
import { YbiSkill } from '@/lib/parsers/ybi-parser';

interface SkillDetailEditorProps {
  skill: YbiSkill | null;
  onSave: (skill: YbiSkill) => void;
  onMarkAsEdited: (skillId: string) => void;
  isEdited: boolean;
}

export function SkillDetailEditor({ skill, onSave, onMarkAsEdited, isEdited }: SkillDetailEditorProps) {
  const [editedSkill, setEditedSkill] = useState<YbiSkill | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (skill) {
      setEditedSkill({ ...skill });
      setHasChanges(false);
    }
  }, [skill]);

  const handleFieldChange = (field: keyof YbiSkill, value: any) => {
    if (!editedSkill) return;

    const newSkill = { ...editedSkill, [field]: value };
    setEditedSkill(newSkill);
    setHasChanges(true);
  };

  const handleSave = () => {
    if (!editedSkill || !hasChanges) return;

    onSave(editedSkill);
    onMarkAsEdited(editedSkill.id.toString());
    setHasChanges(false);
  };

  const handleReset = () => {
    if (skill) {
      setEditedSkill({ ...skill });
      setHasChanges(false);
    }
  };

  const getSkillTypeLabel = (type: number) => {
    switch (type) {
      case 0: return 'Label';
      case 1: return 'Active Skill';
      case 2: return 'Passive Skill';
      default: return `Type ${type}`;
    }
  };

  if (!skill || !editedSkill) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-muted-foreground">
            <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Chọn một skill từ danh sách để chỉnh sửa</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Skill #{editedSkill.id}
              {isEdited && <Badge variant="secondary">Đã chỉnh sửa</Badge>}
              {hasChanges && <Badge variant="destructive">Chưa lưu</Badge>}
            </CardTitle>
            <CardDescription>
              Chỉnh sửa thông tin chi tiết của skill
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <Save className="h-4 w-4 mr-2" />
              Lưu
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="id">ID</Label>
                  <Input
                    id="id"
                    type="number"
                    value={editedSkill.id}
                    onChange={(e) => handleFieldChange('id', parseInt(e.target.value) || 0)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select
                    value={editedSkill.type.toString()}
                    onValueChange={(value) => handleFieldChange('type', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Label</SelectItem>
                      <SelectItem value="1">Active Skill</SelectItem>
                      <SelectItem value="2">Passive Skill</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    {getSkillTypeLabel(editedSkill.type)}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Tên</Label>
                <Input
                  id="name"
                  value={editedSkill.name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="desc">Mô tả</Label>
                <Textarea
                  id="desc"
                  value={editedSkill.desc}
                  onChange={(e) => handleFieldChange('desc', e.target.value)}
                  rows={3}
                />
              </div>
            </div>

            <Separator />

            {/* Requirements */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Yêu cầu</h3>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="job">Job</Label>
                  <Input
                    id="job"
                    type="number"
                    value={editedSkill.job}
                    onChange={(e) => handleFieldChange('job', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="jobLevel">Job Level</Label>
                  <Input
                    id="jobLevel"
                    type="number"
                    value={editedSkill.jobLevel}
                    onChange={(e) => handleFieldChange('jobLevel', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Input
                    id="level"
                    type="number"
                    value={editedSkill.level}
                    onChange={(e) => handleFieldChange('level', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="zx">ZX (Faction)</Label>
                  <Input
                    id="zx"
                    type="number"
                    value={editedSkill.zx}
                    onChange={(e) => handleFieldChange('zx', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Combat Stats */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thống kê chiến đấu</h3>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="mana">Mana Cost</Label>
                  <Input
                    id="mana"
                    type="number"
                    value={editedSkill.mana}
                    onChange={(e) => handleFieldChange('mana', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="atk">Attack Power</Label>
                  <Input
                    id="atk"
                    type="number"
                    value={editedSkill.atk}
                    onChange={(e) => handleFieldChange('atk', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sp">SP Cost</Label>
                  <Input
                    id="sp"
                    type="number"
                    value={editedSkill.sp}
                    onChange={(e) => handleFieldChange('sp', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="atkEachLevel">ATK per Level</Label>
                  <Input
                    id="atkEachLevel"
                    type="number"
                    value={editedSkill.atkEachLevel}
                    onChange={(e) => handleFieldChange('atkEachLevel', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sp_upgrade">SP Upgrade Cost</Label>
                  <Input
                    id="sp_upgrade"
                    type="number"
                    value={editedSkill.sp_upgrade}
                    onChange={(e) => handleFieldChange('sp_upgrade', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sp_tt">SP TT</Label>
                  <Input
                    id="sp_tt"
                    type="number"
                    value={editedSkill.sp_tt}
                    onChange={(e) => handleFieldChange('sp_tt', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Skill Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thuộc tính Skill</h3>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="effect">Effect</Label>
                  <Input
                    id="effect"
                    type="number"
                    value={editedSkill.effect}
                    onChange={(e) => handleFieldChange('effect', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="index">Index</Label>
                  <Input
                    id="index"
                    type="number"
                    value={editedSkill.index}
                    onChange={(e) => handleFieldChange('index', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="totalSkill">Total Skills</Label>
                  <Input
                    id="totalSkill"
                    type="number"
                    value={editedSkill.totalSkill}
                    onChange={(e) => handleFieldChange('totalSkill', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unknown1">Unknown 1</Label>
                  <Input
                    id="unknown1"
                    type="number"
                    value={editedSkill.unknown1}
                    onChange={(e) => handleFieldChange('unknown1', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Unknown Fields */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Unknown Fields</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Các trường chưa xác định chức năng. Chỉnh sửa cẩn thận.
              </p>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_54">U_54</Label>
                  <Input
                    id="u_54"
                    type="number"
                    value={editedSkill.u_54}
                    onChange={(e) => handleFieldChange('u_54', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_58">U_58</Label>
                  <Input
                    id="u_58"
                    type="number"
                    value={editedSkill.u_58}
                    onChange={(e) => handleFieldChange('u_58', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_60">U_60</Label>
                  <Input
                    id="u_60"
                    type="number"
                    value={editedSkill.u_60}
                    onChange={(e) => handleFieldChange('u_60', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_194">U_194</Label>
                  <Input
                    id="u_194"
                    type="number"
                    value={editedSkill.u_194}
                    onChange={(e) => handleFieldChange('u_194', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="u_198">U_198</Label>
                  <Input
                    id="u_198"
                    type="number"
                    value={editedSkill.u_198}
                    onChange={(e) => handleFieldChange('u_198', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="offset">Offset</Label>
                  <Input
                    id="offset"
                    type="number"
                    value={editedSkill.offset || 0}
                    onChange={(e) => handleFieldChange('offset', parseInt(e.target.value) || 0)}
                    disabled
                    className="bg-muted"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Additional Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thuộc tính khác</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="offset">Offset</Label>
                  <Input
                    id="offset"
                    type="number"
                    value={editedSkill.offset || 0}
                    onChange={(e) => handleFieldChange('offset', parseInt(e.target.value) || 0)}
                    disabled
                    className="bg-muted"
                  />
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
