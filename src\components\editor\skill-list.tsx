import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import {  Zap } from 'lucide-react';
import { YbiSkill } from '@/lib/parsers/ybi-parser';
import { displayText } from '@/lib/text-converter';

interface SkillListProps {
  skills: YbiSkill[];
  selectedSkillId: string | null;
  onSelectSkill: (skill: YbiSkill) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedItems: Set<string>;
  editedCount: number;
}

export function SkillList({
  skills,
  selectedSkillId,
  onSelectSkill,
  searchTerm,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedItems,
}: SkillListProps) {
  // Filter skills based on search
  const filteredSkills = skills.filter(skill =>
    skill.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    skill.desc.toLowerCase().includes(searchTerm.toLowerCase()) ||
    skill.id.toString().includes(searchTerm)
  );

  // Pagination
  const totalPages = Math.ceil(filteredSkills.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentSkills = filteredSkills.slice(startIndex, endIndex);

  const getSkillTypeLabel = (type: number) => {
    switch (type) {
      case 0: return 'Label';
      case 1: return 'Active';
      case 2: return 'Passive';
      default: return `Type ${type}`;
    }
  };

  const getSkillTypeColor = (type: number) => {
    switch (type) {
      case 0: return 'secondary';
      case 1: return 'default';
      case 2: return 'outline';
      default: return 'destructive';
    }
  };

  return (
    <Card className="h-full">
      {/* <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Skills ({filteredSkills.length.toLocaleString()})
          </div>
          {editedCount > 0 && (
            <Badge variant="secondary">
              {editedCount} đã chỉnh sửa
            </Badge>
          )}
        </CardTitle>
        
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm skills..."
            value={searchTerm}
            onChange={(e) => {
              onSearchChange(e.target.value);
              onPageChange(1); // Reset to first page when searching
            }}
            className="pl-8"
          />
        </div>

        {totalPages > 1 && (
          <AdvancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
            itemsPerPage={itemsPerPage}
            totalItems={filteredSkills.length}
          />
        )}
      </CardHeader> */}

      <CardContent className="p-0">
        {/* <ScrollArea className="h-[calc(100vh-180px)]"> */}
          <div className="space-y-2 p-3 h-[calc(100vh-180px)] overflow-y-auto">
            {currentSkills.length > 0 ? (
              currentSkills.map((skill) => (
                <div
                  key={skill.id}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedSkillId === skill.id.toString() ? 'bg-accent border-primary' : 'border-border'}
                  `}
                  onClick={() => onSelectSkill(skill)}
                >
                  <div className="flex items-start gap-3">
                    {/* Skill Icon */}
                    <div className="flex-shrink-0">
                      <img
                        src={`http://one.chamthoi.com/mugong/${skill.id}.jpg`}
                        alt={`Skill ${skill.id}`}
                        className="w-8 h-8 rounded border bg-muted object-cover"
                        onError={(e) => {
                          // Fallback to a default skill icon
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjRUZGNkZGIiByeD0iNCIvPgo8cGF0aCBkPSJNMTYgOEwxOSAxNEgxM0wxNiA4WiIgZmlsbD0iIzg4NTVGRiIvPgo8cGF0aCBkPSJNMTYgMjRMMTMgMThIMTlMMTYgMjRaIiBmaWxsPSIjODg1NUZGIi8+CjxjaXJjbGUgY3g9IjE2IiBjeT0iMTYiIHI9IjMiIGZpbGw9IiM4ODU1RkYiLz4KPC9zdmc+';
                        }}
                      />
                    </div>

                    {/* Skill Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">#{skill.id}</span>
                        <Badge variant={getSkillTypeColor(skill.type) as any} className="text-xs px-1 py-0">
                          {getSkillTypeLabel(skill.type)}
                        </Badge>
                        {editedItems.has(skill.id.toString()) && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            Đã sửa
                          </Badge>
                        )}
                      </div>

                      {/* Skill Name - with overflow hidden */}
                      <h4 className="text-sm font-medium text-foreground mb-1 overflow-hidden">
                        <span className="block truncate" title={displayText(skill.name, '(Không có tên)')}>
                          {displayText(skill.name, '(Không có tên)')}
                        </span>
                      </h4>

                      {/* Skill Description - with overflow hidden */}
                      <p className="text-xs text-muted-foreground mb-2 overflow-hidden">
                        <span className="block truncate" title={displayText(skill.desc, '(Không có mô tả)')}>
                          {displayText(skill.desc, '(Không có mô tả)')}
                        </span>
                      </p>

                      {/* Skill Stats - Compact layout */}
                      <div className="flex flex-wrap gap-x-3 gap-y-1 text-xs text-muted-foreground">
                        <span>Lv.{skill.level}</span>
                        <span>Job:{skill.job}</span>
                        <span className="text-blue-600 font-medium">MP:{skill.mana}</span>
                        <span className="text-red-600 font-medium">ATK:{skill.atk}</span>
                        <span>SP:{skill.sp}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? (
                  <div>
                    <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không tìm thấy skill nào với từ khóa {searchTerm}</p>
                  </div>
                ) : (
                  <div>
                    <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không có skills</p>
                  </div>
                )}
              </div>
            )}
          </div>
        {/* </ScrollArea> */}

        {/* Bottom Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredSkills.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
