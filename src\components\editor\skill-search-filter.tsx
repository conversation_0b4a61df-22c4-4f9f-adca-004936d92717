import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Search, Zap, Filter, X } from 'lucide-react';
import { YbiSkill } from '@/lib/parsers/ybi-parser';

interface SkillSearchFilterProps {
  skills: YbiSkill[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedCount: number;
  filteredSkills: YbiSkill[];
  // Filter states
  levelFilter: string;
  onLevelFilterChange: (level: string) => void;
  typeFilter: string;
  onTypeFilterChange: (type: string) => void;
  jobFilter: string;
  onJobFilterChange: (job: string) => void;
  onClearFilters: () => void;
}

export function SkillSearchFilter({
  skills,
  searchTerm,
  onSearchChange,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedCount,
  filteredSkills,
  levelFilter,
  onLevelFilterChange,
  typeFilter,
  onTypeFilterChange,
  jobFilter,
  onJobFilterChange,
  onClearFilters
}: SkillSearchFilterProps) {
  const totalPages = Math.ceil(filteredSkills.length / itemsPerPage);
  
  // Skill Type labels
  const getSkillTypeLabel = (type: number) => {
    switch (type) {
      case 0: return 'Label';
      case 1: return 'Active';
      case 2: return 'Passive';
      default: return `Type ${type}`;
    }
  };

  // Get unique values from data
  const uniqueTypes = Array.from(new Set(skills.map(skill => skill.type))).sort((a, b) => a - b);
  const uniqueJobs = Array.from(new Set(skills.map(skill => skill.job))).sort((a, b) => a - b);
  
  // Get level ranges
  const levelRanges = [
    { value: 'all', label: 'Tất cả level' },
    { value: '1-10', label: 'Level 1-10' },
    { value: '11-30', label: 'Level 11-30' },
    { value: '31-50', label: 'Level 31-50' },
    { value: '51-70', label: 'Level 51-70' },
    { value: '71-90', label: 'Level 71-90' },
    { value: '91+', label: 'Level 91+' },
  ];

  const hasActiveFilters = searchTerm || 
    (levelFilter && levelFilter !== 'all') || 
    (typeFilter && typeFilter !== 'all') ||
    (jobFilter && jobFilter !== 'all');

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Skills ({filteredSkills.length.toLocaleString()} / {skills.length.toLocaleString()})
            {editedCount > 0 && (
              <Badge variant="secondary">
                {editedCount} đã chỉnh sửa
              </Badge>
            )}
          </div>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
              className="h-8"
            >
              <X className="h-4 w-4 mr-2" />
              Xóa bộ lọc
            </Button>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm theo tên, mô tả hoặc ID..."
            value={searchTerm}
            onChange={(e) => {
              onSearchChange(e.target.value);
              onPageChange(1);
            }}
            className="pl-8"
          />
        </div>

        {/* Filters Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
          {/* Level Filter */}
          <div>
            <Select value={levelFilter || "all"} onValueChange={(value) => {
              onLevelFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Level" />
              </SelectTrigger>
              <SelectContent>
                {levelRanges.map((range) => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Type Filter */}
          <div>
            <Select value={typeFilter || "all"} onValueChange={(value) => {
              onTypeFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Loại skill" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                {uniqueTypes.map((type) => (
                  <SelectItem key={type} value={type.toString()}>
                    {getSkillTypeLabel(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Job Filter */}
          <div>
            <Select value={jobFilter || "all"} onValueChange={(value) => {
              onJobFilterChange(value === "all" ? "" : value);
              onPageChange(1);
            }}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Job" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả job</SelectItem>
                {uniqueJobs.map((job) => (
                  <SelectItem key={job} value={job.toString()}>
                    Job {job}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2">
            {searchTerm && (
              <Badge variant="secondary" className="gap-1">
                <Search className="h-3 w-3" />
                {searchTerm}
              </Badge>
            )}
            {levelFilter && levelFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                Level: {levelFilter}
              </Badge>
            )}
            {typeFilter && typeFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                {getSkillTypeLabel(parseInt(typeFilter))}
              </Badge>
            )}
            {jobFilter && jobFilter !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                Job: {jobFilter}
              </Badge>
            )}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredSkills.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
