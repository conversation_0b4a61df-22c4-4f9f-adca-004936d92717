import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Save, RotateCcw, Crown } from 'lucide-react';
import { YbiHeroTitle } from '@/lib/parsers/ybi-parser';

interface TitleDetailEditorProps {
  title: YbiHeroTitle | null;
  onSave: (title: YbiHeroTitle) => void;
  onMarkAsEdited: (titleId: string) => void;
  isEdited: boolean;
}

export function TitleDetailEditor({ title, onSave, onMarkAsEdited, isEdited }: TitleDetailEditorProps) {
  const [editedTitle, setEditedTitle] = useState<YbiHeroTitle | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (title) {
      setEditedTitle({ ...title });
      setHasChanges(false);
    }
  }, [title]);

  const handleFieldChange = (field: keyof YbiHeroTitle, value: any) => {
    if (!editedTitle) return;

    const newTitle = { ...editedTitle, [field]: value };
    setEditedTitle(newTitle);
    setHasChanges(true);
  };

  const handleSave = () => {
    if (!editedTitle || !hasChanges) return;

    onSave(editedTitle);
    onMarkAsEdited(editedTitle.offset.toString());
    setHasChanges(false);
  };

  const handleReset = () => {
    if (title) {
      setEditedTitle({ ...title });
      setHasChanges(false);
    }
  };

  if (!title || !editedTitle) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-muted-foreground">
            <Crown className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Chọn một title từ danh sách để chỉnh sửa</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5" />
              Hero Title #{editedTitle.offset}
              {isEdited && <Badge variant="secondary">Đã chỉnh sửa</Badge>}
              {hasChanges && <Badge variant="destructive">Chưa lưu</Badge>}
            </CardTitle>
            <CardDescription>
              Chỉnh sửa thông tin chi tiết của hero title
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <Save className="h-4 w-4 mr-2" />
              Lưu
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>
              
              <div className="space-y-2">
                <Label htmlFor="offset">Offset (ID)</Label>
                <Input
                  id="offset"
                  type="number"
                  value={editedTitle.offset}
                  onChange={(e) => handleFieldChange('offset', parseInt(e.target.value) || 0)}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">
                  Vị trí trong file, không thể thay đổi
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Tên Title</Label>
                <Input
                  id="name"
                  value={editedTitle.name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                  placeholder="Nhập tên title..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="job">Job</Label>
                  <Input
                    id="job"
                    type="number"
                    value={editedTitle.job}
                    onChange={(e) => handleFieldChange('job', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="zx">ZX (Faction)</Label>
                  <Input
                    id="zx"
                    type="number"
                    value={editedTitle.zx}
                    onChange={(e) => handleFieldChange('zx', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="jobLevel">Job Level</Label>
                  <Input
                    id="jobLevel"
                    type="number"
                    value={editedTitle.jobLevel}
                    onChange={(e) => handleFieldChange('jobLevel', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Input
                    id="level"
                    type="number"
                    value={editedTitle.level}
                    onChange={(e) => handleFieldChange('level', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="u_40">U_40</Label>
                  <Input
                    id="u_40"
                    type="number"
                    value={editedTitle.u_40}
                    onChange={(e) => handleFieldChange('u_40', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Title Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thuộc tính Title</h3>
              
              <div className="bg-muted/50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Offset:</span>
                    <span className="ml-2 text-muted-foreground">{editedTitle.offset}</span>
                  </div>
                  <div>
                    <span className="font-medium">Type:</span>
                    <span className="ml-2 text-muted-foreground">Hero Title</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Tips */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Ghi chú</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Hero Titles là danh hiệu mà người chơi có thể đạt được</li>
                  <li>• Offset là vị trí duy nhất trong file, không thể thay đổi</li>
                  <li>• Tên title sẽ hiển thị trong game</li>
                  <li>• Mô tả giải thích cách thức đạt được title hoặc ý nghĩa của nó</li>
                  <li>• Titles thường liên quan đến thành tựu, level, hoặc sự kiện đặc biệt</li>
                </ul>
              </div>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Xem trước</h3>
              <div className="border rounded-lg p-4 bg-background">
                <div className="flex items-center gap-3">
                  <img
                    src={`http://one.chamthoi.com/title/${editedTitle.offset}.jpg`}
                    alt={`Title ${editedTitle.offset}`}
                    className="w-12 h-12 rounded border bg-muted object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRkVGMkU4IiByeD0iNiIvPgo8cGF0aCBkPSJNMjQgOUwyOCAxOEgyMEwyNCA5WiIgZmlsbD0iI0Y1OUUwQiIvPgo8cGF0aCBkPSJNMTUgMThIMzNMMzAgMjdIMThMMTUgMThaIiBmaWxsPSIjRjU5RTBCIi8+CjxjaXJjbGUgY3g9IjI0IiBjeT0iMzYiIHI9IjMiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+';
                    }}
                  />
                  <div>
                    <h4 className="font-medium">{editedTitle.name || '(Chưa có tên)'}</h4>
                    <p className="text-sm text-muted-foreground">
                      Job: {editedTitle.job}, ZX: {editedTitle.zx}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Hero Title #{editedTitle.offset}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
