import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Crown } from 'lucide-react';
import { YbiHeroTitle } from '@/lib/parsers/ybi-parser';
import { displayText } from '@/lib/text-converter';

interface TitleListProps {
  titles: YbiHeroTitle[];
  selectedTitleId: string | null;
  onSelectTitle: (title: YbiHeroTitle) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedItems: Set<string>;
  editedCount: number;
}

export function TitleList({
  titles,
  selectedTitleId,
  onSelectTitle,
  searchTerm,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedItems,
}: TitleListProps) {
  // Filter titles based on search
  const filteredTitles = titles.filter(title =>
    title.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    title.offset.toString().includes(searchTerm)
  );

  // Pagination
  const totalPages = Math.ceil(filteredTitles.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTitles = filteredTitles.slice(startIndex, endIndex);

  const getTitleRarityColor = (offset: number) => {
    // Based on offset to determine rarity
    if (offset < 50) return 'default';
    if (offset < 100) return 'secondary';
    if (offset < 150) return 'outline';
    return 'destructive';
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <Crown className="h-4 w-4" />
          Danh sách Titles
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0 w-full">
        {/* <ScrollArea className="h-[calc(100vh-180px)]"> */}
          <div className="space-y-2 p-3 h-[calc(100vh-180px)] overflow-y-auto">
            {currentTitles.length > 0 ? (
              currentTitles.map((title) => (
                <div
                  key={title.offset}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedTitleId === title.offset.toString() ? 'bg-accent border-primary' : 'border-border'}
                  `}
                  onClick={() => onSelectTitle(title)}
                >
                  <div className="flex items-start gap-3">
                    {/* Title Icon */}
                    <div className="flex-shrink-0">
                      <img
                        src={`http://one.chamthoi.com/title/${title.offset}.jpg`}
                        alt={`Title ${title.offset}`}
                        className="w-8 h-8 rounded border bg-muted object-cover"
                        onError={(e) => {
                          // Fallback to a default title icon
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjRkVGMkU4IiByeD0iNCIvPgo8cGF0aCBkPSJNMTYgNkwxOSAxMkgxM0wxNiA2WiIgZmlsbD0iI0Y1OUUwQiIvPgo8cGF0aCBkPSJNMTAgMTJIMjJMMjAgMThIMTJMMTAgMTJaIiBmaWxsPSIjRjU5RTBCIi8+CjxjaXJjbGUgY3g9IjE2IiBjeT0iMjQiIHI9IjIiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+';
                        }}
                      />
                    </div>

                    {/* Title Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">#{title.offset}</span>
                        <Badge variant={getTitleRarityColor(title.offset) as any} className="text-xs px-1 py-0">
                          Title
                        </Badge>
                        {editedItems.has(title.offset.toString()) && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            Đã sửa
                          </Badge>
                        )}
                      </div>
                      
                      {/* Title Name - with overflow hidden */}
                      <h4 className="text-sm font-medium text-foreground mb-1 overflow-hidden">
                        <span className="block truncate" title={displayText(title.name, '(Không có tên)')}>
                          {displayText(title.name, '(Không có tên)')}
                        </span>
                      </h4>
                      
                      {/* Title Info - with overflow hidden */}
                      <p className="text-xs text-muted-foreground mb-2 overflow-hidden">
                        <span className="block truncate" title={`Job: ${title.job}, ZX: ${title.zx}`}>
                          Job: {title.job}, ZX: {title.zx}
                        </span>
                      </p>

                      {/* Title Stats - Compact layout */}
                      <div className="flex flex-wrap gap-x-3 gap-y-1 text-xs text-muted-foreground">
                        <span>Offset: {title.offset}</span>
                        <span className="text-yellow-600 font-medium">Hero Title</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? (
                  <div>
                    <Crown className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không tìm thấy title nào với từ khóa {searchTerm}</p>
                  </div>
                ) : (
                  <div>
                    <Crown className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Không có titles</p>
                  </div>
                )}
              </div>
            )}
          </div>
        {/* </ScrollArea> */}

        {/* Bottom Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredTitles.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
