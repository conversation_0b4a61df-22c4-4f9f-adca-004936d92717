import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AdvancedPagination } from '@/components/ui/advanced-pagination';
import { Search, Crown, X } from 'lucide-react';
import { YbiHeroTitle } from '@/lib/parsers/ybi-parser';

interface TitleSearchFilterProps {
  titles: YbiHeroTitle[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  editedCount: number;
  filteredTitles: YbiHeroTitle[];
  onClearFilters: () => void;
}

export function TitleSearchFilter({
  titles,
  searchTerm,
  onSearchChange,
  currentPage,
  onPageChange,
  itemsPerPage,
  editedCount,
  filteredTitles,
  onClearFilters
}: TitleSearchFilterProps) {
  const totalPages = Math.ceil(filteredTitles.length / itemsPerPage);

  const hasActiveFilters = searchTerm;

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Hero Titles ({filteredTitles.length.toLocaleString()} / {titles.length.toLocaleString()})
            {editedCount > 0 && (
              <Badge variant="secondary">
                {editedCount} đã chỉnh sửa
              </Badge>
            )}
          </div>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
              className="h-8"
            >
              <X className="h-4 w-4 mr-2" />
              Xóa bộ lọc
            </Button>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm theo tên, mô tả hoặc offset..."
            value={searchTerm}
            onChange={(e) => {
              onSearchChange(e.target.value);
              onPageChange(1);
            }}
            className="pl-8"
          />
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2">
            {searchTerm && (
              <Badge variant="secondary" className="gap-1">
                <Search className="h-3 w-3" />
                {searchTerm}
              </Badge>
            )}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center">
            <AdvancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={onPageChange}
              itemsPerPage={itemsPerPage}
              totalItems={filteredTitles.length}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
