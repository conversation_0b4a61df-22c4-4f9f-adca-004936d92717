import React, { useState, useRef } from 'react';
import { toast } from 'sonner';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Upload,
  Save,
  Info,
  Package,
  Zap,
  Star,
  Crown,
  Users,
  Map,
  Settings
} from 'lucide-react';
import { YbiParser, YbiFile, YbiItem, YbiSkill, YbiAbility, YbiHeroTitle, YbiNpcInfo, YbiMapInfo, YbiParserConfig, getDefaultParserConfig } from '@/lib/parsers/ybi-parser';
import { ParserSelector } from '@/components/ui/parser-selector';
import { ItemList } from '@/components/editor/item-list';
import { ItemDetailEditor } from '@/components/editor/item-detail-editor';
import { ItemSearchFilter } from '@/components/editor/item-search-filter';
import { SkillList } from '@/components/editor/skill-list';
import { SkillDetailEditor } from '@/components/editor/skill-detail-editor';
import { NpcList } from '@/components/editor/npc-list';
import { NpcDetailEditor } from '@/components/editor/npc-detail-editor';
import { MapList } from '@/components/editor/map-list';
import { MapDetailEditor } from '@/components/editor/map-detail-editor';
import { AbilityList } from '@/components/editor/ability-list';
import { AbilityDetailEditor } from '@/components/editor/ability-detail-editor';
import { TitleList } from '@/components/editor/title-list';
import { TitleDetailEditor } from '@/components/editor/title-detail-editor';
import { SkillSearchFilter } from '@/components/editor/skill-search-filter';
import { TitleSearchFilter } from '@/components/editor/title-search-filter';

type DataType = 'items' | 'skills' | 'abilities' | 'heroTitles' | 'npcInfos' | 'mapInfos';

const dataTypeTabs = [
  { id: 'items' as DataType, label: 'Items', icon: Package, description: 'Game items, weapons, armor, consumables' },
  { id: 'skills' as DataType, label: 'Skills', icon: Zap, description: 'Character skills and abilities' },
  { id: 'abilities' as DataType, label: 'Abilities', icon: Star, description: 'Special abilities and passive skills' },
  { id: 'heroTitles' as DataType, label: 'Titles', icon: Crown, description: 'Hero titles and achievements' },
  { id: 'npcInfos' as DataType, label: 'NPCs', icon: Users, description: 'Non-player characters and monsters' },
  { id: 'mapInfos' as DataType, label: 'Maps', icon: Map, description: 'Game maps and locations' },
];

export function YbiEditor() {
  const [ybiFile, setYbiFile] = useState<YbiFile | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeDataType, setActiveDataType] = useState<DataType>('items');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showParserSelector, setShowParserSelector] = useState(false);
  const [selectedParserConfig, setSelectedParserConfig] = useState<YbiParserConfig>(getDefaultParserConfig());
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [editedItems, setEditedItems] = useState<Set<string>>(new Set());
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [reside1Filter, setReside1Filter] = useState<string>('all');
  const [reside2Filter, setReside2Filter] = useState<string>('all');
  const [jobLevelFilter, setJobLevelFilter] = useState<string>('all');
  const [sexFilter, setSexFilter] = useState<string>('all');
  const [zxFilter, setZxFilter] = useState<string>('all');

  // Skill filters
  const [skillLevelFilter, setSkillLevelFilter] = useState<string>('all');
  const [skillTypeFilter, setSkillTypeFilter] = useState<string>('all');
  const [skillJobFilter, setSkillJobFilter] = useState<string>('all');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleOpenFile = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setPendingFile(file);
    setShowParserSelector(true);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleParserSelect = async (config: YbiParserConfig) => {
    if (pendingFile) {
      setLoading(true);
      try {
        const buffer = await pendingFile.arrayBuffer();
        const parsedFile = YbiParser.parseWithConfig(buffer, config, pendingFile.name);
        setYbiFile(parsedFile);
        setSelectedParserConfig(config);
        setHasUnsavedChanges(false);
        setCurrentPage(1);
        setSearchTerm('');
        setSelectedItemId(null);
        setEditedItems(new Set());

        toast.success(`Đã tải file ${pendingFile.name} thành công với parser ${config.name}`);
      } catch (error) {
        console.error('Error parsing file:', error);
        toast.error(`Lỗi khi đọc file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
        setPendingFile(null);
      }
    } else if (ybiFile) {
      setSelectedParserConfig(config);
      toast.info(`Đã chọn parser ${config.name}. Để áp dụng, vui lòng mở lại file.`);
    }
  };

  const handleSelectItem = (item: YbiItem | YbiSkill | YbiAbility | YbiHeroTitle | YbiNpcInfo | YbiMapInfo) => {
    let itemId: string;
    if ('id' in item) {
      itemId = item.id.toString();
    } else if ('offset' in item) {
      itemId = item.offset.toString();
    } else {
      itemId = '0';
    }
    setSelectedItemId(itemId);
  };

  const handleSaveItem = (item: YbiItem | YbiSkill | YbiAbility | YbiHeroTitle | YbiNpcInfo | YbiMapInfo) => {
    if (!ybiFile) return;

    const newYbiFile = { ...ybiFile };
    
    switch (activeDataType) {
      case 'items':
        const itemIndex = newYbiFile.items.findIndex(i => i.id === (item as YbiItem).id);
        if (itemIndex !== -1) {
          newYbiFile.items[itemIndex] = item as YbiItem;
        }
        break;
      case 'skills':
        const skillIndex = newYbiFile.skills.findIndex(s => s.id === (item as YbiSkill).id);
        if (skillIndex !== -1) {
          newYbiFile.skills[skillIndex] = item as YbiSkill;
        }
        break;
      case 'abilities':
        const abilityIndex = newYbiFile.abilities.findIndex(a => a.id === (item as YbiAbility).id);
        if (abilityIndex !== -1) {
          newYbiFile.abilities[abilityIndex] = item as YbiAbility;
        }
        break;
      case 'heroTitles':
        const titleIndex = newYbiFile.heroTitles.findIndex(t => t.offset === (item as YbiHeroTitle).offset);
        if (titleIndex !== -1) {
          newYbiFile.heroTitles[titleIndex] = item as YbiHeroTitle;
        }
        break;
      case 'npcInfos':
        const npcIndex = newYbiFile.npcInfos.findIndex(n => n.id === (item as YbiNpcInfo).id);
        if (npcIndex !== -1) {
          newYbiFile.npcInfos[npcIndex] = item as YbiNpcInfo;
        }
        break;
      case 'mapInfos':
        const mapIndex = newYbiFile.mapInfos.findIndex(m => m.id === (item as YbiMapInfo).id);
        if (mapIndex !== -1) {
          newYbiFile.mapInfos[mapIndex] = item as YbiMapInfo;
        }
        break;
    }

    setYbiFile(newYbiFile);
    setHasUnsavedChanges(true);
  };

  const handleMarkAsEdited = (itemId: string) => {
    setEditedItems(prev => new Set(prev).add(itemId));
  };

  const handleSaveFile = async () => {
    if (!ybiFile) return;

    try {
      setLoading(true);
      const binaryData = YbiParser.generate(ybiFile);
      const blob = new Blob([binaryData], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = ybiFile.fileName || 'Ybi.cfg';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setHasUnsavedChanges(false);
      toast.success('File đã được lưu thành công!');
    } catch (error) {
      console.error('Error saving file:', error);
      toast.error(`Lỗi khi lưu file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentData = () => {
    if (!ybiFile) return [];

    switch (activeDataType) {
      case 'items':
        return ybiFile.items;
      case 'skills':
        return ybiFile.skills;
      case 'abilities':
        return ybiFile.abilities;
      case 'heroTitles':
        return ybiFile.heroTitles;
      case 'npcInfos':
        return ybiFile.npcInfos;
      case 'mapInfos':
        return ybiFile.mapInfos;
      default:
        return [];
    }
  };

  const getFilteredItems = () => {
    const data = getCurrentData();

    if (activeDataType === 'items') {
      let filtered = data as YbiItem[];

      // Apply search filter
      if (searchTerm) {
        filtered = filtered.filter(item =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.desc.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.id.toString().includes(searchTerm)
        );
      }

      // Apply level filter
      if (levelFilter && levelFilter !== 'all') {
        filtered = filtered.filter(item => {
          const level = item.level;
          switch (levelFilter) {
            case '1-10': return level >= 1 && level <= 10;
            case '11-30': return level >= 11 && level <= 30;
            case '31-50': return level >= 31 && level <= 50;
            case '51-70': return level >= 51 && level <= 70;
            case '71-90': return level >= 71 && level <= 90;
            case '91+': return level >= 91;
            default: return true;
          }
        });
      }

      // Apply type filter
      if (typeFilter && typeFilter !== 'all') {
        filtered = filtered.filter(item => item.itemType.toString() === typeFilter);
      }

      // Apply reside1 filter (Job/Class)
      if (reside1Filter && reside1Filter !== 'all') {
        filtered = filtered.filter(item => item.reside1.toString() === reside1Filter);
      }

      // Apply reside2 filter (Equipment type)
      if (reside2Filter && reside2Filter !== 'all') {
        filtered = filtered.filter(item => item.reside2.toString() === reside2Filter);
      }

      // Apply job level filter
      if (jobLevelFilter && jobLevelFilter !== 'all') {
        filtered = filtered.filter(item => item.jobLevel.toString() === jobLevelFilter);
      }

      // Apply sex filter
      if (sexFilter && sexFilter !== 'all') {
        filtered = filtered.filter(item => item.sex.toString() === sexFilter);
      }

      // Apply zx filter (Faction)
      if (zxFilter && zxFilter !== 'all') {
        filtered = filtered.filter(item => item.zx.toString() === zxFilter);
      }

      return filtered;
    }

    if (activeDataType === 'skills') {
      let filtered = data as YbiSkill[];

      // Apply search filter
      if (searchTerm) {
        filtered = filtered.filter(skill =>
          skill.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          skill.desc.toLowerCase().includes(searchTerm.toLowerCase()) ||
          skill.id.toString().includes(searchTerm)
        );
      }

      // Apply skill level filter
      if (skillLevelFilter && skillLevelFilter !== 'all') {
        filtered = filtered.filter(skill => {
          const level = skill.level;
          switch (skillLevelFilter) {
            case '1-10': return level >= 1 && level <= 10;
            case '11-30': return level >= 11 && level <= 30;
            case '31-50': return level >= 31 && level <= 50;
            case '51-70': return level >= 51 && level <= 70;
            case '71-90': return level >= 71 && level <= 90;
            case '91+': return level >= 91;
            default: return true;
          }
        });
      }

      // Apply skill type filter
      if (skillTypeFilter && skillTypeFilter !== 'all') {
        filtered = filtered.filter(skill => skill.type.toString() === skillTypeFilter);
      }

      // Apply skill job filter
      if (skillJobFilter && skillJobFilter !== 'all') {
        filtered = filtered.filter(skill => skill.job.toString() === skillJobFilter);
      }

      return filtered;
    }

    if (activeDataType === 'heroTitles') {
      let filtered = data as YbiHeroTitle[];

      // Apply search filter
      if (searchTerm) {
        filtered = filtered.filter(title =>
          title.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          title.offset.toString().includes(searchTerm)
        );
      }

      return filtered;
    }

    // For other data types, just apply basic search
    if (searchTerm) {
      return data.filter((item: any) =>
        (item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (item.desc && item.desc.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (item.id && item.id.toString().includes(searchTerm)) ||
        (item.offset && item.offset.toString().includes(searchTerm))
      );
    }

    return data;
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setLevelFilter('all');
    setTypeFilter('all');
    setReside1Filter('all');
    setReside2Filter('all');
    setJobLevelFilter('all');
    setSexFilter('all');
    setZxFilter('all');
    setSkillLevelFilter('all');
    setSkillTypeFilter('all');
    setSkillJobFilter('all');
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      <input
        ref={fileInputRef}
        type="file"
        accept=".cfg"
        onChange={handleFileChange}
        className="hidden"
      />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Ybi Editor
            {hasUnsavedChanges && (
              <Badge variant="destructive" className="ml-2">
                Unsaved Changes
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Chỉnh sửa file Ybi.cfg - Game items, skills, abilities và các data khác
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button onClick={handleOpenFile} variant="outline" disabled={loading}>
              <Upload className="h-4 w-4 mr-2" />
              {loading ? 'Đang tải...' : 'Mở File'}
            </Button>

            {ybiFile && (
              <Button onClick={handleSaveFile} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                Lưu File
              </Button>
            )}

            {ybiFile && (
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Info className="h-4 w-4" />
                  <span>
                    {ybiFile.items.length} items, {ybiFile.skills.length} skills,
                    {ybiFile.abilities.length} abilities, {ybiFile.heroTitles.length} titles,
                    {ybiFile.npcInfos.length} NPCs, {ybiFile.mapInfos.length} maps
                  </span>
                  {editedItems.size > 0 && (
                    <Badge variant="secondary">
                      {editedItems.size} đã chỉnh sửa
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    Parser: {ybiFile.parserConfig.name}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Skill: {ybiFile.parserConfig.skillFormat}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    NPC: {ybiFile.parserConfig.npcFormat}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowParserSelector(true)}
                    className="h-6 px-2 text-xs"
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Đổi Parser
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {ybiFile ? (
        <>
          {/* Data Type Tabs */}
          <Tabs value={activeDataType} onValueChange={(value) => {
            setActiveDataType(value as DataType);
            setCurrentPage(1);
            setSearchTerm('');
            setSelectedItemId(null); // Reset selection when changing tabs
          }}>
            <TabsList className="grid w-full grid-cols-6">
              {dataTypeTabs.map((tab) => (
                <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                  <tab.icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {dataTypeTabs.map((tab) => (
              <TabsContent key={tab.id} value={tab.id} className="space-y-4">
                {activeDataType === 'items' ? (
                  <>
                    {/* Search & Filter Section */}
                    <ItemSearchFilter
                      items={getCurrentData() as YbiItem[]}
                      searchTerm={searchTerm}
                      onSearchChange={setSearchTerm}
                      currentPage={currentPage}
                      onPageChange={setCurrentPage}
                      itemsPerPage={itemsPerPage}
                      editedCount={editedItems.size}
                      filteredItems={getFilteredItems() as YbiItem[]}
                      levelFilter={levelFilter}
                      onLevelFilterChange={setLevelFilter}
                      typeFilter={typeFilter}
                      onTypeFilterChange={setTypeFilter}
                      reside1Filter={reside1Filter}
                      onReside1FilterChange={setReside1Filter}
                      reside2Filter={reside2Filter}
                      onReside2FilterChange={setReside2Filter}
                      jobLevelFilter={jobLevelFilter}
                      onJobLevelFilterChange={setJobLevelFilter}
                      sexFilter={sexFilter}
                      onSexFilterChange={setSexFilter}
                      zxFilter={zxFilter}
                      onZxFilterChange={setZxFilter}
                      onClearFilters={handleClearFilters}
                    />

                    {/* Items List & Detail Editor - 1/3 and 2/3 layout */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-[calc(100vh-300px)]">
                      {/* Left: Item List (1/3) */}
                      <div className="lg:col-span-1 h-full">
                        <ItemList
                          items={getFilteredItems() as YbiItem[]}
                          selectedItemId={selectedItemId}
                          onSelectItem={handleSelectItem}
                          searchTerm={searchTerm}
                          onSearchChange={setSearchTerm}
                          currentPage={currentPage}
                          onPageChange={setCurrentPage}
                          itemsPerPage={itemsPerPage}
                          editedItems={editedItems}
                          editedCount={editedItems.size}
                        />
                      </div>

                      {/* Right: Detail Editor (2/3) */}
                      <div className="lg:col-span-2 h-full">
                        <ItemDetailEditor
                          item={selectedItemId ? (getFilteredItems() as YbiItem[]).find(item => item.id.toString() === selectedItemId) || null : null}
                          onSave={handleSaveItem}
                          onMarkAsEdited={handleMarkAsEdited}
                          isEdited={selectedItemId ? editedItems.has(selectedItemId) : false}
                        />
                      </div>
                    </div>
                  </>
                ) : activeDataType === 'skills' ? (
                  <>
                    {/* Skills Search & Filter Section */}
                    <SkillSearchFilter
                      skills={getCurrentData() as YbiSkill[]}
                      searchTerm={searchTerm}
                      onSearchChange={setSearchTerm}
                      currentPage={currentPage}
                      onPageChange={setCurrentPage}
                      itemsPerPage={itemsPerPage}
                      editedCount={editedItems.size}
                      filteredSkills={getFilteredItems() as YbiSkill[]}
                      levelFilter={skillLevelFilter}
                      onLevelFilterChange={setSkillLevelFilter}
                      typeFilter={skillTypeFilter}
                      onTypeFilterChange={setSkillTypeFilter}
                      jobFilter={skillJobFilter}
                      onJobFilterChange={setSkillJobFilter}
                      onClearFilters={handleClearFilters}
                    />

                    {/* Skills List & Detail Editor - 1/3 and 2/3 layout */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-[calc(100vh-300px)]">
                      {/* Left: Skill List (1/3) */}
                      <div className="lg:col-span-1 h-full">
                        <SkillList
                          skills={getFilteredItems() as YbiSkill[]}
                          selectedSkillId={selectedItemId}
                          onSelectSkill={handleSelectItem}
                          searchTerm={searchTerm}
                          onSearchChange={setSearchTerm}
                          currentPage={currentPage}
                          onPageChange={setCurrentPage}
                          itemsPerPage={itemsPerPage}
                          editedItems={editedItems}
                          editedCount={editedItems.size}
                        />
                      </div>

                      {/* Right: Detail Editor (2/3) */}
                      <div className="lg:col-span-1 h-full">
                        <SkillDetailEditor
                          skill={selectedItemId ? (getFilteredItems() as YbiSkill[]).find(skill => skill.id.toString() === selectedItemId) || null : null}
                          onSave={handleSaveItem}
                          onMarkAsEdited={handleMarkAsEdited}
                          isEdited={selectedItemId ? editedItems.has(selectedItemId) : false}
                        />
                      </div>
                    </div>
                  </>
                ) : activeDataType === 'heroTitles' ? (
                  <>
                    {/* Titles Search & Filter Section */}
                    <TitleSearchFilter
                      titles={getCurrentData() as YbiHeroTitle[]}
                      searchTerm={searchTerm}
                      onSearchChange={setSearchTerm}
                      currentPage={currentPage}
                      onPageChange={setCurrentPage}
                      itemsPerPage={itemsPerPage}
                      editedCount={editedItems.size}
                      filteredTitles={getFilteredItems() as YbiHeroTitle[]}
                      onClearFilters={handleClearFilters}
                    />

                    {/* Titles List & Detail Editor - 1/3 and 2/3 layout */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-[calc(100vh-300px)]">
                      {/* Left: Title List (1/3) */}
                      <div className="lg:col-span-1 h-full">
                        <TitleList
                          titles={getFilteredItems() as YbiHeroTitle[]}
                          selectedTitleId={selectedItemId}
                          onSelectTitle={handleSelectItem}
                          searchTerm={searchTerm}
                          onSearchChange={setSearchTerm}
                          currentPage={currentPage}
                          onPageChange={setCurrentPage}
                          itemsPerPage={itemsPerPage}
                          editedItems={editedItems}
                          editedCount={editedItems.size}
                        />
                      </div>

                      {/* Right: Detail Editor (2/3) */}
                      <div className="lg:col-span-2 h-full">
                        <TitleDetailEditor
                          title={selectedItemId ? (getFilteredItems() as YbiHeroTitle[]).find(title => title.offset.toString() === selectedItemId) || null : null}
                          onSave={handleSaveItem}
                          onMarkAsEdited={handleMarkAsEdited}
                          isEdited={selectedItemId ? editedItems.has(selectedItemId) : false}
                        />
                      </div>
                    </div>
                  </>
                ) : (
                  /* Other data types - keep original two-column layout */
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-[calc(100vh-200px)]">
                    {/* Left: List */}
                    <div className="h-full">
                      {activeDataType === 'npcInfos' && (
                        <NpcList
                          npcs={getCurrentData() as YbiNpcInfo[]}
                          selectedNpcId={selectedItemId}
                          onSelectNpc={handleSelectItem}
                          searchTerm={searchTerm}
                          onSearchChange={setSearchTerm}
                          currentPage={currentPage}
                          onPageChange={setCurrentPage}
                          itemsPerPage={itemsPerPage}
                          editedItems={editedItems}
                          editedCount={editedItems.size}
                        />
                      )}
                      {activeDataType === 'mapInfos' && (
                        <MapList
                          maps={getCurrentData() as YbiMapInfo[]}
                          selectedMapId={selectedItemId}
                          onSelectMap={handleSelectItem}
                          searchTerm={searchTerm}
                          onSearchChange={setSearchTerm}
                          currentPage={currentPage}
                          onPageChange={setCurrentPage}
                          itemsPerPage={itemsPerPage}
                          editedItems={editedItems}
                          editedCount={editedItems.size}
                        />
                      )}
                      {activeDataType === 'abilities' && (
                        <AbilityList
                          abilities={getCurrentData() as YbiAbility[]}
                          selectedAbilityId={selectedItemId}
                          onSelectAbility={handleSelectItem}
                          searchTerm={searchTerm}
                          onSearchChange={setSearchTerm}
                          currentPage={currentPage}
                          onPageChange={setCurrentPage}
                          itemsPerPage={itemsPerPage}
                          editedItems={editedItems}
                          editedCount={editedItems.size}
                        />
                      )}
                      {/* Placeholder for other data types */}
                      {activeDataType !== 'npcInfos' && activeDataType !== 'mapInfos' && activeDataType !== 'abilities' && (
                        <Card className="h-full">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <tab.icon className="h-5 w-5" />
                              {tab.label}
                              <Badge variant="outline" className="ml-2">
                                {getCurrentData().length} records
                              </Badge>
                              {editedItems.size > 0 && (
                                <Badge variant="secondary">
                                  {editedItems.size} đã chỉnh sửa
                                </Badge>
                              )}
                            </CardTitle>
                            <CardDescription>{tab.description}</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <p className="text-muted-foreground">
                              List component cho {tab.label} sẽ được thêm vào sau.
                            </p>
                          </CardContent>
                        </Card>
                      )}
                    </div>

                    {/* Right: Detail Editor */}
                    <div className="h-full lg:col-span-1">
                      {activeDataType === 'npcInfos' && (
                        <NpcDetailEditor
                          npc={selectedItemId ? (getCurrentData() as YbiNpcInfo[]).find(npc => npc.id.toString() === selectedItemId) || null : null}
                          onSave={handleSaveItem}
                          onMarkAsEdited={handleMarkAsEdited}
                          isEdited={selectedItemId ? editedItems.has(selectedItemId) : false}
                        />
                      )}
                      {activeDataType === 'mapInfos' && (
                        <MapDetailEditor
                          map={selectedItemId ? (getCurrentData() as YbiMapInfo[]).find(map => map.id.toString() === selectedItemId) || null : null}
                          onSave={handleSaveItem}
                          onMarkAsEdited={handleMarkAsEdited}
                          isEdited={selectedItemId ? editedItems.has(selectedItemId) : false}
                        />
                      )}
                      {activeDataType === 'abilities' && (
                        <AbilityDetailEditor
                          ability={selectedItemId ? (getCurrentData() as YbiAbility[]).find(ability => ability.id.toString() === selectedItemId) || null : null}
                          onSave={handleSaveItem}
                          onMarkAsEdited={handleMarkAsEdited}
                          isEdited={selectedItemId ? editedItems.has(selectedItemId) : false}
                        />
                      )}
                      {/* Placeholder for other data types */}
                      {activeDataType !== 'npcInfos' && activeDataType !== 'mapInfos' && activeDataType !== 'abilities' && (
                        <Card className="h-full">
                          <CardContent className="flex items-center justify-center h-full">
                            <div className="text-center text-muted-foreground">
                              <tab.icon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                              <p>Detail editor cho {tab.label} sẽ được thêm vào sau</p>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  </div>
                )}
              </TabsContent>
            ))}
          </Tabs>
        </>
      ) : (
        /* Welcome Card */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Hướng dẫn sử dụng
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">File Format:</h4>
              <p className="text-muted-foreground text-sm">
                Ybi.cfg chứa tất cả dữ liệu game bao gồm items, skills, abilities, hero titles, NPCs và maps.
                File được mã hóa và cần được giải mã trước khi đọc.
              </p>
            </div>
            {/* <div>
              <h4 className="font-medium mb-2">Cấu trúc:</h4>
              <ul className="text-muted-foreground text-sm space-y-1 ml-4">
                <li>• Header: 8 bytes chứa thông tin file</li>
                <li>• Items: Mỗi item 852 bytes</li>
                <li>• Skills: 1024 skill packs, hỗ trợ v23 và normal format</li>
                <li>• Abilities: 1024 abilities, mỗi ability 2964 bytes</li>
                <li>• Hero Titles: 256 titles, mỗi title 72 bytes</li>
                <li>• NPCs: 3134 NPCs với 2 versions (v20/v24)</li>
                <li>• Maps: Variable length, mỗi map 744 bytes</li>
                <li>• Encoding: Latin1 (1-byte) cho Vietnamese text</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Chức năng:</h4>
              <ul className="text-muted-foreground text-sm space-y-1 ml-4">
                <li>• Xem và chỉnh sửa tất cả loại dữ liệu game</li>
                <li>• Tìm kiếm theo tên, ID hoặc mô tả</li>
                <li>• Phân trang để xử lý dữ liệu lớn</li>
                <li>• Inline editing cho các trường text</li>
                <li>• Tự động detect version format</li>
                <li>• Encryption/decryption tự động</li>
              </ul>
            </div>
            <div className="pt-4">
              <Button onClick={handleOpenFile} className="w-full">
                <Upload className="h-4 w-4 mr-2" />
                Chọn file Ybi.cfg để bắt đầu
              </Button>
            </div> */}
          </CardContent>
        </Card>
      )}

      {/* Parser Selector Dialog */}
      <ParserSelector
        open={showParserSelector}
        onOpenChange={setShowParserSelector}
        onSelectParser={handleParserSelect}
        selectedConfig={selectedParserConfig}
      />
    </div>
  );
}
