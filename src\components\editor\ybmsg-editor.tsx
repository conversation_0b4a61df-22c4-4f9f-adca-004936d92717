'use client';

import { useState, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  FileText,
  Upload,
  Save,
  Plus,
  Trash2,
  Search,
  RefreshCw,
  Info,
  ChevronLeft,
  ChevronRight,
  Undo
} from 'lucide-react';
import { toast } from 'sonner';
import { YbmsgParser, YbmsgFile, YbmsgRecord } from '@/lib/parsers/ybmsg-parser';

export function YbmsgEditor() {
  const [ybmsgFile, setYbmsgFile] = useState<YbmsgFile | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingRecord, setEditingRecord] = useState<YbmsgRecord | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingRecord, setDeletingRecord] = useState<YbmsgRecord | null>(null);
  const [newRecord, setNewRecord] = useState<Partial<YbmsgRecord>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(100);

  // Inline editing states
  const [editedRecords, setEditedRecords] = useState<Map<number, YbmsgRecord>>(new Map());
  const [originalRecords, setOriginalRecords] = useState<Map<number, YbmsgRecord>>(new Map());

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle inline editing
  const handleInlineEdit = (record: YbmsgRecord, field: keyof YbmsgRecord, value: any) => {
    // Store original record if not already stored
    setOriginalRecords(prev => {
      if (!prev.has(record.id)) {
        const newMap = new Map(prev);
        newMap.set(record.id, { ...record });
        return newMap;
      }
      return prev;
    });

    const updatedRecord = { ...record, [field]: value };

    // Update edited records map
    setEditedRecords(prev => {
      const newMap = new Map(prev);
      newMap.set(record.id, updatedRecord);
      return newMap;
    });

    // Update the main file data
    if (ybmsgFile) {
      const updatedRecords = ybmsgFile.records.map(r =>
        r.id === record.id ? updatedRecord : r
      );

      setYbmsgFile({
        ...ybmsgFile,
        records: updatedRecords
      });
    }

    setHasUnsavedChanges(true);
  };

  // Handle revert record to original state
  const handleRevertRecord = (record: YbmsgRecord) => {
    const originalRecord = originalRecords.get(record.id);
    if (!originalRecord) return;

    // Remove from edited records
    setEditedRecords(prev => {
      const newMap = new Map(prev);
      newMap.delete(record.id);
      return newMap;
    });

    // Remove from original records
    setOriginalRecords(prev => {
      const newMap = new Map(prev);
      newMap.delete(record.id);
      return newMap;
    });

    // Update the main file data
    if (ybmsgFile) {
      const updatedRecords = ybmsgFile.records.map(r =>
        r.id === record.id ? originalRecord : r
      );

      setYbmsgFile({
        ...ybmsgFile,
        records: updatedRecords
      });
    }

    // Update unsaved changes state
    const remainingEdits = editedRecords.size - 1;
    setHasUnsavedChanges(remainingEdits > 0);

    toast.success('Đã khôi phục record về trạng thái gốc');
  };

  // Get edited records count
  const getEditedCount = () => editedRecords.size;

  // Reset edited records
  const resetEditedRecords = () => {
    setEditedRecords(new Map());
    setOriginalRecords(new Map());
    setHasUnsavedChanges(false);
  };

  // Handle file selection
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    try {
      const buffer = await file.arrayBuffer();
      
      // Validate file
      const validation = YbmsgParser.validate(buffer);
      if (!validation.valid) {
        toast.error(`File không hợp lệ: ${validation.error}`);
        return;
      }

      // Parse file
      const parsedFile = YbmsgParser.parse(buffer, file.name);
      setYbmsgFile(parsedFile);
      resetEditedRecords();
      setCurrentPage(1); // Reset to first page

      toast.success(`Đã tải file ${file.name} thành công (${parsedFile.records.length} records)`);

      // Debug info
      console.log('Parsed ybmsg file:', {
        fileName: file.name,
        fileSize: buffer.byteLength,
        header: parsedFile.header,
        recordCount: parsedFile.records.length,
        firstRecord: parsedFile.records[0],
        originalHeaderHex: parsedFile.header.originalHeaderBytes
          ? Array.from(parsedFile.header.originalHeaderBytes).map(b => b.toString(16).padStart(2, '0')).join(' ')
          : 'None'
      });
    } catch (error) {
      console.error('Error parsing file:', error);
      toast.error(`Lỗi khi đọc file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, []);

  // Handle open file button
  const handleOpenFile = () => {
    fileInputRef.current?.click();
  };

  // Handle save file
  const handleSaveFile = useCallback(() => {
    if (!ybmsgFile) return;

    try {
      // Debug: Log header info before saving
      console.log('Saving file with header:', {
        totalRecords: ybmsgFile.header.totalRecords,
        headerSize: ybmsgFile.header.headerSize,
        hasOriginalHeader: !!ybmsgFile.header.originalHeaderBytes,
        originalHeaderHex: ybmsgFile.header.originalHeaderBytes
          ? Array.from(ybmsgFile.header.originalHeaderBytes).map(b => b.toString(16).padStart(2, '0')).join(' ')
          : 'None'
      });

      const buffer = YbmsgParser.generate(ybmsgFile);

      // Debug: Log generated header
      const generatedHeader = new Uint8Array(buffer, 0, 8);
      console.log('Generated header:', Array.from(generatedHeader).map(b => b.toString(16).padStart(2, '0')).join(' '));

      const blob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = ybmsgFile.fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      resetEditedRecords();
      toast.success('Đã lưu file thành công');
    } catch (error) {
      console.error('Error saving file:', error);
      toast.error(`Lỗi khi lưu file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [ybmsgFile]);

  // Handle add record
  const handleAddRecord = () => {
    if (!ybmsgFile || !newRecord.id || !newRecord.message) {
      toast.error('Vui lòng nhập đầy đủ thông tin');
      return;
    }

    // Check if ID already exists
    if (ybmsgFile.records.some(r => r.id === newRecord.id)) {
      toast.error('ID đã tồn tại');
      return;
    }

    const record: YbmsgRecord = {
      id: newRecord.id,
      message: newRecord.message,
      u_203: newRecord.u_203 || 0,
      u_204: newRecord.u_204 || 0
    };

    const updatedFile = {
      ...ybmsgFile,
      records: [...ybmsgFile.records, record].sort((a, b) => a.id - b.id)
    };

    setYbmsgFile(updatedFile);
    setHasUnsavedChanges(true);
    setIsAddDialogOpen(false);
    setNewRecord({});

    // Navigate to the page containing the new record
    const newRecordIndex = updatedFile.records.findIndex(r => r.id === record.id);
    const newPage = Math.ceil((newRecordIndex + 1) / itemsPerPage);
    setCurrentPage(newPage);

    toast.success('Đã thêm record thành công');
  };

  // Handle edit record
  const handleEditRecord = () => {
    if (!ybmsgFile || !editingRecord) return;

    const updatedRecords = ybmsgFile.records.map(r => 
      r.id === editingRecord.id ? editingRecord : r
    );

    const updatedFile = {
      ...ybmsgFile,
      records: updatedRecords
    };

    setYbmsgFile(updatedFile);
    setHasUnsavedChanges(true);
    setIsEditDialogOpen(false);
    setEditingRecord(null);
    toast.success('Đã cập nhật record thành công');
  };

  // Handle delete record
  const handleDeleteRecord = () => {
    if (!ybmsgFile || !deletingRecord) return;

    const updatedRecords = ybmsgFile.records.filter(r => r.id !== deletingRecord.id);
    const updatedFile = {
      ...ybmsgFile,
      records: updatedRecords
    };

    setYbmsgFile(updatedFile);
    setHasUnsavedChanges(true);
    setIsDeleteDialogOpen(false);
    setDeletingRecord(null);
    toast.success('Đã xóa record thành công');
  };

  // Filter records based on search
  const filteredRecords = ybmsgFile?.records.filter(record =>
    record.id.toString().includes(searchTerm) ||
    record.message.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Pagination calculations
  const totalPages = Math.ceil(filteredRecords.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedRecords = filteredRecords.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle search with page reset
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Ybmsg Editor
            {hasUnsavedChanges && (
              <Badge variant="destructive" className="ml-2">
                {getEditedCount()} records edited
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Button onClick={handleOpenFile} variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Mở File
            </Button>
            
            {ybmsgFile && (
              <>
                <Button onClick={handleSaveFile} disabled={!hasUnsavedChanges}>
                  <Save className="h-4 w-4 mr-2" />
                  Lưu File
                </Button>
                
                <Button onClick={() => setIsAddDialogOpen(true)} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm Record
                </Button>
              </>
            )}

            {loading && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                Đang xử lý...
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept=".cfg"
            onChange={handleFileSelect}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* File Info */}
      {/* {ybmsgFile && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Thông tin File
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Tên File</Label>
                  <p className="text-sm">{ybmsgFile.fileName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Kích thước</Label>
                  <p className="text-sm">{(ybmsgFile.fileSize / 1024).toFixed(2)} KB</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Header Size</Label>
                  <p className="text-sm">{ybmsgFile.header.headerSize} bytes</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Tổng Records</Label>
                  <p className="text-sm">{ybmsgFile.records.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Format Specification
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Header:</span>
                  <span className="font-mono">8 bytes (preserved)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Record Size:</span>
                  <span className="font-mono">516 bytes (0x204)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Message:</span>
                  <span className="font-mono">512 bytes (0x200)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">ID:</span>
                  <span className="font-mono">2 bytes @ 0x200</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">U203/U204:</span>
                  <span className="font-mono">1 byte each</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Encoding:</span>
                  <span className="font-mono">Latin1</span>
                </div>
                {ybmsgFile.header.originalHeaderBytes && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Original Header:</span>
                    <span className="font-mono text-xs">
                      {Array.from(ybmsgFile.header.originalHeaderBytes)
                        .map(b => b.toString(16).padStart(2, '0'))
                        .join(' ')}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )} */}

      {/* Records Table */}
      {ybmsgFile && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                Records ({filteredRecords.length})
                {totalPages > 1 && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    - Page {currentPage} of {totalPages}
                  </span>
                )}
              </CardTitle>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm kiếm ID hoặc message..."
                      value={searchTerm}
                      onChange={(e) => handleSearchChange(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>

                  {totalPages > 1 && (
                    <div className="flex items-center gap-1">
                      <Label className="text-xs text-muted-foreground">Page:</Label>
                      <Input
                        type="number"
                        min="1"
                        max={totalPages}
                        value={currentPage}
                        onChange={(e) => {
                          const page = parseInt(e.target.value);
                          if (page >= 1 && page <= totalPages) {
                            handlePageChange(page);
                          }
                        }}
                        className="w-16 h-8 text-xs"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="border rounded-lg overflow-hidden">
              {/* Fixed Header */}
              <div className="border-b bg-muted/50 sticky top-0 z-10">
                <div className="grid grid-cols-12 gap-2 p-3 text-sm font-medium">
                  <div className="col-span-1 text-center">ID</div>
                  <div className="col-span-7 md:col-span-8">Message</div>
                  <div className="col-span-1 text-center hidden sm:block">U203</div>
                  <div className="col-span-1 text-center hidden sm:block">U204</div>
                  <div className="col-span-2 sm:col-span-2 md:col-span-1 text-center">Action</div>
                </div>
              </div>

              {/* Scrollable Body */}
              <div className="max-h-[calc(100vh-450px)] min-h-[400px] overflow-y-auto">
                <div className="divide-y">
                  {paginatedRecords.map((record) => {
                    const isEdited = editedRecords.has(record.id);
                    const hasOriginal = originalRecords.has(record.id);

                    return (
                      <div
                        key={record.id}
                        className={`grid grid-cols-12 gap-2 p-3 hover:bg-muted/50 transition-colors ${
                          isEdited ? 'bg-yellow-50 dark:bg-yellow-950/20' : ''
                        }`}
                      >
                        {/* ID Column */}
                        <div className="col-span-1 flex items-center justify-center">
                          <span className="font-mono text-sm">{record.id}</span>
                        </div>

                        {/* Message Column */}
                        <div className="col-span-7 md:col-span-8 flex items-center">
                          <Input
                            value={record.message}
                            onChange={(e) => handleInlineEdit(record, 'message', e.target.value)}
                            className="border-0 bg-transparent p-0 h-auto text-sm focus-visible:ring-1 w-full"
                            placeholder="Enter message..."
                            title={`Record ${record.id}: ${record.message || 'Empty message'}`}
                          />
                        </div>

                        {/* U203 Column */}
                        <div className="col-span-1 hidden sm:flex items-center justify-center">
                          <Input
                            type="number"
                            min="0"
                            max="255"
                            value={record.u_203 || 0}
                            onChange={(e) => handleInlineEdit(record, 'u_203', parseInt(e.target.value) || 0)}
                            className="border-0 bg-transparent p-0 h-auto text-xs text-center w-full focus-visible:ring-1"
                          />
                        </div>

                        {/* U204 Column */}
                        <div className="col-span-1 hidden sm:flex items-center justify-center">
                          <Input
                            type="number"
                            min="0"
                            max="255"
                            value={record.u_204 || 0}
                            onChange={(e) => handleInlineEdit(record, 'u_204', parseInt(e.target.value) || 0)}
                            className="border-0 bg-transparent p-0 h-auto text-xs text-center w-full focus-visible:ring-1"
                          />
                        </div>

                        {/* Action Column */}
                        <div className="col-span-2 sm:col-span-2 md:col-span-1 flex items-center justify-center gap-1">
                          {/* Mobile U203/U204 inputs */}
                          <div className="flex sm:hidden gap-1">
                            <Input
                              type="number"
                              min="0"
                              max="255"
                              value={record.u_203 || 0}
                              onChange={(e) => handleInlineEdit(record, 'u_203', parseInt(e.target.value) || 0)}
                              className="border-0 bg-transparent p-0 h-auto text-xs text-center w-8 focus-visible:ring-1"
                              title="U203"
                            />
                            <Input
                              type="number"
                              min="0"
                              max="255"
                              value={record.u_204 || 0}
                              onChange={(e) => handleInlineEdit(record, 'u_204', parseInt(e.target.value) || 0)}
                              className="border-0 bg-transparent p-0 h-auto text-xs text-center w-8 focus-visible:ring-1"
                              title="U204"
                            />
                          </div>

                          {/* Action Button */}
                          {hasOriginal ? (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRevertRecord(record)}
                              className="h-8 w-8 p-0"
                              title="Khôi phục về trạng thái gốc"
                            >
                              <Undo className="h-3 w-3" />
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setDeletingRecord(record);
                                setIsDeleteDialogOpen(true);
                              }}
                              className="h-8 w-8 p-0"
                              title="Xóa record"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {paginatedRecords.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchTerm ? 'Không tìm thấy record nào' : 'Chưa có record nào'}
                  </div>
                )}
              </div>
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-4 py-3 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>
                    {startIndex + 1}-{Math.min(endIndex, filteredRecords.length)} của {filteredRecords.length} records
                  </span>
                  {getEditedCount() > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {getEditedCount()} đã sửa
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Trước
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          size="sm"
                          variant={currentPage === pageNum ? "default" : "outline"}
                          onClick={() => handlePageChange(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                  >
                    Sau
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* No file loaded state */}
      {!ybmsgFile && !loading && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Chưa có file nào được tải</h3>
              <p className="text-muted-foreground mb-4">
                Chọn file ybmsg.cfg để bắt đầu chỉnh sửa
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Hướng dẫn sử dụng
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <h4 className="font-medium mb-1">File Format:</h4>
                <p className="text-muted-foreground">
                  Ybmsg.cfg chứa các message strings của game.
                </p>
              </div>
              {/* <div>
                <h4 className="font-medium mb-1">Cấu trúc:</h4>
                <ul className="text-muted-foreground space-y-1 ml-4">
                  <li>• Header: 8 bytes</li>
                  <li>• Mỗi record: 516 bytes (512 message + 2 ID + 2 unknown)</li>
                  <li>• Encoding: Latin1</li>
                </ul>
              </div> */}
              <div>
                <h4 className="font-medium mb-1">Chức năng:</h4>
                <ul className="text-muted-foreground space-y-1 ml-4">
                  <li>• Xem và chỉnh sửa messages</li>
                  <li>• Thêm/xóa records</li>
                  <li>• Tìm kiếm theo ID hoặc nội dung</li>
                  <li>• Xuất file đã chỉnh sửa</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Add Record Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Thêm Record Mới</DialogTitle>
            <DialogDescription>
              Nhập thông tin cho record mới
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-id">ID *</Label>
              <Input
                id="new-id"
                type="number"
                value={newRecord.id || ''}
                onChange={(e) => setNewRecord(prev => ({ ...prev, id: parseInt(e.target.value) || 0 }))}
                placeholder="Nhập ID"
              />
            </div>
            <div>
              <Label htmlFor="new-message">Message *</Label>
              <Textarea
                id="new-message"
                value={newRecord.message || ''}
                onChange={(e) => setNewRecord(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Nhập message"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="new-u203">U203 (Optional)</Label>
                <Input
                  id="new-u203"
                  type="number"
                  min="0"
                  max="255"
                  value={newRecord.u_203 || ''}
                  onChange={(e) => setNewRecord(prev => ({ ...prev, u_203: parseInt(e.target.value) || 0 }))}
                  placeholder="0"
                />
              </div>
              <div>
                <Label htmlFor="new-u204">U204 (Optional)</Label>
                <Input
                  id="new-u204"
                  type="number"
                  min="0"
                  max="255"
                  value={newRecord.u_204 || ''}
                  onChange={(e) => setNewRecord(prev => ({ ...prev, u_204: parseInt(e.target.value) || 0 }))}
                  placeholder="0"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Hủy
            </Button>
            <Button onClick={handleAddRecord}>
              Thêm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Record Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Chỉnh sửa Record</DialogTitle>
            <DialogDescription>
              Cập nhật thông tin record
            </DialogDescription>
          </DialogHeader>
          {editingRecord && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-id">ID</Label>
                <Input
                  id="edit-id"
                  type="number"
                  value={editingRecord.id}
                  onChange={(e) => setEditingRecord(prev => prev ? ({ ...prev, id: parseInt(e.target.value) || 0 }) : null)}
                  disabled // Usually don't allow changing ID
                />
              </div>
              <div>
                <Label htmlFor="edit-message">Message *</Label>
                <Textarea
                  id="edit-message"
                  value={editingRecord.message}
                  onChange={(e) => setEditingRecord(prev => prev ? ({ ...prev, message: e.target.value }) : null)}
                  placeholder="Nhập message"
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-u203">U203</Label>
                  <Input
                    id="edit-u203"
                    type="number"
                    min="0"
                    max="255"
                    value={editingRecord.u_203 || ''}
                    onChange={(e) => setEditingRecord(prev => prev ? ({ ...prev, u_203: parseInt(e.target.value) || 0 }) : null)}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-u204">U204</Label>
                  <Input
                    id="edit-u204"
                    type="number"
                    min="0"
                    max="255"
                    value={editingRecord.u_204 || ''}
                    onChange={(e) => setEditingRecord(prev => prev ? ({ ...prev, u_204: parseInt(e.target.value) || 0 }) : null)}
                  />
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Hủy
            </Button>
            <Button onClick={handleEditRecord}>
              Cập nhật
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Record Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xóa Record</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa record ID {deletingRecord?.id}?
              Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRecord}>
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
