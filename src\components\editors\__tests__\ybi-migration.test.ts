/**
 * YBI Migration Tool Tests
 * Tests for the YBI migration functionality
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { YbiParser, YbiFile, YbiItem, YbiSkill } from '@/lib/parsers/ybi-parser';

// Mock data for testing
const createMockYbiFile = (items: YbiItem[], skills: YbiSkill[] = []): YbiFile => ({
  header: {
    totalItems: items.length,
    headerSize: 8,
    originalHeaderBytes: new Uint8Array(8)
  },
  items,
  skills,
  abilities: [],
  heroTitles: [],
  npcInfos: [],
  mapInfos: [],
  fileName: 'test.cfg',
  fileSize: 1024,
  parserConfig: {
    id: 'v24_2',
    name: 'V24.2 (Latest)',
    description: 'Test config',
    skillFormat: 'normal',
    npcFormat: 'v24_2',
    constants: {
      skillByteLength: 6992,
      skillByteSize: 0x1A0,
      npcByteLength: 0x1eb8 + 4,
      maxNpcs: 3134,
      npcOffsetAdjustment: 3088,
      mapOffsetAdjustment: 2552
    }
  },
  originalDecryptedBuffer: new ArrayBuffer(1024)
});

const createMockItem = (id: number, name: string, desc: string, offset: number = 0): YbiItem => ({
  offset,
  id,
  name,
  desc,
  unknown1: 0,
  level: 1,
  jobLevel: 0,
  sex: 0,
  zx: 0,
  maxAtk: 10,
  minAtk: 5,
  def: 5,
  reside1: 0,
  reside2: 0,
  weight: 1,
  shield: 0,
  gold: 100,
  nj: 0,
  el: 0,
  unknown2: 0,
  unknown3: 0,
  unknown4: 0,
  unknown5: 0,
  unknown6: 0,
  recycleGold: 50,
  wx: 0,
  wxjd: 0,
  unknown7: 0,
  qg_sll: 0,
  itemType: 1,
  setBonusDamage: 0,
  lock: 0
});

const createMockSkill = (id: number, name: string, desc: string, offset: number = 0): YbiSkill => ({
  offset,
  type: 1,
  id,
  name,
  desc,
  unknown1: 0,
  zx: 0,
  job: 0,
  jobLevel: 0,
  level: 1,
  mana: 10,
  atk: 20,
  sp: 100,
  u_54: 0,
  u_58: 0,
  u_60: 0,
  effect: 0,
  index: 0,
  u_194: 0,
  u_198: 0,
  sp_upgrade: 0,
  atkEachLevel: 1,
  sp_tt: 0,
  totalSkill: 0
});

describe('YBI Migration Tool', () => {
  let sourceFile: YbiFile;
  let targetFile: YbiFile;

  beforeEach(() => {
    // Setup source file with English names
    const sourceItems = [
      createMockItem(1, 'Iron Sword', 'A basic iron sword', 100),
      createMockItem(2, 'Steel Shield', 'A sturdy steel shield', 200),
      createMockItem(3, 'Health Potion', 'Restores 100 HP', 300)
    ];

    const sourceSkills = [
      createMockSkill(1, 'Fireball', 'Casts a fireball spell', 400),
      createMockSkill(2, 'Heal', 'Heals the target', 500)
    ];

    sourceFile = createMockYbiFile(sourceItems, sourceSkills);

    // Setup target file with Vietnamese names (to be updated)
    const targetItems = [
      createMockItem(1, 'Kiếm Sắt', 'Một thanh kiếm sắt cơ bản', 100),
      createMockItem(2, 'Khiên Thép', 'Một tấm khiên thép chắc chắn', 200),
      createMockItem(4, 'Mana Potion', 'Restores 50 MP', 400) // Different ID
    ];

    const targetSkills = [
      createMockSkill(1, 'Cầu Lửa', 'Tung ra một quả cầu lửa', 400),
      createMockSkill(3, 'Lightning', 'Strikes with lightning', 600) // Different ID
    ];

    targetFile = createMockYbiFile(targetItems, targetSkills);
  });

  describe('Field Selection', () => {
    it('should handle field selection correctly', () => {
      const selectedFields = new Set<string>();
      
      // Test adding fields
      selectedFields.add('items.name');
      selectedFields.add('items.desc');
      selectedFields.add('skills.name');
      
      expect(selectedFields.has('items.name')).toBe(true);
      expect(selectedFields.has('items.desc')).toBe(true);
      expect(selectedFields.has('skills.name')).toBe(true);
      expect(selectedFields.has('skills.desc')).toBe(false);
      expect(selectedFields.size).toBe(3);
    });

    it('should handle field deselection correctly', () => {
      const selectedFields = new Set<string>(['items.name', 'items.desc']);
      
      selectedFields.delete('items.name');
      
      expect(selectedFields.has('items.name')).toBe(false);
      expect(selectedFields.has('items.desc')).toBe(true);
      expect(selectedFields.size).toBe(1);
    });
  });

  describe('ID Matching Logic', () => {
    it('should find matching items by ID', () => {
      const sourceItemsMap = new Map();
      sourceFile.items.forEach(item => {
        sourceItemsMap.set(item.id, item);
      });

      // Should find matches for IDs 1 and 2
      expect(sourceItemsMap.has(1)).toBe(true);
      expect(sourceItemsMap.has(2)).toBe(true);
      expect(sourceItemsMap.has(3)).toBe(true);
      expect(sourceItemsMap.has(4)).toBe(false); // Only in target

      // Check target matching
      const targetItem1 = targetFile.items.find(item => item.id === 1);
      const sourceItem1 = sourceItemsMap.get(1);
      
      expect(targetItem1).toBeDefined();
      expect(sourceItem1).toBeDefined();
      expect(sourceItem1?.name).toBe('Iron Sword');
      expect(targetItem1?.name).toBe('Kiếm Sắt');
    });

    it('should find matching skills by ID', () => {
      const sourceSkillsMap = new Map();
      sourceFile.skills.forEach(skill => {
        sourceSkillsMap.set(skill.id, skill);
      });

      // Should find match for ID 1 only
      expect(sourceSkillsMap.has(1)).toBe(true);
      expect(sourceSkillsMap.has(2)).toBe(true);
      expect(sourceSkillsMap.has(3)).toBe(false); // Only in target

      const targetSkill1 = targetFile.skills.find(skill => skill.id === 1);
      const sourceSkill1 = sourceSkillsMap.get(1);
      
      expect(targetSkill1).toBeDefined();
      expect(sourceSkill1).toBeDefined();
      expect(sourceSkill1?.name).toBe('Fireball');
      expect(targetSkill1?.name).toBe('Cầu Lửa');
    });
  });

  describe('Migration Statistics', () => {
    it('should calculate correct migration counts', () => {
      // Simulate migration counting
      let itemNameMigrations = 0;
      let itemDescMigrations = 0;
      let skillNameMigrations = 0;

      const sourceItemsMap = new Map();
      sourceFile.items.forEach(item => {
        sourceItemsMap.set(item.id, item);
      });

      // Count item migrations
      for (const targetItem of targetFile.items) {
        const sourceItem = sourceItemsMap.get(targetItem.id);
        if (sourceItem) {
          itemNameMigrations++;
          itemDescMigrations++;
        }
      }

      const sourceSkillsMap = new Map();
      sourceFile.skills.forEach(skill => {
        sourceSkillsMap.set(skill.id, skill);
      });

      // Count skill migrations
      for (const targetSkill of targetFile.skills) {
        const sourceSkill = sourceSkillsMap.get(targetSkill.id);
        if (sourceSkill) {
          skillNameMigrations++;
        }
      }

      expect(itemNameMigrations).toBe(2); // IDs 1 and 2 match
      expect(itemDescMigrations).toBe(2);
      expect(skillNameMigrations).toBe(1); // Only ID 1 matches
    });
  });

  describe('String Encoding', () => {
    it('should handle Latin-1 encoding correctly', () => {
      const testString = 'Kiếm Sắt';
      const buffer = new ArrayBuffer(64);
      const view = new DataView(buffer);
      
      // Simulate writeStringToBuffer function
      const maxLength = 64;
      
      // Clear buffer
      for (let i = 0; i < maxLength; i++) {
        view.setUint8(i, 0);
      }
      
      // Write string
      const writeLength = Math.min(testString.length, maxLength - 1);
      for (let i = 0; i < writeLength; i++) {
        const charCode = testString.charCodeAt(i);
        const byte = charCode > 255 ? 63 : charCode; // Use '?' for unsupported chars
        view.setUint8(i, byte);
      }
      
      // Verify null termination
      expect(view.getUint8(writeLength)).toBe(0);
      
      // Verify string was written
      let result = '';
      for (let i = 0; i < writeLength; i++) {
        const byte = view.getUint8(i);
        if (byte === 0) break;
        result += String.fromCharCode(byte);
      }
      
      // Note: Vietnamese characters may not encode perfectly to Latin-1
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle ASCII strings correctly', () => {
      const testString = 'Iron Sword';
      const buffer = new ArrayBuffer(64);
      const view = new DataView(buffer);
      
      // Clear and write
      for (let i = 0; i < 64; i++) {
        view.setUint8(i, 0);
      }
      
      for (let i = 0; i < testString.length; i++) {
        view.setUint8(i, testString.charCodeAt(i));
      }
      
      // Read back
      let result = '';
      for (let i = 0; i < 64; i++) {
        const byte = view.getUint8(i);
        if (byte === 0) break;
        result += String.fromCharCode(byte);
      }
      
      expect(result).toBe('Iron Sword');
    });
  });

  describe('Buffer Operations', () => {
    it('should preserve buffer structure during migration', () => {
      const originalBuffer = new ArrayBuffer(1024);
      const originalView = new DataView(originalBuffer);
      
      // Set some test data
      originalView.setUint32(0, 0x12345678, true);
      originalView.setUint32(4, 0x9ABCDEF0, true);
      
      // Create a copy (simulating migration process)
      const modifiedBuffer = originalBuffer.slice(0);
      const modifiedView = new DataView(modifiedBuffer);
      
      // Verify original data is preserved
      expect(modifiedView.getUint32(0, true)).toBe(0x12345678);
      expect(modifiedView.getUint32(4, true)).toBe(0x9ABCDEF0);
      
      // Modify specific field (simulating string write)
      const testOffset = 100;
      const testString = 'Test';
      
      for (let i = 0; i < testString.length; i++) {
        modifiedView.setUint8(testOffset + i, testString.charCodeAt(i));
      }
      
      // Verify modification
      let readString = '';
      for (let i = 0; i < testString.length; i++) {
        readString += String.fromCharCode(modifiedView.getUint8(testOffset + i));
      }
      expect(readString).toBe('Test');
      
      // Verify other data is still intact
      expect(modifiedView.getUint32(0, true)).toBe(0x12345678);
      expect(modifiedView.getUint32(4, true)).toBe(0x9ABCDEF0);
    });
  });
});
