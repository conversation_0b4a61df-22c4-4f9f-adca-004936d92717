'use client';

import React, { useState, useRef, use<PERSON><PERSON>back } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Upload, 
  Download, 
  ArrowRight, 
  FileText,
  Database,
  Settings,
  Package,
  MapPin,
  Users,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  YbiParserConfig, 
  YBI_PARSER_CONFIGS,
  getParserConfig
} from '@/lib/parsers/ybi-parser';

interface MigrationField {
  key: string;
  label: string;
  category: 'items' | 'skills' | 'abilities' | 'npcInfos' | 'mapInfos';
  description: string;
  type: 'string' | 'number' | 'float' | 'boolean';
}

// Migration profiles for quick selection
interface MigrationProfile {
  id: string;
  name: string;
  description: string;
  icon: string;
  fields: string[]; // Array of "category.field" keys
}

const MIGRATION_PROFILES: MigrationProfile[] = [
  {
    id: 'all',
    name: 'Migrate All',
    description: 'Migrate tất cả các field có thể',
    icon: '🔄',
    fields: [] // Will be populated dynamically
  },
  {
    id: 'strings',
    name: 'Migrate Strings',
    description: 'Chỉ migrate các field text/string',
    icon: '📝',
    fields: [] // Will be populated dynamically
  },
  {
    id: 'numbers',
    name: 'Migrate Numbers',
    description: 'Chỉ migrate các field số',
    icon: '🔢',
    fields: [] // Will be populated dynamically
  },
  {
    id: 'names_only',
    name: 'Names Only',
    description: 'Chỉ migrate tên (name fields)',
    icon: '🏷️',
    fields: [
      'items.name',
      'skills.name',
      'abilities.name',
      'npcInfos.name',
      'mapInfos.name'
    ]
  },
  {
    id: 'descriptions_only',
    name: 'Descriptions Only',
    description: 'Chỉ migrate mô tả (desc fields)',
    icon: '📄',
    fields: [
      'items.desc',
      'skills.desc',
      'abilities.desc',
      'npcInfos.desc',
      'npcInfos.desc2',
      'npcInfos.desc3',
      'npcInfos.desc4',
      'npcInfos.desc5',
      'npcInfos.desc6',
      'npcInfos.desc7',
      'npcInfos.desc8',
      'npcInfos.desc9',
      'npcInfos.desc10',
      'npcInfos.desc11'
    ]
  }
];

const MIGRATION_FIELDS: MigrationField[] = [
  // Items - Based on YbiItem interface
  { key: 'name', label: 'Tên vật phẩm', category: 'items', description: 'Tên hiển thị của vật phẩm', type: 'string' },
  { key: 'desc', label: 'Mô tả vật phẩm', category: 'items', description: 'Mô tả chi tiết của vật phẩm', type: 'string' },
  { key: 'level', label: 'Level yêu cầu', category: 'items', description: 'Level tối thiểu để sử dụng', type: 'number' },
  { key: 'jobLevel', label: 'Job Level yêu cầu', category: 'items', description: 'Job level tối thiểu', type: 'number' },
  { key: 'sex', label: 'Giới tính', category: 'items', description: 'Giới tính có thể sử dụng (0=All, 1=Nam, 2=Nữ)', type: 'number' },
  { key: 'maxAtk', label: 'Sát thương tối đa', category: 'items', description: 'Sát thương tối đa của vũ khí', type: 'number' },
  { key: 'minAtk', label: 'Sát thương tối thiểu', category: 'items', description: 'Sát thương tối thiểu của vũ khí', type: 'number' },
  { key: 'def', label: 'Phòng thủ', category: 'items', description: 'Chỉ số phòng thủ', type: 'number' },
  { key: 'weight', label: 'Trọng lượng', category: 'items', description: 'Trọng lượng của vật phẩm', type: 'number' },
  { key: 'shield', label: 'Shield', category: 'items', description: 'Chỉ số shield', type: 'number' },
  { key: 'gold', label: 'Giá bán', category: 'items', description: 'Giá bán cho NPC', type: 'number' },
  { key: 'recycleGold', label: 'Giá tái chế', category: 'items', description: 'Giá khi tái chế', type: 'number' },
  { key: 'nj', label: 'Nội công', category: 'items', description: 'Chỉ số nội công', type: 'number' },
  { key: 'el', label: 'Thể lực', category: 'items', description: 'Chỉ số thể lực', type: 'number' },
  { key: 'wx', label: 'WX', category: 'items', description: 'Chỉ số WX', type: 'number' },
  { key: 'wxjd', label: 'WXJD', category: 'items', description: 'Chỉ số WXJD', type: 'number' },
  { key: 'qg_sll', label: 'QG SLL', category: 'items', description: 'Chỉ số QG SLL', type: 'number' },
  { key: 'itemType', label: 'Loại vật phẩm', category: 'items', description: 'Loại vật phẩm', type: 'number' },
  { key: 'setBonusDamage', label: 'Set Bonus Damage', category: 'items', description: 'Sát thương bonus khi đủ set', type: 'number' },
  { key: 'lock', label: 'Khóa', category: 'items', description: 'Trạng thái khóa', type: 'number' },

  // Skills - Based on YbiSkill interface
  { key: 'name', label: 'Tên kỹ năng', category: 'skills', description: 'Tên hiển thị của kỹ năng', type: 'string' },
  { key: 'desc', label: 'Mô tả kỹ năng', category: 'skills', description: 'Mô tả chi tiết của kỹ năng', type: 'string' },
  { key: 'job', label: 'Nghề nghiệp', category: 'skills', description: 'Nghề nghiệp có thể học', type: 'number' },
  { key: 'jobLevel', label: 'Job Level yêu cầu', category: 'skills', description: 'Job level tối thiểu', type: 'number' },
  { key: 'level', label: 'Level yêu cầu', category: 'skills', description: 'Level tối thiểu để học', type: 'number' },
  { key: 'mana', label: 'Mana tiêu hao', category: 'skills', description: 'Lượng mana cần để sử dụng', type: 'number' },
  { key: 'atk', label: 'Sát thương', category: 'skills', description: 'Sát thương của kỹ năng', type: 'number' },
  { key: 'sp', label: 'Skill Point', category: 'skills', description: 'Điểm kỹ năng cần để học', type: 'number' },
  { key: 'effect', label: 'Hiệu ứng', category: 'skills', description: 'ID hiệu ứng', type: 'number' },
  { key: 'sp_upgrade', label: 'SP nâng cấp', category: 'skills', description: 'SP cần để nâng cấp', type: 'number' },
  { key: 'atkEachLevel', label: 'ATK mỗi level', category: 'skills', description: 'Sát thương tăng mỗi level', type: 'number' },
  { key: 'sp_tt', label: 'SP TT', category: 'skills', description: 'SP TT', type: 'number' },

  // Abilities - Based on YbiAbility interface
  { key: 'name', label: 'Tên khí công', category: 'abilities', description: 'Tên hiển thị của khí công', type: 'string' },
  { key: 'desc', label: 'Mô tả khí công', category: 'abilities', description: 'Mô tả chi tiết của khí công', type: 'string' },
  { key: 'job', label: 'Nghề nghiệp', category: 'abilities', description: 'Nghề nghiệp có thể học', type: 'number' },
  { key: 'level', label: 'Level yêu cầu', category: 'abilities', description: 'Level tối thiểu', type: 'number' },
  { key: 'jobLevel', label: 'Job Level yêu cầu', category: 'abilities', description: 'Job level tối thiểu', type: 'number' },
  { key: 'o_1_perLevel', label: 'O1 per Level', category: 'abilities', description: 'Giá trị O1 mỗi level', type: 'float' },
  { key: 'o_2_perLevel', label: 'O2 per Level', category: 'abilities', description: 'Giá trị O2 mỗi level', type: 'float' },
  { key: 'o_3_perLevel', label: 'O3 per Level', category: 'abilities', description: 'Giá trị O3 mỗi level', type: 'float' },
  { key: 'o_4_perLevel', label: 'O4 per Level', category: 'abilities', description: 'Giá trị O4 mỗi level', type: 'float' },
  { key: 'o_5_perLevel', label: 'O5 per Level', category: 'abilities', description: 'Giá trị O5 mỗi level', type: 'float' },
  { key: 'o_1', label: 'O1 Text', category: 'abilities', description: 'Text mô tả O1', type: 'string' },
  { key: 'o_1_prefix', label: 'O1 Prefix', category: 'abilities', description: 'Prefix cho O1', type: 'string' },
  { key: 'o_2', label: 'O2 Text', category: 'abilities', description: 'Text mô tả O2', type: 'string' },
  { key: 'o_2_prefix', label: 'O2 Prefix', category: 'abilities', description: 'Prefix cho O2', type: 'string' },
  { key: 'o_3', label: 'O3 Text', category: 'abilities', description: 'Text mô tả O3', type: 'string' },
  { key: 'o_3_prefix', label: 'O3 Prefix', category: 'abilities', description: 'Prefix cho O3', type: 'string' },
  { key: 'o_4', label: 'O4 Text', category: 'abilities', description: 'Text mô tả O4', type: 'string' },
  { key: 'o_4_prefix', label: 'O4 Prefix', category: 'abilities', description: 'Prefix cho O4', type: 'string' },
  { key: 'o_5', label: 'O5 Text', category: 'abilities', description: 'Text mô tả O5', type: 'string' },
  { key: 'o_5_prefix', label: 'O5 Prefix', category: 'abilities', description: 'Prefix cho O5', type: 'string' },

  // NPCs - Based on YbiNpcInfo interface
  { key: 'name', label: 'Tên NPC', category: 'npcInfos', description: 'Tên hiển thị của NPC', type: 'string' },
  { key: 'desc', label: 'Mô tả NPC', category: 'npcInfos', description: 'Mô tả chính của NPC', type: 'string' },
  { key: 'desc2', label: 'Mô tả 2', category: 'npcInfos', description: 'Mô tả bổ sung 2', type: 'string' },
  { key: 'desc3', label: 'Mô tả 3', category: 'npcInfos', description: 'Mô tả bổ sung 3', type: 'string' },
  { key: 'desc4', label: 'Mô tả 4', category: 'npcInfos', description: 'Mô tả bổ sung 4', type: 'string' },
  { key: 'desc5', label: 'Mô tả 5', category: 'npcInfos', description: 'Mô tả bổ sung 5', type: 'string' },
  { key: 'desc6', label: 'Mô tả 6', category: 'npcInfos', description: 'Mô tả bổ sung 6', type: 'string' },
  { key: 'desc7', label: 'Mô tả 7', category: 'npcInfos', description: 'Mô tả bổ sung 7', type: 'string' },
  { key: 'desc8', label: 'Mô tả 8', category: 'npcInfos', description: 'Mô tả bổ sung 8', type: 'string' },
  { key: 'desc9', label: 'Mô tả 9', category: 'npcInfos', description: 'Mô tả bổ sung 9', type: 'string' },
  { key: 'desc10', label: 'Mô tả 10', category: 'npcInfos', description: 'Mô tả bổ sung 10', type: 'string' },
  { key: 'desc11', label: 'Mô tả 11', category: 'npcInfos', description: 'Mô tả bổ sung 11', type: 'string' },
  { key: 'level', label: 'Level NPC', category: 'npcInfos', description: 'Level của NPC', type: 'number' },
  { key: 'hp', label: 'HP NPC', category: 'npcInfos', description: 'Máu của NPC', type: 'number' },
  { key: 'menu1', label: 'Menu 1', category: 'npcInfos', description: 'Menu option 1', type: 'number' },
  { key: 'menu2', label: 'Menu 2', category: 'npcInfos', description: 'Menu option 2', type: 'number' },
  { key: 'menu3', label: 'Menu 3', category: 'npcInfos', description: 'Menu option 3', type: 'number' },
  { key: 'menu4', label: 'Menu 4', category: 'npcInfos', description: 'Menu option 4', type: 'number' },

  // Maps - Based on YbiMapInfo interface
  { key: 'name', label: 'Tên bản đồ', category: 'mapInfos', description: 'Tên hiển thị của bản đồ', type: 'string' },
  { key: 'x', label: 'Tọa độ X', category: 'mapInfos', description: 'Tọa độ X của bản đồ', type: 'float' },
  { key: 'y', label: 'Tọa độ Y', category: 'mapInfos', description: 'Tọa độ Y của bản đồ', type: 'float' },
  { key: 'z', label: 'Tọa độ Z', category: 'mapInfos', description: 'Tọa độ Z của bản đồ', type: 'float' },
  { key: 'x_2', label: 'Tọa độ X2', category: 'mapInfos', description: 'Tọa độ X2 của bản đồ', type: 'float' },
  { key: 'y_2', label: 'Tọa độ Y2', category: 'mapInfos', description: 'Tọa độ Y2 của bản đồ', type: 'float' },
  { key: 'z_2', label: 'Tọa độ Z2', category: 'mapInfos', description: 'Tọa độ Z2 của bản đồ', type: 'float' },
  { key: 'bgm1', label: 'BGM 1', category: 'mapInfos', description: 'File nhạc nền 1', type: 'string' },
  { key: 'bgm2', label: 'BGM 2', category: 'mapInfos', description: 'File nhạc nền 2', type: 'string' },
  { key: 'u_168', label: 'Unknown 168', category: 'mapInfos', description: 'Trường unknown 168', type: 'string' },
  { key: 'u_1e8', label: 'Unknown 1E8', category: 'mapInfos', description: 'Trường unknown 1E8', type: 'string' },
  { key: 'u_268', label: 'Unknown 268', category: 'mapInfos', description: 'Trường unknown 268', type: 'string' },
];

// Populate dynamic profiles
const populateProfiles = () => {
  const allFields = MIGRATION_FIELDS.map(field => `${field.category}.${field.key}`);
  const stringFields = MIGRATION_FIELDS.filter(field => field.type === 'string').map(field => `${field.category}.${field.key}`);
  const numberFields = MIGRATION_FIELDS.filter(field => field.type === 'number' || field.type === 'float').map(field => `${field.category}.${field.key}`);

  MIGRATION_PROFILES[0].fields = allFields; // all
  MIGRATION_PROFILES[1].fields = stringFields; // strings
  MIGRATION_PROFILES[2].fields = numberFields; // numbers
};

// Populate profiles on module load
populateProfiles();

export default function YbiMigration() {
  const [sourceFile, setSourceFile] = useState<YbiFile | null>(null);
  const [targetFile, setTargetFile] = useState<YbiFile | null>(null);
  const [sourceConfig, setSourceConfig] = useState<string>('');
  const [targetConfig, setTargetConfig] = useState<string>('');
  const [selectedFields, setSelectedFields] = useState<Set<string>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const [migrationResult, setMigrationResult] = useState<ArrayBuffer | null>(null);

  const sourceFileRef = useRef<HTMLInputElement>(null);
  const targetFileRef = useRef<HTMLInputElement>(null);

  // Handle source file upload
  const handleSourceFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const buffer = await file.arrayBuffer();
      const config = getParserConfig(sourceConfig);
      if (!config) {
        toast.error('Vui lòng chọn config cho file nguồn');
        return;
      }

      const parsedData = YbiParser.parseWithConfig(buffer, config, file.name);
      setSourceFile(parsedData);
      
      toast.success(
        `✅ Đã tải file nguồn thành công\n` +
        `File: ${file.name}\n` +
        `Config: ${config.name}\n` +
        `Items: ${parsedData.items.length}, Skills: ${parsedData.skills.length}, NPCs: ${parsedData.npcInfos.length}`
      );

    } catch (error) {
      console.error('Error parsing source YBI file:', error);
      toast.error('Lỗi khi đọc file nguồn: ' + (error as Error).message);
    }
  }, [sourceConfig]);

  // Handle target file upload
  const handleTargetFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const buffer = await file.arrayBuffer();
      const config = getParserConfig(targetConfig);
      if (!config) {
        toast.error('Vui lòng chọn config cho file đích');
        return;
      }

      const parsedData = YbiParser.parseWithConfig(buffer, config, file.name);
      setTargetFile(parsedData);
      
      toast.success(
        `✅ Đã tải file đích thành công\n` +
        `File: ${file.name}\n` +
        `Config: ${config.name}\n` +
        `Items: ${parsedData.items.length}, Skills: ${parsedData.skills.length}, NPCs: ${parsedData.npcInfos.length}`
      );

    } catch (error) {
      console.error('Error parsing target YBI file:', error);
      toast.error('Lỗi khi đọc file đích: ' + (error as Error).message);
    }
  }, [targetConfig]);

  // Handle field selection
  const handleFieldToggle = useCallback((fieldKey: string, category: string) => {
    const fullKey = `${category}.${fieldKey}`;
    const newSelected = new Set(selectedFields);

    if (newSelected.has(fullKey)) {
      newSelected.delete(fullKey);
    } else {
      newSelected.add(fullKey);
    }

    setSelectedFields(newSelected);
  }, [selectedFields]);

  // Handle profile selection
  const handleProfileSelect = useCallback((profileId: string) => {
    const profile = MIGRATION_PROFILES.find(p => p.id === profileId);
    if (profile) {
      setSelectedFields(new Set(profile.fields));
      toast.success(`Đã áp dụng profile: ${profile.name}`);
    }
  }, []);

  // Handle select all in category
  const handleSelectAllInCategory = useCallback((category: string) => {
    const categoryFields = MIGRATION_FIELDS
      .filter(field => field.category === category)
      .map(field => `${category}.${field.key}`);

    const newSelected = new Set(selectedFields);
    categoryFields.forEach(field => newSelected.add(field));
    setSelectedFields(newSelected);
  }, [selectedFields]);

  // Handle deselect all in category
  const handleDeselectAllInCategory = useCallback((category: string) => {
    const categoryFields = MIGRATION_FIELDS
      .filter(field => field.category === category)
      .map(field => `${category}.${field.key}`);

    const newSelected = new Set(selectedFields);
    categoryFields.forEach(field => newSelected.delete(field));
    setSelectedFields(newSelected);
  }, [selectedFields]);

  // Perform migration
  const performMigration = useCallback(async () => {
    if (!sourceFile || !targetFile) {
      toast.error('Vui lòng tải cả hai file trước khi thực hiện migration');
      return;
    }

    if (selectedFields.size === 0) {
      toast.error('Vui lòng chọn ít nhất một field để migrate');
      return;
    }

    setIsProcessing(true);
    
    try {
      // Create a copy of target file's decrypted buffer for modification
      if (!targetFile.originalDecryptedBuffer) {
        throw new Error('Target file không có dữ liệu decrypt gốc');
      }

      const modifiedBuffer = targetFile.originalDecryptedBuffer.slice(0);
      const view = new DataView(modifiedBuffer);
      let migratedCount = 0;

      // Process each selected field
      for (const fieldKey of selectedFields) {
        const [category, field] = fieldKey.split('.');
        
        if (category === 'items' && field !== 'title') {
          migratedCount += await migrateItemsField(view, sourceFile, targetFile, field);
        } else if (category === 'skills' && field !== 'title') {
          migratedCount += await migrateSkillsField(view, sourceFile, targetFile, field);
        } else if (category === 'abilities' && field !== 'title') {
          migratedCount += await migrateAbilitiesField(view, sourceFile, targetFile, field);
        } else if (category === 'npcInfos' && field !== 'title') {
          migratedCount += await migrateNpcInfosField(view, sourceFile, targetFile, field);
        } else if (category === 'mapInfos' && field !== 'title') {
          migratedCount += await migrateMapInfosField(view, sourceFile, targetFile, field);
        }
      }

      // Encrypt the modified buffer
      const encryptedResult = YbiParser.cryptData(modifiedBuffer);
      setMigrationResult(encryptedResult);

      toast.success(
        `🎉 Migration hoàn thành!\n` +
        `Đã migrate ${migratedCount} records\n` +
        `Fields: ${selectedFields.size} fields\n` +
        `Có thể tải file kết quả`
      );

    } catch (error) {
      console.error('Migration error:', error);
      toast.error('Lỗi khi thực hiện migration: ' + (error as Error).message);
    } finally {
      setIsProcessing(false);
    }
  }, [sourceFile, targetFile, selectedFields]);

  // Download migrated file
  const downloadMigratedFile = useCallback(() => {
    if (!migrationResult || !targetFile) {
      toast.error('Chưa có kết quả migration để tải');
      return;
    }

    const blob = new Blob([migrationResult], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    a.download = `${targetFile.fileName}_migrated_${timestamp}.cfg`;
    
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success(`Đã tải file migration: ${a.download}`);
  }, [migrationResult, targetFile]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">YBI Migration Tool</h2>
          <p className="text-muted-foreground">Migrate dữ liệu giữa các file YBI với config khác nhau</p>
        </div>
        <div className="flex items-center gap-2">
          {migrationResult && (
            <Badge variant="default" className="bg-green-600">
              <CheckCircle className="h-3 w-3 mr-1" />
              Migration hoàn thành
            </Badge>
          )}
        </div>
      </div>

      {/* File Upload Section */}
      <div className="grid grid-cols-2 gap-6">
        {/* Source File */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              File Nguồn (Source)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Parser Config</Label>
              <Select value={sourceConfig} onValueChange={setSourceConfig}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn config cho file nguồn" />
                </SelectTrigger>
                <SelectContent>
                  {YBI_PARSER_CONFIGS.map(config => (
                    <SelectItem key={config.id} value={config.id}>
                      {config.name} - {config.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Button
              onClick={() => sourceFileRef.current?.click()}
              disabled={!sourceConfig}
              className="w-full flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Chọn file nguồn
            </Button>
            
            {sourceFile && (
              <div className="space-y-2">
                <Badge variant="outline">{sourceFile.fileName}</Badge>
                <div className="text-sm text-muted-foreground">
                  <div>Items: {sourceFile.items.length}</div>
                  <div>Skills: {sourceFile.skills.length}</div>
                  <div>NPCs: {sourceFile.npcInfos.length}</div>
                  <div>Maps: {sourceFile.mapInfos.length}</div>
                </div>
              </div>
            )}
            
            <input
              ref={sourceFileRef}
              type="file"
              accept=".cfg"
              onChange={handleSourceFileUpload}
              className="hidden"
            />
          </CardContent>
        </Card>

        {/* Target File */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              File Đích (Target)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Parser Config</Label>
              <Select value={targetConfig} onValueChange={setTargetConfig}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn config cho file đích" />
                </SelectTrigger>
                <SelectContent>
                  {YBI_PARSER_CONFIGS.map(config => (
                    <SelectItem key={config.id} value={config.id}>
                      {config.name} - {config.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Button
              onClick={() => targetFileRef.current?.click()}
              disabled={!targetConfig}
              className="w-full flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Chọn file đích
            </Button>
            
            {targetFile && (
              <div className="space-y-2">
                <Badge variant="outline">{targetFile.fileName}</Badge>
                <div className="text-sm text-muted-foreground">
                  <div>Items: {targetFile.items.length}</div>
                  <div>Skills: {targetFile.skills.length}</div>
                  <div>NPCs: {targetFile.npcInfos.length}</div>
                  <div>Maps: {targetFile.mapInfos.length}</div>
                </div>
              </div>
            )}
            
            <input
              ref={targetFileRef}
              type="file"
              accept=".cfg"
              onChange={handleTargetFileUpload}
              className="hidden"
            />
          </CardContent>
        </Card>
      </div>

      {/* Migration Configuration */}
      {sourceFile && targetFile && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Cấu hình Migration
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Migration Profiles */}
            <div className="mb-6">
              <Label className="text-base font-semibold mb-3 block">Quick Selection Profiles</Label>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                {MIGRATION_PROFILES.map(profile => (
                  <Button
                    key={profile.id}
                    variant="outline"
                    size="sm"
                    onClick={() => handleProfileSelect(profile.id)}
                    className="flex flex-col items-center gap-2 h-auto py-3 px-3 text-center"
                  >
                    <span className="text-lg">{profile.icon}</span>
                    <div>
                      <div className="font-medium text-xs leading-tight">{profile.name}</div>
                      <div className="text-xs text-muted-foreground leading-tight mt-1">{profile.description}</div>
                    </div>
                  </Button>
                ))}
              </div>

              {/* Selection Summary */}
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <div className="flex items-center justify-between flex-wrap gap-2">
                  <div className="flex items-center gap-4">
                    <span className="text-sm font-medium">
                      Đã chọn: <Badge variant="secondary">{selectedFields.size}</Badge> fields
                    </span>
                    {selectedFields.size > 0 && (
                      <span className="text-xs text-muted-foreground">
                        {/* Show breakdown by type */}
                        {(() => {
                          const selectedFieldsList = Array.from(selectedFields);
                          const stringCount = selectedFieldsList.filter(key => {
                            const [category, fieldKey] = key.split('.');
                            const field = MIGRATION_FIELDS.find(f => f.category === category && f.key === fieldKey);
                            return field?.type === 'string';
                          }).length;
                          const numberCount = selectedFieldsList.filter(key => {
                            const [category, fieldKey] = key.split('.');
                            const field = MIGRATION_FIELDS.find(f => f.category === category && f.key === fieldKey);
                            return field?.type === 'number' || field?.type === 'float';
                          }).length;

                          return (
                            <span>
                              {stringCount > 0 && <Badge variant="outline" className="text-xs mr-1">📝 {stringCount}</Badge>}
                              {numberCount > 0 && <Badge variant="outline" className="text-xs">🔢 {numberCount}</Badge>}
                            </span>
                          );
                        })()}
                      </span>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedFields(new Set())}
                    disabled={selectedFields.size === 0}
                  >
                    Clear All
                  </Button>
                </div>
              </div>
            </div>

            <Tabs defaultValue="items" className="w-full">
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5 h-auto">
                <TabsTrigger value="items" className="text-xs sm:text-sm">📦 Items</TabsTrigger>
                <TabsTrigger value="skills" className="text-xs sm:text-sm">⚔️ Skills</TabsTrigger>
                <TabsTrigger value="abilities" className="text-xs sm:text-sm">🌟 Abilities</TabsTrigger>
                <TabsTrigger value="npcInfos" className="text-xs sm:text-sm">👥 NPCs</TabsTrigger>
                <TabsTrigger value="mapInfos" className="text-xs sm:text-sm">🗺️ Maps</TabsTrigger>
              </TabsList>

              {['items', 'skills', 'abilities', 'npcInfos', 'mapInfos'].map(category => {
                const categoryFields = MIGRATION_FIELDS.filter(field => field.category === category);
                const selectedInCategory = categoryFields.filter(field => selectedFields.has(`${category}.${field.key}`)).length;

                return (
                  <TabsContent key={category} value={category} className="space-y-4">
                    {/* Category Controls */}
                    <div className="flex items-center justify-between p-3 bg-muted rounded-lg mb-4">
                      <div className="flex items-center gap-3">
                        <span className="font-medium">
                          {category === 'items' ? '📦 Items' :
                           category === 'skills' ? '⚔️ Skills' :
                           category === 'abilities' ? '🌟 Abilities' :
                           category === 'npcInfos' ? '👥 NPCs' : '🗺️ Maps'}
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {selectedInCategory}/{categoryFields.length}
                        </Badge>
                        {selectedInCategory > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {Math.round((selectedInCategory / categoryFields.length) * 100)}%
                          </Badge>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectAllInCategory(category)}
                          disabled={selectedInCategory === categoryFields.length}
                          className="text-xs px-3"
                        >
                          All
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeselectAllInCategory(category)}
                          disabled={selectedInCategory === 0}
                          className="text-xs px-3"
                        >
                          None
                        </Button>
                      </div>
                    </div>

                    {/* Fields Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3">
                      {categoryFields.map(field => {
                        const fullKey = `${category}.${field.key}`;
                        const isSelected = selectedFields.has(fullKey);

                        // Type color mapping
                        const typeColors = {
                          string: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                          number: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                          float: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
                          boolean: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                        };

                        return (
                          <div
                            key={fullKey}
                            className="flex flex-col p-3 border rounded-lg hover:bg-muted/50 transition-colors h-full"
                          >
                            {/* Header with checkbox and type badge */}
                            <div className="flex items-start gap-2 mb-2">
                              <Checkbox
                                id={fullKey}
                                checked={isSelected}
                                onCheckedChange={() => handleFieldToggle(field.key, category)}
                                className="mt-0.5 flex-shrink-0"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-1 mb-1">
                                  <Label
                                    htmlFor={fullKey}
                                    className="font-medium cursor-pointer text-sm leading-tight truncate"
                                    title={field.label}
                                  >
                                    {field.label}
                                  </Label>
                                </div>
                                <Badge
                                  variant="secondary"
                                  className={`text-xs ${typeColors[field.type]} mb-1`}
                                >
                                  {field.type}
                                </Badge>
                              </div>
                            </div>

                            {/* Description */}
                            <p
                              className="text-xs text-muted-foreground leading-relaxed flex-1"
                              title={field.description}
                            >
                              {field.description}
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  </TabsContent>
                );
              })}
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Migration Actions */}
      {sourceFile && targetFile && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Badge variant="outline">
                  {selectedFields.size} fields được chọn
                </Badge>
                {migrationResult && (
                  <Badge variant="default" className="bg-green-600">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Sẵn sàng tải xuống
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  onClick={performMigration}
                  disabled={isProcessing || selectedFields.size === 0}
                  className="flex items-center gap-2"
                >
                  {isProcessing ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <ArrowRight className="h-4 w-4" />
                  )}
                  {isProcessing ? 'Đang xử lý...' : 'Thực hiện Migration'}
                </Button>
                
                <Button
                  onClick={downloadMigratedFile}
                  disabled={!migrationResult}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Tải file kết quả
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      {!sourceFile || !targetFile ? (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p><strong>Hướng dẫn sử dụng YBI Migration:</strong></p>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Chọn parser config phù hợp cho từng file (nguồn và đích)</li>
                <li>Tải lên file YBI nguồn (chứa dữ liệu cần migrate)</li>
                <li>Tải lên file YBI đích (file sẽ được cập nhật)</li>
                <li>Chọn các fields cần migrate (trừ title - danh hiệu)</li>
                <li>Thực hiện migration và tải file kết quả</li>
              </ol>
              <p className="text-xs text-muted-foreground mt-2">
                Lưu ý: Tool sẽ tự động padding với 0x00 và preserve cấu trúc file gốc
              </p>
            </div>
          </AlertDescription>
        </Alert>
      ) : null}
    </div>
  );
}

// Migration helper functions

// Helper function to write string to buffer with exact length and padding
function writeStringToBuffer(view: DataView, offset: number, str: string, length: number): void {
  // Clear the entire field first (fill with 0x00)
  for (let i = 0; i < length; i++) {
    view.setUint8(offset + i, 0);
  }

  // Encode string to bytes (using Latin-1 encoding)
  for (let i = 0; i < Math.min(str.length, length); i++) {
    const charCode = str.charCodeAt(i);
    // Ensure we stay within Latin-1 range (0-255)
    view.setUint8(offset + i, charCode > 255 ? 63 : charCode); // Use '?' (63) for unsupported chars
  }
}

// Helper function to write different data types to buffer
function writeFieldToBuffer(view: DataView, offset: number, value: any, fieldType: 'string' | 'number' | 'float' | 'boolean', fieldLength?: number): void {
  switch (fieldType) {
    case 'string':
      if (fieldLength) {
        writeStringToBuffer(view, offset, String(value), fieldLength);
      }
      break;
    case 'number':
      if (fieldLength === 1) {
        view.setUint8(offset, Number(value));
      } else if (fieldLength === 2) {
        view.setUint16(offset, Number(value), true); // little-endian
      } else if (fieldLength === 4) {
        view.setUint32(offset, Number(value), true); // little-endian
      } else if (fieldLength === 8) {
        view.setBigUint64(offset, BigInt(Number(value)), true); // little-endian
      }
      break;
    case 'float':
      if (fieldLength === 4) {
        view.setFloat32(offset, Number(value), true); // little-endian
      } else if (fieldLength === 8) {
        view.setFloat64(offset, Number(value), true); // little-endian
      }
      break;
    case 'boolean':
      view.setUint8(offset, value ? 1 : 0);
      break;
  }
}

// Field offset mappings for different categories
const ITEM_FIELD_OFFSETS: Record<string, { offset: number; length: number; type: 'string' | 'number' | 'float' | 'boolean' }> = {
  name: { offset: 0x08, length: 0x40, type: 'string' },
  desc: { offset: 0x9C, length: 0x100, type: 'string' },
  level: { offset: 0x4C, length: 2, type: 'number' },
  jobLevel: { offset: 0x4E, length: 1, type: 'number' },
  sex: { offset: 0x4F, length: 1, type: 'number' },
  maxAtk: { offset: 0x54, length: 2, type: 'number' },
  minAtk: { offset: 0x56, length: 2, type: 'number' },
  def: { offset: 0x58, length: 2, type: 'number' },
  weight: { offset: 0x52, length: 2, type: 'number' },
  shield: { offset: 0x8E, length: 2, type: 'number' },
  gold: { offset: 0x64, length: 8, type: 'number' },
  recycleGold: { offset: 0x6C, length: 8, type: 'number' },
  nj: { offset: 0x60, length: 2, type: 'number' },
  el: { offset: 0x71, length: 2, type: 'number' },
  wx: { offset: 0x19C, length: 4, type: 'number' },
  wxjd: { offset: 0x1A0, length: 4, type: 'number' },
  qg_sll: { offset: 0x1C4, length: 4, type: 'number' },
  itemType: { offset: 0x250, length: 1, type: 'number' },
  setBonusDamage: { offset: 0x251, length: 1, type: 'number' },
  lock: { offset: 0x6F, length: 1, type: 'number' },
};

const SKILL_FIELD_OFFSETS: Record<string, { offset: number; length: number; type: 'string' | 'number' | 'float' | 'boolean' }> = {
  name: { offset: 0x04, length: 0x40, type: 'string' },
  desc: { offset: 0x68, length: 0x100, type: 'string' },
  job: { offset: 0x46, length: 1, type: 'number' },
  jobLevel: { offset: 0x4A, length: 1, type: 'number' },
  level: { offset: 0x48, length: 2, type: 'number' },
  mana: { offset: 0x50, length: 2, type: 'number' },
  atk: { offset: 0x52, length: 2, type: 'number' },
  sp: { offset: 0x4C, length: 4, type: 'number' },
  effect: { offset: 0x62, length: 2, type: 'number' },
  sp_upgrade: { offset: 0x16C, length: 4, type: 'number' },
  atkEachLevel: { offset: 0x170, length: 2, type: 'number' },
  sp_tt: { offset: 0x174, length: 1, type: 'number' },
};

const ABILITY_FIELD_OFFSETS: Record<string, { offset: number; length: number; type: 'string' | 'number' | 'float' | 'boolean' }> = {
  name: { offset: 0x04, length: 0x40, type: 'string' },
  desc: { offset: 0x694, length: 0x100, type: 'string' }, // O_6 offset
  job: { offset: 0x46, length: 1, type: 'number' },
  level: { offset: 0x48, length: 1, type: 'number' },
  jobLevel: { offset: 0x4A, length: 1, type: 'number' },
  o_1_perLevel: { offset: 0x4C, length: 4, type: 'float' },
  o_2_perLevel: { offset: 0x50, length: 4, type: 'float' },
  o_3_perLevel: { offset: 0x54, length: 4, type: 'float' },
  o_4_perLevel: { offset: 0x58, length: 4, type: 'float' },
  o_5_perLevel: { offset: 0x5C, length: 4, type: 'float' },
  o_1: { offset: 0x94, length: 0x100, type: 'string' },
  o_1_prefix: { offset: 0x194, length: 0x100, type: 'string' },
  o_2: { offset: 0x294, length: 0x100, type: 'string' },
  o_2_prefix: { offset: 0x394, length: 0x100, type: 'string' },
  o_3: { offset: 0x494, length: 0x100, type: 'string' },
  o_3_prefix: { offset: 0x594, length: 0x100, type: 'string' },
  o_4: { offset: 0x694, length: 0x100, type: 'string' },
  o_4_prefix: { offset: 0x794, length: 0x100, type: 'string' },
  o_5: { offset: 0x894, length: 0x100, type: 'string' },
  o_5_prefix: { offset: 0x994, length: 0x100, type: 'string' },
};

const NPC_FIELD_OFFSETS: Record<string, { offset: number; length: number; type: 'string' | 'number' | 'float' | 'boolean' }> = {
  name: { offset: 0x04, length: 0x40, type: 'string' },
  desc: { offset: 0x4C, length: 0x400, type: 'string' },
  desc2: { offset: 0x574, length: 0xC8, type: 'string' },
  desc3: { offset: 0x63C, length: 0xC8, type: 'string' },
  desc4: { offset: 0x704, length: 0xC8, type: 'string' },
  desc5: { offset: 0x7CC, length: 0xC8, type: 'string' },
  desc6: { offset: 0x894, length: 0xC8, type: 'string' },
  desc7: { offset: 0x95C, length: 0xC8, type: 'string' },
  desc8: { offset: 0xA24, length: 0xC8, type: 'string' },
  desc9: { offset: 0xAEC, length: 0xC8, type: 'string' },
  desc10: { offset: 0xBB4, length: 0xC8, type: 'string' },
  desc11: { offset: 0xC7C, length: 0xC8, type: 'string' },
  level: { offset: 0x46, length: 1, type: 'number' },
  hp: { offset: 0x48, length: 4, type: 'number' },
  menu1: { offset: 0x44C, length: 4, type: 'number' },
  menu2: { offset: 0x450, length: 4, type: 'number' },
  menu3: { offset: 0x454, length: 4, type: 'number' },
  menu4: { offset: 0x458, length: 4, type: 'number' },
};

const MAP_FIELD_OFFSETS: Record<string, { offset: number; length: number; type: 'string' | 'number' | 'float' | 'boolean' }> = {
  name: { offset: 0x04, length: 0x40, type: 'string' },
  x: { offset: 0x44, length: 4, type: 'float' },
  y: { offset: 0x4C, length: 4, type: 'float' },
  z: { offset: 0x48, length: 4, type: 'float' },
  x_2: { offset: 0x50, length: 4, type: 'float' },
  y_2: { offset: 0x58, length: 4, type: 'float' },
  z_2: { offset: 0x54, length: 4, type: 'float' },
  bgm1: { offset: 0x68, length: 0x80, type: 'string' },
  bgm2: { offset: 0xE8, length: 0x80, type: 'string' },
  u_168: { offset: 0x168, length: 0x80, type: 'string' },
  u_1e8: { offset: 0x1E8, length: 0x80, type: 'string' },
  u_268: { offset: 0x268, length: 0x80, type: 'string' },
};

async function migrateItemsField(view: DataView, source: YbiFile, target: YbiFile, field: string): Promise<number> {
  let migratedCount = 0;

  // Create lookup map from source items by ID
  const sourceItemsMap = new Map();
  source.items.forEach(item => {
    sourceItemsMap.set(item.id, item);
  });

  // Get field configuration
  const fieldConfig = ITEM_FIELD_OFFSETS[field];
  if (!fieldConfig) {
    console.warn(`Unknown item field: ${field}`);
    return 0;
  }

  // Migrate matching items in target
  for (const targetItem of target.items) {
    const sourceItem = sourceItemsMap.get(targetItem.id);
    if (sourceItem && sourceItem[field as keyof typeof sourceItem] !== undefined) {
      const sourceValue = sourceItem[field as keyof typeof sourceItem];

      // Write the field directly to the decrypted buffer at the correct offset
      if (fieldConfig.type === 'string') {
        writeStringToBuffer(view, targetItem.offset + fieldConfig.offset, String(sourceValue), fieldConfig.length);
      } else {
        writeFieldToBuffer(view, targetItem.offset + fieldConfig.offset, sourceValue, fieldConfig.type, fieldConfig.length);
      }
      migratedCount++;
    }
  }

  return migratedCount;
}

async function migrateSkillsField(view: DataView, source: YbiFile, target: YbiFile, field: string): Promise<number> {
  let migratedCount = 0;

  // Create lookup map from source skills by ID
  const sourceSkillsMap = new Map();
  source.skills.forEach(skill => {
    sourceSkillsMap.set(skill.id, skill);
  });

  // Get field configuration
  const fieldConfig = SKILL_FIELD_OFFSETS[field];
  if (!fieldConfig) {
    console.warn(`Unknown skill field: ${field}`);
    return 0;
  }

  // Migrate matching skills in target
  for (const targetSkill of target.skills) {
    const sourceSkill = sourceSkillsMap.get(targetSkill.id);
    if (sourceSkill && sourceSkill[field as keyof typeof sourceSkill] !== undefined) {
      const sourceValue = sourceSkill[field as keyof typeof sourceSkill];

      // Write the field directly to the decrypted buffer at the correct offset
      if (fieldConfig.type === 'string') {
        writeStringToBuffer(view, targetSkill.offset + fieldConfig.offset, String(sourceValue), fieldConfig.length);
      } else {
        writeFieldToBuffer(view, targetSkill.offset + fieldConfig.offset, sourceValue, fieldConfig.type, fieldConfig.length);
      }
      migratedCount++;
    }
  }

  return migratedCount;
}

async function migrateAbilitiesField(view: DataView, source: YbiFile, target: YbiFile, field: string): Promise<number> {
  let migratedCount = 0;

  // Create lookup map from source abilities by ID
  const sourceAbilitiesMap = new Map();
  source.abilities.forEach(ability => {
    sourceAbilitiesMap.set(ability.id, ability);
  });

  // Get field configuration
  const fieldConfig = ABILITY_FIELD_OFFSETS[field];
  if (!fieldConfig) {
    console.warn(`Unknown ability field: ${field}`);
    return 0;
  }

  // Migrate matching abilities in target
  for (const targetAbility of target.abilities) {
    const sourceAbility = sourceAbilitiesMap.get(targetAbility.id);
    if (sourceAbility && sourceAbility[field as keyof typeof sourceAbility] !== undefined) {
      const sourceValue = sourceAbility[field as keyof typeof sourceAbility];

      // Write the field directly to the decrypted buffer at the correct offset
      if (fieldConfig.type === 'string') {
        writeStringToBuffer(view, targetAbility.offset + fieldConfig.offset, String(sourceValue), fieldConfig.length);
      } else {
        writeFieldToBuffer(view, targetAbility.offset + fieldConfig.offset, sourceValue, fieldConfig.type, fieldConfig.length);
      }
      migratedCount++;
    }
  }

  return migratedCount;
}

async function migrateNpcInfosField(view: DataView, source: YbiFile, target: YbiFile, field: string): Promise<number> {
  let migratedCount = 0;

  // Create lookup map from source NPCs by ID
  const sourceNpcsMap = new Map();
  source.npcInfos.forEach(npc => {
    sourceNpcsMap.set(npc.id, npc);
  });

  // Get field configuration
  const fieldConfig = NPC_FIELD_OFFSETS[field];
  if (!fieldConfig) {
    console.warn(`Unknown NPC field: ${field}`);
    return 0;
  }

  // Migrate matching NPCs in target
  for (const targetNpc of target.npcInfos) {
    const sourceNpc = sourceNpcsMap.get(targetNpc.id);
    if (sourceNpc && sourceNpc[field as keyof typeof sourceNpc] !== undefined) {
      const sourceValue = sourceNpc[field as keyof typeof sourceNpc];

      // Write the field directly to the decrypted buffer at the correct offset
      if (fieldConfig.type === 'string') {
        writeStringToBuffer(view, targetNpc.offset + fieldConfig.offset, String(sourceValue), fieldConfig.length);
      } else {
        writeFieldToBuffer(view, targetNpc.offset + fieldConfig.offset, sourceValue, fieldConfig.type, fieldConfig.length);
      }
      migratedCount++;
    }
  }

  return migratedCount;
}

async function migrateMapInfosField(view: DataView, source: YbiFile, target: YbiFile, field: string): Promise<number> {
  let migratedCount = 0;

  // Create lookup map from source maps by ID
  const sourceMapsMap = new Map();
  source.mapInfos.forEach(map => {
    sourceMapsMap.set(map.id, map);
  });

  // Get field configuration
  const fieldConfig = MAP_FIELD_OFFSETS[field];
  if (!fieldConfig) {
    console.warn(`Unknown map field: ${field}`);
    return 0;
  }

  // Migrate matching maps in target
  for (const targetMap of target.mapInfos) {
    const sourceMap = sourceMapsMap.get(targetMap.id);
    if (sourceMap && sourceMap[field as keyof typeof sourceMap] !== undefined) {
      const sourceValue = sourceMap[field as keyof typeof sourceMap];

      // Write the field directly to the decrypted buffer at the correct offset
      if (fieldConfig.type === 'string') {
        writeStringToBuffer(view, targetMap.offset + fieldConfig.offset, String(sourceValue), fieldConfig.length);
      } else {
        writeFieldToBuffer(view, targetMap.offset + fieldConfig.offset, sourceValue, fieldConfig.type, fieldConfig.length);
      }
      migratedCount++;
    }
  }

  return migratedCount;
}