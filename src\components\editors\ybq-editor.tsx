'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  Download, 
  Search, 
  Plus, 
  Copy, 
  Trash2, 
  FileText,
  Database,
  Settings,
  Package,
  MapPin,
  Users
} from 'lucide-react';
import { toast } from 'sonner';

import { 
  YbqData, 
  Quest, 
  QuestStage, 
} from '@/types/ybq';
import { 
  parseYbqFile, 
  exportYbqData, 
  createNewQuest, 
  findNextAvailableQuestID, 
  cloneQuest,
  newLenData,
  newStringData
} from '@/lib/ybq-parser';

interface QuestListItem {
  id: number;
  name: string;
  level: number;
  stageCount: number;
  rewardCount: number;
}

export default function YbqEditor() {
  const [ybqData, setYbqData] = useState<YbqData | null>(null);
  const [selectedQuest, setSelectedQuest] = useState<Quest | null>(null);
  const [selectedStageIndex, setSelectedStageIndex] = useState<number>(-1);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get quest list for display
  const getQuestList = useCallback((): QuestListItem[] => {
    if (!ybqData) return [];

    return Object.values(ybqData.quests)
      .filter(quest => quest.questID.value > 0)
      .map(quest => ({
        id: quest.questID.value,
        name: quest.questName.value || `Quest ${quest.questID.value}`,
        level: quest.questLevel.value,
        stageCount: quest.questStages.length,
        rewardCount: quest.rewardItems.length
      }))
      .filter(item =>
        searchTerm === '' ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.id.toString().includes(searchTerm)
      )
      .sort((a, b) => a.id - b.id);
  }, [ybqData, searchTerm]);

  // Handle file upload
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    try {
      const buffer = await file.arrayBuffer();
      const parsedData = parseYbqFile(buffer);
      setYbqData(parsedData);
      setSelectedQuest(null);
      setSelectedStageIndex(-1);

      const questCount = Object.keys(parsedData.quests).length;
      const totalQuests = parsedData.totalQuest;

      if (questCount === totalQuests) {
        toast.success(
          `✅ Đã tải thành công file ${file.name}\n` +
          `Parsed: ${questCount}/${totalQuests} quests (100%)\n` +
          `File size: ${(buffer.byteLength / 1024).toFixed(1)} KB`
        );
      } else {
        toast.warning(
          `⚠️ Tải file ${file.name} với một số vấn đề\n` +
          `Parsed: ${questCount}/${totalQuests} quests (${((questCount/totalQuests)*100).toFixed(1)}%)\n` +
          `File size: ${(buffer.byteLength / 1024).toFixed(1)} KB\n` +
          `Kiểm tra console để xem chi tiết`
        );
      }

      console.log(`YBQ File loaded successfully:`, {
        fileName: file.name,
        fileSize: buffer.byteLength,
        sign: parsedData.sign,
        signEx: parsedData.signEx,
        totalQuests: parsedData.totalQuest,
        parsedQuests: questCount,
        encryptedSize: parsedData.encrypted.length,
        decryptedSize: parsedData.decrypted.length
      });

    } catch (error) {
      console.error('Error parsing YBQ file:', error);
      toast.error('Lỗi khi đọc file YBQ: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle file export
  const handleExport = useCallback((format: 'ybq' | 'json' | 'decrypt') => {
    if (!ybqData) {
      toast.error('Chưa có dữ liệu để xuất');
      return;
    }

    try {
      const exportedData = exportYbqData(ybqData, format);
      const blob = new Blob([exportedData], {
        type: format === 'json' ? 'application/json' : 'application/octet-stream'
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const extension = format === 'ybq' ? 'cfg' : format;
      a.download = `ybq_${format}_${timestamp}.${extension}`;

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      const fileSize = (exportedData.length / 1024).toFixed(1);
      toast.success(
        `Đã xuất file ${format.toUpperCase()} thành công\n` +
        `Kích thước: ${fileSize} KB\n` +
        `Quests: ${Object.keys(ybqData.quests).length}`
      );

      console.log(`Exported ${format} file:`, {
        format,
        size: exportedData.length,
        questCount: Object.keys(ybqData.quests).length,
        filename: a.download
      });

    } catch (error) {
      console.error('Error exporting file:', error);
      toast.error('Lỗi khi xuất file: ' + (error as Error).message);
    }
  }, [ybqData]);

  // Handle quest selection
  const handleQuestSelect = useCallback((questId: number) => {
    if (!ybqData) return;
    
    const quest = ybqData.quests[questId];
    if (quest) {
      setSelectedQuest(quest);
      setSelectedStageIndex(-1);
    }
  }, [ybqData]);

  // Handle quest creation
  const handleCreateQuest = useCallback(() => {
    if (!ybqData) {
      toast.error('Vui lòng mở file YBQ trước khi tạo quest mới');
      return;
    }

    const newQuestID = findNextAvailableQuestID(ybqData.quests);
    const newQuest = createNewQuest(newQuestID);
    
    setYbqData(prev => ({
      ...prev!,
      quests: {
        ...prev!.quests,
        [newQuestID]: newQuest
      }
    }));
    
    setSelectedQuest(newQuest);
    toast.success(`Đã tạo quest mới với ID: ${newQuestID}`);
  }, [ybqData]);

  // Handle quest cloning
  const handleCloneQuest = useCallback(() => {
    if (!ybqData || !selectedQuest) {
      toast.error('Vui lòng chọn một quest để clone');
      return;
    }

    const newQuestID = findNextAvailableQuestID(ybqData.quests);
    const clonedQuest = cloneQuest(selectedQuest, newQuestID);
    
    setYbqData(prev => ({
      ...prev!,
      quests: {
        ...prev!.quests,
        [newQuestID]: clonedQuest
      }
    }));
    
    setSelectedQuest(clonedQuest);
    toast.success(`Đã clone quest thành công với ID: ${newQuestID}`);
  }, [ybqData, selectedQuest]);

  // Handle quest deletion
  const handleDeleteQuest = useCallback(() => {
    if (!ybqData || !selectedQuest) {
      toast.error('Vui lòng chọn một quest để xóa');
      return;
    }

    const questId = selectedQuest.questID.value;
    const questName = selectedQuest.questName.value;
    
    if (confirm(`Bạn có chắc chắn muốn xóa quest ID: ${questId}, Tên: ${questName}?`)) {
      const newQuests = { ...ybqData.quests };
      delete newQuests[questId];
      
      setYbqData(prev => ({
        ...prev!,
        quests: newQuests
      }));
      
      setSelectedQuest(null);
      setSelectedStageIndex(-1);
      toast.success(`Đã xóa quest ID: ${questId}`);
    }
  }, [ybqData, selectedQuest]);

  // Update quest field
  const updateQuestField = useCallback((field: keyof Quest, value: any) => {
    if (!selectedQuest || !ybqData) return;

    try {
      const updatedQuest = { ...selectedQuest };

      if (field === 'questName') {
        updatedQuest.questName = newStringData(value);
      } else if (field === 'questLevel') {
        const level = parseInt(value) || 0;
        if (level < 0 || level > 999) {
          toast.error('Level phải từ 0 đến 999');
          return;
        }
        updatedQuest.questLevel = newLenData(level);
      } else if (typeof updatedQuest[field] === 'object' && 'value' in (updatedQuest[field] as any)) {
        const numValue = parseInt(value) || 0;
        if (numValue < 0) {
          toast.error('Giá trị không được âm');
          return;
        }
        (updatedQuest[field] as any) = newLenData(numValue);
      } else if (typeof updatedQuest[field] === 'object' && 'value' in (updatedQuest[field] as any)) {
        (updatedQuest[field] as any) = newStringData(value);
      }

      setSelectedQuest(updatedQuest);
      setYbqData(prev => ({
        ...prev!,
        quests: {
          ...prev!.quests,
          [updatedQuest.questID.value]: updatedQuest
        }
      }));

    } catch (error) {
      console.error('Error updating quest field:', error);
      toast.error('Lỗi khi cập nhật quest: ' + (error as Error).message);
    }
  }, [selectedQuest, ybqData]);

  const questList = getQuestList();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">YBQ Editor</h2>
          <p className="text-muted-foreground">Chỉnh sửa file ybq.cfg (quest data)</p>
        </div>
        <div className="flex items-center gap-2">
          {ybqData ? (
            <>
              <Badge variant="default">
                {Object.keys(ybqData.quests).length}/{ybqData.totalQuest} quests
              </Badge>
              <Badge variant="outline">
                {ybqData.sign} {ybqData.signEx}
              </Badge>
              <Badge variant="secondary">
                {(ybqData.encrypted.length / 1024).toFixed(1)} KB
              </Badge>
            </>
          ) : (
            <Badge variant="secondary">Chưa tải file</Badge>
          )}
        </div>
      </div>

      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Điều khiển
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-2">
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Mở file YBQ
            </Button>
            
            <Button
              onClick={() => handleExport('ybq')}
              disabled={!ybqData}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Lưu YBQ
            </Button>
            
            <Button
              onClick={() => handleExport('decrypt')}
              disabled={!ybqData}
              variant="outline"
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              Lưu Decrypt
            </Button>
            
            <Button
              onClick={() => handleExport('json')}
              disabled={!ybqData}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              Xuất JSON
            </Button>
            
            <Separator orientation="vertical" className="h-6" />
            
            <Button
              onClick={handleCreateQuest}
              disabled={!ybqData}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Thêm Quest
            </Button>
            
            <Button
              onClick={handleCloneQuest}
              disabled={!selectedQuest}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              Clone Quest
            </Button>
            
            <Button
              onClick={handleDeleteQuest}
              disabled={!selectedQuest}
              variant="destructive"
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Xóa Quest
            </Button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept=".cfg"
            onChange={handleFileUpload}
            className="hidden"
          />
        </CardContent>
      </Card>

      {!ybqData ? (
        <Alert>
          <Upload className="h-4 w-4" />
          <AlertDescription>
            Vui lòng tải file ybq.cfg để bắt đầu chỉnh sửa quest data.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="grid grid-cols-12 gap-6">
          {/* Quest List */}
          <div className="col-span-4">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Danh sách Quest
                </CardTitle>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm theo ID hoặc tên..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <ScrollArea className="h-[calc(60vh)]">
                  <div className="space-y-1 p-4">
                    {questList.map((quest) => (
                      <div
                        key={quest.id}
                        onClick={() => handleQuestSelect(quest.id)}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedQuest?.questID.value === quest.id
                            ? 'bg-primary/10 border-primary'
                            : 'hover:bg-muted/50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {quest.id}
                            </Badge>
                            {ybqData.quests[quest.id]?.unknown10.value === 16 && (
                              <Badge variant="destructive" className="text-xs">
                                Special
                              </Badge>
                            )}
                            <span className="font-medium text-sm">
                              {quest.name || 'Unnamed Quest'}
                            </span>
                          </div>
                          <Badge variant="secondary" className="text-xs">
                            Lv.{quest.level}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {quest.stageCount} stages
                          </span>
                          <span className="flex items-center gap-1">
                            <Package className="h-3 w-3" />
                            {quest.rewardCount} rewards
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Quest Details */}
          <div className="col-span-8">
            {selectedQuest ? (
              <QuestDetailsPanel 
                quest={selectedQuest}
                onUpdateQuest={updateQuestField}
                selectedStageIndex={selectedStageIndex}
                onStageSelect={setSelectedStageIndex}
              />
            ) : (
              <Card className="h-[600px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Chọn một quest từ danh sách để xem chi tiết</p>
                </div>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Quest Details Panel Component
function QuestDetailsPanel({
  quest,
  onUpdateQuest,
  selectedStageIndex,
  onStageSelect
}: {
  quest: Quest;
  onUpdateQuest: (field: keyof Quest, value: any) => void;
  selectedStageIndex: number;
  onStageSelect: (index: number) => void;
}) {
  return (
    <Card className="h-[calc(100vh-180px)]">
      <CardHeader>
        <CardTitle>Chi tiết Quest #{quest.questID.value} - {quest.questName.value} - {quest.offset} - 0x{quest?.offset?.toString(16).toUpperCase()}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="p-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Thông tin cơ bản</TabsTrigger>
                <TabsTrigger value="rewards">Phần thưởng</TabsTrigger>
                <TabsTrigger value="stages">Giai đoạn</TabsTrigger>
                <TabsTrigger value="advanced">Nâng cao</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <BasicInfoTab quest={quest} onUpdateQuest={onUpdateQuest} />
              </TabsContent>

              <TabsContent value="rewards" className="space-y-4">
                <RewardsTab quest={quest} onUpdateQuest={onUpdateQuest} />
              </TabsContent>

              <TabsContent value="stages" className="space-y-4">
                <StagesTab
                  quest={quest}
                  onUpdateQuest={onUpdateQuest}
                  selectedStageIndex={selectedStageIndex}
                  onStageSelect={onStageSelect}
                />
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <AdvancedTab quest={quest} onUpdateQuest={onUpdateQuest} />
              </TabsContent>
            </Tabs>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

// Basic Info Tab
function BasicInfoTab({ quest, onUpdateQuest }: {
  quest: Quest;
  onUpdateQuest: (field: keyof Quest, value: any) => void;
}) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="questName">Tên Quest</Label>
          <Input
            id="questName"
            value={quest.questName.value}
            onChange={(e) => onUpdateQuest('questName', e.target.value)}
            placeholder="Nhập tên quest"
          />
        </div>
        <div>
          <Label htmlFor="questLevel">Cấp độ</Label>
          <Input
            id="questLevel"
            type="number"
            value={quest.questLevel.value}
            onChange={(e) => onUpdateQuest('questLevel', e.target.value)}
            placeholder="Nhập cấp độ"
          />
        </div>
      </div>

      <Separator />

      <div>
        <h4 className="font-medium mb-3 flex items-center gap-2">
          <Users className="h-4 w-4" />
          Thông tin NPC
        </h4>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <Label htmlFor="npcID">NPC ID</Label>
            <Input
              id="npcID"
              type="number"
              value={quest.npcID.value}
              onChange={(e) => onUpdateQuest('npcID', e.target.value)}
              placeholder="NPC ID"
            />
          </div>
          <div>
            <Label htmlFor="npcUnknown1">NPC Unknown1</Label>
            <Input
              id="npcUnknown1"
              type="number"
              value={quest.npcUnknown1.value}
              onChange={(e) => onUpdateQuest('npcUnknown1', e.target.value)}
              placeholder="Unknown1"
            />
          </div>
          <div>
            <Label htmlFor="questStageNumber">Số giai đoạn</Label>
            <Input
              id="questStageNumber"
              type="number"
              value={quest.questStageNumber.value}
              onChange={(e) => onUpdateQuest('questStageNumber', e.target.value)}
              placeholder="Số giai đoạn"
            />
          </div>
        </div>

        <div className="grid grid-cols-4 gap-4 mt-4">
          <div>
            <Label htmlFor="npcMapID">Map ID</Label>
            <Input
              id="npcMapID"
              type="number"
              value={quest.npcCoords.mapID.value}
              onChange={(e) => {
                const newCoords = { ...quest.npcCoords };
                newCoords.mapID = newLenData(parseInt(e.target.value) || 0);
                onUpdateQuest('npcCoords', newCoords);
              }}
              placeholder="Map ID"
            />
          </div>
          <div>
            <Label htmlFor="npcCoordsX">Tọa độ X</Label>
            <Input
              id="npcCoordsX"
              type="number"
              value={quest.npcCoords.coordsX.value}
              onChange={(e) => {
                const newCoords = { ...quest.npcCoords };
                newCoords.coordsX = newLenData(parseInt(e.target.value) || 0);
                onUpdateQuest('npcCoords', newCoords);
              }}
              placeholder="X"
            />
          </div>
          <div>
            <Label htmlFor="npcCoordsY">Tọa độ Y</Label>
            <Input
              id="npcCoordsY"
              type="number"
              value={quest.npcCoords.coordsY.value}
              onChange={(e) => {
                const newCoords = { ...quest.npcCoords };
                newCoords.coordsY = newLenData(parseInt(e.target.value) || 0);
                onUpdateQuest('npcCoords', newCoords);
              }}
              placeholder="Y"
            />
          </div>
          <div>
            <Label htmlFor="npcCoordsZ">Tọa độ Z</Label>
            <Input
              id="npcCoordsZ"
              type="number"
              value={quest.npcCoords.coordsZ.value}
              onChange={(e) => {
                const newCoords = { ...quest.npcCoords };
                newCoords.coordsZ = newLenData(parseInt(e.target.value) || 0);
                onUpdateQuest('npcCoords', newCoords);
              }}
              placeholder="Z"
            />
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <h4 className="font-medium mb-3">Đối thoại Quest</h4>
        <div className="space-y-3">
          <div>
            <Label htmlFor="questAccept1">Lời thoại chấp nhận 1</Label>
            <Textarea
              id="questAccept1"
              value={quest.questAccept1.value}
              onChange={(e) => onUpdateQuest('questAccept1', e.target.value)}
              placeholder="Nhập lời thoại chấp nhận 1"
              rows={2}
            />
          </div>
          <div>
            <Label htmlFor="questAccept2">Lời thoại chấp nhận 2</Label>
            <Textarea
              id="questAccept2"
              value={quest.questAccept2.value}
              onChange={(e) => onUpdateQuest('questAccept2', e.target.value)}
              placeholder="Nhập lời thoại chấp nhận 2"
              rows={2}
            />
          </div>
          <div>
            <Label htmlFor="questRefuse1">Lời thoại từ chối 1</Label>
            <Textarea
              id="questRefuse1"
              value={quest.questRefuse1.value}
              onChange={(e) => onUpdateQuest('questRefuse1', e.target.value)}
              placeholder="Nhập lời thoại từ chối 1"
              rows={2}
            />
          </div>
          <div>
            <Label htmlFor="questRefuse2">Lời thoại từ chối 2</Label>
            <Textarea
              id="questRefuse2"
              value={quest.questRefuse2.value}
              onChange={(e) => onUpdateQuest('questRefuse2', e.target.value)}
              placeholder="Nhập lời thoại từ chối 2"
              rows={2}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Rewards Tab
function RewardsTab({ quest, onUpdateQuest }: {
  quest: Quest;
  onUpdateQuest: (field: keyof Quest, value: any) => void;
}) {
  const addRewardItem = () => {
    const newRewards = [...quest.rewardItems, {
      itemID: newLenData(0),
      itemAmount: newLenData(1)
    }];
    onUpdateQuest('rewardItems', newRewards);
  };

  const removeRewardItem = (index: number) => {
    const newRewards = quest.rewardItems.filter((_, i) => i !== index);
    onUpdateQuest('rewardItems', newRewards);
  };

  const updateRewardItem = (index: number, field: 'itemID' | 'itemAmount', value: number) => {
    const newRewards = [...quest.rewardItems];
    newRewards[index] = {
      ...newRewards[index],
      [field]: newLenData(value)
    };
    onUpdateQuest('rewardItems', newRewards);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium flex items-center gap-2">
          <Package className="h-4 w-4" />
          Phần thưởng ({quest.rewardItems.length} items)
        </h4>
        <Button onClick={addRewardItem} size="sm" className="flex items-center gap-2">
          <Plus className="h-3 w-3" />
          Thêm phần thưởng
        </Button>
      </div>

      <div className="space-y-3">
        {quest.rewardItems.map((item, index) => (
          <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
            <Badge variant="outline" className="text-xs">#{index + 1}</Badge>
            <div className="flex-1 grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor={`reward-id-${index}`} className="text-xs">Item ID</Label>
                <Input
                  id={`reward-id-${index}`}
                  type="number"
                  value={item.itemID.value}
                  onChange={(e) => updateRewardItem(index, 'itemID', parseInt(e.target.value) || 0)}
                  placeholder="Item ID"
                />
              </div>
              <div>
                <Label htmlFor={`reward-amount-${index}`} className="text-xs">Số lượng</Label>
                <Input
                  id={`reward-amount-${index}`}
                  type="number"
                  value={item.itemAmount.value}
                  onChange={(e) => updateRewardItem(index, 'itemAmount', parseInt(e.target.value) || 0)}
                  placeholder="Số lượng"
                />
              </div>
            </div>
            <Button
              onClick={() => removeRewardItem(index)}
              size="sm"
              variant="destructive"
              className="flex items-center gap-1"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        ))}

        {quest.rewardItems.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Chưa có phần thưởng nào</p>
            <p className="text-sm">Nhấn Thêm phần thưởng để bắt đầu</p>
          </div>
        )}
      </div>

      <Separator />

      <div>
        <h4 className="font-medium mb-3 flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          Vật phẩm yêu cầu ({quest.requiredItems.length} items)
        </h4>

        <div className="flex items-center justify-between mb-3">
          <span className="text-sm text-muted-foreground">
            Các vật phẩm cần thiết để hoàn thành quest
          </span>
          <Button
            onClick={() => {
              const newRequiredItems = [...quest.requiredItems, {
                itemID: newLenData(0),
                itemAmount: newLenData(1),
                mapID: newLenData(0),
                coordsX: newLenData(0),
                coordsY: newLenData(0),
                coordsZ: newLenData(0)
              }];
              onUpdateQuest('requiredItems', newRequiredItems);
            }}
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="h-3 w-3" />
            Thêm vật phẩm
          </Button>
        </div>

        <div className="space-y-3">
          {quest.requiredItems.map((item, index) => (
            <div key={index} className="p-3 border rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <Badge variant="outline" className="text-xs">Vật phẩm #{index + 1}</Badge>
                <Button
                  onClick={() => {
                    const newRequiredItems = quest.requiredItems.filter((_, i) => i !== index);
                    onUpdateQuest('requiredItems', newRequiredItems);
                  }}
                  size="sm"
                  variant="destructive"
                  className="flex items-center gap-1"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-3">
                <div>
                  <Label className="text-xs">Item ID</Label>
                  <Input
                    type="number"
                    value={item.itemID.value}
                    onChange={(e) => {
                      const newRequiredItems = [...quest.requiredItems];
                      newRequiredItems[index] = {
                        ...newRequiredItems[index],
                        itemID: newLenData(parseInt(e.target.value) || 0)
                      };
                      onUpdateQuest('requiredItems', newRequiredItems);
                    }}
                    placeholder="Item ID"
                  />
                </div>
                <div>
                  <Label className="text-xs">Số lượng</Label>
                  <Input
                    type="number"
                    value={item.itemAmount.value}
                    onChange={(e) => {
                      const newRequiredItems = [...quest.requiredItems];
                      newRequiredItems[index] = {
                        ...newRequiredItems[index],
                        itemAmount: newLenData(parseInt(e.target.value) || 0)
                      };
                      onUpdateQuest('requiredItems', newRequiredItems);
                    }}
                    placeholder="Số lượng"
                  />
                </div>
              </div>

              <div className="grid grid-cols-4 gap-3">
                <div>
                  <Label className="text-xs">Map ID</Label>
                  <Input
                    type="number"
                    value={item.mapID.value}
                    onChange={(e) => {
                      const newRequiredItems = [...quest.requiredItems];
                      newRequiredItems[index] = {
                        ...newRequiredItems[index],
                        mapID: newLenData(parseInt(e.target.value) || 0)
                      };
                      onUpdateQuest('requiredItems', newRequiredItems);
                    }}
                    placeholder="Map"
                  />
                </div>
                <div>
                  <Label className="text-xs">X</Label>
                  <Input
                    type="number"
                    value={item.coordsX.value}
                    onChange={(e) => {
                      const newRequiredItems = [...quest.requiredItems];
                      newRequiredItems[index] = {
                        ...newRequiredItems[index],
                        coordsX: newLenData(parseInt(e.target.value) || 0)
                      };
                      onUpdateQuest('requiredItems', newRequiredItems);
                    }}
                    placeholder="X"
                  />
                </div>
                <div>
                  <Label className="text-xs">Y</Label>
                  <Input
                    type="number"
                    value={item.coordsY.value}
                    onChange={(e) => {
                      const newRequiredItems = [...quest.requiredItems];
                      newRequiredItems[index] = {
                        ...newRequiredItems[index],
                        coordsY: newLenData(parseInt(e.target.value) || 0)
                      };
                      onUpdateQuest('requiredItems', newRequiredItems);
                    }}
                    placeholder="Y"
                  />
                </div>
                <div>
                  <Label className="text-xs">Z</Label>
                  <Input
                    type="number"
                    value={item.coordsZ.value}
                    onChange={(e) => {
                      const newRequiredItems = [...quest.requiredItems];
                      newRequiredItems[index] = {
                        ...newRequiredItems[index],
                        coordsZ: newLenData(parseInt(e.target.value) || 0)
                      };
                      onUpdateQuest('requiredItems', newRequiredItems);
                    }}
                    placeholder="Z"
                  />
                </div>
              </div>
            </div>
          ))}

          {quest.requiredItems.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Chưa có vật phẩm yêu cầu nào</p>
              <p className="text-sm">Nhấn Thêm vật phẩm để bắt đầu</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Stages Tab
function StagesTab({ quest, onUpdateQuest, selectedStageIndex, onStageSelect }: {
  quest: Quest;
  onUpdateQuest: (field: keyof Quest, value: any) => void;
  selectedStageIndex: number;
  onStageSelect: (index: number) => void;
}) {
  const addStage = () => {
    const newStage: QuestStage = {
      content: newStringData(''),
      npcID: newLenData(0),
      npcUnknown1: newLenData(0),
      npcMapID: newLenData(0),
      npcCoordsX: newLenData(0),
      npcCoordsY: newLenData(0),
      npcCoordsZ: newLenData(0),
      requiredItems: [],
      conditionMatchPrompt1: newStringData(''),
      conditionMatchPrompt2: newStringData(''),
      conditionMatchPrompt3: newStringData(''),
      conditionMatchPrompt4: newStringData(''),
      conditionMatchPrompt5: newStringData(''),
      conditionNoMatchPrompt1: newStringData(''),
      conditionNoMatchPrompt2: newStringData(''),
      conditionNoMatchPrompt3: newStringData(''),
      conditionNoMatchPrompt4: newStringData(''),
      conditionNoMatchPrompt5: newStringData('')
    };

    const newStages = [...quest.questStages, newStage];
    onUpdateQuest('questStages', newStages);
    onUpdateQuest('questStageNumber', newStages.length + 1);
  };

  const removeStage = (index: number) => {
    const newStages = quest.questStages.filter((_, i) => i !== index);
    onUpdateQuest('questStages', newStages);
    onUpdateQuest('questStageNumber', newStages.length + 1);
    if (selectedStageIndex === index) {
      onStageSelect(-1);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          Giai đoạn Quest ({quest.questStages.length} giai đoạn)
        </h4>
        <Button onClick={addStage} size="sm" className="flex items-center gap-2">
          <Plus className="h-3 w-3" />
          Thêm giai đoạn
        </Button>
      </div>

      <div className="grid grid-cols-5 gap-4">
        {/* Stage List */}
        <div className="col-span-2">
          <div className="space-y-2">
            {quest.questStages.map((stage, index) => (
              <div
                key={index}
                onClick={() => onStageSelect(index)}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedStageIndex === index
                    ? 'bg-primary/10 border-primary'
                    : 'hover:bg-muted/50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="text-xs">
                    Giai đoạn {index + 1}
                  </Badge>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeStage(index);
                    }}
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1 truncate">
                  {stage.content.value || 'Chưa có nội dung'}
                </p>
              </div>
            ))}

            {quest.questStages.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Chưa có giai đoạn nào</p>
              </div>
            )}
          </div>
        </div>

        {/* Stage Details */}
        <div className="col-span-3">
          {selectedStageIndex >= 0 && selectedStageIndex < quest.questStages.length ? (
            <StageDetailsPanel
              stage={quest.questStages[selectedStageIndex]}
              stageIndex={selectedStageIndex}
              onUpdateStage={(updatedStage) => {
                const newStages = [...quest.questStages];
                newStages[selectedStageIndex] = updatedStage;
                onUpdateQuest('questStages', newStages);
              }}
            />
          ) : (
            <div className="border rounded-lg p-8 text-center text-muted-foreground">
              <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Chọn một giai đoạn để xem chi tiết</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Stage Details Panel
function StageDetailsPanel({ stage, stageIndex, onUpdateStage }: {
  stage: QuestStage;
  stageIndex: number;
  onUpdateStage: (stage: QuestStage) => void;
}) {
  const updateStageField = (field: keyof QuestStage, value: any) => {
    const updatedStage = { ...stage };

    if (field === 'content') {
      updatedStage.content = newStringData(value);
    } else if (typeof updatedStage[field] === 'object' && 'value' in (updatedStage[field] as any)) {
      if (typeof value === 'string') {
        (updatedStage[field] as any) = newStringData(value);
      } else {
        (updatedStage[field] as any) = newLenData(parseInt(value) || 0);
      }
    }

    onUpdateStage(updatedStage);
  };

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <h5 className="font-medium">Chi tiết Giai đoạn {stageIndex + 1}</h5>

      <div>
        <Label htmlFor={`stage-content-${stageIndex}`}>Nội dung giai đoạn</Label>
        <Textarea
          id={`stage-content-${stageIndex}`}
          value={stage.content.value}
          onChange={(e) => updateStageField('content', e.target.value)}
          placeholder="Nhập nội dung giai đoạn"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-3 gap-3">
        <div>
          <Label className="text-xs">NPC ID</Label>
          <Input
            type="number"
            value={stage.npcID.value}
            onChange={(e) => updateStageField('npcID', e.target.value)}
            placeholder="NPC ID"
          />
        </div>
        <div>
          <Label className="text-xs">NPC Unknown1</Label>
          <Input
            type="number"
            value={stage.npcUnknown1.value}
            onChange={(e) => updateStageField('npcUnknown1', e.target.value)}
            placeholder="Unknown1"
          />
        </div>
        <div>
          <Label className="text-xs">Map ID</Label>
          <Input
            type="number"
            value={stage.npcMapID.value}
            onChange={(e) => updateStageField('npcMapID', e.target.value)}
            placeholder="Map ID"
          />
        </div>
      </div>

      <div className="grid grid-cols-3 gap-3">
        <div>
          <Label className="text-xs">Tọa độ X</Label>
          <Input
            type="number"
            value={stage.npcCoordsX.value}
            onChange={(e) => updateStageField('npcCoordsX', e.target.value)}
            placeholder="X"
          />
        </div>
        <div>
          <Label className="text-xs">Tọa độ Y</Label>
          <Input
            type="number"
            value={stage.npcCoordsY.value}
            onChange={(e) => updateStageField('npcCoordsY', e.target.value)}
            placeholder="Y"
          />
        </div>
        <div>
          <Label className="text-xs">Tọa độ Z</Label>
          <Input
            type="number"
            value={stage.npcCoordsZ.value}
            onChange={(e) => updateStageField('npcCoordsZ', e.target.value)}
            placeholder="Z"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-xs">Thông báo điều kiện phù hợp</Label>
        {[1, 2, 3, 4, 5].map(num => (
          <Textarea
            key={num}
            value={(stage as any)[`conditionMatchPrompt${num}`].value}
            onChange={(e) => updateStageField(`conditionMatchPrompt${num}` as keyof QuestStage, e.target.value)}
            placeholder={`Thông báo điều kiện phù hợp ${num}`}
            rows={1}
            className="text-xs"
          />
        ))}
      </div>

      <div className="space-y-2">
        <Label className="text-xs">Thông báo điều kiện không phù hợp</Label>
        {[1, 2, 3, 4, 5].map(num => (
          <Textarea
            key={num}
            value={(stage as any)[`conditionNoMatchPrompt${num}`].value}
            onChange={(e) => updateStageField(`conditionNoMatchPrompt${num}` as keyof QuestStage, e.target.value)}
            placeholder={`Thông báo điều kiện không phù hợp ${num}`}
            rows={1}
            className="text-xs"
          />
        ))}
      </div>
    </div>
  );
}

// Advanced Tab
function AdvancedTab({ quest, onUpdateQuest }: {
  quest: Quest;
  onUpdateQuest: (field: keyof Quest, value: any) => void;
}) {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-medium mb-3 flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Thông tin Unknown
        </h4>
        <div className="grid grid-cols-4 gap-3">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map(num => (
            <div key={num}>
              <Label className="text-xs">Unknown{num}</Label>
              <Input
                type="number"
                value={(quest as any)[`unknown${num}`].value}
                onChange={(e) => onUpdateQuest(`unknown${num}` as keyof Quest, e.target.value)}
                placeholder={`Unknown${num}`}
              />
            </div>
          ))}
        </div>
      </div>

      <Separator />

      <div>
        <h4 className="font-medium mb-3">Lời chào chấp nhận</h4>
        <div className="space-y-3">
          {[1, 2, 3, 4, 5].map(num => (
            <div key={num}>
              <Label className="text-xs">Lời chào chấp nhận {num}</Label>
              <Textarea
                value={(quest as any)[`welcomeAcceptPrompt${num}`].value}
                onChange={(e) => onUpdateQuest(`welcomeAcceptPrompt${num}` as keyof Quest, e.target.value)}
                placeholder={`Nhập lời chào chấp nhận ${num}`}
                rows={2}
                className="text-sm"
              />
            </div>
          ))}
        </div>
      </div>

      <Separator />

      <div>
        <h4 className="font-medium mb-3">Lời chào từ chối</h4>
        <div className="space-y-3">
          {[1, 2, 3, 4, 5].map(num => (
            <div key={num}>
              <Label className="text-xs">Lời chào từ chối {num}</Label>
              <Textarea
                value={(quest as any)[`welcomeRefusePrompt${num}`].value}
                onChange={(e) => onUpdateQuest(`welcomeRefusePrompt${num}` as keyof Quest, e.target.value)}
                placeholder={`Nhập lời chào từ chối ${num}`}
                rows={2}
                className="text-sm"
              />
            </div>
          ))}
        </div>
      </div>

      <Separator />

      <div>
        <h4 className="font-medium mb-3">Thông tin mở rộng</h4>
        <div>
          <Label htmlFor="footerExtend">Footer Extend</Label>
          <Textarea
            id="footerExtend"
            value={quest.footerExtend.value}
            onChange={(e) => onUpdateQuest('footerExtend', e.target.value)}
            placeholder="Nhập thông tin mở rộng"
            rows={3}
          />
        </div>
      </div>
    </div>
  );
}
