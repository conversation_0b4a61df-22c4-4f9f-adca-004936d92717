'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { GameServer } from '@/types/gameserver';
import { apiClientService } from '@/services/api-client.service';

interface ConfigManagementProps {
  server: GameServer;
}

export function ConfigManagement({ server }: ConfigManagementProps) {
  const [reloadLoading, setReloadLoading] = useState(false);
  const [selectedConfigType, setSelectedConfigType] = useState<'all' | 'config' | 'template'>('all');

  const handleReloadConfig = async () => {
    try {
      setReloadLoading(true);
      
      const response = await apiClientService.reloadConfig({
        serverId: server.id,
        clusterId: server.clusterId,
        configType: selectedConfigType
      });

      if (response.success) {
        toast.success(`Config reloaded successfully`);
      } else {
        toast.error(`Config reload failed: ${response.message}`);
      }
    } catch (error) {
      console.error('Error reloading config:', error);
      toast.error('Config reload failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setReloadLoading(false);
    }
  };

  const isOnline = server.status;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuration Management</CardTitle>
        <CardDescription>
          Reload server configurations without restart
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Config Reload</h4>
          <div className="flex gap-2">
            <Select
              value={selectedConfigType}
              onValueChange={(value: 'all' | 'config' | 'template') => setSelectedConfigType(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select config type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Configs</SelectItem>
                <SelectItem value="config">Config</SelectItem>
                <SelectItem value="template">Template</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              size="sm"
              variant="outline"
              onClick={handleReloadConfig}
              disabled={reloadLoading || !isOnline}
            >
              {reloadLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Reload Config
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="text-sm font-medium">Quick Actions</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setSelectedConfigType('config');
                handleReloadConfig();
              }}
              disabled={reloadLoading || !isOnline}
            >
              <Settings className="h-4 w-4 mr-2" />
              Game Settings
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setSelectedConfigType('template');
                handleReloadConfig();
              }}
              disabled={reloadLoading || !isOnline}
            >
              <Settings className="h-4 w-4 mr-2" />
              Template Settings
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setSelectedConfigType('all');
                handleReloadConfig();
              }}
              disabled={reloadLoading || !isOnline}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reload All
            </Button>
          </div>
        </div>

        {!isOnline && (
          <div className="text-sm text-muted-foreground bg-muted p-3 rounded">
            Server must be online to reload configurations.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
