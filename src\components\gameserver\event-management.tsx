'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Calendar, Play, Square, Pause, RotateCcw, Users } from 'lucide-react';
import { toast } from 'sonner';
import { GameServer, ServerEventInfo } from '@/types/gameserver';
import { apiClientService } from '@/services/api-client.service';

interface EventManagementProps {
  server: GameServer;
  events: ServerEventInfo[];
  loading: boolean;
  onEventsUpdate: () => void;
}

export function EventManagement({ server, events, loading, onEventsUpdate }: EventManagementProps) {
  const [actionLoading, setActionLoading] = useState<{ [key: string]: string }>({});

  const handleEventAction = async (eventId: string, action: 'start' | 'stop' | 'pause' | 'resume') => {
    try {
      setActionLoading(prev => ({ ...prev, [eventId]: action }));
      
      const response = await apiClientService.manageEvent({
        serverId: server.id,
        clusterId: server.clusterId,
        eventId,
        action
      });

      if (response.success) {
        toast.success(`Event ${action} successful: ${response.message}`);
        onEventsUpdate();
      } else {
        toast.error(`Event ${action} failed: ${response.message}`);
      }
    } catch (error) {
      console.error(`Error ${action} event:`, error);
      toast.error(`Event ${action} failed: ` + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setActionLoading(prev => {
        const newState = { ...prev };
        delete newState[eventId];
        return newState;
      });
    }
  };

  const getEventStatusBadge = (event: ServerEventInfo) => {
    if (event.isActive) {
      return <Badge className="bg-green-500">Active</Badge>;
    } else if (event.endTime && new Date(event.endTime) < new Date()) {
      return <Badge variant="secondary">Ended</Badge>;
    } else {
      return <Badge variant="outline">Inactive</Badge>;
    }
  };

  const getEventActions = (event: ServerEventInfo) => {
    const isLoading = actionLoading[event.eventId];
    const isOnline = server.status;

    return (
      <div className="flex gap-2">
        {!event.isActive && (
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleEventAction(event.eventId, 'start')}
            disabled={!!isLoading || !isOnline}
          >
            {isLoading === 'start' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            Start
          </Button>
        )}
        
        {event.isActive && (
          <>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleEventAction(event.eventId, 'pause')}
              disabled={!!isLoading || !isOnline}
            >
              {isLoading === 'pause' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Pause className="h-4 w-4" />
              )}
              Pause
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleEventAction(event.eventId, 'stop')}
              disabled={!!isLoading || !isOnline}
            >
              {isLoading === 'stop' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Square className="h-4 w-4" />
              )}
              Stop
            </Button>
          </>
        )}
        
        <Button
          size="sm"
          variant="outline"
          onClick={() => handleEventAction(event.eventId, 'resume')}
          disabled={!!isLoading || !isOnline || event.isActive}
        >
          {isLoading === 'resume' ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RotateCcw className="h-4 w-4" />
          )}
          Resume
        </Button>
      </div>
    );
  };

  const formatDateTime = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Event Management
            </CardTitle>
            <CardDescription>
              Manage active and scheduled events on this server
            </CardDescription>
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={onEventsUpdate}
            disabled={loading}
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Refresh'}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading events...</span>
          </div>
        ) : events.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No events found for this server.
          </div>
        ) : (
          <div className="space-y-4">
            {events.map((event) => (
              <div key={event.eventId} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{event.eventName}</h4>
                    {getEventStatusBadge(event)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <div>Type: {event.eventType}</div>
                    <div>Started: {formatDateTime(event.startTime)}</div>
                    {event.endTime && (
                      <div>Ends: {formatDateTime(event.endTime)}</div>
                    )}
                    <div className="flex items-center gap-1 mt-1">
                      <Users className="h-3 w-3" />
                      <span>{event.participants} participants</span>
                    </div>
                  </div>
                  {event.description && (
                    <div className="text-sm text-muted-foreground">
                      {event.description}
                    </div>
                  )}
                </div>
                
                <div className="flex flex-col gap-2">
                  {getEventActions(event)}
                </div>
              </div>
            ))}
          </div>
        )}

        {!server.status && (
          <div className="text-sm text-muted-foreground bg-muted p-3 rounded mt-4">
            Server must be online to manage events.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
