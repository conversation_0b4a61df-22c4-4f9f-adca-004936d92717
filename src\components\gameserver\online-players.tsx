'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Search, UserX, Ban, MessageSquare } from 'lucide-react';
import { toast } from 'sonner';
import { apiClientService } from '@/services/api-client.service';
import { OnlinePlayer } from '@/types/gameserver';

export function OnlinePlayers() {
  const [players, setPlayers] = useState<OnlinePlayer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [actionLoading, setActionLoading] = useState<{ [key: number]: string }>({});

  // Dialog states
  const [kickDialogOpen, setKickDialogOpen] = useState(false);
  const [banDialogOpen, setBanDialogOpen] = useState(false);
  const [messageDialogOpen, setMessageDialogOpen] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<OnlinePlayer | null>(null);
  const [kickReason, setKickReason] = useState('');
  const [banReason, setBanReason] = useState('');
  const [banDuration, setBanDuration] = useState('60'); // minutes
  const [message, setMessage] = useState('');

  const loadPlayers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClientService.getOnlinePlayers({
        page: currentPage,
        pageSize: 20,
        searchTerm: searchTerm || undefined
      });
      
      if (response.success) {
        setPlayers(response.players);
        setTotalPages(response.totalPages);
        setTotalCount(response.totalCount);
      } else {
        toast.error('Failed to load players: ' + response.message);
      }
    } catch (error) {
      console.error('Error loading players:', error);
      toast.error('Failed to load players: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm]);

  useEffect(() => {
    loadPlayers();
  }, [loadPlayers]);

  const handleKickPlayer = async () => {
    if (!selectedPlayer) return;

    try {
      setActionLoading(prev => ({ ...prev, [selectedPlayer.playerId]: 'kick' }));
      
      const response = await apiClientService.kickPlayer({
        playerId: selectedPlayer.playerId,
        serverId: selectedPlayer.serverId,
        reason: kickReason || 'Kicked by admin'
      });

      if (response.success) {
        toast.success('Player kicked successfully');
        setKickDialogOpen(false);
        setKickReason('');
        setSelectedPlayer(null);
        await loadPlayers();
      } else {
        toast.error('Failed to kick player: ' + response.message);
      }
    } catch (error) {
      console.error('Error kicking player:', error);
      toast.error('Failed to kick player: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setActionLoading(prev => {
        const newState = { ...prev };
        delete newState[selectedPlayer.playerId];
        return newState;
      });
    }
  };

  const handleBanPlayer = async () => {
    if (!selectedPlayer) return;

    try {
      setActionLoading(prev => ({ ...prev, [selectedPlayer.playerId]: 'ban' }));
      
      const response = await apiClientService.banPlayer({
        playerId: selectedPlayer.playerId,
        serverId: selectedPlayer.serverId,
        reason: banReason,
        duration: parseInt(banDuration),
        banType: 'ACCOUNT'
      });

      if (response.success) {
        toast.success('Player banned successfully');
        setBanDialogOpen(false);
        setBanReason('');
        setBanDuration('60');
        setSelectedPlayer(null);
        await loadPlayers();
      } else {
        toast.error('Failed to ban player: ' + response.message);
      }
    } catch (error) {
      console.error('Error banning player:', error);
      toast.error('Failed to ban player: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setActionLoading(prev => {
        const newState = { ...prev };
        delete newState[selectedPlayer.playerId];
        return newState;
      });
    }
  };

  const handleSendMessage = async () => {
    if (!selectedPlayer) return;

    try {
      setActionLoading(prev => ({ ...prev, [selectedPlayer.playerId]: 'message' }));
      
      const response = await apiClientService.sendMessage({
        serverId: selectedPlayer.serverId,
        playerId: selectedPlayer.playerId,
        message: message,
        messageType: 'SYSTEM'
      });

      if (response.success) {
        toast.success('Message sent successfully');
        setMessageDialogOpen(false);
        setMessage('');
        setSelectedPlayer(null);
      } else {
        toast.error('Failed to send message: ' + response.message);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setActionLoading(prev => {
        const newState = { ...prev };
        delete newState[selectedPlayer.playerId];
        return newState;
      });
    }
  };

  const formatLoginTime = (loginTime: Date) => {
    return new Date(loginTime).toLocaleString();
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Online Players ({totalCount})</CardTitle>
          <CardDescription>
            Manage currently online players across all servers
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search players..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button onClick={loadPlayers} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Refresh'}
            </Button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading players...</span>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Character</TableHead>
                    <TableHead>Account</TableHead>
                    <TableHead>Level</TableHead>
                    <TableHead>Map</TableHead>
                    <TableHead>Server</TableHead>
                    <TableHead>Login Time</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {players.map((player) => (
                    <TableRow key={player.playerId}>
                      <TableCell className="font-medium">
                        {player.characterName}
                      </TableCell>
                      <TableCell>{player.accountName}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{player.level}</Badge>
                      </TableCell>
                      <TableCell>{player.mapName}</TableCell>
                      <TableCell>Server {player.serverId}</TableCell>
                      <TableCell>{formatLoginTime(player.loginTime)}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Dialog open={kickDialogOpen && selectedPlayer?.playerId === player.playerId} onOpenChange={setKickDialogOpen}>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedPlayer(player)}
                                disabled={!!actionLoading[player.playerId]}
                              >
                                <UserX className="h-3 w-3" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Kick Player</DialogTitle>
                                <DialogDescription>
                                  Kick {player.characterName} from the server?
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <Textarea
                                  placeholder="Reason for kick (optional)"
                                  value={kickReason}
                                  onChange={(e) => setKickReason(e.target.value)}
                                />
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setKickDialogOpen(false)}>
                                  Cancel
                                </Button>
                                <Button onClick={handleKickPlayer} disabled={actionLoading[player.playerId] === 'kick'}>
                                  {actionLoading[player.playerId] === 'kick' ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    'Kick Player'
                                  )}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          <Dialog open={banDialogOpen && selectedPlayer?.playerId === player.playerId} onOpenChange={setBanDialogOpen}>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedPlayer(player)}
                                disabled={!!actionLoading[player.playerId]}
                              >
                                <Ban className="h-3 w-3" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Ban Player</DialogTitle>
                                <DialogDescription>
                                  Ban {player.characterName} from the server?
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <Textarea
                                  placeholder="Reason for ban (required)"
                                  value={banReason}
                                  onChange={(e) => setBanReason(e.target.value)}
                                />
                                <Input
                                  type="number"
                                  placeholder="Duration in minutes (0 = permanent)"
                                  value={banDuration}
                                  onChange={(e) => setBanDuration(e.target.value)}
                                />
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setBanDialogOpen(false)}>
                                  Cancel
                                </Button>
                                <Button 
                                  onClick={handleBanPlayer} 
                                  disabled={!banReason || actionLoading[player.playerId] === 'ban'}
                                  variant="destructive"
                                >
                                  {actionLoading[player.playerId] === 'ban' ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    'Ban Player'
                                  )}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          <Dialog open={messageDialogOpen && selectedPlayer?.playerId === player.playerId} onOpenChange={setMessageDialogOpen}>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedPlayer(player)}
                                disabled={!!actionLoading[player.playerId]}
                              >
                                <MessageSquare className="h-3 w-3" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Send Message</DialogTitle>
                                <DialogDescription>
                                  Send a message to {player.characterName}
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <Textarea
                                  placeholder="Message content"
                                  value={message}
                                  onChange={(e) => setMessage(e.target.value)}
                                />
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setMessageDialogOpen(false)}>
                                  Cancel
                                </Button>
                                <Button 
                                  onClick={handleSendMessage} 
                                  disabled={!message || actionLoading[player.playerId] === 'message'}
                                >
                                  {actionLoading[player.playerId] === 'message' ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    'Send Message'
                                  )}
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {players.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No online players found.
                </div>
              )}

              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
