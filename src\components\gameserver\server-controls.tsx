'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Play, Square, RotateCcw, Save, ShieldOff, Shield } from 'lucide-react';
import { toast } from 'sonner';
import { GameServer } from '@/types/gameserver';
import { apiClientService } from '@/services/api-client.service';

interface ServerControlsProps {
  server: GameServer;
  onServerUpdate: () => void;
}

export function ServerControls({ server, onServerUpdate }: ServerControlsProps) {
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [connectionsDisabled, setConnectionsDisabled] = useState(false);

  const handleServerAction = async (action: 'start' | 'stop' | 'restart' | 'save-characters' | 'disable-connections' | 'enable-connections') => {
    try {
      setActionLoading(action);
      
      let response;
      switch (action) {
        case 'start':
          response = await apiClientService.startGameServer({
            serverId: server.id,
            clusterId: server.clusterId,
          });
          break;
        case 'stop':
          response = await apiClientService.stopGameServer({
            serverId: server.id,
            clusterId: server.clusterId,
            graceful: true,
            timeoutSeconds: 30
          });
          break;
        case 'restart':
          response = await apiClientService.restartGameServer({
            serverId: server.id,
            clusterId: server.clusterId,
            graceful: true,
          });
          break;
        case 'save-characters':
          response = await apiClientService.saveCharacters({
            serverId: server.id,
            clusterId: server.clusterId,
            forceAll: true
          });
          break;
        case 'disable-connections':
          response = await apiClientService.disableNewConnections({
            serverId: server.id,
            clusterId: server.clusterId,
            disable: true,
            reason: 'Maintenance mode'
          });
          if (response.success) {
            setConnectionsDisabled(true);
          }
          break;
        case 'enable-connections':
          response = await apiClientService.disableNewConnections({
            serverId: server.id,
            clusterId: server.clusterId,
            disable: false,
          });
          if (response.success) {
            setConnectionsDisabled(false);
          }
          break;
      }

      if (response.success) {
        if (action === 'save-characters') {
          toast.success(`Characters saved: ${response?.savedCount} successful, ${response?.failedCount} failed`);
        } else {
          toast.success(`Server ${action} successful: ${response.message}`);
        }
        onServerUpdate();
      } else {
        toast.error(`Server ${action} failed: ${response.message}`);
      }
    } catch (error) {
      console.error(`Error ${action} server:`, error);
      toast.error(`Server ${action} failed: ` + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setActionLoading(null);
    }
  };

  const isOnline = server.status;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Server Controls</CardTitle>
        <CardDescription>
          Basic server operations and maintenance
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Primary Controls */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Power Controls</h4>
          <div className="flex gap-2">
            {!isOnline && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleServerAction('start')}
                disabled={!!actionLoading}
              >
                {actionLoading === 'start' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
                Start Server
              </Button>
            )}
            
            {isOnline && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleServerAction('stop')}
                disabled={!!actionLoading}
              >
                {actionLoading === 'stop' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
                Stop Server
              </Button>
            )}
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleServerAction('restart')}
              disabled={!!actionLoading || !isOnline}
            >
              {actionLoading === 'restart' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4" />
              )}
              Restart Server
            </Button>
          </div>
        </div>

        {/* Data Controls */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Data Management</h4>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleServerAction('save-characters')}
              disabled={!!actionLoading || !isOnline}
            >
              {actionLoading === 'save-characters' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              Save All Characters
            </Button>
          </div>
        </div>

        {/* Connection Controls */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Connection Management</h4>
          <div className="flex gap-2">
            {!connectionsDisabled ? (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleServerAction('disable-connections')}
                disabled={!!actionLoading || !isOnline}
              >
                {actionLoading === 'disable-connections' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <ShieldOff className="h-4 w-4" />
                )}
                Block New Connections
              </Button>
            ) : (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleServerAction('enable-connections')}
                disabled={!!actionLoading || !isOnline}
              >
                {actionLoading === 'enable-connections' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Shield className="h-4 w-4" />
                )}
                Allow New Connections
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
