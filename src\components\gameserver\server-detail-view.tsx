'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Users, Server,  ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import { GameServer, ServerEventInfo } from '@/types/gameserver';
import { apiClientService } from '@/services/api-client.service';
import { useRouter } from 'next/navigation';
import { ServerControls } from './server-controls';
import { ConfigManagement } from './config-management';
import { EventManagement } from './event-management';

interface ServerDetailViewProps {
  serverId: number;
}

export function ServerDetailView({ serverId }: ServerDetailViewProps) {
  const router = useRouter();
  const [server, setServer] = useState<GameServer | null>(null);
  const [events, setEvents] = useState<ServerEventInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [eventsLoading, setEventsLoading] = useState(false);

  const loadServerData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClientService.getServerList({
        includeOffline: true
      });
      
      if (response.success) {
        const foundServer = response.servers.find(s => s.id === serverId);
        if (foundServer) {
          setServer(foundServer);
        } else {
          toast.error('Server not found');
          router.push('/dashboard/gameservers');
        }
      } else {
        toast.error('Failed to load server: ' + response.message);
      }
    } catch (error) {
      console.error('Error loading server:', error);
      toast.error('Failed to load server: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [serverId, router]);

  const loadServerEvents = useCallback(async () => {
    if (!server) return;
    
    try {
      setEventsLoading(true);
      const response = await apiClientService.getServerEvents({
        serverId: server.id,
        clusterId: server.clusterId,
        activeOnly: false
      });
      
      if (response.success) {
        setEvents(response.events);
      } else {
        toast.error('Failed to load events: ' + response.message);
      }
    } catch (error) {
      console.error('Error loading events:', error);
      toast.error('Failed to load events: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setEventsLoading(false);
    }
  }, [server]);

  useEffect(() => {
    loadServerData();
  }, [loadServerData]);

  useEffect(() => {
    if (server) {
      loadServerEvents();
    }
  }, [server, loadServerEvents]);

  const getStatusBadge = (status: boolean) => {
    return (
      <Badge variant={status ? 'default' : 'secondary'} className={status ? 'bg-green-500' : 'bg-gray-500'}>
        {status ? 'Online' : 'Offline'}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading server details...</span>
      </div>
    );
  }

  if (!server) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Server not found.
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button
        variant="outline"
        onClick={() => router.push('/dashboard/gameservers')}
        className="mb-4"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Server List
      </Button>

      {/* Server Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center gap-2">
              <Server className="h-6 w-6" />
              {server.serverName}
            </CardTitle>
            {getStatusBadge(server.status)}
          </div>
          <CardDescription>
            Cluster {server.clusterId} • Server {server.serverId}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">IP Address:</span>
              <div className="text-muted-foreground">{server.serverIP}</div>
            </div>
            <div>
              <span className="font-medium">Game Port:</span>
              <div className="text-muted-foreground">{server.gameServerPort}</div>
            </div>
            <div>
              <span className="font-medium">gRPC Port:</span>
              <div className="text-muted-foreground">{server.gameServerGrpcPort}</div>
            </div>
            <div>
              <span className="font-medium">Version:</span>
              <div className="text-muted-foreground">{server.version || 'N/A'}</div>
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4" />
            <span>
              {server.currentPlayers} / {server.maximumOnline} players online
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Management Sections */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Server Controls */}
        <ServerControls 
          server={server} 
          onServerUpdate={loadServerData}
        />

        {/* Config Management */}
        <ConfigManagement 
          server={server}
        />
      </div>

      {/* Event Management */}
      <EventManagement 
        server={server}
        events={events}
        loading={eventsLoading}
        onEventsUpdate={loadServerEvents}
      />
    </div>
  );
}
