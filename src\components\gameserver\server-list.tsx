'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Play, Square, RotateCcw, Users, Server, Save, ShieldOff, Eye } from 'lucide-react';
import { toast } from 'sonner';
import { GameServer } from '@/types/gameserver';
import { apiClientService } from '@/services/api-client.service';
import { useRouter } from 'next/navigation';

export function ServerList() {
  const router = useRouter();
  const [servers, setServers] = useState<GameServer[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<{ [key: number]: string }>({});

  useEffect(() => {
    loadServers();
  }, []);

  const loadServers = async () => {
    try {
      setLoading(true);
      const response = await apiClientService.getServerList({
        includeOffline: true
      });
      
      if (response.success) {
        setServers(response.servers);
      } else {
        toast.error('Failed to load servers: ' + response.message);
      }
    } catch (error) {
      console.error('Error loading servers:', error);
      toast.error('Failed to load servers: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleServerAction = async (serverId: number, clusterId: number, action: 'start' | 'stop' | 'restart' | 'save-characters' | 'disable-connections') => {
    try {
      setActionLoading(prev => ({ ...prev, [serverId]: action }));

      let response;
      switch (action) {
        case 'start':
          response = await apiClientService.startGameServer({
            serverId,
            clusterId,
            // autoStart: true
          });
          break;
        case 'stop':
          response = await apiClientService.stopGameServer({
            serverId,
            clusterId,
            graceful: true,
            timeoutSeconds: 30
          });
          break;
        case 'restart':
          response = await apiClientService.restartGameServer({
            serverId,
            clusterId,
            graceful: true,
            // autoStart: true
          });
          break;
        case 'save-characters':
          response = await apiClientService.saveCharacters({
            serverId,
            clusterId,
            forceAll: true
          });
          break;
        case 'disable-connections':
          response = await apiClientService.disableNewConnections({
            serverId,
            clusterId,
            disable: true,
            reason: 'Maintenance mode'
          });
          break;
      }

      if (response.success) {
        if (action === 'save-characters') {
          toast.success(`Characters saved: ${response.savedCount} successful, ${response.failedCount} failed`);
        } else {
          toast.success(`Server ${action} successful: ${response.message}`);
        }
        // Reload servers to get updated status
        await loadServers();
      } else {
        toast.error(`Server ${action} failed: ${response.message}`);
      }
    } catch (error) {
      console.error(`Error ${action} server:`, error);
      toast.error(`Server ${action} failed: ` + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setActionLoading(prev => {
        const newState = { ...prev };
        delete newState[serverId];
        return newState;
      });
    }
  };

  const getStatusBadge = (status: boolean) => {
    return (
      <Badge variant={status ? 'default' : 'secondary'} className={status ? 'bg-green-500' : 'bg-gray-500'}>
        {status ? 'Online' : 'Offline'}
      </Badge>
    );
  };

  const getActionButtons = (server: GameServer) => {
    const isLoading = actionLoading[server.id];
    const isOnline = server.status;

    return (
      <div className="space-y-2">
        {/* Primary Actions Row */}
        <div className="flex gap-2">
          {!isOnline && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleServerAction(server.id, server.clusterId, 'start')}
              disabled={!!isLoading}
            >
              {isLoading === 'start' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Start
            </Button>
          )}

          {isOnline && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleServerAction(server.id, server.clusterId, 'stop')}
              disabled={!!isLoading}
            >
              {isLoading === 'stop' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Square className="h-4 w-4" />
              )}
              Stop
            </Button>
          )}

          <Button
            size="sm"
            variant="outline"
            onClick={() => handleServerAction(server.id, server.clusterId, 'restart')}
            disabled={!!isLoading}
          >
            {isLoading === 'restart' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RotateCcw className="h-4 w-4" />
            )}
            Restart
          </Button>
        </div>

        {/* Secondary Actions Row */}
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleServerAction(server.id, server.clusterId, 'save-characters')}
            disabled={!!isLoading || !isOnline}
            title="Save all characters"
          >
            {isLoading === 'save-characters' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            Save
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={() => handleServerAction(server.id, server.clusterId, 'disable-connections')}
            disabled={!!isLoading || !isOnline}
            title="Disable new connections"
          >
            {isLoading === 'disable-connections' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ShieldOff className="h-4 w-4" />
            )}
            Block
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={() => router.push(`/dashboard/gameservers/${server.id}`)}
            title="Server details"
          >
            <Eye className="h-4 w-4" />
            Details
          </Button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading servers...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Game Servers</h2>
        <Button onClick={loadServers} disabled={loading}>
          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Refresh'}
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {servers.map((server) => (
          <Card key={server.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  {server.serverName}
                </CardTitle>
                {getStatusBadge(server.status)}
              </div>
              <CardDescription>
                Cluster {server.clusterId} • Server {server.serverId}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">IP:</span>
                  <div className="text-muted-foreground">{server.serverIP}</div>
                </div>
                <div>
                  <span className="font-medium">Port:</span>
                  <div className="text-muted-foreground">{server.gameServerPort}</div>
                </div>
                <div>
                  <span className="font-medium">gRPC Port:</span>
                  <div className="text-muted-foreground">{server.gameServerGrpcPort}</div>
                </div>
                <div>
                  <span className="font-medium">Version:</span>
                  <div className="text-muted-foreground">{server.version || 'N/A'}</div>
                </div>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Users className="h-4 w-4" />
                <span>
                  {server.currentPlayers} / {server.maximumOnline} players
                </span>
              </div>

              <div className="pt-2">
                {getActionButtons(server)}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {servers.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          No servers found. Make sure HeroLogin is running and configured properly.
        </div>
      )}
    </div>
  );
}



