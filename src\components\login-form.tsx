"use client"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { authClient } from "@/lib/auth-client"
import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { toast } from "sonner"
import Image from "next/image"

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check for error messages from URL params
  useEffect(() => {
    const errorParam = searchParams.get("error");
    if (errorParam) {
      switch (errorParam) {
        case "account_inactive":
          setError("Tài khoản của bạn đã bị vô hiệu hóa. Vui lòng liên hệ quản trị viên.");
          break;
        case "auth_error":
          setError("Có lỗi xảy ra trong quá trình xác thực. Vui lòng thử lại.");
          break;
        default:
          setError("Có lỗi xảy ra. Vui lòng thử lại.");
      }
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const formData = new FormData(e.target as HTMLFormElement);
    const data = Object.fromEntries(formData);
    console.log("Login data:", data.email, data.password);
    try {
      const res :any = await authClient.signIn.email({
        email: data.email as string,
        password: data.password as string,
        rememberMe: false,
        callbackURL: searchParams.get("callbackUrl") || "/dashboard",
      });
      if (res.error) {
        console.error("Error logging in:", res);
        if (res.data.code === "EMAIL_NOT_VERIFIED") {
          toast.error("Email chưa được xác thực");
          return;
        }
        // Handle specific error messages
        if (res.error.message?.includes("Invalid email or password")) {
          setError("Email hoặc mật khẩu không đúng.");
        } else if (res.error.message?.includes("User not found")) {
          setError("Tài khoản không tồn tại.");
        } else {
          setError("Đăng nhập thất bại. Vui lòng thử lại.");
        }
      } else {
        // Login successful
        toast.success("Đăng nhập thành công!");
        router.push(searchParams.get("callbackUrl") || "/dashboard");
      }
    } catch (error) {
    console.error("Error logging in:", error);
      setError("Có lỗi xảy ra. Vui lòng thử lại.");
      toast.error("Có lỗi xảy ra khi đăng nhập");
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form className="p-6 md:p-8" onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Chào mừng trở lại</h1>
                <p className="text-muted-foreground text-balance">
                  Đăng nhập vào hệ thống quản trị Yulgang
                </p>
              </div>

              {error && (
                <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md border border-destructive/20">
                  {error}
                </div>
              )}
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  name="email"
                  id="email"
                  type="email"
                  required
                  disabled={isLoading}
                />
              </div>
              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Mật khẩu</Label>
                </div>
                <Input
                  name="password"
                  id="password"
                  type="password"
                  required
                  disabled={isLoading}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Đang đăng nhập..." : "Đăng nhập"}
              </Button>
              <div className="text-center text-sm text-muted-foreground">
                Chỉ quản trị viên mới có thể truy cập hệ thống này.
                <br />
                Vui lòng liên hệ admin để được cấp tài khoản.
              </div>
            </div>
          </form>
          <div className="bg-muted relative hidden md:block">
            <Image
              width={200}
              height={200}
              src="/placeholder.svg"
              alt="Image"
              className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
            />
          </div>
        </CardContent>
      </Card>
      <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
        and <a href="#">Privacy Policy</a>.
      </div>
    </div>
  )
}
