"use client"

import { IconAlarm, IconChartBar, IconCirclePlusFilled, IconDashboard, IconFolder, IconListDetails, IconMail, IconUsers, IconUserCog, IconTemplate, IconTool, IconPackage } from "@tabler/icons-react"

import { Button } from "@/components/ui/button"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Link from "next/link";

export function NavMain() {
  const items = [
    {
      title: "Dashboard",
      url: "/dashboard/",
      icon: IconDashboard,
    },
    {
      title: "Game Servers",
      url: "/dashboard/gameservers",
       icon: IconAlarm,
    },
    {
      title: "Online Accounts",
      url: "/dashboard/accounts",
       icon: IconUsers,
    },
    {
      title: "Character Items",
      url: "/dashboard/character-items",
      icon: IconPackage,
    },
    {
      title: "Users",
      url: "/dashboard/users",
      icon: IconUserCog,
    },
    {
      title: "Events",
      url: "/dashboard/events",
      icon: IconListDetails,

    },
     {
      title: "Template",
      url: "/dashboard/template-management",
      icon: IconTemplate,

    },
    {
      title: "Editor Tools",
      url: "/dashboard/editor-tools",
      icon: IconTool,

    },
    {
      title: "Configs",
      url: "/dashboard/configs",
      icon: IconFolder,
    },
    {
      title: "Stats",
      url: "/dashboard/stats",
       icon: IconChartBar,
    },
  ];
  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <SidebarMenuButton
              tooltip="Send Message"
              className="bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear"
            >
              <IconCirclePlusFilled />
              <span>Send Message</span>
            </SidebarMenuButton>
            <Button
              size="icon"
              className="size-8 group-data-[collapsible=icon]:opacity-0"
              variant="outline"
            >
              <IconMail />
              <span className="sr-only">Inbox</span>
            </Button>
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarMenu>
          {items.map((item) => (
            <Link href={item.url} key={item.title}>
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton tooltip={item.title}>
                {item.icon && <item.icon />}
                <span>{item.title}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
            </Link>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
