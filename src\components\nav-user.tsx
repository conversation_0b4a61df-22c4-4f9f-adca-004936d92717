
import {
  IconUserCircle,
  IconSettings,
  IconDotsVertical,
} from "@tabler/icons-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { Badge } from "@/components/ui/badge"
import { getUserWithRoles } from "@/lib/auth-utils"
import { LogoutButton } from "@/components/auth/logout-button"
import { SidebarWrapper } from "@/components/auth/sidebar-wrapper"
import { Session } from "better-auth"

export default async function NavUser({session}: {session: Session | null}) {

  if (!session?.userId) {
    return null;
  }

  // Get user with roles from database
  const userWithRoles = await getUserWithRoles(session.userId);

  if (!userWithRoles) {
    return null;
  }

  const user = userWithRoles;
  const role = user.roles.length > 0 ? user.roles.reduce((highest, current) => {
    return current.role.level < highest.role.level ? current : highest;
  }).role : null;

  const getRoleBadgeVariant = (level: number) => {
    switch (level) {
      case 1: return 'destructive'; // Admin
      case 2: return 'default'; // Manager
      case 3: return 'secondary'; // Moderator
      case 4: return 'outline'; // Editor
      default: return 'outline';
    }
  };

  const getRoleDisplayName = (name: string) => {
    const roleNames: Record<string, string> = {
      admin: 'Quản trị viên',
      manager: 'Quản lý',
      moderator: 'Điều hành viên',
      editor: 'Biên tập viên',
    };
    return roleNames[name] || name;
  };

  return (
    <SidebarWrapper>
      {(isMobile: boolean) => (
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user.image || undefined} alt={user.name} />
                <AvatarFallback className="rounded-lg">
                  {user.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{user.name}</span>
                <div className="flex items-center space-x-1">
                  <span className="text-muted-foreground truncate text-xs">
                    {user.email}
                  </span>
                  {role && (
                    <Badge
                      variant={getRoleBadgeVariant(role.level)}
                      className="text-xs px-1 py-0"
                    >
                      {getRoleDisplayName(role.name)}
                    </Badge>
                  )}
                </div>
              </div>
              <IconDotsVertical className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                side={isMobile ? "bottom" : "right"}
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src={user.image || undefined} alt={user.name} />
                      <AvatarFallback className="rounded-lg">
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-medium">{user.name}</span>
                      <span className="text-muted-foreground truncate text-xs">
                        {user.email}
                      </span>
                      {role && (
                        <Badge
                          variant={getRoleBadgeVariant(role.level)}
                          className="text-xs w-fit mt-1"
                        >
                          {getRoleDisplayName(role.name)}
                        </Badge>
                      )}
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <IconUserCircle />
                  Hồ sơ
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <IconSettings />
                  Cài đặt
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <LogoutButton />
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      )}
    </SidebarWrapper>
  )
}