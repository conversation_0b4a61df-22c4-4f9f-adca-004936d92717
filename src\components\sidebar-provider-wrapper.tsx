'use client';

import { SidebarProvider } from '@/components/ui/sidebar';
import { ReactNode } from 'react';

interface SidebarProviderWrapperProps {
  children: ReactNode;
}

export function SidebarProviderWrapper({ children }: SidebarProviderWrapperProps) {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      {children}
    </SidebarProvider>
  );
}
