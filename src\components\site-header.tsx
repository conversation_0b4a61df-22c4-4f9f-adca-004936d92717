import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { auth } from "@/lib/auth"
import { headers } from "next/headers"
import { getUserWithRoles } from "@/lib/auth-utils"
// import { Badge } from "@/components/ui/badge"
import { UserMenu } from "@/components/auth/user-menu"

export async function SiteHeader() {
  const session = await auth.api.getSession({
    headers: await headers()
  });

  // let userInfo = null;
  if (session?.user) {
    const userWithRoles = await getUserWithRoles(session.user.id);
    if (userWithRoles && userWithRoles.roles.length > 0) {
      // const role = userWithRoles.roles.reduce((highest, current) => {
      //   return current.role.level < highest.role.level ? current : highest;
      // }).role;

      // const getRoleDisplayName = (name: string) => {
      //   const roleNames: Record<string, string> = {
      //     admin: 'Quản trị viên',
      //     manager: '<PERSON><PERSON><PERSON><PERSON> lý',
      //     moderator: '<PERSON><PERSON><PERSON><PERSON> hành viên',
      //     editor: 'Biên tập viên',
      //   };
      //   return roleNames[name] || name;
      // };

      // const getRoleBadgeVariant = (level: number) => {
      //   switch (level) {
      //     case 1: return 'destructive' as const;
      //     case 2: return 'default' as const;
      //     case 3: return 'secondary' as const;
      //     case 4: return 'outline' as const;
      //     default: return 'outline' as const;
      //   }
      // };

      // userInfo = (
      //   <div className="flex items-center space-x-2">
      //     <span className="text-sm font-medium">{userWithRoles.name}</span>
      //     <Badge variant={getRoleBadgeVariant(role.level)} className="text-xs">
      //       {getRoleDisplayName(role.name)}
      //     </Badge>
      //   </div>
      // );
    }
  }

  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <h1 className="text-base font-medium">Hệ thống quản trị Yulgang</h1>
        <div className="ml-auto flex items-center gap-4">
          {session?.user && (
            <UserMenu
              user={{
                id: session.user.id,
                name: session.user.name,
                email: session.user.email,
                image: session.user.image
              }}
              // role={role}
              variant="button"
            />
          )}
        </div>
      </div>
    </header>
  )
}
