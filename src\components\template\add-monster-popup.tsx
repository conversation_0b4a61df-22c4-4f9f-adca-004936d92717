'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, X } from 'lucide-react';
import { MagicHandler } from '@/lib/items';

interface Monster {
  fldPid: number;
  fldName: string;
  fldLevel: number;
  fldHp: number;
  fldMp: number;
  fldAttack: number;
  fldDefense: number;
  fldJob: number;
}

interface AddMonsterPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (monsterData: any) => void;
  onUpdate?: (monsterData: any) => void;
  coordinates: { x: number; y: number } | null;
  mapId: number;
  editMode?: boolean;
  existingMonster?: any;
}

export default function Add<PERSON>onsterPopup({
  isOpen,
  onClose,
  onAdd,
  onUpdate,
  coordinates,
  mapId,
  editMode = false,
  existingMonster
}: AddMonsterPopupProps) {
  const [monsters, setMonsters] = useState<Monster[]>([]);
  const [filteredMonsters, setFilteredMonsters] = useState<Monster[]>([]);
  const [selectedMonster, setSelectedMonster] = useState<Monster | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  // Initialize magic handler
  const magicHandler = new MagicHandler();
  
  // Form data for monster setbase
  const [formData, setFormData] = useState({
    amount: 1,
    aoe: 0,
    active: 1,
    // Coordinate fields
    x: 0,
    y: 0,
    z: 15,
    // Face fields
    face: 0,
    face0: 0,
    // Stats fields
    hp: 0,
    at: 0,
    df: 0
  });

  // Load monsters from API
  useEffect(() => {
    if (isOpen) {
      if (editMode && existingMonster) {
        // Pre-populate with existing monster data
        setSelectedMonster({
          fldPid: existingMonster.fldNid,
          fldName: existingMonster.fldName,
          fldLevel: existingMonster.fldLevel,
          fldHp: existingMonster.fldHp,
          fldMp: existingMonster.fldMp || 0,
          fldAttack: existingMonster.fldAttack,
          fldDefense: existingMonster.fldDefense,
          fldJob: existingMonster.fldJob || 0
        });
        setFormData({
          amount: existingMonster.fldNum || 1,
          aoe: existingMonster.fldRange || 0,
          active: existingMonster.fldActive || 1,
          x: existingMonster.fldX || 0,
          y: existingMonster.fldY || 0,
          z: existingMonster.fldZ || 15,
          face: existingMonster.fldFace || 0,
          face0: existingMonster.fldFace0 || 0,
          hp: existingMonster.fldHp || 0,
          at: existingMonster.fldAt || 0,
          df: existingMonster.fldDf || 0
        });
      }
      loadMonsters();
    } else if (isOpen && coordinates) {
      // Set coordinates for new monster
      setFormData(prev => ({
        ...prev,
        x: coordinates.x,
        y: coordinates.y
      }));
    }
  }, [isOpen, editMode, existingMonster, coordinates]);

  // Filter monsters based on search
  useEffect(() => {
    if (!searchTerm) {
      setFilteredMonsters(monsters);
      return;
    }

    const filtered = monsters.filter(monster => {
      const name = magicHandler.enConvert(monster.fldName).toLowerCase();
      const search = searchTerm.toLowerCase();
      
      return (
        name.includes(search) ||
        monster.fldPid.toString().includes(search) ||
        monster.fldLevel.toString().includes(search) ||
        monster.fldHp.toString().includes(search)
      );
    });
    
    setFilteredMonsters(filtered);
  }, [searchTerm, monsters]);

  const loadMonsters = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/template/monsters');
      const data = await response.json();
      
      if (data.success) {
        setMonsters(data.data);
        setFilteredMonsters(data.data);
      }
    } catch (error) {
      console.error('Error loading monsters:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMonsterSelect = (monster: Monster) => {
    setSelectedMonster(monster);

    // Auto-fill stats from selected monster (only if not in edit mode)
    if (!editMode) {
      setFormData(prev => ({
        ...prev,
        hp: monster.fldHp || 0,
        at: monster.fldAttack || 0,
        df: monster.fldDefense || 0
      }));
    }
  };

  const handleSubmit = () => {
    if (!selectedMonster) return;

    const monsterData = {
      fldPid: selectedMonster.fldPid,
      fldMid: mapId,
      fldX: formData.x,
      fldY: formData.y,
      fldZ: formData.z,
      fldFace0: formData.face0,
      fldFace: formData.face,
      fldAmount: formData.amount,
      fldAoe: formData.aoe,
      fldActive: formData.active,
      fldNpc: selectedMonster.fldPid < 10000 ? 1 : 0,
      // Use form data for stats, fallback to monster data
      fldHp: formData.hp || selectedMonster.fldHp,
      fldAt: formData.at || selectedMonster.fldAttack,
      fldDf: formData.df || selectedMonster.fldDefense,
      fldNewtime: 5,
      fldLevel: selectedMonster.fldLevel,
      fldExp: 0,
      fldAuto: 0,
      fldBoss: 0,
      fldGold: 0,
      fldAccuracy: 0,
      fldEvasion: 0,
      fldQitemdrop: 0,
      fldQdroppp: 0,
      fldFreedrop: 0
    };

    if (editMode && existingMonster) {
      // Include the index for update
      const updateData = { ...monsterData, fldIndex: existingMonster.fldIndex };
      onUpdate?.(updateData);
    } else {
      onAdd(monsterData);
    }

    handleClose();
  };

  const handleClose = () => {
    setSelectedMonster(null);
    setSearchTerm('');
    setFormData({
      amount: 1,
      aoe: 0,
      active: 1,
      x: 0,
      y: 0,
      z: 15,
      face: 0,
      face0: 0,
      hp: 0,
      at: 0,
      df: 0
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-7xl lg:min-w-7xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="w-5 h-5" />
            {editMode ? 'Chỉnh sửa Monster' : 'Thêm Monster'} tại ({coordinates?.x.toFixed(1)}, {coordinates?.y.toFixed(1)})
          </DialogTitle>
        </DialogHeader>

        <div className="flex gap-4 flex-1 overflow-hidden">
          {/* Monster Selection Panel */}
          <div className="flex-1 flex flex-col">
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Tìm kiếm theo tên, ID, level, HP..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex-1 overflow-y-auto border rounded-lg">
              {loading ? (
                <div className="p-4 text-center">Đang tải monsters...</div>
              ) : (
                <div className="space-y-2 p-2">
                  {filteredMonsters.map((monster) => (
                    <div
                      key={monster.fldPid}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedMonster?.fldPid === monster.fldPid
                          ? 'border-blue-500 bg-gray-900 text-white'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleMonsterSelect(monster)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">
                            {magicHandler.enConvert(monster.fldName)}
                          </div>
                          <div className="text-sm text-gray-300">
                            ID: {monster.fldPid} | Level: {monster.fldLevel}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Badge variant="outline">HP: {monster.fldHp.toLocaleString()}</Badge>
                          <Badge variant="outline">ATK: {monster.fldAttack}</Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Configuration Panel */}
          <div className="w-xl border-l pl-4">
            <h3 className="font-medium mb-4">Cấu hình Monster</h3>
            
            {selectedMonster && (
              <div className="space-y-4">
                {/* Selected Monster Info */}
                <div className="p-3 bg-gray-900 rounded-lg">
                  <div className="font-medium">{magicHandler.enConvert(selectedMonster.fldName)}</div>
                  <div className="text-sm text-gray-300">
                    ID: {selectedMonster.fldPid} | Level: {selectedMonster.fldLevel}
                  </div>
                  <div className="text-sm text-gray-300">
                    HP: {selectedMonster.fldHp.toLocaleString()}
                  </div>
                </div>

                {/* Form Fields */}
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {/* Coordinates Section */}
                  <div className="border-b pb-3">
                    <h4 className="font-medium text-sm text-gray-700 mb-2">Tọa độ</h4>
                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <Label htmlFor="x" className="text-xs">X</Label>
                        <Input
                          id="x"
                          type="number"
                          step="0.1"
                          value={formData.x}
                          onChange={(e) => setFormData(prev => ({ ...prev, x: parseFloat(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>
                      <div>
                        <Label htmlFor="y" className="text-xs">Y</Label>
                        <Input
                          id="y"
                          type="number"
                          step="0.1"
                          value={formData.y}
                          onChange={(e) => setFormData(prev => ({ ...prev, y: parseFloat(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>
                      <div>
                        <Label htmlFor="z" className="text-xs">Z</Label>
                        <Input
                          id="z"
                          type="number"
                          value={formData.z}
                          onChange={(e) => setFormData(prev => ({ ...prev, z: parseInt(e.target.value) || 15 }))}
                          className="text-xs"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Face Section */}
                  <div className="border-b pb-3">
                    <h4 className="font-medium text-sm text-gray-700 mb-2">Hướng</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label htmlFor="face" className="text-xs">Face</Label>
                        <Input
                          id="face"
                          type="number"
                          min="-1"
                          max="1"
                          value={formData.face}
                          onChange={(e) => setFormData(prev => ({ ...prev, face: parseInt(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>
                      <div>
                        <Label htmlFor="face0" className="text-xs">Face0</Label>
                        <Input
                          id="face0"
                          type="number"
                          min="-1"
                          max="1"
                          value={formData.face0}
                          onChange={(e) => setFormData(prev => ({ ...prev, face0: parseInt(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Stats Section */}
                  <div className="border-b pb-3">
                    <h4 className="font-medium text-sm text-gray-700 mb-2">Chỉ số</h4>
                    <div className="space-y-2">
                      <div>
                        <Label htmlFor="hp" className="text-xs">HP</Label>
                        <Input
                          id="hp"
                          type="number"
                          min="0"
                          value={formData.hp}
                          onChange={(e) => setFormData(prev => ({ ...prev, hp: parseInt(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>
                      <div>
                        <Label htmlFor="at" className="text-xs">Attack (AT)</Label>
                        <Input
                          id="at"
                          type="number"
                          min="0"
                          value={formData.at}
                          onChange={(e) => setFormData(prev => ({ ...prev, at: parseInt(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>
                      <div>
                        <Label htmlFor="df" className="text-xs">Defense (DF)</Label>
                        <Input
                          id="df"
                          type="number"
                          min="0"
                          value={formData.df}
                          onChange={(e) => setFormData(prev => ({ ...prev, df: parseInt(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Spawn Settings Section */}
                  <div>
                    <h4 className="font-medium text-sm text-gray-700 mb-2">Cài đặt Spawn</h4>
                    <div className="space-y-2">
                      <div>
                        <Label htmlFor="amount" className="text-xs">Số lượng (Amount)</Label>
                        <Input
                          id="amount"
                          type="number"
                          min="1"
                          max="100"
                          value={formData.amount}
                          onChange={(e) => setFormData(prev => ({ ...prev, amount: parseInt(e.target.value) || 1 }))}
                          className="text-xs"
                        />
                      </div>

                      <div>
                        <Label htmlFor="aoe" className="text-xs">Phạm vi AOE</Label>
                        <Input
                          id="aoe"
                          type="number"
                          min="0"
                          max="500"
                          value={formData.aoe}
                          onChange={(e) => setFormData(prev => ({ ...prev, aoe: parseInt(e.target.value) || 0 }))}
                          className="text-xs"
                        />
                      </div>

                      <div>
                        <Label htmlFor="active" className="text-xs">Trạng thái</Label>
                        <select
                          id="active"
                          value={formData.active}
                          onChange={(e) => setFormData(prev => ({ ...prev, active: parseInt(e.target.value) }))}
                          className="w-full p-2 border border-gray-300 rounded-md text-xs"
                        >
                          <option value={1}>Hoạt động</option>
                          <option value={0}>Không hoạt động</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-4">
                  <Button onClick={handleSubmit} className="flex-1">
                    <Plus className="w-4 h-4 mr-2" />
                    {editMode ? 'Cập nhật Monster' : 'Thêm Monster'}
                  </Button>
                  <Button variant="outline" onClick={handleClose}>
                    <X className="w-4 h-4 mr-2" />
                    Hủy
                  </Button>
                </div>
              </div>
            )}

            {!selectedMonster && (
              <div className="text-center text-gray-500 py-8">
                Chọn một monster để cấu hình
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
