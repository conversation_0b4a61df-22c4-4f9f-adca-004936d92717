'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Trash2,
  Save,
  AlertCircle,
  Gift
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Box, UpdateBoxRequest, getBoxImageUrl, formatPpRate } from '@/types/box';

interface BoxDetailDialogProps {
  box: Box | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onBoxUpdated: () => void;
  onDeleteRequest: () => void;
}

export const BoxDetailDialog: React.FC<BoxDetailDialogProps> = ({
  box,
  open,
  onOpenChange,
  onBoxUpdated,
  onDeleteRequest
}) => {
  const [formData, setFormData] = useState<UpdateBoxRequest>({});
  const [saving, setSaving] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Initialize form data when box changes
  useEffect(() => {
    if (box) {
      setFormData({ 
        fldName: box.fldName || ''
      });
      setImageError(false);
    }
  }, [box]);

  // Handle form field changes
  const handleFieldChange = (field: keyof UpdateBoxRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle save
  const handleSave = async () => {
    if (!box) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/template/boxes/${box.fldPid}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã cập nhật box thành công');
        onBoxUpdated();
        onOpenChange(false);
      } else {
        toast.error(result.message || 'Không thể cập nhật box');
      }
    } catch (error) {
      console.error('Error updating box:', error);
      toast.error('Có lỗi xảy ra khi cập nhật box');
    } finally {
      setSaving(false);
    }
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
  };

  if (!box) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[600px] h-[500px] max-w-[95vw] max-h-[95vh] overflow-y-auto flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Chi tiết Box: {box.fldName || `Box ${box.fldPid}`}
          </DialogTitle>
          <DialogDescription>
            Chỉnh sửa thông tin chi tiết của box
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col justify-start gap-4 flex-grow">
          {/* Box Preview */}
          <div className="flex items-start gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="relative">
              {!imageError ? (
                <img
                  src={getBoxImageUrl(box.fldPid)}
                  alt={box.fldName || `Box ${box.fldPid}`}
                  className="w-16 h-16 object-cover rounded border-2"
                  onError={handleImageError}
                />
              ) : (
                <div className="w-16 h-16 bg-muted rounded border-2 flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{box.fldName || `Box ${box.fldPid}`}</h3>
              <p className="text-sm text-muted-foreground">ID: {box.fldPid}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline">Box</Badge>
                <Badge variant="secondary">{box.rewardCount} rewards</Badge>
                <Badge variant="default">Total PP: {formatPpRate(box.totalPp)}</Badge>
              </div>
            </div>
          </div>

          {/* Form Fields */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="fldPid">Box ID (Không thể thay đổi)</Label>
              <Input
                id="fldPid"
                type="number"
                value={box.fldPid}
                disabled
                className="bg-muted"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="fldName">Tên Box</Label>
              <Input
                id="fldName"
                value={formData.fldName || ''}
                onChange={(e) => handleFieldChange('fldName', e.target.value)}
                placeholder="Nhập tên box..."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Số lượng Rewards</Label>
                <Input
                  value={box.rewardCount}
                  disabled
                  className="bg-muted"
                />
              </div>
              
              <div className="space-y-2">
                <Label>Tổng PP</Label>
                <Input
                  value={formatPpRate(box.totalPp)}
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-end">
          <Button
            variant="destructive"
            onClick={onDeleteRequest}
            className="flex items-center gap-2 mr-auto"
          >
            <Trash2 className="h-4 w-4" />
            Xóa Box
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
