'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Plus,
  Search,
  AlertCircle,
  Package
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { CreateBoxRewardRequest, DEFAULT_BOX_REWARD_VALUES, BOX_REWARD_FIELD_CATEGORIES, BOX_REWARD_FIELD_LABELS } from '@/types/box';
import { TemplateItem } from '@/types/template';

interface BoxRewardAddDialogProps {
  boxId: number | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRewardCreated: () => void;
}

export const BoxRewardAddDialog: React.FC<BoxRewardAddDialogProps> = ({
  boxId,
  open,
  onOpenChange,
  onRewardCreated
}) => {
  const [formData, setFormData] = useState<CreateBoxRewardRequest>({ ...DEFAULT_BOX_REWARD_VALUES, fldPidx: 0 });
  const [saving, setSaving] = useState(false);
  const [selectedItem, setSelectedItem] = useState<TemplateItem | null>(null);
  
  // Item selector state
  const [items, setItems] = useState<TemplateItem[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setFormData({ ...DEFAULT_BOX_REWARD_VALUES, fldPidx: 0 });
      setSelectedItem(null);
      setSearchTerm('');
      setImageErrors(new Set());
      loadItems();
    }
  }, [open]);

  // Load items for selection
  const loadItems = async () => {
    setLoadingItems(true);
    try {
      const params = new URLSearchParams({
        page: '1',
        limit: '100',
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/api/template/game-items?${params}`);
      const result = await response.json();

      if (result.success) {
        setItems(result.data.items);
      } else {
        toast.error('Không thể tải danh sách items');
      }
    } catch (error) {
      console.error('Error loading items:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách items');
    } finally {
      setLoadingItems(false);
    }
  };

  // Handle item search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (open) {
        loadItems();
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, open]);

  // Handle form field changes
  const handleFieldChange = (field: keyof CreateBoxRewardRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle item selection
  const handleItemSelect = (item: TemplateItem) => {
    setSelectedItem(item);
    setFormData(prev => ({
      ...prev,
      fldPidx: item.fldPid,
      fldNamex: item.fldName
    }));
  };

  // Handle image error
  const handleImageError = (itemId: number) => {
    setImageErrors(prev => new Set(prev).add(itemId));
  };

  // Handle save
  const handleSave = async () => {
    if (!boxId) {
      toast.error('Không có box được chọn');
      return;
    }

    if (!formData.fldPidx) {
      toast.error('Vui lòng chọn item để tạo reward');
      return;
    }

    setSaving(true);
    try {
      const response = await fetch(`/api/template/boxes/${boxId}/rewards`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã thêm reward thành công');
        onRewardCreated();
        onOpenChange(false);
      } else {
        toast.error(result.message || 'Không thể thêm reward');
      }
    } catch (error) {
      console.error('Error creating reward:', error);
      toast.error('Có lỗi xảy ra khi thêm reward');
    } finally {
      setSaving(false);
    }
  };

  // Filter items based on search
  const filteredItems = items.filter(item =>
    item.fldName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.fldPid.toString().includes(searchTerm)
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[1000px] h-[700px] max-w-[95vw] max-h-[95vh] overflow-y-auto flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Thêm Reward mới cho Box {boxId}
          </DialogTitle>
          <DialogDescription>
            Chọn item và cấu hình thông tin để thêm reward mới
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col justify-start gap-4 flex-grow">
          <Tabs defaultValue="item" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="item">Chọn Item</TabsTrigger>
              <TabsTrigger value="basic">Cơ bản</TabsTrigger>
              <TabsTrigger value="magic">Magic</TabsTrigger>
              <TabsTrigger value="enhancement">Cường hóa</TabsTrigger>
              <TabsTrigger value="config">Cấu hình</TabsTrigger>
            </TabsList>

            {/* Item Selection Tab */}
            <TabsContent value="item" className="space-y-4">
              {/* Search */}
              <div className="space-y-2">
                <Label>Tìm kiếm Item</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm theo tên hoặc ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Selected Item Preview */}
              {selectedItem && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h3 className="font-medium mb-2">Item đã chọn:</h3>
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      {!imageErrors.has(selectedItem.fldPid) ? (
                        <img
                          src={`http://one.chamthoi.com/item/${selectedItem.fldPid}.jpg`}
                          alt={selectedItem.fldName}
                          className="w-12 h-12 object-cover rounded border-2"
                          onError={() => handleImageError(selectedItem.fldPid)}
                        />
                      ) : (
                        <div className="w-12 h-12 bg-muted rounded border-2 flex items-center justify-center">
                          <AlertCircle className="h-6 w-6 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{selectedItem.fldName}</p>
                      <p className="text-sm text-muted-foreground">ID: {selectedItem.fldPid}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Items Grid */}
              <div className="space-y-2">
                <Label>Chọn Item để tạo Reward:</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto border rounded-lg p-4">
                  {loadingItems ? (
                    <div className="col-span-full flex items-center justify-center py-12">
                      <Package className="h-8 w-8 animate-spin" />
                      <span className="ml-2">Đang tải...</span>
                    </div>
                  ) : filteredItems.length === 0 ? (
                    <div className="col-span-full text-center py-12">
                      <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Không tìm thấy item nào</p>
                    </div>
                  ) : (
                    filteredItems.map((item) => (
                      <Card
                        key={item.fldPid}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedItem?.fldPid === item.fldPid ? "ring-2 ring-primary" : ""
                        }`}
                        onClick={() => handleItemSelect(item)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0">
                              {!imageErrors.has(item.fldPid) ? (
                                <img
                                  src={`http://one.chamthoi.com/item/${item.fldPid}.jpg`}
                                  alt={item.fldName}
                                  className="w-8 h-8 object-cover rounded border"
                                  onError={() => handleImageError(item.fldPid)}
                                />
                              ) : (
                                <div className="w-8 h-8 bg-muted rounded border flex items-center justify-center">
                                  <AlertCircle className="h-4 w-4 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm truncate">{item.fldName}</p>
                              <p className="text-xs text-muted-foreground">ID: {item.fldPid}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fldPidx">Item ID *</Label>
                  <Input
                    id="fldPidx"
                    type="number"
                    value={formData.fldPidx || ''}
                    onChange={(e) => handleFieldChange('fldPidx', parseInt(e.target.value) || 0)}
                    disabled={!!selectedItem}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fldNamex">Tên Item</Label>
                  <Input
                    id="fldNamex"
                    value={formData.fldNamex || ''}
                    onChange={(e) => handleFieldChange('fldNamex', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fldNumber">Số lượng</Label>
                  <Input
                    id="fldNumber"
                    type="number"
                    value={formData.fldNumber || 1}
                    onChange={(e) => handleFieldChange('fldNumber', parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fldPp">PP Rate</Label>
                  <Input
                    id="fldPp"
                    type="number"
                    value={formData.fldPp || 0}
                    onChange={(e) => handleFieldChange('fldPp', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Magic Tab */}
            <TabsContent value="magic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {BOX_REWARD_FIELD_CATEGORIES.magic.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{BOX_REWARD_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={(formData as any)[field] || 0}
                      onChange={(e) => handleFieldChange(field as keyof CreateBoxRewardRequest, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Enhancement Tab */}
            <TabsContent value="enhancement" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {BOX_REWARD_FIELD_CATEGORIES.enhancement.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{BOX_REWARD_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={(formData as any)[field] || 0}
                      onChange={(e) => handleFieldChange(field as keyof CreateBoxRewardRequest, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Config Tab */}
            <TabsContent value="config" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {BOX_REWARD_FIELD_CATEGORIES.config.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{BOX_REWARD_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={(formData as any)[field] || 0}
                      onChange={(e) => handleFieldChange(field as keyof CreateBoxRewardRequest, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={handleSave} disabled={saving || !formData.fldPidx}>
            <Plus className="h-4 w-4 mr-2" />
            {saving ? 'Đang thêm...' : 'Thêm Reward'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
