'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Trash2,
  Save,
  AlertCircle,
  Package
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { BoxReward, UpdateBoxRewardRequest, BOX_REWARD_FIELD_LABELS, BOX_REWARD_FIELD_CATEGORIES, getRewardImageUrl, formatPpRate } from '@/types/box';

interface BoxRewardDetailDialogProps {
  reward: BoxReward | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRewardUpdated: () => void;
  onDeleteRequest: () => void;
}

export const BoxRewardDetailDialog: React.FC<BoxRewardDetailDialogProps> = ({
  reward,
  open,
  onOpenChange,
  onRewardUpdated,
  onDeleteRequest
}) => {
  const [formData, setFormData] = useState<UpdateBoxRewardRequest>({});
  const [saving, setSaving] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Initialize form data when reward changes
  useEffect(() => {
    if (reward) {
      setFormData({ ...reward });
      setImageError(false);
    }
  }, [reward]);

  // Handle form field changes
  const handleFieldChange = (field: keyof UpdateBoxRewardRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Reset image error when item ID changes
    if (field === 'fldPidx') {
      setImageError(false);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!reward || !reward.id) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/template/boxes/${reward.fldPid}/rewards/${reward.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã cập nhật reward thành công');
        onRewardUpdated();
        onOpenChange(false);
      } else {
        toast.error(result.message || 'Không thể cập nhật reward');
      }
    } catch (error) {
      console.error('Error updating reward:', error);
      toast.error('Có lỗi xảy ra khi cập nhật reward');
    } finally {
      setSaving(false);
    }
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
  };

  if (!reward) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[1000px] h-[700px] max-w-[95vw] max-h-[95vh] overflow-y-auto flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Chi tiết Reward: {reward.fldNamex || `Item ${reward.fldPidx}`}
          </DialogTitle>
          <DialogDescription>
            Chỉnh sửa thông tin chi tiết của reward
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col justify-start gap-4 flex-grow">
          {/* Reward Preview */}
          <div className="flex items-start gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="relative">
              {!imageError ? (
                <img
                  src={getRewardImageUrl(formData.fldPidx || reward.fldPidx)}
                  alt={formData.fldNamex || reward.fldNamex || `Item ${formData.fldPidx || reward.fldPidx}`}
                  className="w-16 h-16 object-cover rounded border-2"
                  onError={handleImageError}
                  key={formData.fldPidx || reward.fldPidx} // Force re-render when ID changes
                />
              ) : (
                <div className="w-16 h-16 bg-muted rounded border-2 flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{formData.fldNamex || reward.fldNamex || `Item ${formData.fldPidx || reward.fldPidx}`}</h3>
              <p className="text-sm text-muted-foreground">Item ID: {formData.fldPidx || reward.fldPidx}</p>
              <p className="text-sm text-muted-foreground">Box ID: {reward.fldPid}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline">Reward</Badge>
                <Badge variant="secondary">x{formData.fldNumber || reward.fldNumber}</Badge>
                <Badge variant="default">PP: {formatPpRate(formData.fldPp || reward.fldPp || 0)}</Badge>
              </div>
            </div>
          </div>

          {/* Form Fields in Tabs */}
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Cơ bản</TabsTrigger>
              <TabsTrigger value="magic">Magic</TabsTrigger>
              <TabsTrigger value="enhancement">Cường hóa</TabsTrigger>
              <TabsTrigger value="config">Cấu hình</TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fldPidx">Item ID</Label>
                  <Input
                    id="fldPidx"
                    type="number"
                    value={formData.fldPidx || 0}
                    onChange={(e) => handleFieldChange('fldPidx', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fldNamex">Tên Item</Label>
                  <Input
                    id="fldNamex"
                    value={formData.fldNamex || ''}
                    onChange={(e) => handleFieldChange('fldNamex', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fldNumber">Số lượng</Label>
                  <Input
                    id="fldNumber"
                    type="number"
                    value={formData.fldNumber || 0}
                    onChange={(e) => handleFieldChange('fldNumber', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fldPp">PP Rate</Label>
                  <Input
                    id="fldPp"
                    type="number"
                    value={formData.fldPp || 0}
                    onChange={(e) => handleFieldChange('fldPp', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Magic Tab */}
            <TabsContent value="magic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {BOX_REWARD_FIELD_CATEGORIES.magic.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{BOX_REWARD_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={(formData as any)[field] || 0}
                      onChange={(e) => handleFieldChange(field as keyof UpdateBoxRewardRequest, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Enhancement Tab */}
            <TabsContent value="enhancement" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {BOX_REWARD_FIELD_CATEGORIES.enhancement.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{BOX_REWARD_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={(formData as any)[field] || 0}
                      onChange={(e) => handleFieldChange(field as keyof UpdateBoxRewardRequest, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Config Tab */}
            <TabsContent value="config" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {BOX_REWARD_FIELD_CATEGORIES.config.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{BOX_REWARD_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={(formData as any)[field] || 0}
                      onChange={(e) => handleFieldChange(field as keyof UpdateBoxRewardRequest, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-end">
          <Button
            variant="destructive"
            onClick={onDeleteRequest}
            className="flex items-center gap-2 mr-auto"
          >
            <Trash2 className="h-4 w-4" />
            Xóa Reward
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
