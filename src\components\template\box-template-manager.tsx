'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Plus,
  Trash2,
  Edit,
  RefreshCw,
  Package,
  Gift,
  Box as BoxIcon
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Box, 
  BoxReward, 
  BoxFilter, 
  BOX_FIELD_LABELS, 
  SORTABLE_BOX_FIELDS,
  formatPpRate,
  getBoxImageUrl,
  getRewardImageUrl
} from '@/types/box';
import { BoxDetailDialog } from './box-detail-dialog';
import { BoxAddDialog } from './box-add-dialog';
import { BoxRewardDetailDialog } from './box-reward-detail-dialog';
import { BoxRewardAddDialog } from './box-reward-add-dialog';

const BoxTemplateManager = () => {
  // State management
  const [boxes, setBoxes] = useState<Box[]>([]);
  const [rewards, setRewards] = useState<BoxReward[]>([]);
  const [selectedBox, setSelectedBox] = useState<Box | null>(null);
  const [selectedReward, setSelectedReward] = useState<BoxReward | null>(null);
  
  // Loading states
  const [loadingBoxes, setLoadingBoxes] = useState(false);
  const [loadingRewards, setLoadingRewards] = useState(false);
  
  // Pagination state for boxes
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(20);
  
  // Filter state for boxes
  const [filters, setFilters] = useState<BoxFilter>({
    search: '',
    sortBy: 'fldPid',
    sortOrder: 'asc'
  });
  
  // Dialog states
  const [showBoxDetailDialog, setShowBoxDetailDialog] = useState(false);
  const [showRewardDetailDialog, setShowRewardDetailDialog] = useState(false);
  const [showAddBoxDialog, setShowAddBoxDialog] = useState(false);
  const [showAddRewardDialog, setShowAddRewardDialog] = useState(false);
  const [showDeleteBoxDialog, setShowDeleteBoxDialog] = useState(false);
  const [showDeleteRewardDialog, setShowDeleteRewardDialog] = useState(false);
  
  // Image error tracking
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  // Load boxes data
  const loadBoxes = useCallback(async () => {
    setLoadingBoxes(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.fldPid && { fldPid: filters.fldPid.toString() }),
        ...(filters.sortBy && { sortBy: filters.sortBy }),
        ...(filters.sortOrder && { sortOrder: filters.sortOrder })
      });

      const response = await fetch(`/api/template/boxes?${params}`);
      const result = await response.json();

      if (result.success) {
        setBoxes(result.data.boxes);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        toast.error(result.message || 'Không thể tải danh sách boxes');
      }
    } catch (error) {
      console.error('Error loading boxes:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách boxes');
    } finally {
      setLoadingBoxes(false);
    }
  }, [currentPage, itemsPerPage, filters]);

  // Load rewards for selected box
  const loadRewards = useCallback(async (boxId: number) => {
    setLoadingRewards(true);
    try {
      const response = await fetch(`/api/template/boxes/${boxId}/rewards?limit=100`);
      const result = await response.json();

      if (result.success) {
        setRewards(result.data.rewards);
      } else {
        toast.error(result.message || 'Không thể tải danh sách rewards');
        setRewards([]);
      }
    } catch (error) {
      console.error('Error loading rewards:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách rewards');
      setRewards([]);
    } finally {
      setLoadingRewards(false);
    }
  }, []);

  // Handle image error
  const handleImageError = (itemId: number) => {
    setImageErrors(prev => new Set(prev).add(itemId));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof BoxFilter, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle search
  const handleSearch = (value: string) => {
    handleFilterChange('search', value);
  };

  // Handle box selection
  const handleBoxSelect = (box: Box) => {
    setSelectedBox(box);
    setSelectedReward(null);
    loadRewards(box.fldPid);
  };

  // Handle reward selection
  const handleRewardSelect = (reward: BoxReward) => {
    setSelectedReward(reward);
    setShowRewardDetailDialog(true);
  };

  // Handle box updated
  const handleBoxUpdated = () => {
    loadBoxes();
    if (selectedBox) {
      loadRewards(selectedBox.fldPid);
    }
  };

  // Handle reward updated
  const handleRewardUpdated = () => {
    if (selectedBox) {
      loadRewards(selectedBox.fldPid);
      loadBoxes(); // Reload boxes to update totals
    }
  };

  // Handle box created
  const handleBoxCreated = () => {
    loadBoxes();
  };

  // Handle reward created
  const handleRewardCreated = () => {
    handleRewardUpdated();
  };

  // Handle delete box request from detail dialog
  const handleDeleteBoxRequest = () => {
    setShowBoxDetailDialog(false);
    setShowDeleteBoxDialog(true);
  };

  // Handle delete reward request from detail dialog
  const handleDeleteRewardRequest = () => {
    setShowRewardDetailDialog(false);
    setShowDeleteRewardDialog(true);
  };

  // Handle delete box
  const handleDeleteBox = async () => {
    if (!selectedBox) return;

    try {
      const response = await fetch(`/api/template/boxes/${selectedBox.fldPid}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã xóa box và tất cả rewards thành công');
        setShowDeleteBoxDialog(false);
        setSelectedBox(null);
        setRewards([]);
        loadBoxes();
      } else {
        toast.error(result.message || 'Không thể xóa box');
      }
    } catch (error) {
      console.error('Error deleting box:', error);
      toast.error('Có lỗi xảy ra khi xóa box');
    }
  };

  // Handle delete reward
  const handleDeleteReward = async () => {
    if (!selectedBox || !selectedReward) {
      toast.error('Không có reward được chọn để xóa');
      return;
    }

    if (!selectedReward.id || selectedReward.id <= 0) {
      toast.error(`Reward không có ID hợp lệ (ID: ${selectedReward.id})`);
      console.error('Invalid reward ID:', selectedReward);
      return;
    }

    console.log('Deleting reward:', {
      boxId: selectedBox.fldPid,
      rewardId: selectedReward.id,
      reward: selectedReward
    });

    try {
      const response = await fetch(`/api/template/boxes/${selectedBox.fldPid}/rewards/${selectedReward.id}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      console.log('Delete response:', result);

      if (result.success) {
        toast.success('Đã xóa reward thành công');
        setShowDeleteRewardDialog(false);
        setSelectedReward(null);
        handleRewardUpdated();
      } else {
        toast.error(result.message || 'Không thể xóa reward');
      }
    } catch (error) {
      console.error('Error deleting reward:', error);
      toast.error('Có lỗi xảy ra khi xóa reward');
    }
  };

  // Load boxes on component mount and when dependencies change
  useEffect(() => {
    loadBoxes();
  }, [loadBoxes]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Gift className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold">Quản lý Box Template</h1>
        </div>
        <Button onClick={() => setShowAddBoxDialog(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Thêm Box mới
        </Button>
      </div>

      {/* Main Layout - 2 Columns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Boxes List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BoxIcon className="h-5 w-5" />
              Danh sách Boxes
              <Badge variant="secondary">{boxes.length} boxes</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Filters */}
            <div className="space-y-4">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm theo tên hoặc ID..."
                    value={filters.search || ''}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadBoxes}
                  disabled={loadingBoxes}
                >
                  <RefreshCw className={`h-4 w-4 ${loadingBoxes ? 'animate-spin' : ''}`} />
                </Button>
              </div>

              {/* Sort */}
              <div className="flex gap-2">
                <Select
                  value={filters.sortBy || 'fldPid'}
                  onValueChange={(value) => handleFilterChange('sortBy', value)}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SORTABLE_BOX_FIELDS.map(field => (
                      <SelectItem key={field} value={field}>
                        {BOX_FIELD_LABELS[field] || field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={filters.sortOrder || 'asc'}
                  onValueChange={(value) => handleFilterChange('sortOrder', value as 'asc' | 'desc')}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">↑</SelectItem>
                    <SelectItem value="desc">↓</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Boxes Grid */}
            {loadingBoxes ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin" />
                <span className="ml-2">Đang tải...</span>
              </div>
            ) : boxes.length === 0 ? (
              <div className="text-center py-12">
                <BoxIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Không tìm thấy box nào</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 max-h-96 overflow-y-auto">
                  {boxes.map((box) => (
                    <div
                      key={box.fldPid}
                      className={`relative group cursor-pointer p-3 border rounded-lg transition-all hover:shadow-md ${
                        selectedBox?.fldPid === box.fldPid ? "ring-2 ring-primary bg-primary/5" : "hover:border-primary"
                      }`}
                      onClick={() => handleBoxSelect(box)}
                    >
                      <div className="flex flex-col items-center space-y-2">
                        {/* Box Image */}
                        <div className="w-12 h-12 border rounded overflow-hidden">
                          {!imageErrors.has(box.fldPid) ? (
                            <img
                              src={getBoxImageUrl(box.fldPid)}
                              alt={box.fldName || `Box ${box.fldPid}`}
                              className="w-full h-full object-cover"
                              onError={() => handleImageError(box.fldPid)}
                            />
                          ) : (
                            <div className="w-full h-full bg-muted flex items-center justify-center">
                              <Gift className="h-6 w-6 text-muted-foreground" />
                            </div>
                          )}
                        </div>

                        {/* Box Info */}
                        <div className="text-center">
                          <div className="font-medium text-sm truncate">
                            {box.fldName || `Box ${box.fldPid}`}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ID: {box.fldPid}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {box.rewardCount} rewards
                          </div>
                          <div className="text-xs font-medium text-primary">
                            Total PP: {formatPpRate(box.totalPp)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Trang {currentPage} / {totalPages}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1 || loadingBoxes}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages || loadingBoxes}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Right Column - Rewards List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Rewards
                {selectedBox && (
                  <Badge variant="secondary">{rewards.length} items</Badge>
                )}
              </div>
              {selectedBox && (
                <Button
                  size="sm"
                  onClick={() => setShowAddRewardDialog(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Thêm Reward
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedBox ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Chọn một box để xem rewards</p>
              </div>
            ) : loadingRewards ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin" />
                <span className="ml-2">Đang tải rewards...</span>
              </div>
            ) : rewards.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Box này chưa có rewards</p>
                <Button
                  className="mt-4"
                  onClick={() => setShowAddRewardDialog(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm Reward đầu tiên
                </Button>
              </div>
            ) : (
              <>
                {/* Selected Box Info */}
                <div className="mb-4 p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 border rounded overflow-hidden">
                      {!imageErrors.has(selectedBox.fldPid) ? (
                        <img
                          src={getBoxImageUrl(selectedBox.fldPid)}
                          alt={selectedBox.fldName || `Box ${selectedBox.fldPid}`}
                          className="w-full h-full object-cover"
                          onError={() => handleImageError(selectedBox.fldPid)}
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <Gift className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{selectedBox.fldName || `Box ${selectedBox.fldPid}`}</h3>
                      <div className="text-sm text-muted-foreground">
                        {selectedBox.rewardCount} rewards • Total PP: {formatPpRate(selectedBox.totalPp)}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowBoxDetailDialog(true)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowDeleteBoxDialog(true)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Rewards Grid */}
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 max-h-96 overflow-y-auto">
                  {rewards.map((reward) => (
                    <div
                      key={reward.id || `${reward.fldPid}-${reward.fldPidx}`}
                      className="relative group cursor-pointer p-2 border rounded-lg transition-all hover:shadow-md hover:border-primary"
                      onClick={() => handleRewardSelect(reward)}
                    >
                      <div className="flex flex-col items-center space-y-2">
                        {/* Reward Image */}
                        <div className="w-12 h-12 border rounded overflow-hidden">
                          {!imageErrors.has(reward.fldPidx) ? (
                            <img
                              src={getRewardImageUrl(reward.fldPidx)}
                              alt={reward.fldNamex || `Item ${reward.fldPidx}`}
                              className="w-full h-full object-cover"
                              onError={() => handleImageError(reward.fldPidx)}
                            />
                          ) : (
                            <div className="w-full h-full bg-muted flex items-center justify-center">
                              <Package className="h-6 w-6 text-muted-foreground" />
                            </div>
                          )}
                        </div>

                        {/* Reward Info */}
                        <div className="text-center">
                          <div className="font-medium text-xs truncate">
                            {reward.fldNamex || `Item ${reward.fldPidx}`}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Item: {reward.fldPidx} | DB: {reward.id}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            x{reward.fldNumber}
                          </div>
                          <div className="text-xs font-medium text-primary">
                            PP: {formatPpRate(reward.fldPp || 0)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Delete Box Confirmation Dialog */}
      <Dialog open={showDeleteBoxDialog} onOpenChange={setShowDeleteBoxDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa Box</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa box {selectedBox?.fldName || selectedBox?.fldPid} và tất cả {selectedBox?.rewardCount} rewards của nó không?
              Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteBoxDialog(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDeleteBox}>
              <Trash2 className="h-4 w-4 mr-2" />
              Xóa Box
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Box Add Dialog */}
      <BoxAddDialog
        open={showAddBoxDialog}
        onOpenChange={setShowAddBoxDialog}
        onBoxCreated={handleBoxCreated}
      />

      {/* Box Detail Dialog */}
      <BoxDetailDialog
        box={selectedBox}
        open={showBoxDetailDialog}
        onOpenChange={setShowBoxDetailDialog}
        onBoxUpdated={handleBoxUpdated}
        onDeleteRequest={handleDeleteBoxRequest}
      />

      {/* Reward Add Dialog */}
      <BoxRewardAddDialog
        boxId={selectedBox?.fldPid || null}
        open={showAddRewardDialog}
        onOpenChange={setShowAddRewardDialog}
        onRewardCreated={handleRewardCreated}
      />

      {/* Reward Detail Dialog */}
      <BoxRewardDetailDialog
        reward={selectedReward}
        open={showRewardDetailDialog}
        onOpenChange={setShowRewardDetailDialog}
        onRewardUpdated={handleRewardUpdated}
        onDeleteRequest={handleDeleteRewardRequest}
      />

      {/* Delete Reward Confirmation Dialog */}
      <Dialog open={showDeleteRewardDialog} onOpenChange={setShowDeleteRewardDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa Reward</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa reward {selectedReward?.fldNamex || `Item ${selectedReward?.fldPidx}`} không?
              <br />
              <strong>DB ID:</strong> {selectedReward?.id} | <strong>Item ID:</strong> {selectedReward?.fldPidx}
              <br />
              Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteRewardDialog(false)}>
              Hủy
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteReward}
              disabled={!selectedReward?.id || selectedReward.id <= 0}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {(!selectedReward?.id || selectedReward.id <= 0) ? 'ID không hợp lệ' : 'Xóa Reward'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BoxTemplateManager;
