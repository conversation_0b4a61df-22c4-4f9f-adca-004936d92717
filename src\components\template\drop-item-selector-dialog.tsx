'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Package,
  RefreshCw,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { MagicHandler } from '@/lib/items';

interface GameItem {
  fldPid: number;
  fldName: string;
  fldLevel: number;
  fldReside1: number;
  fldReside2: number;
  fldType: number;
  fldJobLevel: number;
  fldNj: number;
  fldDf: number;
  fldAt1: number;
  fldAt2: number;
  fldAp: number;
  fldMagic1: number;
  fldMagic2: number;
  fldMagic3: number;
  fldMagic4: number;
  fldMagic5: number;
  fldSocapphuhon: number;
  fldTrungcapphuhon: number;
  fldTienhoa: number;
  fldKhoalai: number;
  fldDays: number;
}

interface DropItemSelectorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onItemSelect: () => void;
}

export function DropItemSelectorDialog({
  open,
  onOpenChange,
  onItemSelect
}: DropItemSelectorDialogProps) {
  const [items, setItems] = useState<GameItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<GameItem | null>(null);
  const magicHandler = new MagicHandler();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [minLevel, setMinLevel] = useState('');
  const [maxLevel, setMaxLevel] = useState('');
  const [reside1, setReside1] = useState('');
  const [reside2, setReside2] = useState('');
  const [itemType, setItemType] = useState('');

  // Drop form data
  const [dropLevel1, setDropLevel1] = useState('');
  const [dropLevel2, setDropLevel2] = useState('');
  const [dropRate, setDropRate] = useState('');
  const [dropMagic0, setDropMagic0] = useState('0');
  const [dropMagic1, setDropMagic1] = useState('0');
  const [dropMagic2, setDropMagic2] = useState('0');
  const [dropMagic3, setDropMagic3] = useState('0');
  const [dropMagic4, setDropMagic4] = useState('0');
  const [dropSocapphuhon, setDropSocapphuhon] = useState('0');
  const [dropTrungcapphuhon, setDropTrungcapphuhon] = useState('0');
  const [dropTienhoa, setDropTienhoa] = useState('0');
  const [dropKhoalai, setDropKhoalai] = useState('0');
  const [dropDays, setDropDays] = useState('0');
  const [dropSunx, setDropSunx] = useState('');
  const [comothongbao, setComothongbao] = useState('0');

  // Load items data
  const loadItems = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      });

      if (searchTerm) params.append('name', searchTerm);
      if (minLevel) params.append('minLevel', minLevel);
      if (maxLevel) params.append('maxLevel', maxLevel);
      if (reside1) params.append('fldReside1', reside1);
      if (reside2) params.append('fldReside2', reside2);
      if (itemType) params.append('fldType', itemType);

      const response = await fetch(`/api/template/items/search?${params}`);
      const result = await response.json();

      if (result.success) {
        setItems(result.data.items);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        toast.error(result.message || 'Failed to load items');
      }
    } catch (error) {
      console.error('Error loading items:', error);
      toast.error('Failed to load items');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, minLevel, maxLevel, reside1, reside2, itemType]);

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    loadItems();
  };

  // Handle item selection
  const handleItemSelect = (item: GameItem) => {
    setSelectedItem(item);
    // Auto-fill form with item data
    setDropMagic1(item.fldMagic1?.toString() || '0');
    setDropMagic2(item.fldMagic2?.toString() || '0');
    setDropMagic3(item.fldMagic3?.toString() || '0');
    setDropMagic4(item.fldMagic4?.toString() || '0');
    setDropSocapphuhon(item.fldSocapphuhon?.toString() || '0');
    setDropTrungcapphuhon(item.fldTrungcapphuhon?.toString() || '0');
    setDropTienhoa(item.fldTienhoa?.toString() || '0');
    setDropKhoalai(item.fldKhoalai?.toString() || '0');
    setDropDays(item.fldDays?.toString() || '0');
  };

  // Handle add drop item
  const handleAddDropItem = async () => {
    if (!selectedItem || !dropLevel1 || !dropLevel2 || !dropRate) {
      toast.error('Vui lòng điền đầy đủ thông tin bắt buộc');
      return;
    }

    if (parseInt(dropLevel1) > parseInt(dropLevel2)) {
      toast.error('Level 1 không thể lớn hơn Level 2');
      return;
    }

    try {
      const response = await fetch('/api/template/drops', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fldLevel1: parseInt(dropLevel1),
          fldLevel2: parseInt(dropLevel2),
          fldPid: selectedItem.fldPid,
          fldName: selectedItem.fldName,
          fldMagic0: parseInt(dropMagic0),
          fldMagic1: parseInt(dropMagic1),
          fldMagic2: parseInt(dropMagic2),
          fldMagic3: parseInt(dropMagic3),
          fldMagic4: parseInt(dropMagic4),
          fldSocapphuhon: parseInt(dropSocapphuhon),
          fldTrungcapphuhon: parseInt(dropTrungcapphuhon),
          fldTienhoa: parseInt(dropTienhoa),
          fldKhoalai: parseInt(dropKhoalai),
          fldPp: parseInt(dropRate),
          fldSunx: dropSunx,
          comothongbao: parseInt(comothongbao),
          fldDays: parseInt(dropDays)
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Thêm drop item thành công');
        onItemSelect();
        onOpenChange(false);
        // Reset form
        setSelectedItem(null);
        setDropLevel1('');
        setDropLevel2('');
        setDropRate('');
        setDropMagic0('0');
        setDropMagic1('0');
        setDropMagic2('0');
        setDropMagic3('0');
        setDropMagic4('0');
        setDropSocapphuhon('0');
        setDropTrungcapphuhon('0');
        setDropTienhoa('0');
        setDropKhoalai('0');
        setDropDays('0');
        setDropSunx('');
        setComothongbao('0');
      } else {
        toast.error(result.message || 'Failed to add drop item');
      }
    } catch (error) {
      console.error('Error adding drop item:', error);
      toast.error('Failed to add drop item');
    }
  };

  useEffect(() => {
    if (open) {
      loadItems();
    }
  }, [open, loadItems]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl lg:min-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Thêm Drop Item</DialogTitle>
          <DialogDescription>
            Chọn item và cấu hình thông tin drop
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-hidden">
          {/* Item Selection */}
          <div className="space-y-4">
            <div className="space-y-4">
              <h3 className="font-medium">Tìm kiếm Item</h3>
              
              {/* Search Filters */}
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-xs">Tên item</Label>
                  <Input
                    placeholder="Tên item..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Type</Label>
                  <Input
                    type="number"
                    placeholder="Type"
                    value={itemType}
                    onChange={(e) => setItemType(e.target.value)}
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Min Level</Label>
                  <Input
                    type="number"
                    placeholder="Min"
                    value={minLevel}
                    onChange={(e) => setMinLevel(e.target.value)}
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Max Level</Label>
                  <Input
                    type="number"
                    placeholder="Max"
                    value={maxLevel}
                    onChange={(e) => setMaxLevel(e.target.value)}
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Reside 1</Label>
                  <Input
                    type="number"
                    placeholder="Reside 1"
                    value={reside1}
                    onChange={(e) => setReside1(e.target.value)}
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Reside 2</Label>
                  <Input
                    type="number"
                    placeholder="Reside 2"
                    value={reside2}
                    onChange={(e) => setReside2(e.target.value)}
                    className="h-8"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleSearch} size="sm" className="flex-1">
                  <Search className="h-4 w-4 mr-2" />
                  Tìm kiếm
                </Button>
                <Button onClick={loadItems} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Item List */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Danh sách Items ({items.length})</h3>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1 || loading}
                    size="sm"
                    variant="outline"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    {currentPage} / {totalPages}
                  </span>
                  <Button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages || loading}
                    size="sm"
                    variant="outline"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-1 max-h-[300px] overflow-y-auto border rounded-lg p-2">
                {loading ? (
                  <div className="text-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                    <p className="text-sm">Đang tải...</p>
                  </div>
                ) : items.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="h-6 w-6 mx-auto mb-2" />
                    <p className="text-sm">Không có item nào</p>
                  </div>
                ) : (
                  items.map((item) => (
                    <div
                      key={item.fldPid}
                      className={`
                        p-2 border rounded cursor-pointer transition-all hover:bg-accent
                        ${selectedItem?.fldPid === item.fldPid 
                          ? 'bg-primary/10 border-primary' 
                          : 'bg-card'
                        }
                      `}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-muted rounded flex items-center justify-center overflow-hidden">
                          <img
                            src={`http://one.chamthoi.com/item/${item.fldPid}.jpg`}
                            alt={item.fldName}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.parentElement!.innerHTML = '<Package class="h-4 w-4 text-muted-foreground" />';
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm truncate">
                            {magicHandler.enConvert(item.fldName || `Item ${item.fldPid}`)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ID: {item.fldPid} • Lv: {item.fldLevel} • Type: {item.fldType}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Drop Configuration */}
          <div className="space-y-4">
            <h3 className="font-medium">Cấu hình Drop</h3>
            
            {selectedItem && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-8 h-8 bg-background rounded overflow-hidden">
                    <img
                      src={`http://one.chamthoi.com/item/${selectedItem.fldPid}.jpg`}
                      alt={selectedItem.fldName}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.parentElement!.innerHTML = '<Package class="h-4 w-4 text-muted-foreground" />';
                      }}
                    />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{magicHandler.enConvert(selectedItem.fldName)}</div>
                    <div className="text-xs text-muted-foreground">ID: {selectedItem.fldPid}</div>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-3 max-h-[400px] overflow-y-auto">
              <div className="space-y-1">
                <Label className="text-xs">Level 1 *</Label>
                <Input
                  type="number"
                  placeholder="Min level"
                  value={dropLevel1}
                  onChange={(e) => setDropLevel1(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Level 2 *</Label>
                <Input
                  type="number"
                  placeholder="Max level"
                  value={dropLevel2}
                  onChange={(e) => setDropLevel2(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Tỉ lệ *</Label>
                <Input
                  type="number"
                  placeholder="Drop rate"
                  value={dropRate}
                  onChange={(e) => setDropRate(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 0</Label>
                <Input
                  type="number"
                  value={dropMagic0}
                  onChange={(e) => setDropMagic0(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 1</Label>
                <Input
                  type="number"
                  value={dropMagic1}
                  onChange={(e) => setDropMagic1(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 2</Label>
                <Input
                  type="number"
                  value={dropMagic2}
                  onChange={(e) => setDropMagic2(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 3</Label>
                <Input
                  type="number"
                  value={dropMagic3}
                  onChange={(e) => setDropMagic3(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 4</Label>
                <Input
                  type="number"
                  value={dropMagic4}
                  onChange={(e) => setDropMagic4(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Số cấp phù hồn</Label>
                <Input
                  type="number"
                  value={dropSocapphuhon}
                  onChange={(e) => setDropSocapphuhon(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Trung cấp phù hồn</Label>
                <Input
                  type="number"
                  value={dropTrungcapphuhon}
                  onChange={(e) => setDropTrungcapphuhon(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Tiến hóa</Label>
                <Input
                  type="number"
                  value={dropTienhoa}
                  onChange={(e) => setDropTienhoa(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Khóa lại</Label>
                <Input
                  type="number"
                  value={dropKhoalai}
                  onChange={(e) => setDropKhoalai(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Ngày</Label>
                <Input
                  type="number"
                  value={dropDays}
                  onChange={(e) => setDropDays(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Có thông báo</Label>
                <Input
                  type="number"
                  value={comothongbao}
                  onChange={(e) => setComothongbao(e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="col-span-2 space-y-1">
                <Label className="text-xs">Sunx</Label>
                <Input
                  placeholder="Sunx value"
                  value={dropSunx}
                  onChange={(e) => setDropSunx(e.target.value)}
                  className="h-8"
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button 
            onClick={handleAddDropItem}
            disabled={!selectedItem || !dropLevel1 || !dropLevel2 || !dropRate}
          >
            <Plus className="h-4 w-4 mr-2" />
            Thêm Drop Item
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
