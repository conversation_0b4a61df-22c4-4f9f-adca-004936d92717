'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Plus,
  Trash2,
  Edit,
  Package,
  RefreshCw,
  Filter
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { DropItemSelectorDialog } from './drop-item-selector-dialog';
import { MagicHandler } from '@/lib/items';
import { DropTemplateLoadingSkeleton } from './template-loading-skeletons';

interface DropItem {
  fldLevel1: number;
  fldLevel2: number;
  fldPid: number;
  fldName: string;
  fldMagic0: number;
  fldMagic1: number;
  fldMagic2: number;
  fldMagic3: number;
  fldMagic4: number;
  fldSocapphuhon: number;
  fldTrungcapphuhon: number;
  fldTienhoa: number;
  fldKhoalai: number;
  fldPp: number;
  fldSunx: string;
  comothongbao: number;
  fldDays: number;
  // Item details from join
  itemName: string;
  itemLevel: number;
  itemReside1: number;
  itemReside2: number;
  itemType: number;
}

interface DropItemDetail extends DropItem {
  itemSex: number;
  itemUpLevel: number;
  itemRecycleMoney: number;
  itemSaleMoney: number;
  itemQuestitem: number;
  itemNj: number;
  itemDf: number;
  itemAt1: number;
  itemAt2: number;
  itemAp: number;
  itemJobLevel: number;
  itemZx: number;
  itemEl: number;
  itemWx: number;
  itemWxjd: number;
  itemMoney: number;
  itemWeight: number;
  itemNeedMoney: number;
  itemNeedFightexp: number;
  itemMagic1: number;
  itemMagic2: number;
  itemMagic3: number;
  itemMagic4: number;
  itemMagic5: number;
  itemSide: number;
  itemSellType: number;
  itemLock: number;
  itemSeries: number;
  itemIntegration: number;
  itemDes: string;
  itemHeadWear: number;
}

export function DropTemplateManager() {
  const [drops, setDrops] = useState<DropItem[]>([]);
  const [selectedDrop, setSelectedDrop] = useState<DropItem | null>(null);
  const [dropDetail, setDropDetail] = useState<DropItemDetail | null>(null);
  const magicHandler = new MagicHandler();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [minLevel, setMinLevel] = useState('');
  const [maxLevel, setMaxLevel] = useState('');
  const [isItemSelectorOpen, setIsItemSelectorOpen] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [dropToDelete, setDropToDelete] = useState<DropItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<DropItemDetail>>({});

  // Load drops data
  const loadDrops = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '50'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (minLevel) params.append('minLevel', minLevel);
      if (maxLevel) params.append('maxLevel', maxLevel);

      const response = await fetch(`/api/template/drops?${params}`);
      const result = await response.json();

      if (result.success) {
        setDrops(result.data.drops);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        toast.error(result.message || 'Failed to load drops');
      }
    } catch (error) {
      console.error('Error loading drops:', error);
      toast.error('Failed to load drops');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, minLevel, maxLevel]);

  // Load drop detail
  const loadDropDetail = useCallback(async (drop: DropItem) => {
    try {
      const response = await fetch(`/api/template/drops/${drop.fldLevel1}/${drop.fldLevel2}/${drop.fldPid}`);
      const result = await response.json();

      if (result.success) {
        setDropDetail(result.data);
      } else {
        toast.error(result.message || 'Failed to load drop detail');
      }
    } catch (error) {
      console.error('Error loading drop detail:', error);
      toast.error('Failed to load drop detail');
    }
  }, []);

  // Handle drop selection
  const handleDropSelect = (drop: DropItem) => {
    setSelectedDrop(drop);
    setIsEditing(false);
    loadDropDetail(drop);
  };

  // Handle start editing
  const handleStartEdit = () => {
    if (dropDetail) {
      setEditForm({ ...dropDetail });
      setIsEditing(true);
    }
  };

  // Handle cancel editing
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm({});
  };

  // Handle update drop item
  const handleUpdateDrop = async () => {
    if (!selectedDrop || !editForm) return;

    try {
      const response = await fetch('/api/template/drops', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          originalLevel1: selectedDrop.fldLevel1,
          originalLevel2: selectedDrop.fldLevel2,
          originalPid: selectedDrop.fldPid,
          fldLevel1: editForm.fldLevel1,
          fldLevel2: editForm.fldLevel2,
          fldPid: editForm.fldPid,
          fldName: editForm.fldName,
          fldMagic0: editForm.fldMagic0,
          fldMagic1: editForm.fldMagic1,
          fldMagic2: editForm.fldMagic2,
          fldMagic3: editForm.fldMagic3,
          fldMagic4: editForm.fldMagic4,
          fldSocapphuhon: editForm.fldSocapphuhon,
          fldTrungcapphuhon: editForm.fldTrungcapphuhon,
          fldTienhoa: editForm.fldTienhoa,
          fldKhoalai: editForm.fldKhoalai,
          fldPp: editForm.fldPp,
          fldSunx: editForm.fldSunx,
          comothongbao: editForm.comothongbao,
          fldDays: editForm.fldDays
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Drop item updated successfully');
        setIsEditing(false);
        setEditForm({});
        loadDrops();
        // Reload detail with updated data
        if (editForm.fldLevel1 && editForm.fldLevel2 && editForm.fldPid) {
          const updatedDrop = {
            ...selectedDrop,
            fldLevel1: editForm.fldLevel1,
            fldLevel2: editForm.fldLevel2,
            fldPid: editForm.fldPid
          };
          loadDropDetail(updatedDrop);
        }
      } else {
        toast.error(result.message || 'Failed to update drop item');
      }
    } catch (error) {
      console.error('Error updating drop:', error);
      toast.error('Failed to update drop item');
    }
  };

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    loadDrops();
  };

  // Handle delete drop
  const handleDeleteDrop = async () => {
    if (!dropToDelete) return;

    try {
      const response = await fetch('/api/template/drops', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fldLevel1: dropToDelete.fldLevel1,
          fldLevel2: dropToDelete.fldLevel2,
          fldPid: dropToDelete.fldPid
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Drop item deleted successfully');
        setShowDeleteDialog(false);
        setDropToDelete(null);
        if (selectedDrop && 
            selectedDrop.fldLevel1 === dropToDelete.fldLevel1 &&
            selectedDrop.fldLevel2 === dropToDelete.fldLevel2 &&
            selectedDrop.fldPid === dropToDelete.fldPid) {
          setSelectedDrop(null);
          setDropDetail(null);
        }
        loadDrops();
      } else {
        toast.error(result.message || 'Failed to delete drop item');
      }
    } catch (error) {
      console.error('Error deleting drop:', error);
      toast.error('Failed to delete drop item');
    }
  };

  // Handle add item success
  const handleAddItemSuccess = () => {
    loadDrops();
    toast.success('Drop item added successfully');
  };

  useEffect(() => {
    loadDrops();
  }, [loadDrops]);

  // Show loading skeleton on initial load
  if (loading && drops.length === 0) {
    return <DropTemplateLoadingSkeleton />;
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Bộ lọc và tìm kiếm
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Tìm kiếm</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Tên item hoặc ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button onClick={handleSearch} size="sm">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Level tối thiểu</Label>
              <Input
                type="number"
                placeholder="Min level"
                value={minLevel}
                onChange={(e) => setMinLevel(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Level tối đa</Label>
              <Input
                type="number"
                placeholder="Max level"
                value={maxLevel}
                onChange={(e) => setMaxLevel(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Thao tác</Label>
              <div className="flex gap-2">
                <Button onClick={() => setIsItemSelectorOpen(true)} className="flex-1">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm
                </Button>
                <Button onClick={loadDrops} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Drop List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Danh sách Drop Items ({drops.length})
              </span>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1 || loading}
                  size="sm"
                  variant="outline"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  {currentPage} / {totalPages}
                </span>
                <Button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages || loading}
                  size="sm"
                  variant="outline"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-[600px] overflow-y-auto">
              {loading ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p>Đang tải...</p>
                </div>
              ) : drops.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-8 w-8 mx-auto mb-2" />
                  <p>Không có drop item nào</p>
                </div>
              ) : (
                drops.map((drop, index) => (
                  <div
                    key={`${drop.fldLevel1}-${drop.fldLevel2}-${drop.fldPid}`}
                    className={`
                      p-3 border rounded-lg cursor-pointer transition-all hover:bg-accent
                      ${selectedDrop && 
                        selectedDrop.fldLevel1 === drop.fldLevel1 &&
                        selectedDrop.fldLevel2 === drop.fldLevel2 &&
                        selectedDrop.fldPid === drop.fldPid
                        ? 'bg-primary/10 border-primary' 
                        : 'bg-card'
                      }
                    `}
                    onClick={() => handleDropSelect(drop)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium">
                          {magicHandler.enConvert(drop.itemName || drop.fldName || `Item ${drop.fldPid}`)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Level {drop.fldLevel1}-{drop.fldLevel2} • ID: {drop.fldPid} • Tỉ lệ: {drop.fldPp}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          {drop.fldPp}
                        </Badge>
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            setDropToDelete(drop);
                            setShowDeleteDialog(true);
                          }}
                          size="sm"
                          variant="ghost"
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Drop Detail */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Chi tiết Drop Item</span>
              {selectedDrop && dropDetail && (
                <div className="flex gap-2">
                  {!isEditing ? (
                    <Button onClick={handleStartEdit} size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Chỉnh sửa
                    </Button>
                  ) : (
                    <>
                      <Button onClick={handleCancelEdit} variant="outline" size="sm">
                        Hủy
                      </Button>
                      <Button onClick={handleUpdateDrop} size="sm">
                        Cập nhật
                      </Button>
                    </>
                  )}
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedDrop ? (
              <div className="text-center py-12 text-muted-foreground">
                <Package className="h-12 w-12 mx-auto mb-4" />
                <p>Chọn một drop item để xem chi tiết</p>
              </div>
            ) : !dropDetail ? (
              <div className="text-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                <p>Đang tải chi tiết...</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Item Image and Basic Info */}
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                    <img
                      src={`http://one.chamthoi.com/item/${dropDetail.fldPid}.jpg`}
                      alt={dropDetail.itemName || dropDetail.fldName}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.parentElement!.innerHTML = '<Package class="h-8 w-8 text-muted-foreground" />';
                      }}
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">
                      {magicHandler.enConvert(dropDetail.itemName || dropDetail.fldName || `Item ${dropDetail.fldPid}`)}
                    </h3>
                    <p className="text-sm text-muted-foreground">ID: {dropDetail.fldPid}</p>
                    <div className="flex gap-2 mt-2">
                      <Badge>Level {dropDetail.fldLevel1}-{dropDetail.fldLevel2}</Badge>
                      <Badge variant="secondary">Tỉ lệ: {dropDetail.fldPp}</Badge>
                    </div>
                  </div>
                </div>

                {/* Drop Properties */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Thuộc tính Drop</Label>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Level 1</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldLevel1 || ''}
                            onChange={(e) => setEditForm({...editForm, fldLevel1: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldLevel1}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Level 2</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldLevel2 || ''}
                            onChange={(e) => setEditForm({...editForm, fldLevel2: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldLevel2}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Item ID</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldPid || ''}
                            onChange={(e) => setEditForm({...editForm, fldPid: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldPid}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Tỉ lệ</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldPp || ''}
                            onChange={(e) => setEditForm({...editForm, fldPp: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldPp}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 0</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic0 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic0: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldMagic0}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 1</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic1 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic1: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldMagic1}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 2</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic2 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic2: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldMagic2}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 3</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic3 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic3: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldMagic3}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 4</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic4 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic4: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldMagic4}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Sơ cấp phù hồn</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldSocapphuhon || ''}
                            onChange={(e) => setEditForm({...editForm, fldSocapphuhon: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldSocapphuhon}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Trung cấp phù hồn</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldTrungcapphuhon || ''}
                            onChange={(e) => setEditForm({...editForm, fldTrungcapphuhon: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldTrungcapphuhon}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Tiến hóa</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldTienhoa || ''}
                            onChange={(e) => setEditForm({...editForm, fldTienhoa: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldTienhoa}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Khóa lại</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldKhoalai || ''}
                            onChange={(e) => setEditForm({...editForm, fldKhoalai: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldKhoalai}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Ngày</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldDays || ''}
                            onChange={(e) => setEditForm({...editForm, fldDays: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldDays}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Có thông báo</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.comothongbao || ''}
                            onChange={(e) => setEditForm({...editForm, comothongbao: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.comothongbao}</div>
                        )}
                      </div>
                      <div className="col-span-2 space-y-1">
                        <Label className="text-xs">Sunx</Label>
                        {isEditing ? (
                          <Input
                            value={editForm.fldSunx || ''}
                            onChange={(e) => setEditForm({...editForm, fldSunx: e.target.value})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.fldSunx || '-'}</div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Thông tin Item (chỉ đọc)</Label>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Level</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemLevel}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Type</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemType}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Reside 1</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemReside1}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Reside 2</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemReside2}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Job Level</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemJobLevel}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Nội công</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemNj}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Đánh phá</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemDf}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Công kích 1</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemAt1}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Công kích 2</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemAt2}</div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Ám phá</Label>
                        <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{dropDetail.itemAp}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {dropDetail.itemDes && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Mô tả</Label>
                    <p className="text-sm text-muted-foreground bg-muted p-2 rounded">
                      {dropDetail.itemDes}
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Item Selector Dialog */}
      <DropItemSelectorDialog
        open={isItemSelectorOpen}
        onOpenChange={setIsItemSelectorOpen}
        onItemSelect={handleAddItemSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa drop item này không? Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          {dropToDelete && (
            <div className="py-4">
              <p className="font-medium">
                {magicHandler.enConvert(dropToDelete.itemName || dropToDelete.fldName || `Item ${dropToDelete.fldPid}`)}
              </p>
              <p className="text-sm text-muted-foreground">
                Level {dropToDelete.fldLevel1}-{dropToDelete.fldLevel2} • Tỉ lệ: {dropToDelete.fldPp}
              </p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDeleteDrop}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
