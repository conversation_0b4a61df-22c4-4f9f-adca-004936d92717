'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  TemplateManagerWrapper,
  TabDataLoading,
  useDataLoading 
} from './loading';

/**
 * Ví dụ về cách sử dụng loading components trong template manager
 * Có thể áp dụng pattern này cho các template manager khác
 */
export function ExampleTemplateManager() {
  const { isDataLoading, startLoading, stopLoading, setLoadingError } = useDataLoading();
  const [data, setData] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Simulate data loading
  const loadData = async () => {
    startLoading();
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData([
        { id: 1, name: 'Item 1', description: 'Description 1' },
        { id: 2, name: 'Item 2', description: 'Description 2' },
        { id: 3, name: 'Item 3', description: 'Description 3' },
      ]);
      stopLoading();
    } catch {
      setLoadingError('Không thể tải dữ liệu');
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleSearch = async (term: string) => {
    setSearchTerm(term);
    if (term.length > 2) {
      startLoading();
      // Simulate search API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Filter data based on search term
      const filteredData = data.filter(item => 
        item.name.toLowerCase().includes(term.toLowerCase())
      );
      setData(filteredData);
      stopLoading();
    } else if (term.length === 0) {
      loadData(); // Reload all data
    }
  };

  return (
    <TemplateManagerWrapper isLoading={false}>
      <div className="grid grid-cols-12 gap-6">
        {/* Left Column - Search and List */}
        <div className="col-span-4">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Template Items</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <Input
                placeholder="Tìm kiếm..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />

              {/* Data List */}
              {isDataLoading ? (
                <TabDataLoading message="Đang tìm kiếm..." />
              ) : (
                <div className="space-y-2 h-[calc(100vh-400px)] overflow-y-auto">
                  {data.map((item) => (
                    <div key={item.id} className="p-3 rounded-lg border hover:bg-accent cursor-pointer">
                      <h4 className="font-medium">{item.name}</h4>
                      <p className="text-sm text-muted-foreground">{item.description}</p>
                    </div>
                  ))}
                  {data.length === 0 && !isDataLoading && (
                    <div className="text-center py-8 text-muted-foreground">
                      Không tìm thấy dữ liệu
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Details */}
        <div className="col-span-8">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Chi tiết</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                Chọn một item để xem chi tiết
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={loadData} disabled={isDataLoading}>
          Làm mới
        </Button>
        <Button onClick={() => console.log('Add new item')}>
          Thêm mới
        </Button>
      </div>
    </TemplateManagerWrapper>
  );
}
