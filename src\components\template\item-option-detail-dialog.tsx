'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Trash2,
  Save,
  AlertCircle,
  Wrench
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { ItemOption, UpdateItemOptionRequest, ITEM_OPTION_FIELD_LABELS, ITEM_OPTION_FIELD_CATEGORIES } from '@/types/item-option';

interface ItemOptionDetailDialogProps {
  itemOption: ItemOption | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onItemOptionUpdated: () => void;
  onDeleteRequest: () => void;
}

export const ItemOptionDetailDialog: React.FC<ItemOptionDetailDialogProps> = ({
  itemOption,
  open,
  onOpenChange,
  onItemOptionUpdated,
  onDeleteRequest
}) => {
  const [formData, setFormData] = useState<UpdateItemOptionRequest>({});
  const [saving, setSaving] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Initialize form data when item option changes
  useEffect(() => {
    if (itemOption) {
      setFormData({ ...itemOption });
      setImageError(false);
    }
  }, [itemOption]);

  // Handle form field changes
  const handleFieldChange = (field: keyof UpdateItemOptionRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle save
  const handleSave = async () => {
    if (!itemOption) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/template/item-options/${itemOption.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã cập nhật item option thành công');
        onItemOptionUpdated();
        onOpenChange(false);
      } else {
        toast.error(result.message || 'Không thể cập nhật item option');
      }
    } catch (error) {
      console.error('Error updating item option:', error);
      toast.error('Có lỗi xảy ra khi cập nhật item option');
    } finally {
      setSaving(false);
    }
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
  };

  if (!itemOption) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[1000px] h-[700px] max-w-[95vw] max-h-[95vh] overflow-y-auto flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            Chi tiết Item Option: {itemOption.fldName || `Item Option ${itemOption.fldPid}`}
          </DialogTitle>
          <DialogDescription>
            Chỉnh sửa thông tin chi tiết của item option
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col justify-start gap-4 flex-grow">
          {/* Item Option Preview */}
          <div className="flex items-start gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="relative">
              {itemOption.fldPid && !imageError ? (
                <img
                  src={`http://one.chamthoi.com/item/${itemOption.fldPid}.jpg`}
                  alt={itemOption.fldName || `Item Option ${itemOption.fldPid}`}
                  className="w-16 h-16 object-cover rounded border-2"
                  onError={handleImageError}
                />
              ) : (
                <div className="w-16 h-16 bg-muted rounded border-2 flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{itemOption.fldName || `Item Option ${itemOption.fldPid}`}</h3>
              <p className="text-sm text-muted-foreground">ID: {itemOption.fldPid || 'N/A'}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline">Item Option</Badge>
                {itemOption.bonusAtk && itemOption.bonusAtk > 0 && (
                  <Badge variant="secondary">ATK: +{itemOption.bonusAtk}</Badge>
                )}
              </div>
            </div>
          </div>

          {/* Form Fields in Tabs */}
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Cơ bản</TabsTrigger>
              <TabsTrigger value="hpMp">HP/MP</TabsTrigger>
              <TabsTrigger value="combat">Chiến đấu</TabsTrigger>
              <TabsTrigger value="advanced">Nâng cao</TabsTrigger>
              <TabsTrigger value="special">Đặc biệt</TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fldPid">Item ID *</Label>
                  <Input
                    id="fldPid"
                    type="number"
                    value={formData.fldPid || ''}
                    onChange={(e) => handleFieldChange('fldPid', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fldName">Tên Item</Label>
                  <Input
                    id="fldName"
                    value={formData.fldName || ''}
                    onChange={(e) => handleFieldChange('fldName', e.target.value)}
                  />
                </div>
              </div>
            </TabsContent>

            {/* HP/MP Tab */}
            <TabsContent value="hpMp" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {ITEM_OPTION_FIELD_CATEGORIES.hpMp.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{ITEM_OPTION_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Combat Tab */}
            <TabsContent value="combat" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {ITEM_OPTION_FIELD_CATEGORIES.combat.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{ITEM_OPTION_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {ITEM_OPTION_FIELD_CATEGORIES.advanced.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{ITEM_OPTION_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Special Tab */}
            <TabsContent value="special" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {ITEM_OPTION_FIELD_CATEGORIES.special.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{ITEM_OPTION_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-end">
          <Button
            variant="destructive"
            onClick={onDeleteRequest}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Xóa Item Option
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
