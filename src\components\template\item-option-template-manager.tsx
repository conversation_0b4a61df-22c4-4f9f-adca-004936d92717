'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Plus,
  Trash2,
  Filter,
  RefreshCw,
  Package,
  AlertCircle,
  Wrench
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ItemOption, ItemOptionFilter, ITEM_OPTION_FIELD_LABELS, SORTABLE_ITEM_OPTION_FIELDS } from '@/types/item-option';
import { ItemOptionDetailDialog } from './item-option-detail-dialog';
import { ItemOptionAddDialog } from './item-option-add-dialog';

const ItemOptionTemplateManager = () => {
  // State management
  const [itemOptions, setItemOptions] = useState<ItemOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItemOption, setSelectedItemOption] = useState<ItemOption | null>(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(100);
  
  // Filter state
  const [filters, setFilters] = useState<ItemOptionFilter>({
    search: '',
    sortBy: 'id',
    sortOrder: 'asc'
  });
  
  // Dialog states
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  // Image error tracking
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  // Load item options data
  const loadItemOptions = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.fldPid && { fldPid: filters.fldPid.toString() }),
        ...(filters.sortBy && { sortBy: filters.sortBy }),
        ...(filters.sortOrder && { sortOrder: filters.sortOrder })
      });

      const response = await fetch(`/api/template/item-options?${params}`);
      const result = await response.json();

      if (result.success) {
        setItemOptions(result.data.itemOptions);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        toast.error(result.message || 'Không thể tải danh sách item options');
      }
    } catch (error) {
      console.error('Error loading item options:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách item options');
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, filters]);

  // Handle image error
  const handleImageError = (fldPid: number) => {
    setImageErrors(prev => new Set(prev).add(fldPid));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof ItemOptionFilter, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle search
  const handleSearch = (value: string) => {
    handleFilterChange('search', value);
  };

  // Handle item option selection
  const handleItemOptionSelect = (itemOption: ItemOption) => {
    setSelectedItemOption(itemOption);
    setShowDetailDialog(true);
  };

  // Handle pill updated
  const handleItemOptionUpdated = () => {
    loadItemOptions();
  };

  // Handle delete request from detail dialog
  const handleDeleteRequest = () => {
    setShowDetailDialog(false);
    setShowDeleteDialog(true);
  };

  // Handle pill created
  const handleItemOptionCreated = () => {
    loadItemOptions();
  };

  // Handle delete item option
  const handleDeleteItemOption = async () => {
    if (!selectedItemOption) return;

    try {
      const response = await fetch(`/api/template/item-options/${selectedItemOption.id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã xóa item option thành công');
        setShowDeleteDialog(false);
        setShowDetailDialog(false);
        setSelectedItemOption(null);
        loadItemOptions();
      } else {
        toast.error(result.message || 'Không thể xóa item option');
      }
    } catch (error) {
      console.error('Error deleting item option:', error);
      toast.error('Có lỗi xảy ra khi xóa item option');
    }
  };

  // Load item options on component mount and when dependencies change
  useEffect(() => {
    loadItemOptions();
  }, [loadItemOptions]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Wrench className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold">Quản lý Item Option Template</h1>
        </div>
        <Button onClick={() => setShowAddDialog(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Thêm Item Option mới
        </Button>
      </div>

      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Bộ lọc và tìm kiếm
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <Label>Tìm kiếm</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm theo tên hoặc ID..."
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Item ID Filter */}
            <div className="space-y-2">
              <Label>Item ID</Label>
              <Input
                type="number"
                placeholder="Nhập Item ID..."
                value={filters.fldPid || ''}
                onChange={(e) => handleFilterChange('fldPid', e.target.value ? parseInt(e.target.value) : undefined)}
              />
            </div>

            {/* Sort */}
            <div className="space-y-2 col-span-2">
              <Label>Sắp xếp</Label>
              <div className="flex gap-2">
                <Select
                  value={filters.sortBy || 'id'}
                  onValueChange={(value) => handleFilterChange('sortBy', value)}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SORTABLE_ITEM_OPTION_FIELDS.map(field => (
                      <SelectItem key={field} value={field}>
                        {ITEM_OPTION_FIELD_LABELS[field] || field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={filters.sortOrder || 'asc'}
                  onValueChange={(value) => handleFilterChange('sortOrder', value as 'asc' | 'desc')}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">↑</SelectItem>
                    <SelectItem value="desc">↓</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Item Options Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Danh sách Item Options
              <Badge variant="secondary">{itemOptions.length} item options</Badge>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadItemOptions}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Làm mới
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin" />
              <span className="ml-2">Đang tải...</span>
            </div>
          ) : itemOptions.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Không tìm thấy item option nào</p>
            </div>
          ) : (
            <>
              {/* Item Options Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-4 mb-6">
                {itemOptions.map((itemOption) => (
                  <div
                    key={itemOption.id}
                    className="relative group cursor-pointer"
                    onClick={() => handleItemOptionSelect(itemOption)}
                  >
                    <div className="aspect-square border-2 border-muted rounded-lg overflow-hidden hover:border-primary transition-colors bg-card">
                      {itemOption.fldPid && !imageErrors.has(itemOption.fldPid) ? (
                        <img
                          src={`http://one.chamthoi.com/item/${itemOption.fldPid}.jpg`}
                          alt={itemOption.fldName || `Item Option ${itemOption.fldPid}`}
                          className="w-full h-full object-cover"
                          onError={() => itemOption.fldPid && handleImageError(itemOption.fldPid)}
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <AlertCircle className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}

                      {/* Overlay with item option info */}
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col justify-end p-2">
                        <div className="text-white text-xs">
                          <div className="font-medium truncate">
                            {itemOption.fldName || `Item Option ${itemOption.fldPid}`}
                          </div>
                          <div className="text-xs opacity-80">
                            ID: {itemOption.fldPid || 'N/A'}
                          </div>
                          {itemOption.bonusAtk && itemOption.bonusAtk > 0 && (
                            <div className="text-xs opacity-80">
                              ATK: +{itemOption.bonusAtk}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Trang {currentPage} / {totalPages}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1 || loading}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Trước
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages || loading}
                  >
                    Sau
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Item Option Add Dialog */}
      <ItemOptionAddDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onItemOptionCreated={handleItemOptionCreated}
      />

      {/* Item Option Detail Dialog */}
      <ItemOptionDetailDialog
        itemOption={selectedItemOption}
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        onItemOptionUpdated={handleItemOptionUpdated}
        onDeleteRequest={handleDeleteRequest}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa item option {selectedItemOption?.fldName || selectedItemOption?.fldPid} không?
              Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDeleteItemOption}>
              <Trash2 className="h-4 w-4 mr-2" />
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ItemOptionTemplateManager;
