'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ItemSelectorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onItemSelect: () => void;
}

export function ItemSelectorDialog({
  open,
  onOpenChange,
  onItemSelect
}: ItemSelectorDialogProps) {
  // Form state for all item fields
  const [formData, setFormData] = useState({
    fldPid: '',
    fldName: '',
    fldReside1: '0',
    fldReside2: '0',
    fldSex: '0',
    fldLevel: '0',
    fldUpLevel: '0',
    fldRecycleMoney: '0',
    fldSaleMoney: '0',
    fldQuestitem: '0',
    fldNj: '0',
    fldDf: '0',
    fldAt1: '0',
    fldAt2: '0',
    fldAp: '0',
    fldJobLevel: '0',
    fldZx: '0',
    fldEl: '0',
    fldWx: '0',
    fldWxjd: '0',
    fldMoney: '0',
    fldWeight: '0',
    fldType: '0',
    fldNeedMoney: '0',
    fldNeedFightexp: '0',
    fldMagic1: '0',
    fldMagic2: '0',
    fldMagic3: '0',
    fldMagic4: '0',
    fldMagic5: '0',
    fldSide: '0',
    fldSellType: '0',
    fldLock: '0',
    fldSeries: '0',
    fldIntegration: '0',
    fldDes: '',
    fldHeadWear: '0'
  });

  // Handle form field changes
  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle add item
  const handleAddItem = async () => {
    if (!formData.fldPid || !formData.fldName) {
      toast.error('Vui lòng điền Item ID và tên item');
      return;
    }

    try {
      const response = await fetch('/api/template/game-items', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fldPid: parseInt(formData.fldPid),
          fldName: formData.fldName,
          fldReside1: parseInt(formData.fldReside1),
          fldReside2: parseInt(formData.fldReside2),
          fldSex: parseInt(formData.fldSex),
          fldLevel: parseInt(formData.fldLevel),
          fldUpLevel: parseInt(formData.fldUpLevel),
          fldRecycleMoney: parseInt(formData.fldRecycleMoney),
          fldSaleMoney: parseInt(formData.fldSaleMoney),
          fldQuestitem: parseInt(formData.fldQuestitem),
          fldNj: parseInt(formData.fldNj),
          fldDf: parseInt(formData.fldDf),
          fldAt1: parseInt(formData.fldAt1),
          fldAt2: parseInt(formData.fldAt2),
          fldAp: parseInt(formData.fldAp),
          fldJobLevel: parseInt(formData.fldJobLevel),
          fldZx: parseInt(formData.fldZx),
          fldEl: parseInt(formData.fldEl),
          fldWx: parseInt(formData.fldWx),
          fldWxjd: parseInt(formData.fldWxjd),
          fldMoney: parseInt(formData.fldMoney),
          fldWeight: parseInt(formData.fldWeight),
          fldType: parseInt(formData.fldType),
          fldNeedMoney: parseInt(formData.fldNeedMoney),
          fldNeedFightexp: parseInt(formData.fldNeedFightexp),
          fldMagic1: parseInt(formData.fldMagic1),
          fldMagic2: parseInt(formData.fldMagic2),
          fldMagic3: parseInt(formData.fldMagic3),
          fldMagic4: parseInt(formData.fldMagic4),
          fldMagic5: parseInt(formData.fldMagic5),
          fldSide: parseInt(formData.fldSide),
          fldSellType: parseInt(formData.fldSellType),
          fldLock: parseInt(formData.fldLock),
          fldSeries: parseInt(formData.fldSeries),
          fldIntegration: parseInt(formData.fldIntegration),
          fldDes: formData.fldDes,
          fldHeadWear: parseInt(formData.fldHeadWear)
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Thêm item thành công');
        onItemSelect();
        onOpenChange(false);
        // Reset form
        setFormData({
          fldPid: '',
          fldName: '',
          fldReside1: '0',
          fldReside2: '0',
          fldSex: '0',
          fldLevel: '0',
          fldUpLevel: '0',
          fldRecycleMoney: '0',
          fldSaleMoney: '0',
          fldQuestitem: '0',
          fldNj: '0',
          fldDf: '0',
          fldAt1: '0',
          fldAt2: '0',
          fldAp: '0',
          fldJobLevel: '0',
          fldZx: '0',
          fldEl: '0',
          fldWx: '0',
          fldWxjd: '0',
          fldMoney: '0',
          fldWeight: '0',
          fldType: '0',
          fldNeedMoney: '0',
          fldNeedFightexp: '0',
          fldMagic1: '0',
          fldMagic2: '0',
          fldMagic3: '0',
          fldMagic4: '0',
          fldMagic5: '0',
          fldSide: '0',
          fldSellType: '0',
          fldLock: '0',
          fldSeries: '0',
          fldIntegration: '0',
          fldDes: '',
          fldHeadWear: '0'
        });
      } else {
        toast.error(result.message || 'Failed to add item');
      }
    } catch (error) {
      console.error('Error adding item:', error);
      toast.error('Failed to add item');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl lg:min-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Thêm Item Mới</DialogTitle>
          <DialogDescription>
            Điền thông tin để tạo item mới
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-y-auto max-h-[70vh] pr-2">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="font-medium">Thông tin cơ bản</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label className="text-xs">Item ID *</Label>
                <Input
                  type="number"
                  placeholder="Item ID"
                  value={formData.fldPid}
                  onChange={(e) => handleFieldChange('fldPid', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Level</Label>
                <Input
                  type="number"
                  value={formData.fldLevel}
                  onChange={(e) => handleFieldChange('fldLevel', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="col-span-2 space-y-1">
                <Label className="text-xs">Tên item *</Label>
                <Input
                  placeholder="Tên item"
                  value={formData.fldName}
                  onChange={(e) => handleFieldChange('fldName', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Type</Label>
                <Input
                  type="number"
                  value={formData.fldType}
                  onChange={(e) => handleFieldChange('fldType', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Sex</Label>
                <Input
                  type="number"
                  value={formData.fldSex}
                  onChange={(e) => handleFieldChange('fldSex', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Reside 1</Label>
                <Input
                  type="number"
                  value={formData.fldReside1}
                  onChange={(e) => handleFieldChange('fldReside1', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Reside 2</Label>
                <Input
                  type="number"
                  value={formData.fldReside2}
                  onChange={(e) => handleFieldChange('fldReside2', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Job Level</Label>
                <Input
                  type="number"
                  value={formData.fldJobLevel}
                  onChange={(e) => handleFieldChange('fldJobLevel', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Up Level</Label>
                <Input
                  type="number"
                  value={formData.fldUpLevel}
                  onChange={(e) => handleFieldChange('fldUpLevel', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Weight</Label>
                <Input
                  type="number"
                  value={formData.fldWeight}
                  onChange={(e) => handleFieldChange('fldWeight', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Money</Label>
                <Input
                  type="number"
                  value={formData.fldMoney}
                  onChange={(e) => handleFieldChange('fldMoney', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="col-span-2 space-y-1">
                <Label className="text-xs">Mô tả</Label>
                <Input
                  placeholder="Mô tả item"
                  value={formData.fldDes}
                  onChange={(e) => handleFieldChange('fldDes', e.target.value)}
                  className="h-8"
                />
              </div>
            </div>
          </div>

          {/* Combat Stats */}
          <div className="space-y-4">
            <h3 className="font-medium">Thuộc tính chiến đấu</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label className="text-xs">Nội công</Label>
                <Input
                  type="number"
                  value={formData.fldNj}
                  onChange={(e) => handleFieldChange('fldNj', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Đánh phá</Label>
                <Input
                  type="number"
                  value={formData.fldDf}
                  onChange={(e) => handleFieldChange('fldDf', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Công kích 1</Label>
                <Input
                  type="number"
                  value={formData.fldAt1}
                  onChange={(e) => handleFieldChange('fldAt1', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Công kích 2</Label>
                <Input
                  type="number"
                  value={formData.fldAt2}
                  onChange={(e) => handleFieldChange('fldAt2', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">AP</Label>
                <Input
                  type="number"
                  value={formData.fldAp}
                  onChange={(e) => handleFieldChange('fldAp', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 1</Label>
                <Input
                  type="number"
                  value={formData.fldMagic1}
                  onChange={(e) => handleFieldChange('fldMagic1', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 2</Label>
                <Input
                  type="number"
                  value={formData.fldMagic2}
                  onChange={(e) => handleFieldChange('fldMagic2', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 3</Label>
                <Input
                  type="number"
                  value={formData.fldMagic3}
                  onChange={(e) => handleFieldChange('fldMagic3', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 4</Label>
                <Input
                  type="number"
                  value={formData.fldMagic4}
                  onChange={(e) => handleFieldChange('fldMagic4', e.target.value)}
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Magic 5</Label>
                <Input
                  type="number"
                  value={formData.fldMagic5}
                  onChange={(e) => handleFieldChange('fldMagic5', e.target.value)}
                  className="h-8"
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button 
            onClick={handleAddItem}
            disabled={!formData.fldPid || !formData.fldName}
          >
            <Plus className="h-4 w-4 mr-2" />
            Thêm Item
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
