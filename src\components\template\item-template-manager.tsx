'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Plus,
  Trash2,
  Edit,
  Package,
  RefreshCw,
  Filter
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ItemSelectorDialog } from './item-selector-dialog';
import { MagicHandler } from '@/lib/items';
import { ItemTemplateLoadingSkeleton } from './template-loading-skeletons';

interface GameItem {
  fldPid: number;
  fldName: string;
  fldReside1: number;
  fldReside2: number;
  fldSex: number;
  fldLevel: number;
  fldUpLevel: number;
  fldRecycleMoney: number;
  fldSaleMoney: number;
  fldQuestitem: number;
  fldNj: number;
  fldDf: number;
  fldAt1: number;
  fldAt2: number;
  fldAp: number;
  fldJobLevel: number;
  fldZx: number;
  fldEl: number;
  fldWx: number;
  fldWxjd: number;
  fldMoney: number;
  fldWeight: number;
  fldType: number;
  fldNeedMoney: number;
  fldNeedFightexp: number;
  fldMagic1: number;
  fldMagic2: number;
  fldMagic3: number;
  fldMagic4: number;
  fldMagic5: number;
  fldSide: number;
  fldSellType: number;
  fldLock: number;
  fldSeries: number;
  fldIntegration: number;
  fldDes: string;
  fldHeadWear: number;
}

export function ItemTemplateManager() {
  const [items, setItems] = useState<GameItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<GameItem | null>(null);
  const [itemDetail, setItemDetail] = useState<GameItem | null>(null);
  const magicHandler = new MagicHandler();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [minLevel, setMinLevel] = useState('');
  const [maxLevel, setMaxLevel] = useState('');
  const [itemType, setItemType] = useState('');
  const [reside1, setReside1] = useState('');
  const [reside2, setReside2] = useState('');
  const [isItemSelectorOpen, setIsItemSelectorOpen] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<GameItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<GameItem>>({});

  // Load items data
  const loadItems = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '50'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (minLevel) params.append('minLevel', minLevel);
      if (maxLevel) params.append('maxLevel', maxLevel);
      if (itemType) params.append('fldType', itemType);
      if (reside1) params.append('fldReside1', reside1);
      if (reside2) params.append('fldReside2', reside2);

      const response = await fetch(`/api/template/game-items?${params}`);
      const result = await response.json();

      if (result.success) {
        setItems(result.data.items);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        toast.error(result.message || 'Failed to load items');
      }
    } catch (error) {
      console.error('Error loading items:', error);
      toast.error('Failed to load items');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, minLevel, maxLevel, itemType, reside1, reside2]);

  // Load item detail
  const loadItemDetail = useCallback(async (item: GameItem) => {
    try {
      const response = await fetch(`/api/template/game-items/${item.fldPid}`);
      const result = await response.json();

      if (result.success) {
        setItemDetail(result.data);
      } else {
        toast.error(result.message || 'Failed to load item detail');
      }
    } catch (error) {
      console.error('Error loading item detail:', error);
      toast.error('Failed to load item detail');
    }
  }, []);

  // Handle item selection
  const handleItemSelect = (item: GameItem) => {
    setSelectedItem(item);
    setIsEditing(false);
    loadItemDetail(item);
  };

  // Handle start editing
  const handleStartEdit = () => {
    if (itemDetail) {
      setEditForm({ ...itemDetail });
      setIsEditing(true);
    }
  };

  // Handle cancel editing
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm({});
  };

  // Handle update item
  const handleUpdateItem = async () => {
    if (!selectedItem || !editForm) return;

    try {
      const response = await fetch('/api/template/game-items', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          originalPid: selectedItem.fldPid,
          ...editForm
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Item updated successfully');
        setIsEditing(false);
        setEditForm({});
        loadItems();
        // Reload detail with updated data
        if (editForm.fldPid) {
          const updatedItem = { ...selectedItem, fldPid: editForm.fldPid };
          loadItemDetail(updatedItem);
        }
      } else {
        toast.error(result.message || 'Failed to update item');
      }
    } catch (error) {
      console.error('Error updating item:', error);
      toast.error('Failed to update item');
    }
  };

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    loadItems();
  };

  // Handle delete item
  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    try {
      const response = await fetch('/api/template/game-items', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fldPid: itemToDelete.fldPid
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Item deleted successfully');
        setShowDeleteDialog(false);
        setItemToDelete(null);
        if (selectedItem && selectedItem.fldPid === itemToDelete.fldPid) {
          setSelectedItem(null);
          setItemDetail(null);
        }
        loadItems();
      } else {
        toast.error(result.message || 'Failed to delete item');
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error('Failed to delete item');
    }
  };

  // Handle add item success
  const handleAddItemSuccess = () => {
    loadItems();
    toast.success('Item added successfully');
  };

  useEffect(() => {
    loadItems();
  }, [loadItems]);

  // Show loading skeleton on initial load
  if (loading && items.length === 0) {
    return <ItemTemplateLoadingSkeleton />;
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Bộ lọc và tìm kiếm
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
            <div className="space-y-2">
              <Label>Tìm kiếm</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Tên item hoặc ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button onClick={handleSearch} size="sm">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Level tối thiểu</Label>
              <Input
                type="number"
                placeholder="Min level"
                value={minLevel}
                onChange={(e) => setMinLevel(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Level tối đa</Label>
              <Input
                type="number"
                placeholder="Max level"
                value={maxLevel}
                onChange={(e) => setMaxLevel(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Type</Label>
              <Input
                type="number"
                placeholder="Item type"
                value={itemType}
                onChange={(e) => setItemType(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Reside 1</Label>
              <Input
                type="number"
                placeholder="Reside 1"
                value={reside1}
                onChange={(e) => setReside1(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Reside 2</Label>
              <Input
                type="number"
                placeholder="Reside 2"
                value={reside2}
                onChange={(e) => setReside2(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Thao tác</Label>
              <div className="flex gap-2">
                <Button onClick={() => setIsItemSelectorOpen(true)} className="flex-1">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm
                </Button>
                <Button onClick={loadItems} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Item List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Danh sách Items ({items.length})
              </span>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1 || loading}
                  size="sm"
                  variant="outline"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  {currentPage} / {totalPages}
                </span>
                <Button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages || loading}
                  size="sm"
                  variant="outline"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-[600px] overflow-y-auto">
              {loading ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p>Đang tải...</p>
                </div>
              ) : items.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-8 w-8 mx-auto mb-2" />
                  <p>Không có item nào</p>
                </div>
              ) : (
                items.map((item) => (
                  <div
                    key={item.fldPid}
                    className={`
                      p-3 border rounded-lg cursor-pointer transition-all hover:bg-accent
                      ${selectedItem && selectedItem.fldPid === item.fldPid
                        ? 'bg-primary/10 border-primary' 
                        : 'bg-card'
                      }
                    `}
                    onClick={() => handleItemSelect(item)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-muted rounded flex items-center justify-center overflow-hidden">
                          <img
                            src={`http://one.chamthoi.com/item/${item.fldPid}.jpg`}
                            alt={item.fldName}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.parentElement!.innerHTML = '<Package class="h-6 w-6 text-muted-foreground" />';
                            }}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">
                            {magicHandler.enConvert(item.fldName || `Item ${item.fldPid}`)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            ID: {item.fldPid} • Level: {item.fldLevel} • Type: {item.fldType}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          Lv.{item.fldLevel}
                        </Badge>
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            setItemToDelete(item);
                            setShowDeleteDialog(true);
                          }}
                          size="sm"
                          variant="ghost"
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Item Detail - Will be continued in next part */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Chi tiết Item</span>
              {selectedItem && itemDetail && (
                <div className="flex gap-2">
                  {!isEditing ? (
                    <Button onClick={handleStartEdit} size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Chỉnh sửa
                    </Button>
                  ) : (
                    <>
                      <Button onClick={handleCancelEdit} variant="outline" size="sm">
                        Hủy
                      </Button>
                      <Button onClick={handleUpdateItem} size="sm">
                        Cập nhật
                      </Button>
                    </>
                  )}
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedItem ? (
              <div className="text-center py-12 text-muted-foreground">
                <Package className="h-12 w-12 mx-auto mb-4" />
                <p>Chọn một item để xem chi tiết</p>
              </div>
            ) : !itemDetail ? (
              <div className="text-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                <p>Đang tải chi tiết...</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Item Image and Basic Info */}
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                    <img
                      src={`http://one.chamthoi.com/item/${itemDetail.fldPid}.jpg`}
                      alt={itemDetail.fldName}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.parentElement!.innerHTML = '<Package class="h-8 w-8 text-muted-foreground" />';
                      }}
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">
                      {magicHandler.enConvert(itemDetail.fldName || `Item ${itemDetail.fldPid}`)}
                    </h3>
                    <p className="text-sm text-muted-foreground">ID: {itemDetail.fldPid}</p>
                    <div className="flex gap-2 mt-2">
                      <Badge>Level {itemDetail.fldLevel}</Badge>
                      <Badge variant="secondary">Type: {itemDetail.fldType}</Badge>
                    </div>
                  </div>
                </div>

                {/* Item Properties */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Thông tin cơ bản</Label>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Item ID</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldPid || ''}
                            onChange={(e) => setEditForm({...editForm, fldPid: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldPid}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Level</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldLevel || ''}
                            onChange={(e) => setEditForm({...editForm, fldLevel: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldLevel}</div>
                        )}
                      </div>
                      <div className="col-span-2 space-y-1">
                        <Label className="text-xs">Tên item</Label>
                        {isEditing ? (
                          <Input
                            value={editForm.fldName || ''}
                            onChange={(e) => setEditForm({...editForm, fldName: e.target.value})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldName}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Type</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldType || ''}
                            onChange={(e) => setEditForm({...editForm, fldType: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldType}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Sex</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldSex || ''}
                            onChange={(e) => setEditForm({...editForm, fldSex: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldSex}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Reside 1</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldReside1 || ''}
                            onChange={(e) => setEditForm({...editForm, fldReside1: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldReside1}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Reside 2</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldReside2 || ''}
                            onChange={(e) => setEditForm({...editForm, fldReside2: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldReside2}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Job Level</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldJobLevel || ''}
                            onChange={(e) => setEditForm({...editForm, fldJobLevel: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldJobLevel}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Up Level</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldUpLevel || ''}
                            onChange={(e) => setEditForm({...editForm, fldUpLevel: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldUpLevel}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Weight</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldWeight || ''}
                            onChange={(e) => setEditForm({...editForm, fldWeight: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldWeight}</div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Thuộc tính chiến đấu</Label>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Nội công</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldNj || ''}
                            onChange={(e) => setEditForm({...editForm, fldNj: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldNj}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Đánh phá</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldDf || ''}
                            onChange={(e) => setEditForm({...editForm, fldDf: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldDf}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Công kích 1</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldAt1 || ''}
                            onChange={(e) => setEditForm({...editForm, fldAt1: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldAt1}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Công kích 2</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldAt2 || ''}
                            onChange={(e) => setEditForm({...editForm, fldAt2: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldAt2}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">AP</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldAp || ''}
                            onChange={(e) => setEditForm({...editForm, fldAp: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldAp}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 1</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic1 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic1: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldMagic1}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 2</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic2 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic2: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldMagic2}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 3</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic3 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic3: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldMagic3}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 4</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic4 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic4: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldMagic4}</div>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Magic 5</Label>
                        {isEditing ? (
                          <Input
                            type="number"
                            value={editForm.fldMagic5 || ''}
                            onChange={(e) => setEditForm({...editForm, fldMagic5: parseInt(e.target.value) || 0})}
                            className="h-8"
                          />
                        ) : (
                          <div className="h-8 px-3 py-2 border rounded-md bg-muted text-sm">{itemDetail.fldMagic5}</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {itemDetail.fldDes && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Mô tả</Label>
                    {isEditing ? (
                      <Input
                        value={editForm.fldDes || ''}
                        onChange={(e) => setEditForm({...editForm, fldDes: e.target.value})}
                        className="h-8"
                      />
                    ) : (
                      <p className="text-sm text-muted-foreground bg-muted p-2 rounded">
                        {itemDetail.fldDes}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Item Selector Dialog */}
      <ItemSelectorDialog
        open={isItemSelectorOpen}
        onOpenChange={setIsItemSelectorOpen}
        onItemSelect={handleAddItemSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa item này không? Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          {itemToDelete && (
            <div className="py-4">
              <p className="font-medium">
                {magicHandler.enConvert(itemToDelete.fldName || `Item ${itemToDelete.fldPid}`)}
              </p>
              <p className="text-sm text-muted-foreground">
                ID: {itemToDelete.fldPid} • Level: {itemToDelete.fldLevel}
              </p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDeleteItem}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
