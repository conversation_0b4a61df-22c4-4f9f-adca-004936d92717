# Template Loading Components

H<PERSON> thống loading components cho Template Management để cải thiện trải nghiệm người dùng khi chuyển tab và load data.

## Các Components

### 1. Loading Skeletons

#### `ShopTemplateLoadingSkeleton`
- Skeleton cho shop template manager
- Layout 2 cột: danh sách NPC + grid template

#### `ItemTemplateLoadingSkeleton`
- Skeleton cho item template manager
- Layout: filters + grid items + details panel

#### `NpcTemplateLoadingSkeleton`
- Skeleton cho NPC template manager
- Layout 2 cột: danh sách NPC + chi tiết NPC

#### `MapTemplateLoadingSkeleton`
- Skeleton cho map template manager
- Layout 2 cột: danh sách map + canvas

#### `DropTemplateLoadingSkeleton`
- Skeleton cho drop template manager
- Layout 2 cột: danh sách monster + drop items

#### `GenericTabLoadingSkeleton`
- Skeleton chung cho các tab TODO
- Hiển thị thông báo "sẽ được phát triển"

### 2. Transition Loading

#### `TemplateTransitionLoading`
- Loading cho việc chuyển tab
- Hiển thị layout cơ bản với animation

#### `QuickTabTransitionLoading`
- Loading nhanh cho chuyển tab
- Animation dots đơn giản

#### `TabDataLoading`
- Loading cho data trong tab
- Spinner + message tùy chỉnh

#### `TemplateManagementOverlay`
- Loading overlay cho toàn bộ page
- Backdrop blur + centered card

### 3. Wrapper Components

#### `TemplateTabWrapper`
- Wrapper tự động hiển thị loading khi chuyển tab
- Hỗ trợ quick loading và detailed skeleton

#### `TemplateManagerWrapper`
- Wrapper cho từng template manager
- Hiển thị loading khi fetch data

### 4. Hooks

#### `useTabLoading`
- Hook quản lý loading state khi chuyển tab
- Tự động detect tab change và hiển thị loading

#### `useDataLoading`
- Hook quản lý loading state cho data fetching
- Cung cấp startLoading, stopLoading, setError

## Cách sử dụng

### 1. Trong Page Component

```tsx
import { TemplateTabWrapper } from '@/components/template/loading';

export default function TemplateManagementPage() {
  const activeTab = searchParams.get('tab') || 'shop';

  return (
    <TemplateTabWrapper activeTab={activeTab} quickLoading={false}>
      {/* Nội dung tab */}
      {activeTab === 'shop' && <ShopTemplateManager />}
      {activeTab === 'items' && <ItemTemplateManager />}
    </TemplateTabWrapper>
  );
}
```

### 2. Trong Template Manager

```tsx
import { TemplateManagerWrapper, useDataLoading } from '@/components/template/loading';

export function ShopTemplateManager() {
  const { isDataLoading, startLoading, stopLoading } = useDataLoading();

  return (
    <TemplateManagerWrapper isLoading={isDataLoading}>
      {/* Nội dung manager */}
    </TemplateManagerWrapper>
  );
}
```

### 3. Loading cho Data Fetching

```tsx
import { TabDataLoading } from '@/components/template/loading';

function DataList({ isLoading, data }) {
  if (isLoading) {
    return <TabDataLoading message="Đang tải dữ liệu..." />;
  }

  return (
    <div>
      {data.map(item => <div key={item.id}>{item.name}</div>)}
    </div>
  );
}
```

### 4. Quick Loading cho Search

```tsx
import { QuickTabTransitionLoading } from '@/components/template/loading';

function SearchResults({ isSearching, results }) {
  if (isSearching) {
    return <QuickTabTransitionLoading />;
  }

  return <div>{/* Results */}</div>;
}
```

## Tính năng

### 1. Smooth Transitions
- Animation mượt mà khi chuyển tab
- Delay có thể tùy chỉnh (default 300ms)

### 2. Responsive Design
- Tất cả loading components đều responsive
- Tự động adapt với layout của page

### 3. Accessibility
- Proper ARIA labels
- Screen reader friendly

### 4. Performance
- Lazy loading cho heavy components
- Minimal re-renders

### 5. Customizable
- Có thể tùy chỉnh delay, message, style
- Support theme switching

## Best Practices

1. **Sử dụng đúng loại loading:**
   - `QuickTabTransitionLoading` cho chuyển tab nhanh
   - `TabDataLoading` cho fetch data
   - Skeleton components cho initial load

2. **Timing:**
   - Quick loading: 150-300ms
   - Data loading: 500-1000ms
   - Skeleton: > 1000ms

3. **User Feedback:**
   - Luôn hiển thị loading state
   - Cung cấp message rõ ràng
   - Handle error states

4. **Performance:**
   - Avoid unnecessary re-renders
   - Use Suspense boundaries
   - Implement proper cleanup
