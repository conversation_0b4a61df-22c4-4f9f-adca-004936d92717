'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Map as MapIcon,
  RefreshCw,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { MagicHandler } from '@/lib/items';
import AddMonsterPopup from './add-monster-popup';
import { MapTemplateLoadingSkeleton } from './template-loading-skeletons';
import {
  gameToCanvas,
  mouseEventToGameCoordinates,
  formatCoordinates,
  GAME_MAP_SIZE
} from '@/lib/map-coordinates';

interface GameMap {
  fldMid: number;
  fldName: string;
  fldWidth?: number;
  fldHeight?: number;
  fldType?: number;
}

interface MonsterSetBase {
  fldIndex?: number;
  fldMid: number;
  fldNid: number;
  fldX: number;
  fldY: number;
  fldZ?: number;
  fldFace?: number;
  fldFace0?: number;
  fldRange?: number;
  fldNum?: number;
  fldName?: string;
  fldLevel?: number;
  fldType?: 'npc' | 'monster';
  fldJob?: number;
  fldHp?: number;
  fldMp?: number;
  fldAttack?: number;
  fldDefense?: number;
  fldAt?: number;
  fldDf?: number;
  fldActive?: number;
  npcData?: any;
}

export function MapManager() {
  const [maps, setMaps] = useState<GameMap[]>([]);
  const [selectedMap, setSelectedMap] = useState<GameMap | null>(null);
  const [monsters, setMonsters] = useState<MonsterSetBase[]>([]);
  const [npcs, setNpcs] = useState<MonsterSetBase[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Add monster states
  const [showAddMonsterPopup, setShowAddMonsterPopup] = useState(false);
  const [clickedCoordinates, setClickedCoordinates] = useState<{ x: number; y: number } | null>(null);
  const [showAddButton, setShowAddButton] = useState(false);
  const [addButtonPosition, setAddButtonPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Edit monster states
  const [editMode, setEditMode] = useState(false);
  const [selectedMonsterForEdit, setSelectedMonsterForEdit] = useState<MonsterSetBase | null>(null);
  
  // Canvas state
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [zoom, setZoom] = useState(1024/5120); // Start zoomed out to see full map
  const [panX, setPanX] = useState(0);
  const [panY, setPanY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const [mouseDownTime, setMouseDownTime] = useState(0);
  const [mouseDownPos, setMouseDownPos] = useState({ x: 0, y: 0 });
  const [hasMoved, setHasMoved] = useState(false);

  const magicHandler = new MagicHandler();

  // Load maps list
  const loadMaps = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/template/maps');
      const data = await response.json();
      
      if (data.success) {
        setMaps(data.data.maps);
      } else {
        toast.error('Không thể tải danh sách maps');
      }
    } catch (error) {
      console.error('Error loading maps:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách maps');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load monster setbase for selected map
  const loadMapData = useCallback(async (mapId: number) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/template/maps/${mapId}/monsters`);
      const data = await response.json();
      
      if (data.success) {
        // Separate NPCs (id < 10000) and monsters (id >= 10000)
        const allEntities = data.data.monsters;
        const npcList = allEntities.filter((entity: MonsterSetBase) => entity.fldType === 'npc');
        const monsterList = allEntities.filter((entity: MonsterSetBase) => entity.fldType === 'monster');

        console.log('Loaded entities:', {
          total: allEntities.length,
          npcs: npcList.length,
          monsters: monsterList.length,
          sampleNpc: npcList[0],
          sampleMonster: monsterList[0]
        });

        setNpcs(npcList);
        setMonsters(monsterList);
      } else {
        toast.error('Không thể tải dữ liệu map');
      }
    } catch (error) {
      console.error('Error loading map data:', error);
      toast.error('Có lỗi xảy ra khi tải dữ liệu map');
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle map selection
  const handleMapSelect = (map: GameMap) => {
    setSelectedMap(map);
    setImageLoaded(false);
    setImageError(false);
    setZoom(768/5120); // Start zoomed out to see full map
    setPanX(0);
    setPanY(0);
    loadMapData(map.fldMid);
  };

  // Filter maps based on search term
  const filteredMaps = maps.filter(map =>
    magicHandler.enConvert(map.fldName).toLowerCase().includes(searchTerm.toLowerCase()) ||
    map.fldMid.toString().includes(searchTerm)
  );

  // Helper function to draw text with stroke for better visibility
  const drawTextWithStroke = (ctx: CanvasRenderingContext2D, text: string, x: number, y: number, fillColor: string, fontSize: number = 36, strokeWidth: number = 2) => {
    ctx.font = `bold ${fontSize}px "Segoe UI", Arial, sans-serif`;
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.lineWidth = strokeWidth;
    ctx.strokeText(text, x, y);
    ctx.fillStyle = fillColor;
    ctx.fillText(text, x, y);
  };

  // Canvas drawing functions
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    if (!canvas || !selectedMap || !imageLoaded || !image) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Enable high-quality rendering
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Set text rendering properties for better quality
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    // Enable font smoothing (vendor prefixes for better browser support)
    (ctx as any).webkitFontSmoothing = 'antialiased';
    (ctx as any).mozFontSmoothing = 'antialiased';

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Save context for transformations
    ctx.save();

    // Apply zoom and pan
    ctx.translate(panX, panY);
    ctx.scale(zoom, zoom);

    // Draw scaled map image (canvas coordinates 0,0 to 5120,5120)
    ctx.drawImage(image, 0, 0, GAME_MAP_SIZE, GAME_MAP_SIZE);

    // Draw center crosshair at game coordinates (0,0)
    const centerCanvas = gameToCanvas(0, 0);
    ctx.strokeStyle = 'rgba(255, 255, 0, 0.8)'; // Yellow
    ctx.lineWidth = 6;
    ctx.beginPath();
    // Horizontal line
    ctx.moveTo(centerCanvas.x - 100, centerCanvas.y);
    ctx.lineTo(centerCanvas.x + 100, centerCanvas.y);
    // Vertical line
    ctx.moveTo(centerCanvas.x, centerCanvas.y - 100);
    ctx.lineTo(centerCanvas.x, centerCanvas.y + 100);
    ctx.stroke();

    // Draw center point
    ctx.fillStyle = 'rgba(255, 255, 0, 0.9)';
    ctx.beginPath();
    ctx.arc(centerCanvas.x, centerCanvas.y, 20, 0, 2 * Math.PI);
    ctx.fill();

    // Draw coordinate label with better font
    drawTextWithStroke(ctx, '(0,0)', centerCanvas.x + 30, centerCanvas.y - 30, 'rgba(255, 255, 0, 1)', 36, 2);

    // Draw NPCs (blue circles) - convert game coordinates to canvas coordinates
    npcs.forEach(npc => {
      const canvasPos = gameToCanvas(npc.fldX, npc.fldY);
      const radius = 40; // Larger radius for 5120x5120 scale

      ctx.beginPath();
      ctx.arc(canvasPos.x, canvasPos.y, radius, 0, 2 * Math.PI);
      ctx.fillStyle = 'rgba(59, 130, 246, 0.8)'; // Blue
      ctx.fill();
      ctx.strokeStyle = '#1d4ed8';
      ctx.lineWidth = 8;
      ctx.stroke();

      // Draw NPC name (from NPC table, converted from magic encoding)
      const npcName = npc.fldName ? magicHandler.enConvert(npc.fldName) : `NPC ${npc.fldNid}`;
      drawTextWithStroke(ctx, npcName, canvasPos.x + 60, canvasPos.y - 80, '#1d4ed8', 60, 4);

      // Draw NPC details
      const npcDetails = `ID: ${npc.fldNid} | Lv.${npc.fldLevel || 1}`;
      drawTextWithStroke(ctx, npcDetails, canvasPos.x + 60, canvasPos.y - 20, '#1d4ed8', 40, 3);

      // Draw additional NPC info if available
      if (npc.fldHp && npc.fldHp > 0) {
        const hpText = `HP: ${npc.fldHp.toLocaleString()}`;
        drawTextWithStroke(ctx, hpText, canvasPos.x + 60, canvasPos.y + 20, '#1d4ed8', 36, 2);
      }
    });

    // Draw Monsters (red circles with range) - convert game coordinates to canvas coordinates
    monsters.forEach(monster => {
      const canvasPos = gameToCanvas(monster.fldX, monster.fldY);

      // Draw range circle (if available) - AOE radius
      if (monster.fldRange && monster.fldRange > 0) {
        ctx.beginPath();
        ctx.arc(canvasPos.x, canvasPos.y, monster.fldRange, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(239, 68, 68, 0.1)'; // Light red
        ctx.fill();
        ctx.strokeStyle = 'rgba(239, 68, 68, 0.4)';
        ctx.lineWidth = 6;
        ctx.stroke();

        // Draw AOE radius text
        const radiusText = `R:${monster.fldRange}`;
        drawTextWithStroke(ctx, radiusText, canvasPos.x - monster.fldRange + 10, canvasPos.y - monster.fldRange + 30, 'rgba(239, 68, 68, 0.9)', 32, 2);
      }

      // Draw monster
      const radius = 30; // Larger radius for 5120x5120 scale
      ctx.beginPath();
      ctx.arc(canvasPos.x, canvasPos.y, radius, 0, 2 * Math.PI);
      ctx.fillStyle = 'rgba(239, 68, 68, 0.8)'; // Red
      ctx.fill();
      ctx.strokeStyle = '#dc2626';
      ctx.lineWidth = 6;
      ctx.stroke();

      // Draw monster name (from NPC table, converted from magic encoding)
      const monsterName = monster.fldName ? magicHandler.enConvert(monster.fldName) : `Monster ${monster.fldNid}`;
      drawTextWithStroke(ctx, monsterName, canvasPos.x + 50, canvasPos.y - 80, '#dc2626', 60, 4);

      // Draw monster details
      const details = `ID: ${monster.fldNid} | Lv.${monster.fldLevel || 1}${monster.fldNum ? ` | Qty: ${monster.fldNum}` : ''}`;
      drawTextWithStroke(ctx, details, canvasPos.x + 50, canvasPos.y - 20, '#dc2626', 40, 3);

      // Draw monster stats
      if (monster.fldHp && monster.fldHp > 0) {
        const hpText = `HP: ${monster.fldHp.toLocaleString()}`;
        drawTextWithStroke(ctx, hpText, canvasPos.x + 50, canvasPos.y + 20, '#dc2626', 36, 2);
      }

      if (monster.fldAttack && monster.fldAttack > 0) {
        const atkText = `ATK: ${monster.fldAttack}`;
        drawTextWithStroke(ctx, atkText, canvasPos.x + 50, canvasPos.y + 55, '#dc2626', 32, 2);
      }
    });

    // Restore context
    ctx.restore();
  }, [selectedMap, imageLoaded, npcs, monsters, zoom, panX, panY]);

  // Handle canvas mouse events
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const currentTime = Date.now();
    const mousePos = { x: e.clientX, y: e.clientY };

    setMouseDownTime(currentTime);
    setMouseDownPos(mousePos);
    setLastMousePos(mousePos);
    setHasMoved(false);
    setIsDragging(true);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging) return;

    const currentMousePos = { x: e.clientX, y: e.clientY };
    const deltaX = currentMousePos.x - lastMousePos.x;
    const deltaY = currentMousePos.y - lastMousePos.y;

    // Check if mouse has moved significantly (more than 5 pixels)
    const distanceFromStart = Math.sqrt(
      Math.pow(currentMousePos.x - mouseDownPos.x, 2) +
      Math.pow(currentMousePos.y - mouseDownPos.y, 2)
    );

    if (distanceFromStart > 5) {
      setHasMoved(true);
      // Hide add button immediately when dragging starts
      setShowAddButton(false);
    }

    setPanX(prev => prev + deltaX);
    setPanY(prev => prev + deltaY);
    setLastMousePos(currentMousePos);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Check if click is on a monster/NPC
  const getClickedMonster = (gameCoords: { x: number; y: number }) => {
    const clickRadius = 50; // Radius for click detection

    // Check NPCs first
    for (const npc of npcs) {
      const distance = Math.sqrt(
        Math.pow(gameCoords.x - npc.fldX, 2) +
        Math.pow(gameCoords.y - npc.fldY, 2)
      );
      if (distance <= clickRadius) {
        return npc;
      }
    }

    // Check monsters
    for (const monster of monsters) {
      const distance = Math.sqrt(
        Math.pow(gameCoords.x - monster.fldX, 2) +
        Math.pow(gameCoords.y - monster.fldY, 2)
      );
      if (distance <= clickRadius) {
        return monster;
      }
    }

    return null;
  };

  // Handle canvas click to show coordinates and add button
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const currentTime = Date.now();
    const clickDuration = currentTime - mouseDownTime;

    // Only process for genuine single clicks:
    // 1. Not dragging
    // 2. Mouse hasn't moved significantly (< 5px)
    // 3. Click duration is short (< 200ms)
    if (isDragging || hasMoved || clickDuration > 200) {
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    // Use utility function to get game coordinates
    const gameCoords = mouseEventToGameCoordinates(e, canvas, zoom, panX, panY);

    // Check if clicked on a monster/NPC
    const clickedMonster = getClickedMonster(gameCoords);

    if (clickedMonster) {
      // Edit existing monster
      setSelectedMonsterForEdit(clickedMonster);
      setClickedCoordinates({ x: clickedMonster.fldX, y: clickedMonster.fldY });
      setEditMode(true);
      setShowAddMonsterPopup(true);
      toast.info(`Chỉnh sửa: ${magicHandler.enConvert(clickedMonster.fldName || 'Unknown')}`);
    } else {
      // Add new monster
      console.log(`Game Coordinates: ${formatCoordinates(gameCoords.x, gameCoords.y)}`);
      toast.info(`Game Coordinates: ${formatCoordinates(gameCoords.x, gameCoords.y)}`);

      // Store clicked coordinates and show add button
      setClickedCoordinates(gameCoords);
      setAddButtonPosition({ x: e.clientX, y: e.clientY });
      setShowAddButton(true);

      // Hide add button after 3 seconds
      setTimeout(() => {
        setShowAddButton(false);
      }, 3000);
    }
  };

  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    e.stopPropagation();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom(prev => Math.max(0.05, Math.min(2, prev * delta)));
  };

  // Reset view
  const resetView = () => {
    setZoom(768/5120); // Reset to show full 5120x5120 map in 800x800 canvas
    setPanX(0);
    setPanY(0);
  };

  // Handle add monster
  const handleAddMonster = () => {
    setShowAddButton(false);
    setShowAddMonsterPopup(true);
  };

  // Reset click tracking when mouse leaves canvas
  const handleMouseLeave = () => {
    setIsDragging(false);
    setHasMoved(false);
    setShowAddButton(false);
  };

  // Handle popup close
  const handlePopupClose = () => {
    setShowAddMonsterPopup(false);
    setEditMode(false);
    setSelectedMonsterForEdit(null);
  };

  const handleAddMonsterSubmit = async (monsterData: any) => {
    if (!selectedMap) return;

    try {
      const response = await fetch(`/api/template/maps/${selectedMap.fldMid}/monsters/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(monsterData),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Monster đã được thêm thành công!');
        // Reload monsters data
        loadMapData(selectedMap.fldMid);
      } else {
        toast.error(`Lỗi: ${result.message}`);
      }
    } catch (error) {
      console.error('Error adding monster:', error);
      toast.error('Có lỗi xảy ra khi thêm monster');
    }
  };

  const handleUpdateMonsterSubmit = async (monsterData: any) => {
    if (!selectedMap || !selectedMonsterForEdit) return;

    try {
      const response = await fetch(`/api/template/maps/${selectedMap.fldMid}/monsters/${selectedMonsterForEdit.fldIndex}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(monsterData),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Monster đã được cập nhật thành công!');
        // Reload monsters data
        loadMapData(selectedMap.fldMid);
      } else {
        toast.error(`Lỗi: ${result.message}`);
      }
    } catch (error) {
      console.error('Error updating monster:', error);
      toast.error('Có lỗi xảy ra khi cập nhật monster');
    }
  };

  // Redraw canvas when data changes
  useEffect(() => {
    drawCanvas();
  }, [drawCanvas]);

  useEffect(() => {
    loadMaps();
  }, [loadMaps]);

  // Show loading skeleton on initial load
  if (loading && maps.length === 0) {
    return <MapTemplateLoadingSkeleton />;
  }

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - Maps List (1/3) */}
      <div className="col-span-3">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapIcon className="h-5 w-5" />
              Danh sách Maps
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm map..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button onClick={loadMaps} variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>

            {/* Maps List */}
            <div className="space-y-2 h-[calc(100vh-300px)] overflow-y-auto">
              {filteredMaps.map((map) => (
                <div
                  key={map.fldMid}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedMap?.fldMid === map.fldMid ? 'ring-2 ring-primary bg-accent' : ''}
                  `}
                  onClick={() => handleMapSelect(map)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium truncate">{magicHandler.enConvert(map.fldName)}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">ID: {map.fldMid}</span>
                        {map.fldType && (
                          <Badge variant="secondary" className="text-xs">
                            Type: {map.fldType}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {filteredMaps.length === 0 && (
                <div className="text-center py-8">
                  <MapIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Không tìm thấy map nào</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column - Map Details (2/3) */}
      <div className="col-span-9">
        {selectedMap ? (
          <Card className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MapIcon className="h-5 w-5" />
                  {magicHandler.enConvert(selectedMap.fldName)}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    ID: {selectedMap.fldMid}
                  </Badge>
                  <Badge variant="secondary">
                    NPCs: {npcs.length}
                  </Badge>
                  <Badge variant="destructive">
                    Monsters: {monsters.length}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Map Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setZoom(prev => Math.min(2, prev * 1.2))}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setZoom(prev => Math.max(0.05, prev * 0.8))}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetView}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Zoom: {Math.round(zoom * 100)}%
                  </span>
                </div>

                <div className="text-sm text-muted-foreground">
                  <div>Game World: -2560 to +2560 (X: left→right, Y: bottom→top)</div>
                  <div>Click on map to see coordinates</div>
                </div>
              </div>

              {/* Map Display */}
              <div
                className="relative border rounded-lg overflow-hidden bg-gray-100"
                style={{ width: '768px', height: '768px' }}
                onWheel={(e) => e.preventDefault()} // Prevent container scroll
              >
                {/* Hidden Map Image for Canvas Drawing */}
                <img
                  ref={imageRef}
                  src={`http://one.chamthoi.com/map/${selectedMap.fldMid}.jpg`}
                  alt={magicHandler.enConvert(selectedMap.fldName)}
                  className="hidden"
                  onLoad={() => setImageLoaded(true)}
                  onError={() => setImageError(true)}
                />

                {/* Canvas with Game Map Size */}
                <canvas
                  ref={canvasRef}
                  width={GAME_MAP_SIZE}
                  height={GAME_MAP_SIZE}
                  className="absolute inset-0 cursor-default w-full h-full"
                  style={{
                    width: '5120px',
                    height: '5120px',
                    imageRendering: 'auto', // Better for text rendering
                    fontSmooth: 'always',
                    WebkitFontSmoothing: 'antialiased',
                    MozOsxFontSmoothing: 'grayscale'
                  }}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                  onWheel={handleWheel}
                  onClick={handleCanvasClick}
                />

                {/* Loading/Error States */}
                {loading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <RefreshCw className="h-8 w-8 animate-spin text-white" />
                  </div>
                )}

                {imageError && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <MapIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Không thể tải hình ảnh map</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Legend */}
              <div className="flex items-center gap-6 text-sm flex-wrap">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-gray-900 rounded-full"></div>
                  <span>NPCs</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                  <span>Monsters</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-red-300 rounded-full bg-red-100"></div>
                  <span>AOE Radius (R:value) - Attack/spawn range</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                  <span>Map Center (0,0)</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="h-full">
            <CardContent className="h-full flex items-center justify-center">
              <div className="text-center">
                <MapIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Chọn Map</h3>
                <p className="text-muted-foreground">
                  Chọn một map từ danh sách bên trái để xem chi tiết
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Add Monster Button (floating) */}
      {showAddButton && clickedCoordinates && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            left: addButtonPosition.x + 10,
            top: addButtonPosition.y - 40,
          }}
        >
          <Button
            size="sm"
            onClick={handleAddMonster}
            className="pointer-events-auto shadow-lg"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Monster
          </Button>
        </div>
      )}

      {/* Add Monster Popup */}
      <AddMonsterPopup
        isOpen={showAddMonsterPopup}
        onClose={handlePopupClose}
        onAdd={handleAddMonsterSubmit}
        onUpdate={handleUpdateMonsterSubmit}
        coordinates={clickedCoordinates}
        mapId={selectedMap?.fldMid || 0}
        editMode={editMode}
        existingMonster={selectedMonsterForEdit}
      />
    </div>
  );
}
