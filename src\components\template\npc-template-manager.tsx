'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Plus,
  Trash2,
  Edit,
  Users,
  RefreshCw,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { MagicHandler } from '@/lib/items';
import { TemplateNPC } from '@/types/template';
import { NpcTemplateLoadingSkeleton } from './template-loading-skeletons';

export function NpcTemplateManager() {
  const [npcs, setNpcs] = useState<TemplateNPC[]>([]);
  const [selectedNpc, setSelectedNpc] = useState<TemplateNPC | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newNpc, setNewNpc] = useState<Partial<TemplateNPC>>({});
  const [editingNpc, setEditingNpc] = useState<TemplateNPC | null>(null);
  const [deletingNpc, setDeletingNpc] = useState<TemplateNPC | null>(null);

  const magicHandler = new MagicHandler();
  const itemsPerPage = 20;

  // Load NPCs
  const loadNPCs = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/api/template/npcs?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setNpcs(data.data.npcs);
        setTotalPages(Math.ceil(data.data.total / itemsPerPage));
      } else {
        toast.error('Không thể tải danh sách NPC');
      }
    } catch (error) {
      console.error('Error loading NPCs:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách NPC');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm]);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle NPC selection
  const handleNpcSelect = (npc: TemplateNPC) => {
    setSelectedNpc(npc);
  };

  // Handle add NPC
  const handleAddNpc = async () => {
    if (!newNpc.fldPid || !newNpc.fldName) {
      toast.error('Vui lòng nhập đầy đủ thông tin');
      return;
    }

    try {
      const response = await fetch('/api/template/npcs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newNpc)
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã thêm NPC thành công');
        setIsAddDialogOpen(false);
        setNewNpc({});
        loadNPCs();
      } else {
        toast.error(data.message || 'Không thể thêm NPC');
      }
    } catch (error) {
      console.error('Error adding NPC:', error);
      toast.error('Có lỗi xảy ra khi thêm NPC');
    }
  };

  // Handle edit NPC
  const handleEditNpc = async () => {
    if (!editingNpc) return;

    try {
      const response = await fetch(`/api/template/npcs/${editingNpc.fldPid}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editingNpc)
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã cập nhật NPC thành công');
        setIsEditDialogOpen(false);
        setEditingNpc(null);
        loadNPCs();
      } else {
        toast.error(data.message || 'Không thể cập nhật NPC');
      }
    } catch (error) {
      console.error('Error updating NPC:', error);
      toast.error('Có lỗi xảy ra khi cập nhật NPC');
    }
  };

  // Handle delete NPC
  const handleDeleteNpc = async () => {
    if (!deletingNpc) return;

    try {
      const response = await fetch(`/api/template/npcs/${deletingNpc.fldPid}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã xóa NPC thành công');
        setIsDeleteDialogOpen(false);
        setDeletingNpc(null);
        if (selectedNpc?.fldPid === deletingNpc.fldPid) {
          setSelectedNpc(null);
        }
        loadNPCs();
      } else {
        toast.error(data.message || 'Không thể xóa NPC');
      }
    } catch (error) {
      console.error('Error deleting NPC:', error);
      toast.error('Có lỗi xảy ra khi xóa NPC');
    }
  };

  // Initialize
  useEffect(() => {
    loadNPCs();
  }, [loadNPCs]);

  // Show loading skeleton
  if (loading && npcs.length === 0) {
    return <NpcTemplateLoadingSkeleton />;
  }

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - NPC List */}
      <div className="col-span-4">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Template NPCs
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search and Add */}
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm NPC..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button onClick={loadNPCs} variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button onClick={() => setIsAddDialogOpen(true)} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* NPC List */}
            <div className="space-y-2 h-[calc(100vh-350px)] overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                </div>
              ) : npcs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-8 w-8 mx-auto mb-2" />
                  <p>Không tìm thấy NPC nào</p>
                </div>
              ) : (
                npcs.map((npc) => (
                  <div
                    key={npc.fldPid}
                    className={`
                      p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                      ${selectedNpc?.fldPid === npc.fldPid ? 'ring-2 ring-primary bg-accent' : ''}
                    `}
                    onClick={() => handleNpcSelect(npc)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <h4 className="font-medium truncate">
                          {magicHandler.enConvert(npc.fldName)}
                        </h4>
                        <p className="text-xs text-muted-foreground">ID: {npc.fldPid}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          Lv.{npc.fldLevel || 1}
                        </Badge>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              setEditingNpc(npc);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              setDeletingNpc(npc);
                              setIsDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Trang {currentPage} / {totalPages}
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage >= totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Right Column - NPC Details */}
      <div className="col-span-8">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Chi tiết NPC
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedNpc ? (
              <div className="text-center py-12 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4" />
                <p>Chọn một NPC để xem chi tiết</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Tên NPC</Label>
                    <p className="text-lg font-semibold">
                      {magicHandler.enConvert(selectedNpc.fldName)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">ID</Label>
                    <p className="text-lg">{selectedNpc.fldPid}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Level</Label>
                    <p className="text-lg">{selectedNpc.fldLevel || 1}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">HP</Label>
                    <p className="text-lg">{selectedNpc.fldHp || 100}</p>
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Attack</Label>
                    <p className="text-lg">{selectedNpc.fldAt || 0}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Defense</Label>
                    <p className="text-lg">{selectedNpc.fldDf || 0}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Experience</Label>
                    <p className="text-lg">{selectedNpc.fldExp || 0}</p>
                  </div>
                </div>

                {/* Position */}
                {(selectedNpc.fldX || selectedNpc.fldY || selectedNpc.fldZ) && (
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium">X</Label>
                      <p className="text-lg">{selectedNpc.fldX || 0}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Y</Label>
                      <p className="text-lg">{selectedNpc.fldY || 0}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Z</Label>
                      <p className="text-lg">{selectedNpc.fldZ || 0}</p>
                    </div>
                  </div>
                )}

                {/* Flags */}
                <div className="flex gap-2">
                  {selectedNpc.fldBoss === 1 && (
                    <Badge variant="destructive">Boss</Badge>
                  )}
                  {selectedNpc.fldNpc === 1 && (
                    <Badge variant="secondary">NPC</Badge>
                  )}
                  {selectedNpc.fldAuto === 1 && (
                    <Badge variant="outline">Auto</Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Add NPC Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Thêm NPC mới</DialogTitle>
            <DialogDescription>
              Nhập thông tin để tạo NPC template mới
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="npc-id">ID NPC *</Label>
              <Input
                id="npc-id"
                type="number"
                value={newNpc.fldPid || ''}
                onChange={(e) => setNewNpc(prev => ({ ...prev, fldPid: parseInt(e.target.value) || 0 }))}
                placeholder="Nhập ID NPC"
              />
            </div>
            <div>
              <Label htmlFor="npc-name">Tên NPC *</Label>
              <Input
                id="npc-name"
                value={newNpc.fldName || ''}
                onChange={(e) => setNewNpc(prev => ({ ...prev, fldName: e.target.value }))}
                placeholder="Nhập tên NPC"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="npc-level">Level</Label>
                <Input
                  id="npc-level"
                  type="number"
                  value={newNpc.fldLevel || ''}
                  onChange={(e) => setNewNpc(prev => ({ ...prev, fldLevel: parseInt(e.target.value) || 1 }))}
                  placeholder="1"
                />
              </div>
              <div>
                <Label htmlFor="npc-hp">HP</Label>
                <Input
                  id="npc-hp"
                  type="number"
                  value={newNpc.fldHp || ''}
                  onChange={(e) => setNewNpc(prev => ({ ...prev, fldHp: parseInt(e.target.value) || 100 }))}
                  placeholder="100"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Hủy
            </Button>
            <Button onClick={handleAddNpc}>
              Thêm NPC
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit NPC Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Chỉnh sửa NPC</DialogTitle>
            <DialogDescription>
              Cập nhật thông tin NPC template
            </DialogDescription>
          </DialogHeader>
          {editingNpc && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-npc-name">Tên NPC *</Label>
                <Input
                  id="edit-npc-name"
                  value={editingNpc.fldName}
                  onChange={(e) => setEditingNpc(prev => prev ? ({ ...prev, fldName: e.target.value }) : null)}
                  placeholder="Nhập tên NPC"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-npc-level">Level</Label>
                  <Input
                    id="edit-npc-level"
                    type="number"
                    value={editingNpc.fldLevel || ''}
                    onChange={(e) => setEditingNpc(prev => prev ? ({ ...prev, fldLevel: parseInt(e.target.value) || 1 }) : null)}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-npc-hp">HP</Label>
                  <Input
                    id="edit-npc-hp"
                    type="number"
                    value={editingNpc.fldHp || ''}
                    onChange={(e) => setEditingNpc(prev => prev ? ({ ...prev, fldHp: parseInt(e.target.value) || 100 }) : null)}
                  />
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Hủy
            </Button>
            <Button onClick={handleEditNpc}>
              Cập nhật
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete NPC Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xóa NPC</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa NPC {deletingNpc ? magicHandler.enConvert(deletingNpc.fldName) : ''}?
              Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDeleteNpc}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
