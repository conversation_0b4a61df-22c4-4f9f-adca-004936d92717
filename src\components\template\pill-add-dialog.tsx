'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Plus,
  Search,
  AlertCircle,
  Package
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { CreatePillRequest, DEFAULT_PILL_VALUES, PILL_FIELD_CATEGORIES, PILL_FIELD_LABELS, UpdatePillRequest } from '@/types/pill';
import { TemplateItem } from '@/types/template';

interface PillAddDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPillCreated: () => void;
}

export const PillAddDialog: React.FC<PillAddDialogProps> = ({
  open,
  onOpenChange,
  onPillCreated
}) => {
  const [formData, setFormData] = useState<UpdatePillRequest>({ ...DEFAULT_PILL_VALUES, pillId: 0 });
  const [saving, setSaving] = useState(false);
  const [selectedItem, setSelectedItem] = useState<TemplateItem | null>(null);
  
  // Item selector state
  const [items, setItems] = useState<TemplateItem[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setFormData({ ...DEFAULT_PILL_VALUES, pillId: 0 });
      setSelectedItem(null);
      setSearchTerm('');
      setImageErrors(new Set());
      loadItems();
    }
  }, [open]);

  // Load items for selection
  const loadItems = async () => {
    setLoadingItems(true);
    try {
      const params = new URLSearchParams({
        page: '1',
        limit: '100',
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/api/template/game-items?${params}`);
      const result = await response.json();

      if (result.success) {
        setItems(result.data.items);
      } else {
        toast.error('Không thể tải danh sách items');
      }
    } catch (error) {
      console.error('Error loading items:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách items');
    } finally {
      setLoadingItems(false);
    }
  };

  // Handle item search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (open) {
        loadItems();
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, open]);

  // Handle form field changes
  const handleFieldChange = (field: keyof UpdatePillRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle item selection
  const handleItemSelect = (item: TemplateItem) => {
    setSelectedItem(item);
    setFormData(prev => ({
      ...prev,
      pillId: item.fldPid,
      pillName: item.fldName
    }));
  };

  // Handle image error
  const handleImageError = (itemId: number) => {
    setImageErrors(prev => new Set(prev).add(itemId));
  };

  // Handle save
  const handleSave = async () => {
    if (!formData.pillId) {
      toast.error('Vui lòng chọn item để tạo pill');
      return;
    }

    setSaving(true);
    try {
      const response = await fetch('/api/template/pills', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã tạo pill thành công');
        onPillCreated();
        onOpenChange(false);
      } else {
        toast.error(result.message || 'Không thể tạo pill');
      }
    } catch (error) {
      console.error('Error creating pill:', error);
      toast.error('Có lỗi xảy ra khi tạo pill');
    } finally {
      setSaving(false);
    }
  };

  // Filter items based on search
  const filteredItems = items.filter(item =>
    item.fldName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.fldPid.toString().includes(searchTerm)
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[1000px] h-[700px] max-w-[95vw] max-h-[95vh] overflow-y-auto fixed top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Thêm Pill mới
          </DialogTitle>
          <DialogDescription>
            Chọn item và cấu hình thông tin để tạo pill mới
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Tabs defaultValue="item" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="item">Chọn Item</TabsTrigger>
              <TabsTrigger value="basic">Cơ bản</TabsTrigger>
              <TabsTrigger value="stats">Chỉ số</TabsTrigger>
              <TabsTrigger value="combat">Chiến đấu</TabsTrigger>
              <TabsTrigger value="special">Đặc biệt</TabsTrigger>
              <TabsTrigger value="config">Cấu hình</TabsTrigger>
            </TabsList>

            {/* Item Selection Tab */}
            <TabsContent value="item" className="space-y-4">
              {/* Search */}
              <div className="space-y-2">
                <Label>Tìm kiếm Item</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm theo tên hoặc ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Selected Item Preview */}
              {selectedItem && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h3 className="font-medium mb-2">Item đã chọn:</h3>
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      {!imageErrors.has(selectedItem.fldPid) ? (
                        <img
                          src={`http://one.chamthoi.com/item/${selectedItem.fldPid}.jpg`}
                          alt={selectedItem.fldName}
                          className="w-12 h-12 object-cover rounded border-2"
                          onError={() => handleImageError(selectedItem.fldPid)}
                        />
                      ) : (
                        <div className="w-12 h-12 bg-muted rounded border-2 flex items-center justify-center">
                          <AlertCircle className="h-6 w-6 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{selectedItem.fldName}</p>
                      <p className="text-sm text-muted-foreground">ID: {selectedItem.fldPid}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Items Grid */}
              <div className="space-y-2">
                <Label>Chọn Item để tạo Pill:</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto border rounded-lg p-4">
                  {loadingItems ? (
                    <div className="col-span-full flex items-center justify-center py-12">
                      <Package className="h-8 w-8 animate-spin" />
                      <span className="ml-2">Đang tải...</span>
                    </div>
                  ) : filteredItems.length === 0 ? (
                    <div className="col-span-full text-center py-12">
                      <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Không tìm thấy item nào</p>
                    </div>
                  ) : (
                    filteredItems.map((item) => (
                      <Card
                        key={item.fldPid}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedItem?.fldPid === item.fldPid ? "ring-2 ring-primary" : ""
                        }`}
                        onClick={() => handleItemSelect(item)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0">
                              {!imageErrors.has(item.fldPid) ? (
                                <img
                                  src={`http://one.chamthoi.com/item/${item.fldPid}.jpg`}
                                  alt={item.fldName}
                                  className="w-8 h-8 object-cover rounded border"
                                  onError={() => handleImageError(item.fldPid)}
                                />
                              ) : (
                                <div className="w-8 h-8 bg-muted rounded border flex items-center justify-center">
                                  <AlertCircle className="h-4 w-4 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm truncate">{item.fldName}</p>
                              <p className="text-xs text-muted-foreground">ID: {item.fldPid}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="pillId">Pill ID *</Label>
                  <Input
                    id="pillId"
                    type="number"
                    value={formData.pillId || ''}
                    onChange={(e) => handleFieldChange('pillId', parseInt(e.target.value) || 0)}
                    disabled={!!selectedItem}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pillName">Tên Pill</Label>
                  <Input
                    id="pillName"
                    value={formData.pillName || ''}
                    onChange={(e) => handleFieldChange('pillName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="levelUse">Level sử dụng</Label>
                  <Input
                    id="levelUse"
                    value={formData.levelUse || ''}
                    onChange={(e) => handleFieldChange('levelUse', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="onOff">Trạng thái</Label>
                  <Select
                    value={formData.onOff?.toString() || '1'}
                    onValueChange={(value) => handleFieldChange('onOff', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Bật</SelectItem>
                      <SelectItem value="0">Tắt</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Stats Tab */}
            <TabsContent value="stats" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.stats.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Combat Tab */}
            <TabsContent value="combat" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.combat.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Special Tab */}
            <TabsContent value="special" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.special.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Config Tab */}
            <TabsContent value="config" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.config.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    {field === 'cantUse' ? (
                      <Input
                        id={field}
                        value={formData[field] || ''}
                        onChange={(e) => handleFieldChange(field, e.target.value)}
                      />
                    ) : (
                      <Input
                        id={field}
                        type="number"
                        value={formData[field] || 0}
                        onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                      />
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={handleSave} disabled={saving || !formData.pillId}>
            <Plus className="h-4 w-4 mr-2" />
            {saving ? 'Đang tạo...' : 'Tạo Pill'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
