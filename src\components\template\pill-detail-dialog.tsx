'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Trash2,
  Save,
  AlertCircle,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Pill, UpdatePillRequest, PILL_FIELD_LABELS, PILL_FIELD_CATEGORIES } from '@/types/pill';

interface PillDetailDialogProps {
  pill: Pill | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPillUpdated: () => void;
  onDeleteRequest: () => void;
}

export const PillDetailDialog: React.FC<PillDetailDialogProps> = ({
  pill,
  open,
  onOpenChange,
  onPillUpdated,
  onDeleteRequest
}) => {
  const [formData, setFormData] = useState<UpdatePillRequest>({});
  const [saving, setSaving] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Initialize form data when pill changes
  useEffect(() => {
    if (pill) {
      setFormData({ ...pill });
      setImageError(false);
    }
  }, [pill]);

  // Handle form field changes
  const handleFieldChange = (field: keyof UpdatePillRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle save
  const handleSave = async () => {
    if (!pill) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/template/pills/${pill.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã cập nhật pill thành công');
        onPillUpdated();
        onOpenChange(false);
      } else {
        toast.error(result.message || 'Không thể cập nhật pill');
      }
    } catch (error) {
      console.error('Error updating pill:', error);
      toast.error('Có lỗi xảy ra khi cập nhật pill');
    } finally {
      setSaving(false);
    }
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
  };

  if (!pill) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[1000px] h-[700px] max-w-[95vw] max-h-[95vh] overflow-y-auto fixed top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Chi tiết Pill: {pill.pillName || `Pill ${pill.pillId}`}
          </DialogTitle>
          <DialogDescription>
            Chỉnh sửa thông tin chi tiết của pill
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Pill Preview */}
          <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="relative">
              {!imageError ? (
                <img
                  src={`http://one.chamthoi.com/item/${pill.pillId}.jpg`}
                  alt={pill.pillName || `Pill ${pill.pillId}`}
                  className="w-16 h-16 object-cover rounded border-2"
                  onError={handleImageError}
                />
              ) : (
                <div className="w-16 h-16 bg-muted rounded border-2 flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{pill.pillName || `Pill ${pill.pillId}`}</h3>
              <p className="text-sm text-muted-foreground">ID: {pill.pillId}</p>
              {pill.levelUse && (
                <p className="text-sm text-muted-foreground">Level: {pill.levelUse}</p>
              )}
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={pill.onOff ? 'default' : 'secondary'}>
                  {pill.onOff ? 'Bật' : 'Tắt'}
                </Badge>
                {pill.publicPill === 1 && (
                  <Badge variant="outline">Public</Badge>
                )}
              </div>
            </div>
          </div>

          {/* Form Fields in Tabs */}
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Cơ bản</TabsTrigger>
              <TabsTrigger value="stats">Chỉ số</TabsTrigger>
              <TabsTrigger value="combat">Chiến đấu</TabsTrigger>
              <TabsTrigger value="special">Đặc biệt</TabsTrigger>
              <TabsTrigger value="config">Cấu hình</TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="pillId">Pill ID *</Label>
                  <Input
                    id="pillId"
                    type="number"
                    value={formData.pillId || ''}
                    onChange={(e) => handleFieldChange('pillId', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pillName">Tên Pill</Label>
                  <Input
                    id="pillName"
                    value={formData.pillName || ''}
                    onChange={(e) => handleFieldChange('pillName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="levelUse">Level sử dụng</Label>
                  <Input
                    id="levelUse"
                    value={formData.levelUse || ''}
                    onChange={(e) => handleFieldChange('levelUse', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="onOff">Trạng thái</Label>
                  <Select
                    value={formData.onOff?.toString() || '1'}
                    onValueChange={(value) => handleFieldChange('onOff', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Bật</SelectItem>
                      <SelectItem value="0">Tắt</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Stats Tab */}
            <TabsContent value="stats" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.stats.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Combat Tab */}
            <TabsContent value="combat" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.combat.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Special Tab */}
            <TabsContent value="special" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.special.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    <Input
                      id={field}
                      type="number"
                      value={formData[field] || 0}
                      onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                    />
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* Config Tab */}
            <TabsContent value="config" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {PILL_FIELD_CATEGORIES.config.fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>{PILL_FIELD_LABELS[field]}</Label>
                    {field === 'cantUse' ? (
                      <Input
                        id={field}
                        value={formData[field] || ''}
                        onChange={(e) => handleFieldChange(field, e.target.value)}
                      />
                    ) : (
                      <Input
                        id={field}
                        type="number"
                        value={formData[field] || 0}
                        onChange={(e) => handleFieldChange(field, parseInt(e.target.value) || 0)}
                      />
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            variant="destructive"
            onClick={onDeleteRequest}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Xóa Pill
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
