'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Search,
  ChevronLeft,
  ChevronRight,
  Plus,
  Trash2,
  Filter,
  RefreshCw,
  Package,
  AlertCircle,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Pill, PillFilter, PILL_FIELD_LABELS, SORTABLE_PILL_FIELDS } from '@/types/pill';
import { PillDetailDialog } from './pill-detail-dialog';
import { PillAddDialog } from './pill-add-dialog';

const PillTemplateManager = () => {
  // State management
  const [pills, setPills] = useState<Pill[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPill, setSelectedPill] = useState<Pill | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(100);

  // Filter state
  const [filters, setFilters] = useState<PillFilter>({
    search: '',
    sortBy: 'id',
    sortOrder: 'asc'
  });

  // Dialog states
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Image error tracking
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  // Load pills data
  const loadPills = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.pillId && { pillId: filters.pillId.toString() }),
        ...(filters.levelUse && { levelUse: filters.levelUse }),
        ...(filters.onOff !== undefined && { onOff: filters.onOff.toString() }),
        ...(filters.publicPill !== undefined && { publicPill: filters.publicPill.toString() }),
        ...(filters.sortBy && { sortBy: filters.sortBy }),
        ...(filters.sortOrder && { sortOrder: filters.sortOrder })
      });

      const response = await fetch(`/api/template/pills?${params}`);
      const result = await response.json();

      if (result.success) {
        setPills(result.data.pills);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        toast.error(result.message || 'Không thể tải danh sách pills');
      }
    } catch (error) {
      console.error('Error loading pills:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách pills');
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, filters]);

  // Handle image error
  const handleImageError = (pillId: number) => {
    setImageErrors(prev => new Set(prev).add(pillId));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof PillFilter, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle search
  const handleSearch = (value: string) => {
    handleFilterChange('search', value);
  };

  // Handle pill selection
  const handlePillSelect = (pill: Pill) => {
    setSelectedPill(pill);
    setShowDetailDialog(true);
  };

  // Handle pill updated
  const handlePillUpdated = () => {
    loadPills();
  };

  // Handle delete request from detail dialog
  const handleDeleteRequest = () => {
    setShowDetailDialog(false);
    setShowDeleteDialog(true);
  };

  // Handle pill created
  const handlePillCreated = () => {
    loadPills();
  };

  // Handle delete pill
  const handleDeletePill = async () => {
    if (!selectedPill) return;

    try {
      const response = await fetch(`/api/template/pills/${selectedPill.id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Đã xóa pill thành công');
        setShowDeleteDialog(false);
        setShowDetailDialog(false);
        setSelectedPill(null);
        loadPills();
      } else {
        toast.error(result.message || 'Không thể xóa pill');
      }
    } catch (error) {
      console.error('Error deleting pill:', error);
      toast.error('Có lỗi xảy ra khi xóa pill');
    }
  };

  // Load pills on component mount and when dependencies change
  useEffect(() => {
    loadPills();
  }, [loadPills]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Zap className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold">Quản lý Pill Template</h1>
        </div>
        <Button onClick={() => setShowAddDialog(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Thêm Pill mới
        </Button>
      </div>

      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Bộ lọc và tìm kiếm
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <Label>Tìm kiếm</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm theo tên hoặc ID..."
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Pill ID Filter */}
            <div className="space-y-2">
              <Label>Pill ID</Label>
              <Input
                type="number"
                placeholder="Nhập Pill ID..."
                value={filters.pillId || ''}
                onChange={(e) => handleFilterChange('pillId', e.target.value ? parseInt(e.target.value) : undefined)}
              />
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <Label>Trạng thái</Label>
              <Select
                value={filters.onOff?.toString() || 'all'}
                onValueChange={(value) => handleFilterChange('onOff', value === 'all' ? undefined : parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="1">Bật</SelectItem>
                  <SelectItem value="0">Tắt</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort */}
            <div className="space-y-2">
              <Label>Sắp xếp</Label>
              <div className="flex gap-2">
                <Select
                  value={filters.sortBy || 'id'}
                  onValueChange={(value) => handleFilterChange('sortBy', value)}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SORTABLE_PILL_FIELDS.map(field => (
                      <SelectItem key={field} value={field}>
                        {PILL_FIELD_LABELS[field] || field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={filters.sortOrder || 'asc'}
                  onValueChange={(value) => handleFilterChange('sortOrder', value as 'asc' | 'desc')}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">↑</SelectItem>
                    <SelectItem value="desc">↓</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pills Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Danh sách Pills
              <Badge variant="secondary">{pills.length} pills</Badge>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadPills}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Làm mới
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin" />
              <span className="ml-2">Đang tải...</span>
            </div>
          ) : pills.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Không tìm thấy pill nào</p>
            </div>
          ) : (
            <>
              {/* Pills Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-4 mb-6">
                {pills.map((pill) => (
                  <div
                    key={pill.id}
                    className="relative group cursor-pointer"
                    onClick={() => handlePillSelect(pill)}
                  >
                    <div className="aspect-square border-2 border-muted rounded-lg overflow-hidden hover:border-primary transition-colors bg-card">
                      {!imageErrors.has(pill.pillId) ? (
                        <img
                          src={`http://one.chamthoi.com/item/${pill.pillId}.jpg`}
                          alt={pill.pillName || `Pill ${pill.pillId}`}
                          className="w-full h-full object-cover"
                          onError={() => handleImageError(pill.pillId)}
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <AlertCircle className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}

                      {/* Overlay with pill info */}
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col justify-end p-2">
                        <div className="text-white text-xs">
                          <div className="font-medium truncate">
                            {pill.pillName || `Pill ${pill.pillId}`}
                          </div>
                          <div className="text-xs opacity-80">
                            ID: {pill.pillId}
                          </div>
                          {pill.levelUse && (
                            <div className="text-xs opacity-80">
                              Level: {pill.levelUse}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Status indicator */}
                      <div className="absolute top-1 right-1">
                        <div className={`w-2 h-2 rounded-full ${pill.onOff ? 'bg-green-500' : 'bg-red-500'}`} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Trang {currentPage} / {totalPages}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1 || loading}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Trước
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages || loading}
                  >
                    Sau
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Pill Add Dialog */}
      <PillAddDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onPillCreated={handlePillCreated}
      />

      {/* Pill Detail Dialog */}
      <PillDetailDialog
        pill={selectedPill}
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        onPillUpdated={handlePillUpdated}
        onDeleteRequest={handleDeleteRequest}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa pill {selectedPill?.pillName || selectedPill?.pillId} không?
              Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDeletePill}>
              <Trash2 className="h-4 w-4 mr-2" />
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PillTemplateManager;