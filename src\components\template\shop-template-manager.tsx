'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Store,
  Package,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { TemplateNPC, TemplateData, TemplatePage, TEMPLATE_SLOTS_PER_PAGE } from '@/types/template';
import { TemplateGrid } from './template-grid';
import { TemplateItemSelectorDialog } from './template-item-selector-dialog';
import { MagicHandler } from '@/lib/items';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export function ShopTemplateManager() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [npcs, setNpcs] = useState<TemplateNPC[]>([]);
  const [selectedNpc, setSelectedNpc] = useState<TemplateNPC | null>(null);
  const [templateData, setTemplateData] = useState<TemplateData | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('searchId') || '');
  const [isItemSelectorOpen, setIsItemSelectorOpen] = useState(false);
  const [selectedSlotIndex, setSelectedSlotIndex] = useState<number | null>(null);
  const [showMoveDialog, setShowMoveDialog] = useState(false);
  const [moveData, setMoveData] = useState<{from: number, to: number, fromItem: any, toItem: any} | null>(null);
  const [currentSlotItem, setCurrentSlotItem] = useState<any | null>(null);
  const magicHandler = new MagicHandler();

  // Handle item move (drag & drop)
  const handleItemMove = async (fromSlotIndex: number, toSlotIndex: number) => {
    if (!selectedNpc || !templateData) return;

    const currentPageData = getCurrentPageData();
    if (!currentPageData) return;

    const fromItem = currentPageData.items[fromSlotIndex];
    const toItem = currentPageData.items[toSlotIndex];

    // Calculate actual database indices
    const fromDbIndex = (currentPage - 1) * TEMPLATE_SLOTS_PER_PAGE + fromSlotIndex + 1;
    const toDbIndex = (currentPage - 1) * TEMPLATE_SLOTS_PER_PAGE + toSlotIndex + 1;

    if (!fromItem) return; // Nothing to move

    if (toItem) {
      // Target slot has item - show confirmation dialog
      setMoveData({
        from: fromDbIndex,
        to: toDbIndex,
        fromItem,
        toItem
      });
      setShowMoveDialog(true);
    } else {
      // Target slot is empty - move directly
      await performItemMove(fromDbIndex, toDbIndex, fromItem, null);
    }
  };

  // Perform the actual item move
  const performItemMove = async (fromIndex: number, toIndex: number, fromItem: any, toItem: any) => {
    if (!selectedNpc) return;

    try {
      // If target has item, we need to swap
      if (toItem) {
        // Swap items by updating both positions
        await Promise.all([
          fetch('/api/template/items', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              fldNid: selectedNpc.fldPid,
              fldIndex: toIndex,
              fldPid: fromItem.fldPid,
              fldMoney: fromItem.fldMoney,
              fldMagic0: fromItem.fldMagic0,
              fldMagic1: fromItem.fldMagic1,
              fldMagic2: fromItem.fldMagic2,
              fldMagic3: fromItem.fldMagic3,
              fldMagic4: fromItem.fldMagic4,
              fldCanvohuan: fromItem.fldCanvohuan,
              fldDays: fromItem.fldDays,
              fldBd: fromItem.fldBd
            })
          }),
          fetch('/api/template/items', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              fldNid: selectedNpc.fldPid,
              fldIndex: fromIndex,
              fldPid: toItem.fldPid,
              fldMoney: toItem.fldMoney,
              fldMagic0: toItem.fldMagic0,
              fldMagic1: toItem.fldMagic1,
              fldMagic2: toItem.fldMagic2,
              fldMagic3: toItem.fldMagic3,
              fldMagic4: toItem.fldMagic4,
              fldCanvohuan: toItem.fldCanvohuan,
              fldDays: toItem.fldDays,
              fldBd: toItem.fldBd
            })
          })
        ]);
        toast.success('Đã hoán đổi vị trí 2 items');
      } else {
        // Move to empty slot
        await Promise.all([
          // Add item to new position
          fetch('/api/template/items', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              fldNid: selectedNpc.fldPid,
              fldIndex: toIndex,
              fldPid: fromItem.fldPid,
              fldMoney: fromItem.fldMoney,
              fldMagic0: fromItem.fldMagic0,
              fldMagic1: fromItem.fldMagic1,
              fldMagic2: fromItem.fldMagic2,
              fldMagic3: fromItem.fldMagic3,
              fldMagic4: fromItem.fldMagic4,
              fldCanvohuan: fromItem.fldCanvohuan,
              fldDays: fromItem.fldDays,
              fldBd: fromItem.fldBd
            })
          }),
          // Remove item from old position
          fetch('/api/template/items', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              fldNid: selectedNpc.fldPid,
              fldIndex: fromIndex
            })
          })
        ]);
        toast.success('Đã di chuyển item');
      }

      // Reload template data
      if (selectedNpc.fldPid) {
        loadTemplateData(selectedNpc.fldPid);
      }
    } catch (error) {
      console.error('Error moving item:', error);
      toast.error('Có lỗi xảy ra khi di chuyển item');
    }
  };

  // Handle move confirmation
  const handleMoveConfirm = async () => {
    if (!moveData) return;

    await performItemMove(moveData.from, moveData.to, moveData.fromItem, moveData.toItem);
    setShowMoveDialog(false);
    setMoveData(null);
  };

  // Update URL when search term changes
  const updateSearchParams = (search: string, npcId?: number, page?: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('tab', 'shop');
    
    if (search) {
      params.set('searchId', search);
    } else {
      params.delete('searchId');
    }
    
    if (npcId) {
      params.set('npcId', npcId.toString());
    }
    
    if (page && page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }
    
    router.push(`/dashboard/template-management?${params.toString()}`);
  };

  // Load NPCs list (only NPCs with ID < 10000 for shops)
  const loadNPCs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/template/npcs?maxId=10000');
      const data = await response.json();
      
      if (data.success) {
        // sort by fldId
        data.data.npcs.sort((a: TemplateNPC, b: TemplateNPC) => a.fldPid - b.fldPid);
        setNpcs(data.data.npcs);
      } else {
        toast.error('Không thể tải danh sách NPC shop');
      }
    } catch (error) {
      console.error('Error loading NPCs:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách NPC shop');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load template data for selected NPC
  const loadTemplateData = useCallback(async (npcId: number) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/template/npc/${npcId}`);
      const data = await response.json();
      
      if (data.success) {
        setTemplateData(data.data);
      } else {
        toast.error('Không thể tải dữ liệu shop template');
      }
    } catch (error) {
      console.error('Error loading template data:', error);
      toast.error('Có lỗi xảy ra khi tải dữ liệu shop template');
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle NPC selection
  const handleNpcSelect = (npc: TemplateNPC) => {
    setSelectedNpc(npc);
    setCurrentPage(1);
    if (npc.fldPid) {
      updateSearchParams(searchTerm, npc.fldPid, 1);
    }
  };

  // Handle search
  const handleSearch = (search: string) => {
    setSearchTerm(search);
    updateSearchParams(search, selectedNpc?.fldPid);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateSearchParams(searchTerm, selectedNpc?.fldPid, page);
  };

  // Handle slot click for adding/editing items
  const handleSlotClick = (slotIndex: number) => {
    setSelectedSlotIndex(slotIndex);

    // Get current item in this slot if exists
    const currentPageData = getCurrentPageData();
    const currentItem = currentPageData?.items[slotIndex] || null;
    setCurrentSlotItem(currentItem);

    setIsItemSelectorOpen(true);
  };

  // Handle item selection from dialog
  const handleItemSelect = async (itemId: number, price: number, targetIndex?: number) => {
    if (!selectedNpc) return;

    try {
      // Use targetIndex if provided, otherwise calculate from current selection
      const actualSlotIndex = targetIndex || ((currentPage - 1) * TEMPLATE_SLOTS_PER_PAGE + (selectedSlotIndex || 0) + 1);

      const response = await fetch('/api/template/items', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fldNid: selectedNpc.fldPid,
          fldIndex: actualSlotIndex,
          fldPid: itemId,
          fldMoney: price
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã thêm item vào shop template');
        if (selectedNpc.fldPid) {
          loadTemplateData(selectedNpc.fldPid);
        }
      } else {
        toast.error('Không thể thêm item vào shop template');
      }
    } catch (error) {
      console.error('Error adding item to template:', error);
      toast.error('Có lỗi xảy ra khi thêm item');
    }

    setIsItemSelectorOpen(false);
    setSelectedSlotIndex(null);
    setCurrentSlotItem(null);
  };

  // Handle item removal
  const handleItemRemove = async (slotIndex: number) => {
    if (!selectedNpc) return;

    try {
      // Convert from 0-based slot index to 1-based database index
      const actualSlotIndex = (currentPage - 1) * TEMPLATE_SLOTS_PER_PAGE + slotIndex + 1;

      const response = await fetch('/api/template/items', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fldNid: selectedNpc.fldPid,
          fldIndex: actualSlotIndex
        })
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Đã xóa item khỏi shop template');
        if (selectedNpc.fldPid) {
          loadTemplateData(selectedNpc.fldPid);
        }
      } else {
        toast.error('Không thể xóa item khỏi shop template');
      }
    } catch (error) {
      console.error('Error removing item from template:', error);
      toast.error('Có lỗi xảy ra khi xóa item');
    }
  };

  // Filter NPCs based on search term
  const filteredNpcs = npcs.filter(npc => 
    npc.fldName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    npc.fldPid.toString().includes(searchTerm) ||
    npc.fldPid < 1000
  );

  // Get current page data
  const getCurrentPageData = (): TemplatePage | null => {
    if (!templateData) return null;
    return templateData.pages.find(page => page.pageNumber === currentPage) || null;
  };

  // Create existing items map for conflict detection
  const getExistingItemsMap = (): Map<number, any> => {
    const map = new Map();
    if (!templateData) return map;

    templateData.pages.forEach(page => {
      page.items.forEach((item, slotIndex) => {
        if (item) {
          const actualIndex = (page.pageNumber - 1) * TEMPLATE_SLOTS_PER_PAGE + slotIndex + 1;
          map.set(actualIndex, item);
        }
      });
    });

    return map;
  };

  // Initialize from URL params
  useEffect(() => {
    const npcId = searchParams.get('npcId');
    const page = parseInt(searchParams.get('page') || '1');
    
    if (npcId && npcs.length > 0) {
      const npc = npcs.find(n => n.fldPid === parseInt(npcId));
      if (npc) {
        setSelectedNpc(npc);
        setCurrentPage(page);
        loadTemplateData(parseInt(npcId));
      }
    }
  }, [npcs, searchParams, loadTemplateData]);

  useEffect(() => {
    loadNPCs();
  }, [loadNPCs]);

  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - NPC List (1/3) */}
      <div className="col-span-4 ">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Store className="h-5 w-5" />
              NPC Shop
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 ">
            {/* Search */}
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm NPC shop..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button onClick={loadNPCs} variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>

            {/* NPC List */}
            <div className="space-y-2 h-[calc(100vh-300px)] overflow-y-auto flex-1">
              {filteredNpcs.map((npc) => (
                <div
                  key={npc.fldPid}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all hover:bg-accent
                    ${selectedNpc?.fldPid === npc.fldPid ? 'ring-2 ring-primary bg-accent' : ''}
                  `}
                  onClick={() => handleNpcSelect(npc)}
                >
                  <div className="flex items-center justify-between">
                      <h4 className="font-medium truncate">{magicHandler.enConvert(npc.fldName)}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">ID: {npc.fldPid}</span>
                        <Badge variant="secondary" className="text-xs">
                          Lv.{npc.fldLevel || 1}
                        </Badge>
                      </div>
                  </div>
                </div>
              ))}

              {filteredNpcs.length === 0 && (
                <div className="text-center py-8">
                  <Store className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Không tìm thấy NPC nào</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column - Shop Template (2/3) */}
      <div className="col-span-8">
        {selectedNpc ? (
          <Card className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                   {magicHandler.enConvert(selectedNpc.fldName)}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    Trang {currentPage} / {templateData?.totalPages || 1}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(Math.min(templateData?.totalPages || 1, currentPage + 1))}
                    disabled={currentPage >= (templateData?.totalPages || 1)}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="h-full overflow-hidden">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <TemplateGrid
                  pageData={getCurrentPageData()}
                  onSlotClick={handleSlotClick}
                  onItemRemove={handleItemRemove}
                  onItemMove={handleItemMove}
                />
              )}
            </CardContent>
          </Card>
        ) : (
          <Card className="h-full">
            <CardContent className="h-full flex items-center justify-center">
              <div className="text-center">
                <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Chọn NPC Shop</h3>
                <p className="text-muted-foreground">
                  Chọn một NPC từ danh sách bên trái để quản lý shop template
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Template Item Selector Dialog */}
      <TemplateItemSelectorDialog
        isOpen={isItemSelectorOpen}
        onClose={() => {
          setIsItemSelectorOpen(false);
          setSelectedSlotIndex(null);
          setCurrentSlotItem(null);
        }}
        onItemSelect={handleItemSelect}
        currentPage={currentPage}
        totalPages={templateData?.totalPages || 1}
        existingItems={getExistingItemsMap()}
        currentSlotItem={currentSlotItem}
        selectedSlotIndex={selectedSlotIndex}
      />

      {/* Move Confirmation Dialog */}
      <Dialog open={showMoveDialog} onOpenChange={setShowMoveDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-blue-600">
              <Package className="h-5 w-5" />
              Xác nhận di chuyển item
            </DialogTitle>
            <DialogDescription>
              Vị trí đích đã có item. Bạn muốn hoán đổi vị trí 2 items không?
            </DialogDescription>
          </DialogHeader>

          {moveData && (
            <div className="space-y-3">
              <div className="p-3 bg-gray-900 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-800 mb-2">Item sẽ di chuyển:</h4>
                <div className="text-sm text-blue-700">
                  Từ vị trí {moveData.from} → Vị trí {moveData.to}
                </div>
              </div>

              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="text-sm font-medium text-yellow-800 mb-2">Item tại vị trí đích:</h4>
                <div className="text-sm text-yellow-700">
                  Sẽ được di chuyển về vị trí {moveData.from}
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowMoveDialog(false)}
            >
              Hủy
            </Button>
            <Button
              onClick={handleMoveConfirm}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Package className="h-4 w-4 mr-2" />
              Hoán đổi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
