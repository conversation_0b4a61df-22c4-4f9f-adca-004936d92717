'use client';

import { useState, useEffect } from 'react';
import { TemplatePage,  TemplateItem, TEMPLATE_SLOTS_PER_PAGE, TEMPLATE_SLOTS_PER_ROW } from '@/types/template';
import { TemplateItemCard } from './template-item-card';
import { Badge } from '@/components/ui/badge';

interface TemplateGridProps {
  pageData: TemplatePage | null;
  onSlotClick: (slotIndex: number) => void;
  onItemRemove: (slotIndex: number) => void;
  onItemEdit?: (slotIndex: number) => void;
  onItemMove?: (fromSlotIndex: number, toSlotIndex: number) => void;
}

export function TemplateGrid({
  pageData,
  onSlotClick,
  onItemRemove,
  onItemEdit,
  onItemMove
}: TemplateGridProps) {
  const [gameItems, setGameItems] = useState<Map<number, TemplateItem>>(new Map());
  const [selectedSlot, setSelectedSlot] = useState<number | null>(null);
  const [draggedSlot, setDraggedSlot] = useState<number | null>(null);
  const [dragOverSlot, setDragOverSlot] = useState<number | null>(null);

  // Drag & Drop handlers
  const handleDragStart = (slotIndex: number) => {
    setDraggedSlot(slotIndex);
  };

  const handleDragEnd = () => {
    setDraggedSlot(null);
    setDragOverSlot(null);
  };

  const handleDragOver = (e: React.DragEvent, slotIndex: number) => {
    e.preventDefault();
    setDragOverSlot(slotIndex);
  };

  const handleDrop = (toSlotIndex: number) => {
    if (draggedSlot !== null && draggedSlot !== toSlotIndex && onItemMove) {
      onItemMove(draggedSlot, toSlotIndex);
    }
    setDraggedSlot(null);
    setDragOverSlot(null);
  };

  // Load game item data for items in current page
  useEffect(() => {
    if (!pageData) return;

    const loadGameItems = async () => {
      const itemIds = pageData.items
        .filter(item => item !== null)
        .map(item => item!.fldPid);

      if (itemIds.length === 0) return;

      try {
        const response = await fetch('/api/template/game-items', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ itemIds })
        });

        const data = await response.json();
        
        if (data.success) {
          const itemMap = new Map<number, TemplateItem>();
          data.data.items.forEach((item: TemplateItem) => {
            itemMap.set(item.fldPid, item);
          });
          setGameItems(itemMap);
        }
      } catch (error) {
        console.error('Error loading game items:', error);
      }
    };

    loadGameItems();
  }, [pageData]);

  // Create grid slots (60 slots = 10 rows x 6 columns)
  const createGridSlots = () => {
    const slots = [];
    const items = pageData?.items || [];

    for (let row = 0; row < 10; row++) {
      const rowSlots = [];
      
      for (let col = 0; col < TEMPLATE_SLOTS_PER_ROW; col++) {
        const slotIndex = row * TEMPLATE_SLOTS_PER_ROW + col;
        const templateItem = items[slotIndex] || null;
        const gameItem = templateItem ? gameItems.get(templateItem.fldPid) : null;

        rowSlots.push(
          <div key={slotIndex} className="aspect-square">
            <TemplateItemCard
              templateItem={templateItem}
              gameItem={gameItem}
              slotIndex={slotIndex}
              onClick={() => {
                setSelectedSlot(slotIndex);
                onSlotClick(slotIndex);
              }}
              onRemove={templateItem ? () => onItemRemove(slotIndex) : undefined}
              onEdit={templateItem && onItemEdit ? () => onItemEdit(slotIndex) : undefined}
              isSelected={selectedSlot === slotIndex}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              onDragOver={(e) => handleDragOver(e, slotIndex)}
              onDrop={handleDrop}
              isDragOver={dragOverSlot === slotIndex}
            />
          </div>
        );
      }

      slots.push(
        <div key={row} className="grid grid-cols-6 gap-2">
          {rowSlots}
        </div>
      );
    }

    return slots;
  };

  // Calculate statistics
  const getPageStats = () => {
    if (!pageData) return { total: 0, filled: 0, empty: 60 };
    
    const filled = pageData.items.filter(item => item !== null).length;
    const empty = TEMPLATE_SLOTS_PER_PAGE - filled;
    
    return { total: TEMPLATE_SLOTS_PER_PAGE, filled, empty };
  };

  const stats = getPageStats();

  return (
    <div className="space-y-4">
      {/* Page Statistics */}
      <div className="flex items-center justify-between p-3 bg-muted/30 rounded mb-4">
        <div className="flex items-center gap-4">
          <div className="text-sm">
            <span className="font-medium">Trang {pageData?.pageNumber || 1}</span>
            <span className="text-muted-foreground ml-2">
              ({stats.filled}/{stats.total})
            </span>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="default" className="bg-green-500 text-xs">
              {stats.filled}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {stats.empty}
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="flex items-center gap-2">
          <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
            <div
              className="h-full bg-primary transition-all duration-300"
              style={{ width: `${(stats.filled / stats.total) * 100}%` }}
            />
          </div>
          <span className="text-xs text-muted-foreground">
            {Math.round((stats.filled / stats.total) * 100)}%
          </span>
        </div>
      </div>

      {/* Template Grid */}
      <div className="space-y-2 overflow-y-auto max-h-[calc(100vh-400px)]">
        {createGridSlots().map((row, index) => (
          <div key={index}>
            {row}
          </div>
        ))}
      </div>

      {/* Grid Legend */}
      <div className="flex items-center justify-between text-sm p-2 bg-muted/30 rounded">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-dashed border-muted-foreground/25 rounded" />
            <span className="text-muted-foreground">Trống</span>
          </div>

          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-primary/10 border border-primary rounded" />
            <span className="text-muted-foreground">Có item</span>
          </div>

          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-primary border-2 border-primary rounded" />
            <span className="text-muted-foreground">Đang chọn</span>
          </div>
        </div>

        <div className="text-muted-foreground text-xs">
          Click để thêm/sửa item
        </div>
      </div>
    </div>
  );
}
