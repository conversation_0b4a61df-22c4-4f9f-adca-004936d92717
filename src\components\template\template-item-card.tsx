"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Trash2, Edit, Coins, Info, Package, AlertCircle } from "lucide-react";
import { TemplateShopItem, TemplateItem } from "@/types/template";
import { MagicHandler } from "@/lib/items";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface TemplateItemCardProps {
  templateItem: TemplateShopItem | null;
  gameItem?: TemplateItem | null;
  slotIndex: number;
  onEdit?: () => void;
  onRemove?: () => void;
  onClick?: () => void;
  isSelected?: boolean;
  onDragStart?: (slotIndex: number) => void;
  onDragEnd?: () => void;
  onDragOver?: (e: React.DragEvent) => void;
  onDrop?: (slotIndex: number) => void;
  isDragOver?: boolean;
}

export function TemplateItemCard({
  templateItem,
  gameItem,
  slotIndex,
  onEdit,
  onRemove,
  onClick,
  isSelected = false,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  isDragOver = false,
}: TemplateItemCardProps) {
  const [imageError, setImageError] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const magicHandler = new MagicHandler();

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (onRemove) {
      onRemove();
    }
    setShowDeleteDialog(false);
  };

  // Handle delete button click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteDialog(true);
  };

  // Empty slot
  if (!templateItem || !gameItem) {
    return (
      <Card
        className={`
          h-32 w-full cursor-pointer transition-all duration-200
          border-2 border-dashed border-muted-foreground/25
          hover:border-primary/50 hover:bg-accent/50
          ${isSelected ? "ring-2 ring-primary" : ""}
          ${isDragOver ? "border-blue-500 bg-gray-900" : ""}
        `}
        onClick={onClick}
        onDragOver={(e) => {
          e.preventDefault();
          onDragOver?.(e);
        }}
        onDrop={(e) => {
          e.preventDefault();
          onDrop?.(slotIndex);
        }}
      >
        <CardContent className="p-3 h-full flex items-center justify-center">
          <div className="text-center">
            <Package className="h-10 w-10 text-muted-foreground/50 mx-auto mb-2" />
            <span className="text-xs text-muted-foreground font-medium">
              Slot {slotIndex + 1}
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Format price
  const formatPrice = (price: number) => {
    if (price >= 1000000) {
      return `${(price / 1000000).toFixed(1)}M`;
    } else if (price >= 1000) {
      return `${(price / 1000).toFixed(1)}K`;
    }
    return price.toString();
  };

  // Get item rarity color
  const getRarityColor = (item: TemplateItem) => {
    // Based on item type or other properties
    if (item.fldType === 1) return "text-red-500"; // Weapons
    if (item.fldType === 2) return "text-blue-500"; // Armor
    if (item.fldType === 3) return "text-green-500"; // Accessories
    return "text-gray-500";
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Card
            className={`
              h-32 w-full cursor-pointer transition-all duration-200
              hover:shadow-md hover:scale-105
              ${isSelected ? "ring-2 ring-primary shadow-lg" : ""}
              ${isDragOver ? "border-blue-500 bg-gray-900" : ""}
            `}
            onClick={onClick}
            draggable={!!templateItem}
            onDragStart={(e) => {
              if (templateItem) {
                e.dataTransfer.setData('text/plain', slotIndex.toString());
                onDragStart?.(slotIndex);
              }
            }}
            onDragEnd={() => onDragEnd?.()}
            onDragOver={(e) => {
              e.preventDefault();
              onDragOver?.(e);
            }}
            onDrop={(e) => {
              e.preventDefault();
              onDrop?.(slotIndex);
            }}
          >
            <CardContent className="px-3 py-0 h-full">
              <div className="flex flex-col h-full relative">
                {/* Item Name - Top */}
                <div className="mb-2">
                  <h4 className={`text-sm font-medium leading-tight ${getRarityColor(gameItem)} h-4 overflow-hidden`}
                      style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}>
                    {magicHandler.enConvert(gameItem.fldName)}
                  </h4>
                </div>

                {/* Middle Row: Icon, Price, Level */}
                <div className="flex items-center gap-3 mb-2 flex-1">
                  {/* Item Icon - Left */}
                  <div className="relative flex-shrink-0">
                    {!imageError ? (
                      <img
                        src={`http://one.chamthoi.com/item/${templateItem.fldPid}.jpg`}
                        alt={magicHandler.enConvert(gameItem.fldName)}
                        className="w-8 h-8 object-cover rounded border-2"
                        onError={() => setImageError(true)}
                      />
                    ) : (
                      <div className="w-8 h-8 bg-muted rounded border-2 flex items-center justify-center">
                        <AlertCircle className="h-5 w-5 text-muted-foreground" />
                      </div>
                    )}

                  
                  </div>

                  {/* Price and Level - Right */}
                  <div className="flex flex-col gap-1 flex-1 items-end">
                    {gameItem.fldLevel && gameItem.fldLevel > 1 && (
                      <Badge variant="outline" className="text-xs px-2 py-0.5">
                        Lv.{gameItem.fldLevel}
                      </Badge>
                    )}
                    {templateItem.fldDays > 1 && (
                      <Badge
                        variant="secondary"
                        className=""
                      >
                       {templateItem.fldDays} ngày
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-1 justify-end">
                <span className="text-sm font-semibold text-yellow-600 text-right ">
                  {formatPrice(templateItem.fldMoney)}
                </span>
                <Coins className="h-4 w-4 text-yellow-500" />
              </div>
                {/* Action Buttons - Bottom */}
                {(onEdit || onRemove) && (
                  <div className="flex gap-2 justify-center pt-1 border-t border-border/50 absolute -top-8 -right-4">
                    {onEdit && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit();
                        }}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                      </Button>
                    )}

                    {onRemove && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs hover:bg-red-100 hover:text-red-600"
                        onClick={handleDeleteClick}
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TooltipTrigger>

        {/* Delete Confirmation Dialog */}
        {onRemove && (
          <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2 text-red-600">
                  <Trash2 className="h-5 w-5" />
                  Xác nhận xóa item
                </DialogTitle>
                <DialogDescription>
                  Bạn có chắc chắn muốn xóa item này khỏi shop template không?
                </DialogDescription>
              </DialogHeader>

              {/* Item Info */}
              <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                <div className="relative">
                  {!imageError ? (
                    <img
                      src={`http://one.chamthoi.com/item/${templateItem.fldPid}.jpg`}
                      alt={magicHandler.enConvert(gameItem.fldName)}
                      className="w-12 h-12 object-cover rounded border-2"
                      onError={() => setImageError(true)}
                    />
                  ) : (
                    <div className="w-12 h-12 bg-muted rounded border-2 flex items-center justify-center">
                      <AlertCircle className="h-6 w-6 text-muted-foreground" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-sm">
                    {magicHandler.enConvert(gameItem.fldName)}
                  </h4>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-muted-foreground">
                      ID: {templateItem.fldPid}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Slot: {slotIndex + 1}
                    </span>
                  </div>
                </div>
              </div>

              <DialogFooter className="gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteDialog(false)}
                >
                  Hủy
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteConfirm}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        <TooltipContent side="top" className="max-w-xs">
          <div className="space-y-2">
            <div className="font-medium">{magicHandler.enConvert(gameItem.fldName)}</div>

            <div className="text-sm space-y-1">
              <div>ID: {templateItem.fldPid}</div>
              <div>Giá: {templateItem.fldMoney.toLocaleString()} lượng</div>
              <div>Vị trí: {templateItem.fldIndex} (Slot {slotIndex + 1})</div>

              {gameItem.fldLevel && gameItem.fldLevel > 1 && (
                <div>Yêu cầu level: {gameItem.fldLevel}</div>
              )}

              {gameItem.fldReside1 && (
                <div>Nghề nghiệp: {magicHandler.phai(gameItem.fldReside1)}</div>
              )}

              {/* {gameItem.fldDes && (
                <div className="text-xs text-muted-foreground mt-2 border-t pt-2">
                  {magicHandler.enConvert(gameItem.fldDes)}
                </div>
              )} */}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
