"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Search,
  Filter,
  Package,
  Coins,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import { toast } from "sonner";
import {
  TemplateItem,
  TemplateItemFilter,
  JOB_NAMES,
} from "@/types/template";
import { <PERSON><PERSON>and<PERSON> } from "@/lib/items";

interface TemplateItemSelectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onItemSelect: (itemId: number, price: number, targetIndex?: number) => void;
  currentPage?: number;
  totalPages?: number;
  existingItems?: Map<number, any>; // Map of index -> item for checking conflicts
  currentSlotItem?: any; // Current item in the selected slot
  selectedSlotIndex?: number | null; // Currently selected slot index
}

export function TemplateItemSelectorDialog({
  isOpen,
  onClose,
  onItemSelect,
  currentPage: initialPage = 1,
  totalPages: maxPages = 1,
  existingItems = new Map(),
  currentSlotItem = null,
  selectedSlotIndex = null,
}: TemplateItemSelectorDialogProps) {
  const [items, setItems] = useState<TemplateItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<TemplateItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItem, setSelectedItem] = useState<TemplateItem | null>(null);
  const [price, setPrice] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());

  // Position selection
  const [targetPage, setTargetPage] = useState(initialPage);
  const [targetSlot, setTargetSlot] = useState(1);
  const [showConflictDialog, setShowConflictDialog] = useState(false);
  const [conflictItem, setConflictItem] = useState<any>(null);

  const magicHandler = new MagicHandler();

  // Calculate target index based on page and slot
  const calculateTargetIndex = (page: number, slot: number) => {
    return (page - 1) * 60 + slot;
  };

  // Check if target position has existing item
  const checkConflict = (targetIndex: number) => {
    return existingItems.get(targetIndex);
  };

  // Handle confirm selection with position
  const handleConfirmWithPosition = () => {
    if (!selectedItem || price <= 0) {
      toast.error('Vui lòng chọn item và nhập giá hợp lệ');
      return;
    }

    const targetIndex = calculateTargetIndex(targetPage, targetSlot);
    const existingItem = checkConflict(targetIndex);

    if (existingItem) {
      setConflictItem(existingItem);
      setShowConflictDialog(true);
    } else {
      onItemSelect(selectedItem.fldPid, price, targetIndex);
      onClose();
      resetForm();
    }
  };

  // Handle conflict confirmation
  const handleConflictConfirm = () => {
    if (!selectedItem) return;

    const targetIndex = calculateTargetIndex(targetPage, targetSlot);
    onItemSelect(selectedItem.fldPid, price, targetIndex);
    setShowConflictDialog(false);
    onClose();
    resetForm();
  };

  // Reset form
  const resetForm = () => {
    // Don't reset if we have a current slot item (editing mode)
    if (!currentSlotItem) {
      setSelectedItem(null);
      setPrice(0);
      setTargetPage(initialPage);
      setTargetSlot(1);
    }
    setConflictItem(null);
  };

  // Filter state
  const [filter, setFilter] = useState<TemplateItemFilter>({
    name: "",
    fldReside1: undefined,
    fldReside2: undefined,
    minLevel: undefined,
    maxLevel: undefined,
    job: undefined,
    minJobLevel: undefined,
    maxJobLevel: undefined,
    fldType: undefined,
  });

  // Debug filter changes
  useEffect(() => {
    console.log('Filter changed:', filter);
  }, [filter]);

  // Load items with filters
  const loadItems = useCallback(
    async (page = 1) => {
      try {
        setLoading(true);

        const queryParams = new URLSearchParams({
          page: page.toString(),
          limit: "20",
        });

        // Add filters to query
        Object.entries(filter).forEach(([key, value]) => {
          if (value !== undefined && value !== "") {
            queryParams.append(key, value.toString());
          }
        });

        console.log('Loading items with params:', queryParams.toString());
        console.log('Current filter:', filter);

        const response = await fetch(
          `/api/template/items/search?${queryParams}`
        );
        const data = await response.json();

        console.log('API Response:', data);

        if (data.success) {
          setItems(data.data.items);
          setFilteredItems(data.data.items);
          setTotalPages(Math.ceil(data.data.total / 20));
          console.log('Loaded items count:', data.data.items.length);
          console.log('Total items:', data.data.total);
        } else {
          toast.error("Không thể tải danh sách template item");
        }
      } catch (error) {
        console.error("Error loading items:", error);
        toast.error("Có lỗi xảy ra khi tải danh sách template item");
      } finally {
        setLoading(false);
      }
    },
    [filter]
  );

  // Handle filter change
  const handleFilterChange = (key: keyof TemplateItemFilter, value: any) => {
    setFilter((prev) => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  // Handle item selection
  const handleItemSelect = (item: TemplateItem) => {
    setSelectedItem(item);
    setPrice(item.fldSaleMoney || 0);
  };



  // Handle image error
  const handleImageError = (itemId: number) => {
    setImageErrors((prev) => new Set([...prev, itemId]));
  };

  // Reset filters
  const resetFilters = () => {
    setFilter({
      name: "",
      fldReside1: undefined,
      fldReside2: undefined,
      minLevel: undefined,
      maxLevel: undefined,
      job: undefined,
      minJobLevel: undefined,
      maxJobLevel: undefined,
      fldType: undefined,
    });
    setCurrentPage(1);
  };

  // Auto-select current slot item when dialog opens
  useEffect(() => {
    if (isOpen && currentSlotItem) {
      // Set price from current item
      setPrice(currentSlotItem.fldMoney || 0);

      // Set target position to current slot
      if (selectedSlotIndex !== null) {
        const currentPageNum = Math.floor(selectedSlotIndex / 60) + 1;
        const currentSlotNum = (selectedSlotIndex % 60) + 1;
        setTargetPage(currentPageNum);
        setTargetSlot(currentSlotNum);
      }
    }
  }, [isOpen, currentSlotItem, selectedSlotIndex]);

  // Auto-select item in the list when items are loaded
  useEffect(() => {
    if (currentSlotItem && filteredItems.length > 0) {
      // Find the current item in the loaded items list
      const currentItemInList = filteredItems.find(item => item.fldPid === currentSlotItem.fldPid);
      if (currentItemInList) {
        setSelectedItem(currentItemInList);
      } else {
        // If current item is not in the list, search for it specifically
        const searchForCurrentItem = async () => {
          try {
            const response = await fetch(`/api/template/items/search?fldPid=${currentSlotItem.fldPid}&limit=1`);
            const data = await response.json();

            if (data.success && data.data.items.length > 0) {
              const item = data.data.items[0];
              setSelectedItem(item);
              // Add it to the filtered items list for display
              setFilteredItems(prev => [item, ...prev]);
            }
          } catch (error) {
            console.error('Error searching for current item:', error);
          }
        };

        searchForCurrentItem();
      }
    }
  }, [currentSlotItem, filteredItems]);

  useEffect(() => {
    if (isOpen) {
      console.log('Dialog opened, loading items for page:', currentPage);
      console.log('Current filter state:', filter);
      loadItems(currentPage);
    }
  }, [isOpen, currentPage, loadItems]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="lg:w-7xl lg:min-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {currentSlotItem ? 'Chỉnh sửa Template Item' : 'Chọn Template Item'}
          </DialogTitle>
          {currentSlotItem && (
            <DialogDescription className="flex items-center gap-2 text-blue-600">
              <AlertCircle className="h-4 w-4" />
              Đang chỉnh sửa item tại vị trí {selectedSlotIndex !== null ? selectedSlotIndex + 1 : 'N/A'}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          {/* Filters Panel */}
          <div className="space-y-4">
            {/* Current Item Info */}
            {currentSlotItem && (
              <div className="p-3 bg-gray-900 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-white mb-2 flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Item hiện tại
                </h4>
                <div className="space-y-1 text-sm text-white">
                  <div>ID: {currentSlotItem.fldPid}</div>
                  {selectedItem && selectedItem.fldPid === currentSlotItem.fldPid && (
                    <div>Tên: {magicHandler.enConvert(selectedItem.fldName)}</div>
                  )}
                  <div>Giá: {currentSlotItem.fldMoney?.toLocaleString() || 0} lượng</div>
                  <div>Vị trí: {selectedSlotIndex !== null ? selectedSlotIndex + 1 : 'N/A'}</div>
                </div>
              </div>
            )}
            <div className="flex items-center justify-between">
              <h3 className="font-medium flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Bộ lọc
              </h3>
              <Button variant="outline" size="sm" onClick={resetFilters}>
                Reset
              </Button>
            </div>

            <div className="space-y-3">
              {/* Name Filter */}
              <div className="flex flex-col gap-2">
                <Label htmlFor="name">Tên template item</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="name"
                    placeholder="Tìm kiếm..."
                    value={filter.name || ""}
                    onChange={(e) => handleFilterChange("name", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Item Type */}
              <div className="flex flex-row gap-2">
                <div className="flex flex-col gap-2">
                  <Label>Reside 1</Label>
                  <Select
                    value={filter.fldReside1?.toString() || "all"}
                    onValueChange={(value) =>
                      handleFilterChange(
                        "fldReside1",
                        value === "all" ? undefined : parseInt(value)
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn nghề" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      {Object.entries(JOB_NAMES).map(([id, name]) => (
                        <SelectItem key={id} value={id}>
                          {name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-col gap-2">
                  <Label>Reside 2</Label>
                  <Select
                    value={filter.fldReside2?.toString() || "all"}
                    onValueChange={(value) =>
                      handleFilterChange(
                        "fldReside2",
                        value === "all" ? undefined : parseInt(value)
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn nghề" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      {Object.entries(JOB_NAMES).map(([id, name]) => (
                        <SelectItem key={id} value={id}>
                          {name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {/* Level Range */}
              <div className="grid grid-cols-2 gap-2">
                <div className="flex flex-col gap-2">
                  <Label htmlFor="minLevel">Level tối thiểu</Label>
                  <Input
                    id="minLevel"
                    type="number"
                    placeholder="0"
                    value={filter.minLevel || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "minLevel",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <Label htmlFor="maxLevel">Level tối đa</Label>
                  <Input
                    id="maxLevel"
                    type="number"
                    placeholder="999"
                    value={filter.maxLevel || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "maxLevel",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </div>
              </div>

              {/* Job Requirements */}

              {/* Job Level Range */}
              <div className="grid grid-cols-2 gap-2">
                <div className="flex flex-col gap-2">
                  <Label htmlFor="minJobLevel">Job Level min</Label>
                  <Input
                    id="minJobLevel"
                    type="number"
                    placeholder="0"
                    value={filter.minJobLevel || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "minJobLevel",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <Label htmlFor="maxJobLevel">Job Level max</Label>
                  <Input
                    id="maxJobLevel"
                    type="number"
                    placeholder="999"
                    value={filter.maxJobLevel || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "maxJobLevel",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Items List */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Danh sách template item</h3>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => loadItems(currentPage)}
                  disabled={loading}
                >
                  <RefreshCw
                    className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
                  />
                </Button>
                <span className="text-sm text-muted-foreground">
                  Trang {currentPage}/{totalPages}
                </span>
              </div>
            </div>

            {/* Items Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg-grid-cols-3 gap-3 max-h-96 overflow-y-auto">
              {loading ? (
                <div className="col-span-full flex items-center justify-center py-12">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : filteredItems.length === 0 ? (
                <div className="col-span-full text-center py-12">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Không tìm thấy template item nào
                  </p>
                </div>
              ) : (
                filteredItems.map((item) => (
                  <Card
                    key={item.fldPid}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedItem?.fldPid === item.fldPid
                        ? currentSlotItem?.fldPid === item.fldPid
                          ? "ring-2 ring-blue-500 bg-gray-900"
                          : "ring-2 ring-primary"
                        : ""
                    }`}
                    onClick={() => handleItemSelect(item)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        {/* Item Icon */}
                        <div className="flex-shrink-0">
                          {!imageErrors.has(item.fldPid) ? (
                            <img
                              src={`http://one.chamthoi.com/item/${item.fldPid}.jpg`}
                              alt={item.fldName}
                              className="w-8 h-8 object-cover rounded border"
                              onError={() => handleImageError(item.fldPid)}
                            />
                          ) : (
                            <div className="w-8 h-8 bg-muted rounded border flex items-center justify-center">
                              <AlertCircle className="h-4 w-4 text-muted-foreground" />
                            </div>
                          )}
                        </div>

                        {/* Item Info */}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm truncate">
                            {magicHandler.enConvert(item.fldName)}
                          </h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="secondary" className="text-xs">
                              ID: {item.fldPid}
                            </Badge>
                            {item.fldLevel && item.fldLevel > 1 && (
                              <Badge variant="outline" className="text-xs">
                                Lv.{item.fldLevel}
                              </Badge>
                            )}
                            {currentSlotItem?.fldPid === item.fldPid && (
                              <Badge variant="default" className="text-xs bg-white">
                                Hiện tại
                              </Badge>
                            )}
                          </div>
                          {item.fldSaleMoney && (
                            <div className="flex items-center gap-1 mt-1">
                              <Coins className="h-3 w-3 text-yellow-500" />
                              <span className="text-xs text-yellow-600">
                                {item.fldSaleMoney.toLocaleString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage <= 1 || loading}
              >
                Trước
              </Button>
              <span className="text-sm text-muted-foreground">
                {currentPage} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage >= totalPages || loading}
              >
                Sau
              </Button>
            </div>
          </div>
        </div>

        {/* Selected Item & Price */}
        {selectedItem && (
          <>
            <Separator />
            <div className="space-y-4">
              <h3 className="font-medium">Template Item đã chọn</h3>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  {!imageErrors.has(selectedItem.fldPid) ? (
                    <img
                      src={`http://one.chamthoi.com/item/${selectedItem.fldPid}.jpg`}
                      alt={selectedItem.fldName}
                      className="w-10 h-10 object-cover rounded border"
                      onError={() => handleImageError(selectedItem.fldPid)}
                    />
                  ) : (
                    <div className="w-10 h-10 bg-muted rounded border flex items-center justify-center">
                      <AlertCircle className="h-5 w-5 text-muted-foreground" />
                    </div>
                  )}
                  <div>
                    <h4 className="font-medium">
                      {magicHandler.enConvert(selectedItem.fldName)}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      ID: {selectedItem.fldPid}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="price">Giá template:</Label>
                  <Input
                    id="price"
                    type="number"
                    value={price}
                    onChange={(e) => setPrice(parseInt(e.target.value) || 0)}
                    className="w-32"
                    min="0"
                  />
                  <span className="text-sm text-muted-foreground">lượng</span>
                </div>
              </div>

              {/* Position Selection */}
              <div className="space-y-3">
                <h4 className="font-medium">Chọn vị trí trong template</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="targetPage">Trang</Label>
                    <Select
                      value={targetPage.toString()}
                      onValueChange={(value) => setTargetPage(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: Math.max(maxPages, 3) }, (_, i) => (
                          <SelectItem key={i + 1} value={(i + 1).toString()}>
                            Trang {i + 1}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="targetSlot">Slot (1-60)</Label>
                    <Input
                      id="targetSlot"
                      type="number"
                      value={targetSlot}
                      onChange={(e) => setTargetSlot(Math.max(1, Math.min(60, parseInt(e.target.value) || 1)))}
                      min="1"
                      max="60"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Label>Index tính toán</Label>
                    <div className="h-10 px-3 py-2 bg-muted rounded-md flex items-center">
                      <span className="font-mono text-sm">
                        {calculateTargetIndex(targetPage, targetSlot)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Conflict Warning */}
                {checkConflict(calculateTargetIndex(targetPage, targetSlot)) && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2 text-yellow-800">
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Vị trí này đã có item! Việc thêm sẽ thay thế item hiện tại.
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Hủy
          </Button>
          <Button
            onClick={handleConfirmWithPosition}
            disabled={!selectedItem || price <= 0}
          >
            {currentSlotItem ? 'Cập nhật Template' : 'Thêm vào Template'}
          </Button>
        </div>

        {/* Conflict Confirmation Dialog */}
        <Dialog open={showConflictDialog} onOpenChange={setShowConflictDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-yellow-600">
                <AlertCircle className="h-5 w-5" />
                Xác nhận thay thế item
              </DialogTitle>
              <DialogDescription>
                Vị trí này đã có item. Bạn có muốn thay thế item hiện tại không?
              </DialogDescription>
            </DialogHeader>

            {conflictItem && (
              <div className="space-y-3">
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="text-sm font-medium text-red-800 mb-2">Item hiện tại sẽ bị thay thế:</h4>
                  <div className="text-sm text-red-700">
                    Index: {calculateTargetIndex(targetPage, targetSlot)} (Trang {targetPage}, Slot {targetSlot})
                  </div>
                </div>

                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-800 mb-2">Item mới:</h4>
                  <div className="text-sm text-green-700">
                    {selectedItem && magicHandler.enConvert(selectedItem.fldName)} - {price.toLocaleString()} lượng
                  </div>
                </div>
              </div>
            )}

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => setShowConflictDialog(false)}
              >
                Hủy
              </Button>
              <Button
                variant="destructive"
                onClick={handleConflictConfirm}
                className="bg-yellow-600 hover:bg-yellow-700"
              >
                Thay thế
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
}
