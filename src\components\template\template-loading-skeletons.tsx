'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Store, 
  Package, 
  Users, 
  Map as MapIcon,
  Database 
} from 'lucide-react';

// Shop Template Loading Skeleton
export function ShopTemplateLoadingSkeleton() {
  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - NPC List */}
      <div className="col-span-4">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Store className="h-5 w-5" />
              NPC Shop
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Skeleton */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-10 flex-1" />
              <Skeleton className="h-10 w-10" />
            </div>

            {/* NPC List Skeleton */}
            <div className="space-y-2 h-[calc(100vh-300px)] overflow-y-auto">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="p-3 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-5 w-32" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-5 w-12 rounded-full" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column - Template Grid */}
      <div className="col-span-8">
        <Card className="h-full">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                <Skeleton className="h-6 w-32" />
              </CardTitle>
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Template Grid Skeleton */}
            <div className="grid grid-cols-6 gap-2">
              {Array.from({ length: 60 }).map((_, i) => (
                <div key={i} className="aspect-square border rounded-lg p-2">
                  <Skeleton className="w-full h-full rounded" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Item Template Loading Skeleton
export function ItemTemplateLoadingSkeleton() {
  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - Item List */}
      <div className="col-span-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Template Items
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Filters Skeleton */}
            <div className="grid grid-cols-4 gap-4">
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
            </div>

            {/* Items Grid Skeleton */}
            <div className="grid grid-cols-4 gap-4">
              {Array.from({ length: 12 }).map((_, i) => (
                <Card key={i} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="w-12 h-12 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-3 w-16" />
                        <div className="flex gap-1">
                          <Skeleton className="h-4 w-12 rounded-full" />
                          <Skeleton className="h-4 w-12 rounded-full" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination Skeleton */}
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column - Item Details */}
      <div className="col-span-4">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Chi tiết Item
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-muted-foreground">
              <Package className="h-12 w-12 mx-auto mb-4" />
              <p>Chọn một item để xem chi tiết</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// NPC Template Loading Skeleton
export function NpcTemplateLoadingSkeleton() {
  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - NPC List */}
      <div className="col-span-4">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Template NPCs
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Skeleton */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-10 flex-1" />
              <Skeleton className="h-10 w-10" />
            </div>

            {/* NPC List Skeleton */}
            <div className="space-y-2 h-[calc(100vh-300px)] overflow-y-auto">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={i} className="p-3 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-5 w-12 rounded-full" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column - NPC Details */}
      <div className="col-span-8">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Chi tiết NPC
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-4" />
              <p>Chọn một NPC để xem chi tiết</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Map Template Loading Skeleton
export function MapTemplateLoadingSkeleton() {
  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - Map List */}
      <div className="col-span-4">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapIcon className="h-5 w-5" />
              Game Maps
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Skeleton */}
            <Skeleton className="h-10 w-full" />

            {/* Map List Skeleton */}
            <div className="space-y-2 h-[calc(100vh-300px)] overflow-y-auto">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="p-3 rounded-lg border">
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-3 w-20" />
                    <div className="flex gap-2">
                      <Skeleton className="h-4 w-16 rounded-full" />
                      <Skeleton className="h-4 w-16 rounded-full" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column - Map Canvas */}
      <div className="col-span-8">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapIcon className="h-5 w-5" />
              <Skeleton className="h-6 w-32" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">Đang tải bản đồ...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Drop Template Loading Skeleton
export function DropTemplateLoadingSkeleton() {
  return (
    <div className="grid grid-cols-12 gap-6">
      {/* Left Column - Monster List */}
      <div className="col-span-4">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Drop Templates
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Skeleton */}
            <Skeleton className="h-10 w-full" />

            {/* Monster List Skeleton */}
            <div className="space-y-2 h-[calc(100vh-300px)] overflow-y-auto">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="p-3 rounded-lg border animate-pulse">
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-3 w-20" />
                    <div className="flex gap-2">
                      <Skeleton className="h-4 w-12 rounded-full" />
                      <Skeleton className="h-4 w-16 rounded-full" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Column - Drop Items */}
      <div className="col-span-8">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Drop Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4 animate-pulse" />
              <p>Chọn một monster để xem drop items</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Generic Tab Loading Skeleton for TODO tabs
export function GenericTabLoadingSkeleton({
  icon: Icon,
  description
}: {
  icon: any,
  title: string,
  description: string
}) {
  return (
    <div className="text-center py-12">
      <Icon className="h-12 w-12 text-muted-foreground mx-auto mb-4 animate-pulse" />
      <h3 className="text-lg font-semibold mb-2">TODO</h3>
      <p className="text-muted-foreground">
        {description} sẽ được phát triển trong phiên bản tiếp theo.
      </p>
      <div className="mt-6 space-y-2">
        <Skeleton className="h-4 w-64 mx-auto" />
        <Skeleton className="h-4 w-48 mx-auto" />
        <Skeleton className="h-4 w-56 mx-auto" />
      </div>
    </div>
  );
}

// Quick Loading Spinner for fast transitions
export function QuickLoadingSpinner() {
  return (
    <div className="flex items-center justify-center py-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <span className="ml-2 text-muted-foreground">Đang tải...</span>
    </div>
  );
}
