'use client';

import { Suspense } from 'react';
import { useTabLoading } from '@/hooks/use-tab-loading';
import { 
  ShopTemplateLoadingSkeleton,
  ItemTemplateLoadingSkeleton,
  NpcTemplateLoadingSkeleton,
  MapTemplateLoadingSkeleton,
  DropTemplateLoadingSkeleton,
  GenericTabLoadingSkeleton,
  QuickLoadingSpinner
} from './template-loading-skeletons';
import {
  Settings,
  ShoppingCart,
  FileText,
  Shield,
  Zap
} from 'lucide-react';

interface TemplateTabWrapperProps {
  activeTab: string;
  children: React.ReactNode;
  quickLoading?: boolean; // Cho loading nhanh khi chuyển tab
}

export function TemplateTabWrapper({ 
  activeTab, 
  children, 
  quickLoading = false 
}: TemplateTabWrapperProps) {
  const { isLoading } = useTabLoading(activeTab, quickLoading ? 150 : 300);

  // Nếu đang loading và cần hiển thị loading nhanh
  if (isLoading && quickLoading) {
    return <QuickLoadingSpinner />;
  }

  // Nếu đang loading và cần hiển thị skeleton chi tiết
  if (isLoading) {
    return <TemplateLoadingSkeleton activeTab={activeTab} />;
  }

  return (
    <Suspense fallback={<TemplateLoadingSkeleton activeTab={activeTab} />}>
      {children}
    </Suspense>
  );
}

function TemplateLoadingSkeleton({ activeTab }: { activeTab: string }) {
  switch (activeTab) {
    case 'shop':
      return <ShopTemplateLoadingSkeleton />;
    
    case 'items':
      return <ItemTemplateLoadingSkeleton />;
    
    case 'npcs':
      return <NpcTemplateLoadingSkeleton />;
    
    case 'maps':
      return <MapTemplateLoadingSkeleton />;
    
    case 'drops':
      return <DropTemplateLoadingSkeleton />;
    
    case 'quests':
      return (
        <GenericTabLoadingSkeleton
          icon={FileText}
          title="Stones"
          description="Quản lý template đá"
        />
      );
    
    case 'skills':
      return (
        <GenericTabLoadingSkeleton
          icon={Zap}
          title="Skills"
          description="Quản lý template kỹ năng"
        />
      );
    
    case 'abilities':
      return (
        <GenericTabLoadingSkeleton
          icon={Shield}
          title="Abilities"
          description="Quản lý template kĩ năng"
        />
      );
    
    case 'events':
      return (
        <GenericTabLoadingSkeleton
          icon={ShoppingCart}
          title="Events"
          description="Quản lý template sự kiện"
        />
      );
    
    case 'configs':
      return (
        <GenericTabLoadingSkeleton
          icon={Settings}
          title="Configs"
          description="Template cấu hình hệ thống"
        />
      );
    
    default:
      return <ShopTemplateLoadingSkeleton />;
  }
}

// Component để wrap từng template manager
export function TemplateManagerWrapper({ 
  children, 
  isLoading = false 
}: { 
  children: React.ReactNode;
  isLoading?: boolean;
}) {
  if (isLoading) {
    return <QuickLoadingSpinner />;
  }

  return <>{children}</>;
}
