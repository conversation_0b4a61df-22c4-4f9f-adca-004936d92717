'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface TemplateTransitionLoadingProps {
  activeTab: string;
  className?: string;
}

/**
 * Component loading đặc biệt cho việc chuyển tab
 * Hiển thị layout cơ bản với animation mượt mà
 */
export function TemplateTransitionLoading({ 
  className 
}: TemplateTransitionLoadingProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Tab Content Header */}
      <Card className="transition-all duration-300 ease-in-out">
        <CardHeader>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-muted rounded animate-pulse" />
            <Skeleton className="h-6 w-24" />
            <Badge variant="default" className="opacity-60">
              <Skeleton className="h-3 w-12" />
            </Badge>
          </div>
          <Skeleton className="h-4 w-64 mt-2" />
        </CardHeader>
      </Card>

      {/* Main Content Area */}
      <Card className="transition-all duration-300 ease-in-out">
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-12">
            {/* Animated Loading Spinner */}
            <div className="relative">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-muted border-t-primary"></div>
              <div className="absolute inset-0 rounded-full h-12 w-12 border-4 border-transparent border-t-primary/30 animate-ping"></div>
            </div>
            <div className="ml-4 space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Component loading nhỏ gọn cho việc chuyển tab nhanh
 */
export function QuickTabTransitionLoading() {
  return (
    <div className="flex items-center justify-center py-8 space-x-3">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
      </div>
      <span className="text-sm text-muted-foreground">Đang chuyển tab...</span>
    </div>
  );
}

/**
 * Component loading cho data trong tab
 */
export function TabDataLoading({ message = "Đang tải dữ liệu..." }: { message?: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-8 space-y-4">
      <div className="relative">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-muted border-t-primary"></div>
      </div>
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  );
}

/**
 * Component loading overlay cho toàn bộ template management
 */
export function TemplateManagementOverlay({ isVisible }: { isVisible: boolean }) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <Card className="w-96">
        <CardContent className="p-6">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-muted border-t-primary"></div>
              <div className="absolute inset-0 rounded-full h-12 w-12 border-4 border-transparent border-t-primary/30 animate-ping"></div>
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-semibold">Đang tải Template Management</h3>
              <p className="text-sm text-muted-foreground">
                Vui lòng đợi trong giây lát...
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
