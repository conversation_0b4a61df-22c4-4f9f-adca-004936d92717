import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  SkipBack
} from 'lucide-react';

interface AdvancedPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  totalItems: number;
  className?: string;
}

export function AdvancedPagination({
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
  totalItems,
  className = ''
}: AdvancedPaginationProps) {
  const [jumpToPage, setJumpToPage] = useState('');

  const handleJumpToPage = () => {
    const page = parseInt(jumpToPage);
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
      setJumpToPage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  const handleQuickJump = (direction: 'forward' | 'backward', pages: number) => {
    if (direction === 'forward') {
      onPageChange(Math.min(totalPages, currentPage + pages));
    } else {
      onPageChange(Math.max(1, currentPage - pages));
    }
  };

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  if (totalPages <= 1) return null;

  return (
    <div className={`flex flex-col sm:flex-row items-center justify-between gap-4 ${className}`}>
      {/* Info and Items per page */}
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        <span>
          {startItem.toLocaleString()}-{endItem.toLocaleString()} của {totalItems.toLocaleString()} items
        </span>
        <span className="hidden sm:inline">•</span>
        <span>
          Trang {currentPage} / {totalPages}
        </span>
      </div>

      {/* Navigation Controls */}
      <div className="flex items-center gap-2">
        {/* Quick Jump Backward - Small to Large (outside to inside) */}
        <div className="flex items-center gap-1">
          {currentPage > 5 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickJump('backward', 5)}
              title="Lùi 5 trang"
              className="h-8 w-8 p-0"
            >
              <SkipBack className="h-3 w-3" />
              <span className="text-xs">5</span>
            </Button>
          )}
          {/* {currentPage > 10 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickJump('backward', 10)}
              title="Lùi 10 trang"
              className="h-8 w-8 p-0"
            >
              <SkipBack className="h-3 w-3" />
              <span className="text-xs">10</span>
            </Button>
          )}
          {currentPage > 100 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickJump('backward', 100)}
              title="Lùi 100 trang"
              className="h-8 w-8 p-0"
            >
              <SkipBack className="h-3 w-3" />
              <span className="text-xs">100</span>
            </Button>
          )} */}
        </div>

        {/* First Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          title="Trang đầu"
          className="h-8 w-8 p-0"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        {/* Previous Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          title="Trang trước"
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Jump to Page Input */}
        <div className="flex items-center gap-2">
          <Input
            type="number"
            min="1"
            max={totalPages}
            value={jumpToPage}
            onChange={(e) => setJumpToPage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={currentPage.toString()}
            className="h-8 w-16 text-center text-sm"
            title="Nhập số trang để chuyển đến"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={handleJumpToPage}
            disabled={!jumpToPage || parseInt(jumpToPage) < 1 || parseInt(jumpToPage) > totalPages}
            className="h-8 px-2 text-xs"
          >
            Go
          </Button>
        </div>

        {/* Next Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          title="Trang sau"
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Last Page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          title="Trang cuối"
          className="h-8 w-8 p-0"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>

        {/* Quick Jump Forward - Large to Small (inside to outside) */}
        <div className="flex items-center gap-1">
          {/* {currentPage + 100 <= totalPages && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickJump('forward', 100)}
              title="Tiến 100 trang"
              className="h-8 w-8 p-0"
            >
              <span className="text-xs">100</span>
              <SkipForward className="h-3 w-3" />
            </Button>
          )} */}
          {/* {currentPage + 10 <= totalPages && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickJump('forward', 10)}
              title="Tiến 10 trang"
              className="h-8 w-8 p-0"
            >
              <span className="text-xs">10</span>
              <SkipForward className="h-3 w-3" />
            </Button>
          )}
          {currentPage + 5 <= totalPages && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickJump('forward', 5)}
              title="Tiến 5 trang"
              className="h-8 w-8 p-0"
            >
              <span className="text-xs">5</span>
              <SkipForward className="h-3 w-3" />
            </Button>
          )} */}
        </div>
      </div>
    </div>
  );
}
