import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Settings, Info, Zap, Users, FileText } from 'lucide-react';
import { YbiParserConfig, YBI_PARSER_CONFIGS, getDefaultParserConfig } from '@/lib/parsers/ybi-parser';

interface ParserSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectParser: (config: YbiParserConfig) => void;
  selectedConfig?: YbiParserConfig;
}

export function ParserSelector({ open, onOpenChange, onSelectParser, selectedConfig }: ParserSelectorProps) {
  const [selected, setSelected] = React.useState<string>(
    selectedConfig?.id || getDefaultParserConfig().id
  );

  const handleConfirm = () => {
    const config = YBI_PARSER_CONFIGS.find(c => c.id === selected);
    if (config) {
      onSelectParser(config);
    }
    onOpenChange(false);
  };

  const getVersionIcon = (configId: string) => {
    switch (configId) {
      case 'v24_2':
        return <Zap className="h-4 w-4 text-green-500" />;
      case 'v24_1':
        return <Settings className="h-4 w-4 text-blue-500" />;
      case 'v23':
        return <Users className="h-4 w-4 text-orange-500" />;
      case 'v20':
        return <FileText className="h-4 w-4 text-gray-500" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getVersionBadge = (configId: string) => {
    switch (configId) {
      case 'v24_2':
        return <Badge variant="default" className="bg-green-100 text-green-800">Mới nhất</Badge>;
      case 'v24_1':
        return <Badge variant="secondary">Ổn định</Badge>;
      case 'v23':
        return <Badge variant="outline" className="border-orange-300 text-orange-700">Cũ hơn</Badge>;
      case 'v20':
        return <Badge variant="outline" className="border-gray-300 text-gray-600">Legacy</Badge>;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl lg:min-w-7xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Chọn Parser Version
          </DialogTitle>
          <DialogDescription>
            Chọn phiên bản parser phù hợp với file Ybi.cfg của bạn. Mỗi version có cấu trúc dữ liệu khác nhau.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 flex flex-row gap-4">
          <RadioGroup value={selected} onValueChange={setSelected} className='grid grid-cols-2 gap-4'>
            {YBI_PARSER_CONFIGS.map((config) => (
              <div key={config.id} className="space-y-2">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value={config.id} id={config.id} />
                  <Label htmlFor={config.id} className="cursor-pointer flex-1">
                    <Card className={`transition-all ${selected === config.id ? 'ring-2 ring-primary' : ''}`}>
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between text-base">
                          <div className="flex items-center gap-2">
                            {getVersionIcon(config.id)}
                            {config.name}
                          </div>
                          {getVersionBadge(config.id)}
                        </CardTitle>
                        <CardDescription className="text-sm">
                          {config.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                          <div>
                            <span className="font-medium">Skill Format:</span> {config.skillFormat}
                          </div>
                          <div>
                            <span className="font-medium">NPC Format:</span> {config.npcFormat}
                          </div>
                          <div>
                            <span className="font-medium">Max NPCs:</span> {config.constants.maxNpcs.toLocaleString()}
                          </div>
                          <div>
                            <span className="font-medium">Skill Size:</span> {config.constants.skillByteLength} bytes
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Label>
                </div>
              </div>
            ))}
          </RadioGroup>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-500 mt-0.5" />
              <div className="text-sm text-blue-700">
                <p className="font-medium mb-1">Gợi ý chọn version:</p>
                <ul className="space-y-1 text-xs">
                  <li>• <strong>V24.2:</strong> Dành cho file Ybi.cfg mới nhất</li>
                  <li>• <strong>V24.1:</strong> Dành cho file Ybi.cfg phiên bản ổn định</li>
                  <li>• <strong>V23:</strong> Dành cho file Ybi.cfg cũ hơn với skill format khác</li>
                  <li>• <strong>V20:</strong> Dành cho file Ybi.cfg rất cũ (legacy)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={handleConfirm}>
            Xác nhận
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
