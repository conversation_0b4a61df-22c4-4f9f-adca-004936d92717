'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  MoreHorizontal, 
  Search, 
  Edit, 
  Trash2, 
  Shield,
  Users,
  Key,
  Calendar
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { RoleWithStats, getRoleLevelText, getRoleLevelColor } from '@/lib/types/user-management';
import { deleteRole } from '@/lib/services/user-management';

interface RolesDataTableProps {
  data: RoleWithStats[];
  onEdit: (role: RoleWithStats) => void;
  onRefresh: () => void;
}

export function RolesDataTable({ data, onEdit, onRefresh }: RolesDataTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('all');

  // Filter data based on search and filters
  const filteredData = data.filter(role => {
    const matchesSearch = 
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (role.description && role.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesLevel = 
      levelFilter === 'all' || 
      role.level.toString() === levelFilter;

    return matchesSearch && matchesLevel;
  });

  const handleDelete = async (role: RoleWithStats) => {
    if (role.userCount > 0) {
      toast.error('Không thể xóa vai trò đang được sử dụng');
      return;
    }

    if (!confirm(`Bạn có chắc chắn muốn xóa vai trò "${role.name}"?`)) {
      return;
    }

    try {
      const result = await deleteRole(role.id);
      if (result.success) {
        toast.success(result.message || 'Xóa vai trò thành công');
        onRefresh();
      } else {
        toast.error(result.message || 'Không thể xóa vai trò');
      }
    } catch  {
      toast.error('Có lỗi xảy ra khi xóa vai trò');
    }
  };

  const formatPermissions = (permissions: string[] | null) => {
    if (!permissions || permissions.length === 0) {
      return 'Không có quyền';
    }
    
    const displayCount = 3;
    const displayed = permissions.slice(0, displayCount);
    const remaining = permissions.length - displayCount;
    
    return (
      <div className="flex flex-wrap gap-1">
        {displayed.map((permission, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {permission.split('.')[1] || permission}
          </Badge>
        ))}
        {remaining > 0 && (
          <Badge variant="secondary" className="text-xs">
            +{remaining}
          </Badge>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Tìm kiếm theo tên hoặc mô tả..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={levelFilter} onValueChange={setLevelFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Cấp độ" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả cấp độ</SelectItem>
            <SelectItem value="1">Admin (1)</SelectItem>
            <SelectItem value="2">Manager (2)</SelectItem>
            <SelectItem value="3">Moderator (3)</SelectItem>
            <SelectItem value="4">Editor (4)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Vai trò</TableHead>
              <TableHead>Mô tả</TableHead>
              <TableHead>Cấp độ</TableHead>
              <TableHead>Quyền hạn</TableHead>
              <TableHead>Người dùng</TableHead>
              <TableHead>Ngày tạo</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  Không tìm thấy vai trò nào
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((role) => (
                <TableRow key={role.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                        <Shield className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{role.name}</div>
                        <div className="text-sm text-muted-foreground">
                          ID: {role.id.slice(0, 8)}...
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="max-w-[200px]">
                      {role.description ? (
                        <p className="text-sm truncate" title={role.description}>
                          {role.description}
                        </p>
                      ) : (
                        <span className="text-muted-foreground text-sm">
                          Không có mô tả
                        </span>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Badge variant={getRoleLevelColor(role.level)}>
                      {getRoleLevelText(role.level)} ({role.level})
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <div className="max-w-[250px]">
                      {formatPermissions(role.permissions)}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{role.userCount}</span>
                      <span className="text-sm text-muted-foreground">người dùng</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      {format(new Date(role.createdAt), 'dd/MM/yyyy', { locale: vi })}
                    </div>
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Mở menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Thao tác</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => onEdit(role)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => navigator.clipboard.writeText(role.id)}
                        >
                          <Key className="mr-2 h-4 w-4" />
                          Sao chép ID
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleDelete(role)}
                          className="text-destructive"
                          disabled={role.userCount > 0}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Results info */}
      <div className="text-sm text-muted-foreground">
        {filteredData.length} trong tổng số {data.length} vai trò
      </div>
    </div>
  );
}
