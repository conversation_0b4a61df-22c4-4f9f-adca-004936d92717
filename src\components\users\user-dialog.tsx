'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Loader2, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';
import { UserWithRoles, RoleWithStats, CreateUserData, UpdateUserData, getRoleLevelText, getRoleLevelColor } from '@/lib/types/user-management';
import { createUser, updateUser } from '@/lib/services/user-management';

interface UserDialogProps {
  open: boolean;
  onClose: (success?: boolean) => void;
  user?: UserWithRoles | null;
  roles: RoleWithStats[];
}

interface FormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  isActive: boolean;
  roleIds: string[];
}

interface FormErrors {
  name?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
}

export function UserDialog({ open, onClose, user, roles }: UserDialogProps) {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    isActive: true,
    roleIds: [],
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const isEditing = !!user;

  // Reset form when dialog opens/closes or user changes
  useEffect(() => {
    if (open) {
      if (user) {
        setFormData({
          name: user.name,
          email: user.email,
          password: '',
          confirmPassword: '',
          isActive: user.isActive,
          roleIds: user.roles.map(role => role.id),
        });
      } else {
        setFormData({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          isActive: true,
          roleIds: [],
        });
      }
      setErrors({});
      setShowPassword(false);
      setShowConfirmPassword(false);
    }
  }, [open, user]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên là bắt buộc';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    if (!isEditing) {
      if (!formData.password) {
        newErrors.password = 'Mật khẩu là bắt buộc';
      } else if (formData.password.length < 6) {
        newErrors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Mật khẩu xác nhận không khớp';
      }
    } else if (formData.password) {
      if (formData.password.length < 6) {
        newErrors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
      }
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Mật khẩu xác nhận không khớp';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      let result;
      
      if (isEditing && user) {
        const updateData: UpdateUserData & { roleIds?: string[] } = {
          name: formData.name,
          email: formData.email,
          isActive: formData.isActive,
          roleIds: formData.roleIds,
        };

        if (formData.password) {
          updateData.password = formData.password;
        }

        result = await updateUser(user.id, updateData);
      } else {
        const createData: CreateUserData = {
          name: formData.name,
          email: formData.email,
          password: formData.password,
          isActive: formData.isActive,
          roleIds: formData.roleIds,
        };
        
        result = await createUser(createData);
      }

      if (result.success) {
        const successMessage = isEditing
          ? (roleChanges.added.length > 0 || roleChanges.removed.length > 0
              ? 'Cập nhật người dùng và vai trò thành công'
              : 'Cập nhật người dùng thành công')
          : 'Tạo người dùng thành công';
        toast.success(result.message || successMessage);
        onClose(true);
      } else {
        toast.error(result.message || `Không thể ${isEditing ? 'cập nhật' : 'tạo'} người dùng`);
      }
    } catch  {
      toast.error('Có lỗi xảy ra ' );
    } finally {
      setLoading(false);
    }
  };

  const handleRoleToggle = (roleId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      roleIds: checked
        ? [...prev.roleIds, roleId]
        : prev.roleIds.filter(id => id !== roleId)
    }));
  };

  // Get role changes for editing
  const getRoleChanges = () => {
    if (!isEditing || !user) return { added: [], removed: [] };

    const currentRoleIds = user.roles.map(role => role.id);
    const newRoleIds = formData.roleIds;

    const added = newRoleIds.filter(id => !currentRoleIds.includes(id));
    const removed = currentRoleIds.filter(id => !newRoleIds.includes(id));

    return { added, removed };
  };

  const roleChanges = getRoleChanges();

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Cập nhật thông tin người dùng và vai trò. Thay đổi vai trò sẽ được áp dụng ngay lập tức.'
              : 'Tạo tài khoản người dùng mới và gán vai trò.'
            }
          </DialogDescription>
          {isEditing && user && (
            <div className="flex items-center gap-2 mt-2">
              <span className="text-sm text-muted-foreground">Vai trò hiện tại:</span>
              <div className="flex gap-1">
                {user.roles.length === 0 ? (
                  <Badge variant="outline" className="text-xs">Chưa có vai trò</Badge>
                ) : (
                  user.roles.map(role => (
                    <Badge key={role.id} variant={getRoleLevelColor(role.level)} className="text-xs">
                      {role.name}
                    </Badge>
                  ))
                )}
              </div>
            </div>
          )}
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Info */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Tên người dùng</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Nhập tên người dùng"
                disabled={loading}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="Nhập địa chỉ email"
                disabled={loading}
              />
              {errors.email && (
                <p className="text-sm text-destructive">{errors.email}</p>
              )}
            </div>

            {/* Password fields */}
            <div className="space-y-2">
              <Label htmlFor="password">
                {isEditing ? 'Mật khẩu mới (để trống nếu không đổi)' : 'Mật khẩu'}
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder={isEditing ? 'Nhập mật khẩu mới' : 'Nhập mật khẩu'}
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-destructive">{errors.password}</p>
              )}
            </div>

            {(formData.password || !isEditing) && (
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Xác nhận mật khẩu</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    placeholder="Nhập lại mật khẩu"
                    disabled={loading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={loading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-destructive">{errors.confirmPassword}</p>
                )}
              </div>
            )}

            {/* Status */}
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                disabled={loading}
              />
              <Label htmlFor="isActive">Tài khoản hoạt động</Label>
            </div>
          </div>

          {/* Roles Section */}
          <Separator />
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Vai trò</Label>
              <div className="flex items-center gap-2">
                {formData.roleIds.length === 0 && (
                  <Badge variant="outline" className="text-xs text-amber-600">
                    Chưa có vai trò
                  </Badge>
                )}
                {isEditing && formData.roleIds.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {formData.roleIds.length} vai trò được chọn
                  </Badge>
                )}
              </div>
            </div>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {roles.map((role) => (
                <div key={role.id} className="flex items-center space-x-3">
                  <Checkbox
                    id={`role-${role.id}`}
                    checked={formData.roleIds.includes(role.id)}
                    onCheckedChange={(checked) => handleRoleToggle(role.id, checked as boolean)}
                    disabled={loading}
                  />
                  <div className="flex-1 flex items-center justify-between">
                    <div>
                      <Label htmlFor={`role-${role.id}`} className="font-medium">
                        {role.name}
                      </Label>
                      {role.description && (
                        <p className="text-sm text-muted-foreground">
                          {role.description}
                        </p>
                      )}
                    </div>
                    <Badge variant={getRoleLevelColor(role.level)}>
                      {getRoleLevelText(role.level)}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
            {isEditing && (roleChanges.added.length > 0 || roleChanges.removed.length > 0) && (
              <div className="p-3 bg-muted rounded-md space-y-2">
                <p className="text-sm font-medium">Thay đổi vai trò:</p>
                {roleChanges.added.length > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-green-600">Thêm:</span>
                    <div className="flex gap-1">
                      {roleChanges.added.map(roleId => {
                        const role = roles.find(r => r.id === roleId);
                        return role ? (
                          <Badge key={roleId} variant="outline" className="text-xs text-green-600">
                            +{role.name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
                {roleChanges.removed.length > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-red-600">Xóa:</span>
                    <div className="flex gap-1">
                      {roleChanges.removed.map(roleId => {
                        const role = user?.roles.find(r => r.id === roleId);
                        return role ? (
                          <Badge key={roleId} variant="outline" className="text-xs text-red-600">
                            -{role.name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
                <p className="text-xs text-muted-foreground">
                  Thay đổi sẽ được áp dụng ngay lập tức sau khi lưu.
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onClose()}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Cập nhật' : 'Tạo mới'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
