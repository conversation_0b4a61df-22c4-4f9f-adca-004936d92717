'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Shield, UserCheck, Plus, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { UsersDataTable } from './users-data-table';
import { RolesDataTable } from './roles-data-table';
import { UserDialog } from './user-dialog';
// import { UserRoleAssignment } from './user-role-assignment';
// import { RoleDialog } from './role-dialog';
import {
  fetchUsers,
  fetchRoles
} from '@/lib/services/user-management';
import { 
  UserWithRoles, 
  RoleWithStats 
} from '@/lib/types/user-management';

export function UserManager() {
  const [activeTab, setActiveTab] = useState('users');
  const [users, setUsers] = useState<UserWithRoles[]>([]);
  const [roles, setRoles] = useState<RoleWithStats[]>([]);
  // const [permissions, setPermissions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  // const [roleDialogOpen, setRoleDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithRoles | null>(null);
  // const [selectedRole, setSelectedRole] = useState<RoleWithStats | null>(null);

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [usersData, rolesData] = await Promise.all([
        fetchUsers(),
        fetchRoles()
      ]);
      setUsers(usersData);
      setRoles(rolesData);
      // setPermissions(permissionsData);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Không thể tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadData();
  };

  const handleCreateUser = () => {
    setSelectedUser(null);
    setUserDialogOpen(true);
  };

  const handleEditUser = (user: UserWithRoles) => {
    setSelectedUser(user);
    setUserDialogOpen(true);
  };

  const handleCreateRole = () => {
    // setSelectedRole(null);
    // setRoleDialogOpen(true);
  };



  const handleUserDialogClose = (success?: boolean) => {
    setUserDialogOpen(false);
    setSelectedUser(null);
    if (success) {
      loadData();
    }
  };

  // const handleRoleDialogClose = (success?: boolean) => {
  //   setRoleDialogOpen(false);
  //   setSelectedRole(null);
  //   if (success) {
  //     loadData();
  //   }
  // };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const activeUsers = users.filter(u => u.isActive).length;
  const inactiveUsers = users.filter(u => !u.isActive).length;
  const totalRoles = roles.length;

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng người dùng</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
            <div className="flex gap-2 mt-2">
              <Badge variant="secondary" className="text-xs">
                {activeUsers} hoạt động
              </Badge>
              <Badge variant="outline" className="text-xs">
                {inactiveUsers} không hoạt động
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng vai trò</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRoles}</div>
            <p className="text-xs text-muted-foreground mt-2">
              Các vai trò được định nghĩa
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Phân quyền</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.reduce((acc, user) => acc + user.roles.length, 0)}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Tổng số vai trò được gán
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="users">Người dùng</TabsTrigger>
            <TabsTrigger value="roles">Vai trò</TabsTrigger>
            <TabsTrigger value="assignments">Phân quyền</TabsTrigger>
          </TabsList>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleRefresh}>
              Làm mới
            </Button>
            {activeTab === 'users' && (
              <Button onClick={handleCreateUser}>
                <Plus className="h-4 w-4 mr-2" />
                Thêm người dùng
              </Button>
            )}
            {activeTab === 'roles' && (
              <Button onClick={handleCreateRole}>
                <Plus className="h-4 w-4 mr-2" />
                Thêm vai trò
              </Button>
            )}
          </div>
        </div>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Danh sách người dùng</CardTitle>
              <CardDescription>
                Quản lý tài khoản người dùng trong hệ thống
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UsersDataTable
                data={users}
                onEdit={handleEditUser}
                onRefresh={handleRefresh}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Danh sách vai trò</CardTitle>
              <CardDescription>
                Quản lý các vai trò và quyền hạn trong hệ thống
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RolesDataTable
                data={roles}
                onEdit={() => {
                  // TODO: Implement role editing
                  toast.info('Role editing will be implemented soon');
                }}
                onRefresh={handleRefresh}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Phân quyền người dùng</CardTitle>
              <CardDescription>
                Gán và quản lý vai trò cho người dùng
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                UserRoleAssignment component sẽ được tạo
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <UserDialog
        open={userDialogOpen}
        onClose={handleUserDialogClose}
        user={selectedUser}
        roles={roles}
      />

      {/* RoleDialog sẽ được tạo sau */}
    </div>
  );
}
