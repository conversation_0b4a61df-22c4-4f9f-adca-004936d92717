'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

/**
 * Hook để quản lý loading state khi chuyển tab
 * Giúp tạo hiệu ứng loading mượt mà khi người dùng chuyển tab
 */
export function useTabLoading(defaultTab: string = 'shop', loadingDelay: number = 300) {
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || defaultTab;
  
  const [isLoading, setIsLoading] = useState(false);
  const [previousTab, setPreviousTab] = useState(currentTab);

  useEffect(() => {
    // Nếu tab thay đổi, hiển thị loading
    if (currentTab !== previousTab) {
      setIsLoading(true);
      
      // Simulate loading time để tạo hiệu ứng mượt mà
      const timer = setTimeout(() => {
        setIsLoading(false);
        setPreviousTab(currentTab);
      }, loadingDelay);

      return () => clearTimeout(timer);
    }
  }, [currentTab, previousTab, loadingDelay]);

  return {
    isLoading,
    currentTab,
    previousTab
  };
}

/**
 * Hook để quản lý loading state cho data fetching
 */
export function useDataLoading() {
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const startLoading = () => {
    setIsDataLoading(true);
    setError(null);
  };

  const stopLoading = () => {
    setIsDataLoading(false);
  };

  const setLoadingError = (errorMessage: string) => {
    setError(errorMessage);
    setIsDataLoading(false);
  };

  return {
    isDataLoading,
    error,
    startLoading,
    stopLoading,
    setLoadingError
  };
}
