'use server'

import { db } from "@/lib/db";
import { user, roles, userRoles } from "@/../drizzle/web-admin/schema";
import { eq, and, sql } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { userHasPermission, PERMISSIONS, assignRoleToUser, removeRoleFromUser, ROLE_LEVELS } from "@/lib/auth-utils";
import { revalidatePath } from "next/cache";
import { nanoid } from "nanoid";

// Types
export interface RoleWithStats {
  id: string;
  name: string;
  description: string | null;
  level: number;
  permissions: string[] | null;
  createdAt: Date;
  updatedAt: Date;
  userCount: number;
}

export interface CreateRoleData {
  name: string;
  description?: string;
  level: 1 |2 |3 |4;
  permissions: string[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  level?: number | 1 |2 |3 |4;
  permissions?: string[];
}

export interface UserRoleAssignment {
  userId: string;
  roleId: string;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
}

// Get current session and check permissions
async function getCurrentSession() {
  const session = await auth.api.getSession({
    headers: await headers()
  });
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  return session;
}

// Get all roles with user count
export async function getRolesWithStats(): Promise<RoleWithStats[]> {
  const session = await getCurrentSession();
  
  // Check permission
  const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.ROLE_READ);
  if (!hasPermission) {
    throw new Error("Insufficient permissions");
  }

  const rolesData = await db
    .select({
      role: roles,
      userCount: sql<number>`count(${userRoles.userId})::int`
    })
    .from(roles)
    .leftJoin(userRoles, eq(roles.id, userRoles.roleId))
    .groupBy(roles.id)
    .orderBy(roles.level, roles.name);

  return rolesData.map(row => ({
    ...row.role,
    userCount: row.userCount || 0
  }));
}

// Get single role by ID
export async function getRoleById(roleId: string): Promise<RoleWithStats | null> {
  const session = await getCurrentSession();
  
  // Check permission
  const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.ROLE_READ);
  if (!hasPermission) {
    throw new Error("Insufficient permissions");
  }

  const roleData = await db
    .select({
      role: roles,
      userCount: sql<number>`count(${userRoles.userId})::int`
    })
    .from(roles)
    .leftJoin(userRoles, eq(roles.id, userRoles.roleId))
    .where(eq(roles.id, roleId))
    .groupBy(roles.id)
    .limit(1);

  if (roleData.length === 0) {
    return null;
  }

  return {
    ...roleData[0].role,
    userCount: roleData[0].userCount || 0
  };
}

// Create new role
export async function createRole(data: CreateRoleData): Promise<{ success: boolean; message: string; roleId?: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.ROLE_CREATE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    // Check if role name already exists
    const existingRole = await db
      .select()
      .from(roles)
      .where(eq(roles.name, data.name))
      .limit(1);

    if (existingRole.length > 0) {
      return { success: false, message: "Role name already exists" };
    }

    // Validate level
    const validLevels = Object.values(ROLE_LEVELS);
    if (!validLevels.includes(data.level)) {
      return { success: false, message: "Invalid role level" };
    }

    // Create role
    const roleId = nanoid();
    // await db.insert(roles).values({
    //   id: roleId,
    //   name: data.name,
    //   description: data.description || null,
    //   level: data.level,
    //   permissions: data.permissions,
    //   createdAt: new Date(),
    //   updatedAt: new Date(),
    // });

    revalidatePath('/dashboard/users');
    return { success: true, message: "Role created successfully", roleId };
  } catch (error) {
    console.error("Error creating role:", error);
    return { success: false, message: "Failed to create role" };
  }
}

// Update role
export async function updateRole(roleId: string, data: UpdateRoleData): Promise<{ success: boolean; message: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.ROLE_UPDATE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    // Check if role name already exists (if name is being updated)
    if (data.name) {
      const existingRole = await db
        .select()
        .from(roles)
        .where(and(eq(roles.name, data.name), eq(roles.id, roleId)))
        .limit(1);

      if (existingRole.length === 0) {
        const nameExists = await db
          .select()
          .from(roles)
          .where(eq(roles.name, data.name))
          .limit(1);

        if (nameExists.length > 0) {
          return { success: false, message: "Role name already exists" };
        }
      }
    }

    // Validate level if provided
    if (data.level !== undefined) {
      // const validLevels = Object.values(ROLE_LEVELS);
      // if (!validLevels.includes(data.level)) {
      //   return { success: false, message: "Invalid role level" };
      // }
    }

    // Prepare update data
    const updateData: Record<string, unknown> = {
      updatedAt: new Date(),
    };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.level !== undefined) updateData.level = data.level;
    if (data.permissions !== undefined) updateData.permissions = data.permissions;

    // Update role
    await db
      .update(roles)
      .set(updateData)
      .where(eq(roles.id, roleId));

    revalidatePath('/dashboard/users');
    return { success: true, message: "Role updated successfully" };
  } catch (error) {
    console.error("Error updating role:", error);
    return { success: false, message: "Failed to update role" };
  }
}

// Delete role
export async function deleteRole(roleId: string): Promise<{ success: boolean; message: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.ROLE_DELETE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    // Check if role has users assigned
    const assignedUsers = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.roleId, roleId))
      .limit(1);

    if (assignedUsers.length > 0) {
      return { success: false, message: "Cannot delete role with assigned users" };
    }

    // Delete role
    await db
      .delete(roles)
      .where(eq(roles.id, roleId));

    revalidatePath('/dashboard/users');
    return { success: true, message: "Role deleted successfully" };
  } catch (error) {
    console.error("Error deleting role:", error);
    return { success: false, message: "Failed to delete role" };
  }
}

// Assign role to user
export async function assignUserRole(userId: string, roleId: string): Promise<{ success: boolean; message: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_UPDATE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    await assignRoleToUser(userId, roleId, session.user.id);

    revalidatePath('/dashboard/users');
    return { success: true, message: "Role assigned successfully" };
  } catch (error) {
    console.error("Error assigning role:", error);
    if (error instanceof Error && error.message === "User already has this role") {
      return { success: false, message: "User already has this role" };
    }
    return { success: false, message: "Failed to assign role" };
  }
}

// Remove role from user
export async function removeUserRole(userId: string, roleId: string): Promise<{ success: boolean; message: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_UPDATE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    await removeRoleFromUser(userId, roleId);

    revalidatePath('/dashboard/users');
    return { success: true, message: "Role removed successfully" };
  } catch (error) {
    console.error("Error removing role:", error);
    return { success: false, message: "Failed to remove role" };
  }
}

// Get users with specific role
export async function getUsersByRole(roleId: string) {
  const session = await getCurrentSession();
  
  // Check permission
  const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_READ);
  if (!hasPermission) {
    throw new Error("Insufficient permissions");
  }

  const usersWithRole = await db
    .select({
      user: user,
      userRole: userRoles
    })
    .from(user)
    .innerJoin(userRoles, eq(user.id, userRoles.userId))
    .where(and(
      eq(userRoles.roleId, roleId),
      eq(user.isActive, true)
    ))
    .orderBy(user.name);

  return usersWithRole.map(row => ({
    ...row.user,
    assignedAt: row.userRole.assignedAt,
    expiresAt: row.userRole.expiresAt
  }));
}

// Get available permissions list
export async function getAvailablePermissions(): Promise<string[]> {
  const session = await getCurrentSession();
  
  // Check permission
  const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.ROLE_READ);
  if (!hasPermission) {
    throw new Error("Insufficient permissions");
  }

  // Return all available permissions
  return Object.values(PERMISSIONS);
}
