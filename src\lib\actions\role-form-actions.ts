'use server'

import { revalidatePath } from "next/cache";
import { createRole, updateRole, deleteRole } from "./role-actions";

// Form action for creating role
export async function createRoleAction(formData: FormData) {
  const name = formData.get('name') as string;
  const description = formData.get('description') as string;
  const level = parseInt(formData.get('level') as string) as 1 | 2 | 3 | 4;
  const permissions = formData.getAll('permissions') as string[];

  const result = await createRole({
    name,
    description,
    level,
    permissions
  });

  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}

// Form action for updating role
export async function updateRoleAction(formData: FormData) {
  const roleId = formData.get('roleId') as string;
  const name = formData.get('name') as string;
  const description = formData.get('description') as string;
  const level = parseInt(formData.get('level') as string);
  const permissions = formData.getAll('permissions') as string[];

  const result = await updateRole(roleId, {
    name,
    description,
    level,
    permissions
  });
  
  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}

// Form action for deleting role
export async function deleteRoleAction(formData: FormData) {
  const roleId = formData.get('roleId') as string;
  
  const result = await deleteRole(roleId);
  
  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}
