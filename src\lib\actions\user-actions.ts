'use server'

import { db } from "@/lib/db";
// Import schema tables
import { user, roles, userRoles, account } from "@/../drizzle/web-admin/schema";
import { eq, and, desc } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { userHasPermission, PERMISSIONS, getUserWithRoles, assignRoleToUser, canUserManageUser } from "@/lib/auth-utils";
import { revalidatePath } from "next/cache";
import { nanoid } from "nanoid";
import bcrypt from "bcryptjs";

// Types
export interface UserWithRoles {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  isActive: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  roles: Array<{
    id: string;
    name: string;
    description: string | null;
    level: number;
    permissions: string[] | null;
  }>;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  isActive?: boolean;
  roleIds?: string[];
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  isActive?: boolean;
  password?: string;
}

// Get current session and check permissions
async function getCurrentSession() {
  const session = await auth.api.getSession({
    headers: await headers()
  });
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  return session;
}

// Get all users with their roles
export async function getUsersWithRoles(): Promise<UserWithRoles[]> {
  const session = await getCurrentSession();
  
  // Check permission
  const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_READ);
  if (!hasPermission) {
    throw new Error("Insufficient permissions");
  }

  const usersData = await db
    .select({
      user: user,
      role: roles,
      userRole: userRoles,
    })
    .from(user)
    .leftJoin(userRoles, eq(user.id, userRoles.userId))
    .leftJoin(roles, eq(userRoles.roleId, roles.id))
    .orderBy(desc(user.createdAt));

  // Group users with their roles
  const usersMap = new Map<string, UserWithRoles>();
  
  for (const row of usersData) {
    const userId = row.user.id;
    
    if (!usersMap.has(userId)) {
      usersMap.set(userId, {
        ...row.user,
        roles: []
      });
    }
    
    if (row.role) {
      usersMap.get(userId)!.roles.push(row.role);
    }
  }
  
  return Array.from(usersMap.values());
}

// Get single user with roles
export async function getUserById(userId: string): Promise<UserWithRoles | null> {
  const session = await getCurrentSession();
  
  // Check permission
  const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_READ);
  if (!hasPermission) {
    throw new Error("Insufficient permissions");
  }

  const userData = await getUserWithRoles(userId);
  if (!userData) {
    return null;
  }

  return {
    ...userData,
    roles: userData.roles.map(r => r.role)
  };
}

// Create new user
export async function createUser(data: CreateUserData): Promise<{ success: boolean; message: string; userId?: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_CREATE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    // Check if email already exists
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.email, data.email))
      .limit(1);

    if (existingUser.length > 0) {
      return { success: false, message: "Email already exists" };
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 12);
    
    // Create user
    const userId = nanoid();
    await db.insert(user).values({
      id: userId,
      name: data.name,
      email: data.email,
      emailVerified: false,
      image: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: data.isActive ?? true,
      lastLoginAt: null,
    });

    // Create account record for password
    await db.insert(account).values({
      id: nanoid(),
      accountId: data.email,
      providerId: "credential",
      userId: userId,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Assign roles if provided
    if (data.roleIds && data.roleIds.length > 0) {
      for (const roleId of data.roleIds) {
        await assignRoleToUser(userId, roleId, session.user.id);
      }
    }

    revalidatePath('/dashboard/users');
    return { success: true, message: "User created successfully", userId };
  } catch (error) {
    console.error("Error creating user:", error);
    return { success: false, message: "Failed to create user" };
  }
}

// Update user
export async function updateUser(userId: string, data: UpdateUserData): Promise<{ success: boolean; message: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_UPDATE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    // Check if user can manage target user
    const canManage = await canUserManageUser(session.user.id, userId);
    if (!canManage) {
      return { success: false, message: "Cannot manage this user" };
    }

    // Check if email already exists (if email is being updated)
    if (data.email) {
      const existingUser = await db
        .select()
        .from(user)
        .where(and(eq(user.email, data.email), eq(user.id, userId)))
        .limit(1);

      if (existingUser.length === 0) {
        const emailExists = await db
          .select()
          .from(user)
          .where(eq(user.email, data.email))
          .limit(1);

        if (emailExists.length > 0) {
          return { success: false, message: "Email already exists" };
        }
      }
    }

    // Prepare update data
    const updateData: Record<string, unknown> = {
    };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.email !== undefined) updateData.email = data.email;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    // Update user
    await db
      .update(user)
      .set(updateData)
      .where(eq(user.id, userId));

    // Update password if provided
    if (data.password) {
      const hashedPassword = await bcrypt.hash(data.password, 12);
      const { account } = await import("@/../drizzle/web-admin/schema");
      
      await db
        .update(account)
        .set({ 
          password: hashedPassword,
        })
        .where(and(
          eq(account.userId, userId),
          eq(account.providerId, "credential")
        ));
      // await auth.api.changePassword({
      //   body: {
      //     newPassword: data.password,
      //     currentPassword: data.password,
      //     revokeOtherSessions: true,
      //   }
      // })
    }

    revalidatePath('/dashboard/users');
    return { success: true, message: "User updated successfully" };
  } catch (error) {
    console.error("Error updating user:", error);
    return { success: false, message: "Failed to update user" };
  }
}

// Delete user
export async function deleteUser(userId: string): Promise<{ success: boolean; message: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_DELETE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    // Check if user can manage target user
    const canManage = await canUserManageUser(session.user.id, userId);
    if (!canManage) {
      return { success: false, message: "Cannot delete this user" };
    }

    // Cannot delete self
    if (userId === session.user.id) {
      return { success: false, message: "Cannot delete your own account" };
    }

    // Delete user (cascade will handle related records)
    await db
      .delete(user)
      .where(eq(user.id, userId));

    revalidatePath('/dashboard/users');
    return { success: true, message: "User deleted successfully" };
  } catch (error) {
    console.error("Error deleting user:", error);
    return { success: false, message: "Failed to delete user" };
  }
}

// Toggle user active status
export async function toggleUserStatus(userId: string): Promise<{ success: boolean; message: string }> {
  try {
    const session = await getCurrentSession();
    
    // Check permission
    const hasPermission = await userHasPermission(session.user.id, PERMISSIONS.USER_UPDATE);
    if (!hasPermission) {
      return { success: false, message: "Insufficient permissions" };
    }

    // Check if user can manage target user
    const canManage = await canUserManageUser(session.user.id, userId);
    if (!canManage) {
      return { success: false, message: "Cannot manage this user" };
    }

    // Cannot deactivate self
    if (userId === session.user.id) {
      return { success: false, message: "Cannot deactivate your own account" };
    }

    // Get current status
    const currentUser = await db
      .select({ isActive: user.isActive })
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (currentUser.length === 0) {
      return { success: false, message: "User not found" };
    }

    // Toggle status
    const newStatus = !currentUser[0].isActive;
    await db
      .update(user)
      .set({ 
        isActive: newStatus,
      })
      .where(eq(user.id, userId));

    revalidatePath('/dashboard/users');
    return { 
      success: true, 
      message: `User ${newStatus ? 'activated' : 'deactivated'} successfully` 
    };
  } catch (error) {
    console.error("Error toggling user status:", error);
    return { success: false, message: "Failed to update user status" };
  }
}
