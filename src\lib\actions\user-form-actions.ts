'use server'

import { revalidatePath } from "next/cache";
import { createUser, updateUser, deleteUser, toggleUserStatus } from "./user-actions";
import { assignUserRole, removeUserRole } from "./role-actions";

// Form action for creating user
export async function createUserAction(formData: FormData) {
  const name = formData.get('name') as string;
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const isActive = formData.get('isActive') === 'true';
  const roleIds = formData.getAll('roleIds') as string[];

  const result = await createUser({
    name,
    email,
    password,
    isActive,
    roleIds
  });

  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}

// Form action for updating user
export async function updateUserAction(formData: FormData) {
  const userId = formData.get('userId') as string;
  const name = formData.get('name') as string;
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const password_old = formData.get('password_old') as string;
  const isActive = formData.get('isActive') === 'true';

  const updateData: Record<string, unknown> = { name, email, isActive };
  if (password) {
    updateData.password = password;
    updateData.password_old = password_old;
  }

  const result = await updateUser(userId, updateData);
  
  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}

// Form action for deleting user
export async function deleteUserAction(formData: FormData) {
  const userId = formData.get('userId') as string;
  
  const result = await deleteUser(userId);
  
  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}

// Form action for toggling user status
export async function toggleUserStatusAction(formData: FormData) {
  const userId = formData.get('userId') as string;
  
  const result = await toggleUserStatus(userId);
  
  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}

// Form action for assigning role
export async function assignRoleAction(formData: FormData) {
  const userId = formData.get('userId') as string;
  const roleId = formData.get('roleId') as string;
  
  const result = await assignUserRole(userId, roleId);
  
  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}

// Form action for removing role
export async function removeRoleAction(formData: FormData) {
  const userId = formData.get('userId') as string;
  const roleId = formData.get('roleId') as string;
  
  const result = await removeUserRole(userId, roleId);
  
  revalidatePath('/dashboard/users');
  
  if (result.success) {
    return { success: true, message: result.message };
  } else {
    return { success: false, message: result.message };
  }
}
