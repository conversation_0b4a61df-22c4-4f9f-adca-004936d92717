import { db } from "./db";
import { user, roles, userRoles } from "@/../drizzle/web-admin/schema";
import { eq, and } from "drizzle-orm";
import { nanoid } from "nanoid";

// Role levels for hierarchy
export const ROLE_LEVELS = {
  ADMIN: 1,
  MANAGER: 2,
  MODERATOR: 3,
  EDITOR: 4,
} as const;

// Permission constants
export const PERMISSIONS = {
  // User management
  USER_CREATE: "user.create",
  USER_READ: "user.read",
  USER_UPDATE: "user.update",
  USER_DELETE: "user.delete",
  
  // Role management
  ROLE_CREATE: "role.create",
  ROLE_READ: "role.read",
  R<PERSON><PERSON>_UPDATE: "role.update",
  ROLE_DELETE: "role.delete",
  
  // Character management
  CHARACTER_CREATE: "character.create",
  CHARACTER_READ: "character.read",
  CHARACTER_UPDATE: "character.update",
  CHARACTER_DELETE: "character.delete",
  
  // Server management
  SERVER_CREATE: "server.create",
  SERVER_READ: "server.read",
  SERVER_UPDATE: "server.update",
  SERVER_DELETE: "server.delete",
  
  // Event management
  EVENT_CREATE: "event.create",
  EVENT_READ: "event.read",
  EVENT_UPDATE: "event.update",
  EVENT_DELETE: "event.delete",
  
  // News management
  NEWS_CREATE: "news.create",
  NEWS_READ: "news.read",
  NEWS_UPDATE: "news.update",
  NEWS_DELETE: "news.delete",
  
  // Config management
  CONFIG_CREATE: "config.create",
  CONFIG_READ: "config.read",
  CONFIG_UPDATE: "config.update",
  CONFIG_DELETE: "config.delete",
  
  // Analytics and logs
  ANALYTICS_READ: "analytics.read",
  LOGS_READ: "logs.read",
  
  // System management
  SYSTEM_MANAGE: "system.manage",
} as const;

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];
export type RoleLevel = typeof ROLE_LEVELS[keyof typeof ROLE_LEVELS];

// Get user with roles and permissions
export async function getUserWithRoles(userId: string) {
  const userWithRoles = await db
    .select({
      user: user,
      role: roles,
      userRole: userRoles,
    })
    .from(user)
    .leftJoin(userRoles, eq(user.id, userRoles.userId))
    .leftJoin(roles, eq(userRoles.roleId, roles.id))
    .where(and(
      eq(user.id, userId),
      eq(user.isActive, true)
    ));

  if (userWithRoles.length === 0) {
    return null;
  }

  const userData = userWithRoles[0].user;
  const userRoleData = userWithRoles
    .filter(row => row.role !== null)
    .map(row => ({
      role: row.role!,
      userRole: row.userRole!,
    }));

  return {
    ...userData,
    roles: userRoleData,
  };
}

// Check if user has specific permission
export async function userHasPermission(userId: string, permission: Permission): Promise<boolean> {
  const userWithRoles = await getUserWithRoles(userId);
  
  if (!userWithRoles || userWithRoles.roles.length === 0) {
    return false;
  }

  // Check if any of user's roles has the permission
  return userWithRoles.roles.some(({ role }) => {
    return role.permissions && role.permissions.includes(permission);
  });
}

// Check if user has role with minimum level
export async function userHasMinimumRoleLevel(userId: string, minimumLevel: RoleLevel): Promise<boolean> {
  const userWithRoles = await getUserWithRoles(userId);
  
  if (!userWithRoles || userWithRoles.roles.length === 0) {
    return false;
  }

  // Check if user has any role with level <= minimumLevel (lower number = higher privilege)
  return userWithRoles.roles.some(({ role }) => {
    return role.level <= minimumLevel;
  });
}

// Check if user is admin
export async function isAdmin(userId: string): Promise<boolean> {
  return await userHasMinimumRoleLevel(userId, ROLE_LEVELS.ADMIN);
}

// Check if user is manager or higher
export async function isManagerOrHigher(userId: string): Promise<boolean> {
  return await userHasMinimumRoleLevel(userId, ROLE_LEVELS.MANAGER);
}

// Check if user is moderator or higher
export async function isModeratorOrHigher(userId: string): Promise<boolean> {
  return await userHasMinimumRoleLevel(userId, ROLE_LEVELS.MODERATOR);
}

// Get user's highest role (lowest level number)
export async function getUserHighestRole(userId: string) {
  const userWithRoles = await getUserWithRoles(userId);
  
  if (!userWithRoles || userWithRoles.roles.length === 0) {
    return null;
  }

  // Find role with lowest level (highest privilege)
  return userWithRoles.roles.reduce((highest, current) => {
    return current.role.level < highest.role.level ? current : highest;
  }).role;
}

// Assign role to user
export async function assignRoleToUser(userId: string, roleId: string, assignedBy: string) {
  console.log("Update Role:", userId, roleId, assignedBy);
  const existingAssignment = await db
    .select()
    .from(userRoles)
    .where(and(
      eq(userRoles.userId, userId),
      eq(userRoles.roleId, roleId)
    ))
    .limit(1);

  if (existingAssignment.length > 0) {
    throw new Error("User already has this role");
  }
  console.log(assignedBy);
  // TODO : Assign role
  await db.insert(userRoles).values({
    id: nanoid(),
    userId,
    roleId,
    assignedBy,
  });
}

// Remove role from user
export async function removeRoleFromUser(userId: string, roleId: string) {
  await db
    .delete(userRoles)
    .where(and(
      eq(userRoles.userId, userId),
      eq(userRoles.roleId, roleId)
    ));
}

// Get all roles
export async function getAllRoles() {
  return await db.select().from(roles).orderBy(roles.level);
}

// Get role by name
export async function getRoleByName(name: string) {
  const result = await db.select().from(roles).where(eq(roles.name, name)).limit(1);
  return result.length > 0 ? result[0] : null;
}

// Validate if user can perform action based on role hierarchy
export async function canUserManageUser(managerId: string, targetUserId: string): Promise<boolean> {
  const managerRole = await getUserHighestRole(managerId);
  const targetRole = await getUserHighestRole(targetUserId);
  
  if (!managerRole) {
    return false;
  }
  
  // Admin can manage anyone
  if (managerRole.level === ROLE_LEVELS.ADMIN) {
    return true;
  }
  
  // If target has no role, manager can manage
  if (!targetRole) {
    return true;
  }
  
  // Manager can only manage users with lower privilege (higher level number)
  return managerRole.level < targetRole.level;
}
