import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "./db"; // your drizzle instance
import * as schema from "@/../drizzle/web-admin/schema";
import bcrypt from "bcryptjs";

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg", // or "mysql", "sqlite"
    schema: schema,
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true, 
    password: {
      hash(password) {
          return bcrypt.hash(password, 12);
      },
      verify(data) {
          return bcrypt.compare(data.password, data.hash);
      },
    }
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // Update session every 24 hours
  },
  user: {
    additionalFields: {
      isActive: {
        type: "boolean",
        defaultValue: true,
        required: false,
      },
      lastLoginAt: {
        type: "date",
        required: false,
      },
    },
  },
  secret: process.env.BETTER_AUTH_SECRET || "dev-secret-key-change-in-production-12345",
  baseURL: process.env.BETTER_AUTH_URL || "https://ygone.chamthoi.com",
  trustedOrigins: ["http://localhost:3131", "https://yulgang.one", "http://localhost:3000", "https://ygone.chamthoi.com"],
});