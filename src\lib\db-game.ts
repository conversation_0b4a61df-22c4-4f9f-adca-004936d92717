import { drizzle } from "drizzle-orm/postgres-js";
import { type PostgresJsDatabase } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { eq, like, and, or, gte, lte, count, sql, asc } from "drizzle-orm";

import * as schema from "@/../drizzle/game/schema";
import { ItemInfo } from "@/types/player";
import { parseItemFromHex } from "@/lib/items";

declare global {
    var dbGame: PostgresJsDatabase<typeof schema> | undefined;
}

let dbGame: PostgresJsDatabase<typeof schema>;

if (process.env.NODE_ENV === "production") {
    const client = postgres(process.env.DATABASE_URL3!);

    dbGame = drizzle(client, {
        schema,
    });
} else {
    if (!global.dbGame) {
        const client = postgres(process.env.DATABASE_URL3!);

        global.dbGame = drizzle(client, {
            schema,
        });
    }

    dbGame = global.dbGame;
}


type DbInstance = typeof dbGame;

// Character management functions
export interface CharacterListItem {
  fldId: string;
  fldName: string;
  fldLevel: number;
  fldJob: number;
  fldJobLevel: number;
  fldMenow: number;
  fldMoney: string;
  fldHp: number;
  fldMp: number;
}

export interface CharacterFilters {
  fldName?: string;
  fldId?: string;
  fldLevel?: { min?: number; max?: number };
  fldJob?: number;
  fldJobLevel?: { min?: number; max?: number };
  fldMenow?: { min?: number; max?: number };
  fldMoney?: { min?: string; max?: string };
}

export interface PaginationParams {
  page: number;
  pageSize: number;
}

export interface CharacterListResponse {
  characters: CharacterListItem[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export interface CharacterItemData {
  characterName: string;
  wearItems: (ItemInfo | null)[];      // 17 slots
  inventoryItems: (ItemInfo | null)[]; // Variable length
  questItems: (ItemInfo | null)[];     // 36 slots
  gemBagItems: (ItemInfo | null)[];    // 6 slots
  fashionItems: (ItemInfo | null)[];   // 60 slots
  eventBagItems: (ItemInfo | null)[];  // 24 slots
  publicWarehouse: (ItemInfo | null)[]; // 60 slots
  privateWarehouse: (ItemInfo | null)[]; // 60 slots
}

// Get list of characters with pagination and filtering
export async function getCharactersList(
  pagination: PaginationParams,
  filters: CharacterFilters = {}
): Promise<CharacterListResponse> {
  try {
    // Build where conditions
    const whereConditions = [];

    if (filters.fldName) {
      whereConditions.push(like(schema.tblXwwlChar.fldName, `%${filters.fldName}%`));
    }

    if (filters.fldId) {
      whereConditions.push(like(schema.tblXwwlChar.fldId, `%${filters.fldId}%`));
    }

    if (filters.fldLevel?.min !== undefined) {
      whereConditions.push(gte(schema.tblXwwlChar.fldLevel, filters.fldLevel.min));
    }

    if (filters.fldLevel?.max !== undefined) {
      whereConditions.push(lte(schema.tblXwwlChar.fldLevel, filters.fldLevel.max));
    }

    if (filters.fldJob !== undefined) {
      whereConditions.push(eq(schema.tblXwwlChar.fldJob, filters.fldJob));
    }

    if (filters.fldJobLevel?.min !== undefined) {
      whereConditions.push(gte(schema.tblXwwlChar.fldJobLevel, filters.fldJobLevel.min));
    }

    if (filters.fldJobLevel?.max !== undefined) {
      whereConditions.push(lte(schema.tblXwwlChar.fldJobLevel, filters.fldJobLevel.max));
    }

    if (filters.fldMenow?.min !== undefined) {
      whereConditions.push(gte(schema.tblXwwlChar.fldMenow, filters.fldMenow.min));
    }

    if (filters.fldMenow?.max !== undefined) {
      whereConditions.push(lte(schema.tblXwwlChar.fldMenow, filters.fldMenow.max));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get total count
    const totalCountResult = await dbGame
      .select({ count: count() })
      .from(schema.tblXwwlChar)
      .where(whereClause);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / pagination.pageSize);

    // Get paginated characters
    const characters = await dbGame
      .select({
        fldId: schema.tblXwwlChar.fldId,
        fldName: schema.tblXwwlChar.fldName,
        fldLevel: schema.tblXwwlChar.fldLevel,
        fldJob: schema.tblXwwlChar.fldJob,
        fldJobLevel: schema.tblXwwlChar.fldJobLevel,
        fldMenow: schema.tblXwwlChar.fldMenow,
        fldMoney: schema.tblXwwlChar.fldMoney,
        fldHp: schema.tblXwwlChar.fldHp,
        fldMp: schema.tblXwwlChar.fldMp,
      })
      .from(schema.tblXwwlChar)
      .where(whereClause)
      .orderBy(asc(schema.tblXwwlChar.fldId))
      .limit(pagination.pageSize)
      .offset((pagination.page - 1) * pagination.pageSize);

    return {
      characters: characters.map(char => ({
        fldId: char.fldId || '',
        fldName: char.fldName || '',
        fldLevel: char.fldLevel || 0,
        fldJob: char.fldJob || 0,
        fldJobLevel: char.fldJobLevel || 0,
        fldMenow: char.fldMenow || 0,
        fldMoney: char.fldMoney || '0',
        fldHp: char.fldHp || 0,
        fldMp: char.fldMp || 0,
      })),
      totalCount,
      totalPages,
      currentPage: pagination.page,
    };
  } catch (error) {
    console.error('Error fetching characters list:', error);
    return {
      characters: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: pagination.page,
    };
  }
}

// Parse item array from hex string
function parseItemArray(hexData: string | null, expectedSlots: number): (ItemInfo | null)[] {
  if (!hexData) {
    return Array(expectedSlots).fill(null);
  }

  const items: (ItemInfo | null)[] = [];
  const itemSize = 76; // 76 bytes * 2 = 152 hex characters

  for (let i = 0; i < expectedSlots; i++) {
    const start = i * itemSize;
    const end = start + itemSize;

    if (start >= hexData.length) {
      items.push(null);
      continue;
    }

    const itemHex  = hexData.slice(start, end); 
    if (itemHex.length === itemSize) {
      /* @ts-expect-error please disable */
      const item = parseItemFromHex(itemHex.toString("hex"));
      if (item) {
        item.position = i; // Add position property to item
      } // Optional: Add additional logic to handle invalid items or empty slots
      items.push(item);
    } else {
      items.push(null);
    }
  }

  return items;
}

// Get all item data for a specific character
export async function getCharacterItems(characterName: string): Promise<CharacterItemData | null> {
  try {
    // Get character data
    const character = await dbGame
      .select()
      .from(schema.tblXwwlChar)
      .where(eq(schema.tblXwwlChar.fldName, characterName))
      .limit(1);

    if (character.length === 0) {
      return null;
    }

    const charData = character[0];

    // Get public warehouse data
    const publicWarehouse = charData.fldId ? await dbGame
      .select()
      .from(schema.tblXwwlPublicwarehouse)
      .where(eq(schema.tblXwwlPublicwarehouse.fldId, charData.fldId))
      .limit(1) : [];

    // Get private warehouse data
    const privateWarehouse = await dbGame
      .select()
      .from(schema.tblXwwlWarehouse)
      .where(eq(schema.tblXwwlWarehouse.fldName, characterName))
      .limit(1);

    const result: CharacterItemData = {
      characterName,
      wearItems: parseItemArray(charData.fldWearitem, 17),
      inventoryItems: parseItemArray(charData.fldItem, 96),
      questItems: parseItemArray(charData.fldQitem, 36),
      gemBagItems: parseItemArray(charData.fldNtcitem, 6),
      fashionItems: parseItemArray(charData.fldFashionItem, 60),
      eventBagItems: parseItemArray(charData.fldPinkbagItem, 24),
      publicWarehouse: parseItemArray(publicWarehouse[0]?.fldItem || null, 60),
      privateWarehouse: parseItemArray(privateWarehouse[0]?.fldItem || null, 60),
    };

    return result;
  } catch (error) {
    console.error('Error fetching character items:', error);
    return null;
  }
}

// Bulk search interfaces
export interface BulkSearchFilters {
  searchType: 'globalId' | 'itemId' | 'itemOption';
  searchValue: string;
  searchLocations: {
    characters: boolean;
    publicWarehouse: boolean;
    privateWarehouse: boolean;
  };
}

export interface ItemSearchResult {
  characterName: string;
  characterId: string;
  location: 'character' | 'publicWarehouse' | 'privateWarehouse';
  storageType: string;
  slotIndex: number;
  item: ItemInfo;
}

export interface BulkSearchResponse {
  results: ItemSearchResult[];
  totalFound: number;
  searchSummary: {
    charactersWithItems: number;
    totalCharactersSearched: number;
    searchTime: number;
  };
}

// Helper function to convert number to hex bytes for searching
function numberToHexBytes(value: number, byteLength: number = 4): string {
  // Method 1: Try simple hex representation first
  const simpleHex = value.toString(16).toLowerCase();

  // Method 2: Try padded hex
  // const paddedHex = value.toString(16).padStart(byteLength * 2, '0').toLowerCase();

  // Method 3: Try little-endian byte order
  const hex = value.toString(16).padStart(byteLength * 2, '0');
  const bytes = [];
  for (let i = 0; i < hex.length; i += 2) {
    bytes.unshift(hex.substr(i, 2));
  }
  // const littleEndianHex = bytes.join('').toLowerCase();

  // Return the simple hex first (most common case)
  return simpleHex;
}

// Alternative hex conversion methods for testing
function numberToHexBytesAlternative(value: number, method: 'simple' | 'padded' | 'littleEndian' | 'bigEndian' = 'simple'): string {
  switch (method) {
    case 'simple':
      return value.toString(16).toLowerCase();
    case 'padded':
      return value.toString(16).padStart(8, '0').toLowerCase();
    case 'littleEndian':
      const hex = value.toString(16).padStart(8, '0');
      const bytes = [];
      for (let i = 0; i < hex.length; i += 2) {
        bytes.unshift(hex.substr(i, 2));
      }
      return bytes.join('').toLowerCase();
    case 'bigEndian':
      return value.toString(16).padStart(8, '0').toLowerCase();
    default:
      return value.toString(16).toLowerCase();
  }
}

// Bulk search function
export async function bulkSearchItems(filters: BulkSearchFilters): Promise<BulkSearchResponse> {
  const startTime = Date.now();
  const results: ItemSearchResult[] = [];

  try {
    const searchValue = parseInt(filters.searchValue);
    if (isNaN(searchValue)) {
      throw new Error('Search value must be a valid number');
    }

    // Convert search value to hex bytes based on search type
    let hexPattern: string;
    switch (filters.searchType) {
      case 'globalId':
        hexPattern = numberToHexBytes(searchValue, 3); // globalId is 3 bytes
        break;
      case 'itemId':
        hexPattern = numberToHexBytes(searchValue, 4); // itemId is 4 bytes
        break;
      case 'itemOption':
        hexPattern = numberToHexBytes(searchValue, 4); // itemOption is 4 bytes
        break;
      default:
        throw new Error('Invalid search type');
    }

    // Search in character items
    if (filters.searchLocations.characters) {
      const characterResults = await searchInCharacters(hexPattern, filters.searchType, searchValue);
      results.push(...characterResults);
    }

    // Search in public warehouse
    if (filters.searchLocations.publicWarehouse) {
      const publicWarehouseResults = await searchInPublicWarehouse(hexPattern, filters.searchType, searchValue);
      results.push(...publicWarehouseResults);
    }

    // Search in private warehouse
    if (filters.searchLocations.privateWarehouse) {
      const privateWarehouseResults = await searchInPrivateWarehouse(hexPattern, filters.searchType, searchValue);
      results.push(...privateWarehouseResults);
    }

    const endTime = Date.now();
    const uniqueCharacters = new Set(results.map(r => r.characterName)).size;

    return {
      results,
      totalFound: results.length,
      searchSummary: {
        charactersWithItems: uniqueCharacters,
        totalCharactersSearched: 0, // Will be calculated based on actual search
        searchTime: endTime - startTime,
      }
    };

  } catch (error) {
    console.error('Error in bulk search:', error);
    throw error;
  }
}

// Search in character tables
async function searchInCharacters(hexPattern: string, searchType: string, searchValue: number): Promise<ItemSearchResult[]> {
  const results: ItemSearchResult[] = [];

  const patterns = [
    numberToHexBytesAlternative(searchValue, 'littleEndian'),
  ];

  // Build OR conditions for all patterns and all fields
  const whereConditions = [];
   for (const pattern of patterns) {
    whereConditions.push(
      sql`position(decode(${pattern}, 'hex') in "fld_wearitem") > 0`,
      sql`position(decode(${pattern}, 'hex') in "fld_item") > 0`,
      sql`position(decode(${pattern}, 'hex') in "fld_qitem") > 0`,
      sql`position(decode(${pattern}, 'hex') in "fld_ntcitem") > 0`,
      sql`position(decode(${pattern}, 'hex') in "fld_fashion_item") > 0`,
      sql`position(decode(${pattern}, 'hex') in "fld_pinkbag_item") > 0`
    );
  }

  const characters = await dbGame
    .select({
      fldName: schema.tblXwwlChar.fldName,
      fldId: schema.tblXwwlChar.fldId,
      fldWearitem: schema.tblXwwlChar.fldWearitem,
      fldItem: schema.tblXwwlChar.fldItem,
      fldQitem: schema.tblXwwlChar.fldQitem,
      fldNtcitem: schema.tblXwwlChar.fldNtcitem,
      fldFashionItem: schema.tblXwwlChar.fldFashionItem,
      fldPinkbagItem: schema.tblXwwlChar.fldPinkbagItem,
    })
    .from(schema.tblXwwlChar)
    .where(or(...whereConditions));

  for (const char of characters) {
    const characterName = char.fldName || '';
    const characterId = char.fldId || '';

    // Check each storage type
    const storageFields = [
      { data: char.fldWearitem, type: 'wearItems', slots: 17 },
      { data: char.fldItem, type: 'inventoryItems', slots: 96 },
      { data: char.fldQitem, type: 'questItems', slots: 36 },
      { data: char.fldNtcitem, type: 'gemBagItems', slots: 6 },
      { data: char.fldFashionItem, type: 'fashionItems', slots: 60 },
      { data: char.fldPinkbagItem, type: 'eventBagItems', slots: 24 },
    ];

    for (const storage of storageFields) {
      const hexData = storage.data;
      if (hexData) {
        const items = parseItemArray(hexData, storage.slots);

        items.forEach((item, index) => {
          if (item && matchesSearchCriteria(item, searchType, searchValue)) {
            results.push({
              characterName,
              characterId,
              location: 'character',
              storageType: storage.type,
              slotIndex: index,
              item
            });
          }
        });
      }
    }
  }

  return results;
}

// Search in public warehouse
async function searchInPublicWarehouse(hexPattern: string, searchType: string, searchValue: number): Promise<ItemSearchResult[]> {
  const results: ItemSearchResult[] = [];

  const patterns = [
    numberToHexBytesAlternative(searchValue, 'simple'),
  ];

  // Build OR conditions using position with decode
  const whereConditions = [];
  for (const pattern of patterns) {
    whereConditions.push(
      sql`position(decode(${pattern}, 'hex') in "fld_item") > 0`
    );
  }

  const warehouses = await dbGame
    .select({
      fldId: schema.tblXwwlPublicwarehouse.fldId,
      fldItem: schema.tblXwwlPublicwarehouse.fldItem,
    })
    .from(schema.tblXwwlPublicwarehouse)
    .where(or(...whereConditions));

  for (const warehouse of warehouses) {
    const characterName =  '';
    const characterId = warehouse.fldId || '';
    const hexData = warehouse.fldItem;

    if (hexData) {
      const items = parseItemArray(hexData, 60);

      items.forEach((item, index) => {
        if (item && matchesSearchCriteria(item, searchType, searchValue)) {
          results.push({
            characterName,
            characterId,
            location: 'publicWarehouse',
            storageType: 'publicWarehouse',
            slotIndex: index,
            item
          });
        }
      });
    }
  }

  return results;
}

// Search in private warehouse
async function searchInPrivateWarehouse(hexPattern: string, searchType: string, searchValue: number): Promise<ItemSearchResult[]> {
  const results: ItemSearchResult[] = [];

  const patterns = [
    numberToHexBytesAlternative(searchValue, 'simple'),
  ];

  // Build OR conditions using position with decode
  const whereConditions = [];
  for (const pattern of patterns) {
    whereConditions.push(
      sql`position(decode(${pattern}, 'hex') in "fld_item") > 0`
    );
  }

  const warehouses = await dbGame
    .select({
      fldName: schema.tblXwwlWarehouse.fldName,
      fldItem: schema.tblXwwlWarehouse.fldItem,
    })
    .from(schema.tblXwwlWarehouse)
    .where(or(...whereConditions));

  for (const warehouse of warehouses) {
    const characterName = warehouse.fldName || '';
    const hexData = warehouse.fldItem;

    if (hexData) {
      const items = parseItemArray(hexData, 60);

      items.forEach((item, index) => {
        if (item && matchesSearchCriteria(item, searchType, searchValue)) {
          results.push({
            characterName,
            characterId: characterName, // Private warehouse uses name as ID
            location: 'privateWarehouse',
            storageType: 'privateWarehouse',
            slotIndex: index,
            item
          });
        }
      });
    }
  }

  return results;
}

// Helper function to check if item matches search criteria
function matchesSearchCriteria(item: ItemInfo, searchType: string, searchValue: number): boolean {
  switch (searchType) {
    case 'globalId':
      return item.globalId === searchValue;
    case 'itemId':
      return item.itemId === searchValue;
    case 'itemOption':
      return item.itemOption === searchValue;
    default:
      return false;
  }
}

export { dbGame };
export type { DbInstance as DbPubInstance };