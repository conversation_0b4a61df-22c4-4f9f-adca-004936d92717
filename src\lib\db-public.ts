import { drizzle } from "drizzle-orm/postgres-js";
import { type PostgresJsDatabase } from "drizzle-orm/postgres-js";
import postgres from "postgres";

import * as schema from "@/../drizzle/public/schema";

declare global {
    var dbPublic: PostgresJsDatabase<typeof schema> | undefined;
}

let dbPublic: PostgresJsDatabase<typeof schema>;

if (process.env.NODE_ENV === "production") {
    const client = postgres(process.env.DATABASE_URL2!);

    dbPublic = drizzle(client, {
        schema,
    });
} else {
    if (!global.dbPublic) {
        const client = postgres(process.env.DATABASE_URL2!);

        global.dbPublic = drizzle(client, {
            schema,
        });
    }

    dbPublic = global.dbPublic;
}


type DbInstance = typeof dbPublic;

export { dbPublic };
export type { DbInstance as DbPubInstance };