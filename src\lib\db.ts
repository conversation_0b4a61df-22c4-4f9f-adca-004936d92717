import { drizzle } from "drizzle-orm/postgres-js";
import { type PostgresJsDatabase } from "drizzle-orm/postgres-js";
import postgres from "postgres";

import * as schema from "@/../drizzle/web-admin/schema";

declare global {
    var db: PostgresJsDatabase<typeof schema> | undefined;
}

let db: PostgresJsDatabase<typeof schema>;

if (process.env.NODE_ENV === "production") {
    const client = postgres(process.env.DATABASE_URL!);

    db = drizzle(client, {
        schema,
    });
} else {
    if (!global.db) {
        const client = postgres(process.env.DATABASE_URL!);

        global.db = drizzle(client, {
            schema,
        });
    }

    db = global.db;
}


type DbInstance = typeof db;

export { db };
export type { DbInstance };