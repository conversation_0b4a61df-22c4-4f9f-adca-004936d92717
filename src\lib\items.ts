import { ItemInfo } from "@/types/player";

// Standalone function to parse item from hex string
export function parseItemFromHex(itemString: string): ItemInfo | null {
    // Check if item string is valid (152 hex characters)
    if (!itemString || itemString.length !== 152) {
        return null;
    }

    // Check if item is empty (all zeros)
    if (itemString === '0'.repeat(152)) {
        return null;
    }

    // Helper function to convert hex to decimal with little-endian byte order
    const convertHextoDec = (hexStr: string): number => {
        const pairs = hexStr.match(/.{1,2}/g) || [];
        let reversedHex = '';
        pairs.forEach(pair => {
            reversedHex = pair + reversedHex;
        });
        return parseInt(reversedHex, 16);
    };

    // Offset definitions for item data structure
    const offsetId = [0, 6];
    const offsetItem = [16, 8];
    const offsetSl = [24, 4];
    const offsetOp = [32, 8];
    const offsetMagic = [40, 32];
    const offsetOpTrungCap = [80, 2];
    const offsetTimeCreated = [104, 8];
    const offsetTimeExpired = [112, 8];
    const offsetThucTinh = [124, 2];
    const offsetTinhChat = [136, 2];
    const offsetTulinh = [142, 2];
    const offsetLock = [144, 2];

    // Extract data from hex string
    const id = itemString.slice(offsetId[0], offsetId[1]);
    const item = itemString.slice(offsetItem[0], offsetItem[0] + offsetItem[1]);
    const sl = itemString.slice(offsetSl[0], offsetSl[0] + offsetSl[1]);
    const option = itemString.slice(offsetOp[0], offsetOp[0] + offsetOp[1]);
    const magic = itemString.slice(offsetMagic[0], offsetMagic[0] + offsetMagic[1]);
    const trungcap = itemString.slice(offsetOpTrungCap[0], offsetOpTrungCap[0] + offsetOpTrungCap[1]);
    const createdTime = itemString.slice(offsetTimeCreated[0], offsetTimeCreated[0] + offsetTimeCreated[1]);
    const expiredTime = itemString.slice(offsetTimeExpired[0], offsetTimeExpired[0] + offsetTimeExpired[1]);
    const thucTinh = itemString.slice(offsetThucTinh[0], offsetThucTinh[0] + offsetThucTinh[1]);
    const tinhchat = itemString.slice(offsetTinhChat[0], offsetTinhChat[0] + offsetTinhChat[1]);
    const tulinh = itemString.slice(offsetTulinh[0], offsetTulinh[0] + offsetTulinh[1]);
    const lock = itemString.slice(offsetLock[0], offsetLock[0] + offsetLock[1]);

    // Parse magic array (4 magic values, 8 hex chars each)
    const htarr = magic.match(/.{1,8}/g);

    // Check if itemId is 0 (empty item)
    const itemId = convertHextoDec(item);
    if (itemId === 0) {
        return null;
    }

    const data: ItemInfo = {
        globalId: convertHextoDec(id),
        itemId: itemId,
        quantity: convertHextoDec(sl),
        itemOption: convertHextoDec(option),
        itemMagic1: htarr ? convertHextoDec(htarr[0]) : 0,
        itemMagic2: htarr ? convertHextoDec(htarr[1]) : 0,
        itemMagic3: htarr ? convertHextoDec(htarr[2]) : 0,
        itemMagic4: htarr ? convertHextoDec(htarr[3]) : 0,
        mediumSoul: convertHextoDec(trungcap),
        day1: convertHextoDec(createdTime),
        day2: convertHextoDec(expiredTime),
        lowSoul: convertHextoDec(thucTinh),
        quality: convertHextoDec(tinhchat),
        beast: convertHextoDec(tulinh),
        lock: convertHextoDec(lock)
    };
    return data;
}

export class MagicHandler {
    
    public readOneItem(itemString: string): ItemInfo | null {
        return parseItemFromHex(itemString);
    }
    public convertHextoDec(hexStr: string): number {
        const pairs = hexStr.match(/.{1,2}/g) || [];
        let reversedHex = '';
        pairs.forEach(pair => {
            reversedHex = pair + reversedHex;
        });
        return parseInt(reversedHex, 16);
    }

    public magic(val: number): string | null {
        const type = Math.round(val / 100000);
        const indices: { [key: number]: [string, boolean] } = {
            1: ['Công lực', true],
            2: ['Lực phòng ngự', true],
            3: ['Sinh mệnh', true],
            4: ['Nội công', true],
            5: ['Chính xác', true],
            6: ['Né tránh', true],
            7: ['Công lực võ công', false],
            8: ['Khí công', true],
            9: ['Tỉ lệ may mắn', false],
            11: ['Phòng Ngự võ công', true],
            12: ['Tỉ lệ nhận tiền', false],
            13: ['Giảm tổn thất EXP', false],
            15: ['Tăng điểm kinh nghiệm', false]
        };
        const result = indices[type];
        const value = val - type * 100000;
        return result ? `${result[0]} + ${value}${result[1] && '%'}` : null;
    }

    public option(val: number): string | null {
        const type = Math.round(val / 100000);
        const indices: { [key: number]: [string, boolean] } = {
            1: ['Công lực', true],
            2: ['Lực phòng ngự', true],
            3: ['Sinh mệnh', true],
            4: ['Nội công', true],
            5: ['Chính xác', true],
            6: ['Né tránh', true],
            7: ['Công lực võ công', false],
            8: ['Khí công', true],
            9: ['Tỉ lệ may mắn', false],
            11: ['Phòng Ngự võ công', true],
            12: ['Tỉ lệ nhận tiền', false],
            13: ['Giảm tổn thất EXP', false],
            15: ['Tăng điểm kinh nghiệm', false]

        };
        const result = indices[type];
        const value = val - type * 100000;
        return result ? `${result[0]} + ${value}${result[1] && '%'}` : null;
    }
    public getLinhThu(id: number) {
        const linhThuMap: { [key: number]: string } = {
            0: 'Thông thường',
            220000001: 'Cao cấp',
            220000002: 'Quý hiếm',
            220000003: 'Huyền thoại',
            220000004: 'Thần 1',
            220000005: 'Thần 2',
            220000006: 'Thần 3',
            220000007: 'Thần 4',
        }
        return linhThuMap[id]
    }

    public getLinhThuOption(cuonghoa: number, id: number, option: number): string {
        if (cuonghoa <= 0) {
            return ``;
        }
        const baseTCPT = [1, 1.2, 1.5, 2];

        const baseOptions: { [key: number]: number[] } = {
            1000001170: [1, 2, 4, 8],
            1000001171: [0.9, 1.8, 3.6, 7.2],
            1000001172: [0.55, 1.1, 2.2, 4.4],
            1000001173: [0.6, 1.2, 2.4, 4.8],
            1000001174: [0.5, 1, 2, 4],
            1000001175: [0.8, 1.6, 3.2, 6.4],
            1000001176: [1, 2, 4, 8], // assuming a continuation of similar pattern
            1000001177: [0.9, 1.8, 3.6, 7.2],
            1000001178: [0.55, 1.1, 2.2, 4.4],
            1000001179: [0.6, 1.2, 2.4, 4.8],
            1000001180: [0.5, 1, 2, 4],
            1000001181: [0.8, 1.6, 3.2, 6.4]
        };

        const baseHP: { [key: number]: number } = {
            1000001170: 10,
            1000001171: 8,
            1000001172: 1,
            1000001173: 2,
            1000001174: 0,
            1000001175: 6,
            1000001176: 10,
            1000001177: 8,
            1000001178: 1,
            1000001179: 2,
            1000001180: 0,
            1000001181: 6
        };

        const optionIndex = option === 220000000 ? 0 : option - 220000000;
        const baseHp: number = baseHP[id] ?? 0;
        const tileCLVCULPT = baseOptions[id][optionIndex] ?? 1;

        const tancongphongthu = (4.0 + (cuonghoa + 1) * 0.5) * baseTCPT[optionIndex];
        const congkichvocong = (8.0 + 2 * (cuonghoa + 1) * 0.5) * tileCLVCULPT;
        const phongthuvocong = congkichvocong; // Assuming they are the same
        const sinhmenh = cuonghoa > 1 ? baseHp + (cuonghoa * 0.5) * 10 : 0;

        return `<em style='color:#B9645A';>Cường hoá + ${cuonghoa}</em><br>
            <em style='color:#B9645A';>Công kích, phòng ngự + ${tancongphongthu}</em><br>
            <em style='color:#B9645A';>(PVE) Công kích + ${congkichvocong}</em><br>
            <em style='color:#B9645A';>(PVE) Phòng ngự + ${phongthuvocong}</em><br>
            <em style='color:#B9645A';>Điểm sinh mệnh tăng + ${sinhmenh}</em><br>`;
    }

    public getAoChoangOption(cuonghoa: number): string {
        let chimangtranhne = 0;
        let trongluongtui = 0;
        let nhantienvang = 0;
        let tancongphongthu = 0;
        let sinhmenh = 0;
        const baseHP = 0;

        if (cuonghoa > 0) {
            chimangtranhne = cuonghoa > 4 ? cuonghoa * 0.2 : 0;
            trongluongtui = cuonghoa;
            nhantienvang = (cuonghoa - 3) * 2;
            tancongphongthu = 4.0 + (cuonghoa + 1) * 0.5;
            sinhmenh = cuonghoa > 1 ? baseHP + (cuonghoa * 0.5) * 10 : 0;
        }

        return `<em style='color:#B9645A';>Chiêu trí mạng, tránh né + ${chimangtranhne}%</em>
                <em style='color:#B9645A';>Tăng trọng lượng túi hành trang phụ + ${trongluongtui}%</em>
                <em style='color:#B9645A';>Nhận tiền vàng (lượng) + ${nhantienvang}%</em>
                <em style='color:#B9645A';>Lực tấn công, phòng thủ + ${tancongphongthu}</em>
                <em style='color:#B9645A';>Tăng sinh mệnh + ${sinhmenh}</em><br>`;
    }
    public magicopen(val: number): string | null {
        const type = Math.round(val / 100000);
        const indices: { [key: number]: [string, boolean] } = {
            1: ['Công lực', true],
            2: ['Lực phòng ngự', true],
            3: ['Sinh mệnh', true],
            4: ['Nội công', true],
            5: ['Chính xác', true],
            6: ['Né tránh', true],
            7: ['Lực công kích võ công', false],
            8: ['Tất cả khí công', true],
            9: ['Tỉ lệ may mắn', false],
            10: ['Đả kích', true],
            11: ['Uy lực phòng thủ', true],
            12: ['Tỉ lệ nhận tiền', false],
            13: ['Giảm tổn thất EXP', false],
            15: ['Tăng điểm kinh nghiệm', false]

        };
        return indices[type][0] || null;
    }

    public optionneo(input: number): string | null {
        const type = Math.round(input / 100000);
        const numb = input % 100
        const indices: { [key: number]: [string, boolean] } = {
            1: ['Công lực', false],
            2: ['Lực phòng ngự', false],
            3: ['Sinh mệnh', false],
            4: ['Nội công', false],
            5: ['Chính xác', false],
            6: ['Né tránh', false],
            7: ['Công kích võ công', true],
            8: ['Tất cả khí công', false],
            9: ['Tỉ lệ may mắn', true],
            10: ['Đả kích', false],
            11: ['Uy lực phòng thủ', false],
            12: ['Tỉ lệ nhận tiền', true],
            13: ['Giảm tổn thất EXP', true],
            15: ['Tăng điểm kinh nghiệm', false]

        };
        if (!indices[type]) return null
        const value = indices[type][0] + ` ${numb}${indices[type][1] ? '% tăng' : ' tăng'}`
        return value || null;
    }


    public optionName(input: string): string | null {
        const type = (input.length === 9) ? parseInt(input.substring(0, 2)) : parseInt(input.substring(0, 1));
        const indices: { [key: number]: [string, boolean] } = {
            1: ['Công lực', true],
            2: ['Lực phòng ngự', true],
            3: ['Sinh mệnh', true],
            4: ['Nội công', true],
            5: ['Chính xác', true],
            6: ['Né tránh', true],
            7: ['Công kích võ công', false],
            8: ['Tất cả khí công', true],
            9: ['Tỉ lệ may mắn', false],
            10: ['Đả kích', true],
            11: ['Uy lực phòng thủ', true],
            12: ['Tỉ lệ nhận tiền', false],
            13: ['Giảm tổn thất EXP', false],
            15: ['Tăng điểm kinh nghiệm', false]

        };
        const value = indices[type][0] && `${indices[type][0]} ${parseInt(input.substring(2))}${!indices[type][1] && '%' || ''} tăng`;
        return value || null;
    }

    public phai(id: number): string {
        const phaiMap: { [key: number]: string } = {
            1: 'Đao khách',
            2: 'Kiếm khách',
            3: 'Thương khách',
            4: 'Cung thủ',
            5: 'Đại phu',
            6: 'Thích khách',
            7: 'Cầm Sư',
            8: 'HBQ',
            9: 'DHL',
            10: 'QS',
            11: 'Diệu yến',
            12: 'Tử hào',
            13: 'Thần nữ'
        };
        return phaiMap[id] || id.toString();
    }

    public async getOptionStringEnhanced(magic1: string, magic2: string, magic3: string, magic4: string): Promise<string> {
        const magics = [magic1, magic2, magic3, magic4];

        const promises = magics.map(async (magic) => {
            const length = magic.length;
            if (length < 4) return "";

            const number = parseInt(magic.substring(length - 3));
            const type = parseInt(magic.substring(0, 2));
            switch (type) {
                case 80:
                    const num = parseInt(magic.substring(length - 4, length));
                    if (num > 999) {
                        const qigongID = parseInt(magic.substring(length - 5, length - 2));
                        const res = await this.optionNameQigong(qigongID);
                        return `${res} ${parseInt(magic.substring(length - 2))} tăng`;
                    } else {
                        return this.optionName(magic);
                    }
                default:
                    return number === 0 ? "" : this.optionName(magic);
            }
        });

        const options = await Promise.all(promises);
        const filteredOptions = options.filter(option => option);
        return filteredOptions.join('<br/>');
    }

    public async optionNameQigong(qigongID: number) {
        const result = await this.fetchQigongNameFromDatabase(qigongID);
        return this.enConvert(result);
    }

    public async fetchQigongNameFromDatabase(qigongID: number): Promise<string> {
        // const res = await publicDB.tBL_XWWL_SKILL.findFirst({ where: { FLD_PID: qigongID }, select: { FLD_NAME: true } });
        // if (!res || !res.FLD_NAME) return '';
        // return this.capitalizeFirstLetter(this.enConvert(res.FLD_NAME));
        return 'Qigong ' + qigongID;
    }
    private capitalizeFirstLetter(text: string): string {
        return text.charAt(0).toUpperCase() + text.slice(1);
    }

    public enConvert(str: string): string {
        const arr = str.match(/.{1,1}/g);
        const con: string[] = [];

        const unicode =
            ["á", "à", "ả", "ã", "ạ", "â", "ấ", "ầ", "ẩ", "ẫ", "ậ", "ă", "ắ", "ằ", "ẳ", "ẵ", "ặ", "đ", "é",
                "è", "ẻ", "ẽ", "ẹ", "ê", "ế", "ề", "ể", "ễ", "ệ", "í", "ì", "ỉ", "ĩ", "ị", "ó", "ò", "ỏ", "õ",
                "ọ", "ô", "ố", "ồ", "ổ", "ỗ", "ộ", "ơ", "ớ", "ờ", "ở", "ỡ", "ợ", "ú", "ù", "ủ", "ũ", "ụ", "ư", "ứ",
                "ừ", "ử", "ữ", "ự", "ý", "ỳ", "ỷ", "ỹ", "ỵ", "Á", "À", "Ả", "Ã", "Ạ", "Â", "Ấ", "Ầ", "Ẩ", "Ẫ",
                "Ậ", "Ă", "Ắ", "Ằ", "Ẳ", "Ẵ", "Ặ", "Đ", "É", "È", "Ẻ", "Ẽ", "Ẹ", "Ê", "Ế", "Ề", "Ể", "Ễ", "Ệ",
                "Í", "Ì", "Ỉ", "Ĩ", "Ị", "Ó", "Ò", "Ỏ", "Õ", "Ọ", "Ô", "Ố", "Ồ", "Ổ", "Ỗ", "Ộ", "Ơ", "Ớ", "Ờ",
                "Ở", "Ỡ", "Ợ", "Ú", "Ù", "Ủ", "Ũ", "Ụ", "Ư", "Ứ", "Ừ", "Ử", "Ữ", "Ự", "Ý", "Ỳ", "Ỷ", "Ỹ", "Ỵ"]
        const w1258 = ["aì", "aÌ", "aÒ", "aÞ", "aò", "â", "âì", "âÌ", "âÒ", "âÞ", "âò", "ã", "ãì", "ãÌ", "ãÒ", "ãÞ", "ãò", "ð", "eì",
            "eÌ", "eÒ", "eÞ", "eò", "ê", "êì", "êÌ", "êÒ", "êÞ", "êò", "iì", "iÌ", "iÒ", "iÞ", "iò", "oì", "oÌ", "oÒ", "oÞ",
            "oò", "ô", "ôì", "ôÌ", "ôÒ", "ôÞ", "ôò", "õ", "õì", "õÌ", "õÒ", "õÞ", "õò", "uì", "uÌ", "uÒ", "uÞ", "uò", "ý", "ýì",
            "ýÌ", "ýÒ", "ýÞ", "ýò", "yì", "yÌ", "yÒ", "yÞ", "yò", "Aì", "AÌ", "AÒ", "AÞ", "Aò", "Â", "Âì", "ÂÌ", "ÂÒ", "ÂÞ",
            "Âò", "Ã", "Ãì", "ÃÌ", "ÃÒ", "ÃÞ", "Ãò", "Ð", "Eì", "EÌ", "EÒ", "EÞ", "Eò", "Ê", "Êì", "ÊÌ", "ÊÒ", "ÊÞ", "Êò",
            "Iì", "IÌ", "IÒ", "IÞ", "Iò", "Oì", "OÌ", "OÒ", "OÞ", "Oò", "Ô", "Ôì", "ÔÌ", "ÔÒ", "ÔÞ", "Ôò", "Õ", "Õì", "ÕÌ",
            "ÕÒ", "ÕÞ", "Õò", "Uì", "UÌ", "UÒ", "UÞ", "Uò", "Ý", "Ýì", "ÝÌ", "ÝÒ", "ÝÞ", "Ýò", "Yì", "YÌ", "YÒ", "YÞ", "Yò"]
        for (const x in arr) {
            if (str.charCodeAt(parseInt(x)) > 192) {
                const currentChar = arr[parseInt(x)];
                const previousChar = parseInt(x) > 0 ? arr[parseInt(x) - 1] : '';

                if (w1258.includes(currentChar)) {
                    const u = w1258.findIndex((e) => e === currentChar);
                    con[parseInt(x)] = unicode[u] ?? '';
                } else if (w1258.includes(previousChar + currentChar)) {
                    const u = w1258.findIndex((e) => e === previousChar + currentChar);
                    con[parseInt(x) - 1] = '';
                    con[parseInt(x)] = unicode[u] ?? '';
                } else {
                    con[parseInt(x)] = currentChar;
                }
            } else {
                con[parseInt(x)] = arr[parseInt(x)];
            }
        }
        //console.log(con.join(''));
        return con.join('');
    };

    public async getOptionNameSpear(magic0: string) {
        const length = magic0.length;
        const startIndex = length === 6 ? 3 : 2;
        const number1 = parseInt(magic0.substring(startIndex + 1, startIndex + 4));
        // const type1 = magic0.substring(0, startIndex);
        const type1 = Math.floor(+magic0 / 100000);
        let optionSpear = "";
        switch (type1) {
            case 8:
                const number1Extended = parseInt(magic0.substring(startIndex - 1, startIndex + 3));
                if (number1Extended > 999) {
                    const qigongID = parseInt(magic0.substring(startIndex - 2, startIndex + 1));
                    optionSpear = `${await this.optionNameQigong(qigongID)} ${parseInt(magic0.substring(startIndex + 2))} tăng`;
                } else {
                    optionSpear = `${this.optionName(magic0)}`;
                }
                break;
            case 12:
                optionSpear = `${this.magicopen(parseInt(magic0))} ${number1}% tăng`;
                break;
            default:
                optionSpear = number1 !== 0 ? `${this.optionneo(+magic0)} ` : "";
                break;
        }

        return optionSpear;
    }

    public getOptionNameElementSpear(magic0: number): string {
        const elementMap: { [key: number]: string } = {
            2001000: "Thuộc tính Hoả",
            2002000: "Thuộc tính Thuỷ",
            2003000: "Thuộc tính Phong",
            2004000: "Thuộc tính Nội",
            2005000: "Thuộc tính Ngoại",
            2006000: "Thuộc tính Độc"
        };
        return elementMap[magic0] || "";
    }



    public getMediumSoul(soul: number) {
        const soulRanges: Array<{ range: number[], label: string, offset: number, percent?: boolean }> = [
            { range: [1, 5], label: "Tâm", offset: 0 },
            { range: [6, 10], label: "Khí", offset: 5 },
            { range: [11, 15], label: "Thể", offset: 10 },
            { range: [16, 20], label: "Hồn", offset: 15 },
            { range: [21, 21], label: "Thức tỉnh", offset: 20 },
            { range: [22, 22], label: "Thức tỉnh", offset: 21 },
            { range: [23, 27], label: "Phục cừu", offset: 22, percent: true },
            { range: [28, 30], label: "Hấp hồn", offset: 27, percent: true },
            { range: [31, 33], label: "Kỳ duyên", offset: 30, percent: true },
            { range: [34, 36], label: "Phẫn nộ", offset: 33, percent: true },
            { range: [37, 41], label: "Di tinh", offset: 36, percent: true },
            { range: [42, 46], label: "Hộ thể", offset: 41, percent: true },
            { range: [47, 51], label: "Hỗn nguyên", offset: 46, percent: true }
        ];

        for (const range of soulRanges) {
            if (soul >= range.range[0] && soul <= range.range[1]) {
                const level = soul - range.offset;
                // return range.label + " " + level + (range.percent ? "%" : "");
                return {
                    label: range.label,
                    level: level,
                    percent: range.percent
                }
            }
        }
        return ""; // Default empty string if no range matches
    }

    public getTuLinh(tuLinh: number): string | null {
        const tuLinhMap: { [key: number]: string } = {
            1: "Huyền vũ",
            2: "Chu tước",
            3: "Bạch hổ",
            4: "Thanh long"
        };
        return tuLinhMap[tuLinh] || null;
    }

    public getTheLuc(id: number): string {
        const theLucMap: { [key: number]: string } = {
            1: '<font >Chính phái</font>',
            2: '<font >Tà phái</font>',
            3: 'Chung'
        };
        return theLucMap[id];
    }

    public getGioiTinh(id: number): string {
        const genderMap: { [key: number]: string } = {
            1: 'Nam',
            2: 'Nữ',
            3: 'Không'
        };
        return genderMap[id];
    }

    public getCuongHoa(optionId: number) :number {
        if (optionId != 0)
        {
            return optionId % 100;
        }
        return 0;
    }
    
    public getThuocTinh(optionId: number) : { id: string, level: string } {
        const stringNum = optionId.toString();
        const thuocTinh = stringNum.substring(stringNum.length - 4, stringNum.length - 3);
        const level = stringNum.substring(stringNum.length-3, stringNum.length-2);
        return {
            id: this.getThuocTinhName(parseInt(thuocTinh)),
            level : level,
        };
    }
    public getThuocTinhName(magic0: number): string {
        const elementMap: { [key: number]: string } = {
            1: "Thuộc tính Hoả",
            2: "Thuộc tính Thuỷ",
            3: "Thuộc tính Phong",
            4: "Thuộc tính Nội",
            5: "Thuộc tính Ngoại",
            6: "Thuộc tính Độc"
        };
        return elementMap[magic0] || "";
    }
}