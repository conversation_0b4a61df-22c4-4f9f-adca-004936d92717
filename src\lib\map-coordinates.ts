/**
 * Map Coordinate System Utilities
 * 
 * Game World Coordinate System:
 * - X axis: -2560 to +2560 (left to right)
 * - Y axis: -2560 to +2560 (bottom to top)
 * - Center: (0, 0)
 * 
 * Canvas Coordinate System:
 * - X axis: 0 to 5120 (left to right)
 * - Y axis: 0 to 5120 (top to bottom)
 * - Center: (2560, 2560)
 */

export const GAME_MAP_SIZE = 5120;
export const MAP_CENTER = GAME_MAP_SIZE / 2; // 2560

export interface Coordinates {
  x: number;
  y: number;
}

/**
 * Convert game coordinates to canvas coordinates
 * @param gameX Game X coordinate (-2560 to +2560)
 * @param gameY Game Y coordinate (-2560 to +2560, bottom-up)
 * @returns Canvas coordinates (0 to 5120, top-down)
 */
export function gameToCanvas(gameX: number, gameY: number): Coordinates {
  const canvasX = gameX + MAP_CENTER; // -2560~+2560 → 0~5120
  const canvasY = MAP_CENTER - gameY; // -2560~+2560 (bottom-up) → 5120~0 (top-down)
  return { x: canvasX, y: canvasY };
}

/**
 * Convert canvas coordinates to game coordinates
 * @param canvasX Canvas X coordinate (0 to 5120)
 * @param canvasY Canvas Y coordinate (0 to 5120, top-down)
 * @returns Game coordinates (-2560 to +2560, bottom-up)
 */
export function canvasToGame(canvasX: number, canvasY: number): Coordinates {
  const gameX = canvasX - MAP_CENTER; // 0~5120 → -2560~+2560
  const gameY = MAP_CENTER - canvasY; // 5120~0 (top-down) → -2560~+2560 (bottom-up)
  return { x: gameX, y: gameY };
}

/**
 * Validate if game coordinates are within valid bounds
 * @param gameX Game X coordinate
 * @param gameY Game Y coordinate
 * @returns True if coordinates are valid
 */
export function isValidGameCoordinates(gameX: number, gameY: number): boolean {
  return gameX >= -MAP_CENTER && gameX <= MAP_CENTER && 
         gameY >= -MAP_CENTER && gameY <= MAP_CENTER;
}

/**
 * Clamp game coordinates to valid bounds
 * @param gameX Game X coordinate
 * @param gameY Game Y coordinate
 * @returns Clamped coordinates within valid bounds
 */
export function clampGameCoordinates(gameX: number, gameY: number): Coordinates {
  const clampedX = Math.max(-MAP_CENTER, Math.min(MAP_CENTER, gameX));
  const clampedY = Math.max(-MAP_CENTER, Math.min(MAP_CENTER, gameY));
  return { x: clampedX, y: clampedY };
}

/**
 * Get the quadrant of game coordinates
 * @param gameX Game X coordinate
 * @param gameY Game Y coordinate
 * @returns Quadrant name
 */
export function getQuadrant(gameX: number, gameY: number): string {
  if (gameX >= 0 && gameY >= 0) return 'Northeast';
  if (gameX < 0 && gameY >= 0) return 'Northwest';
  if (gameX < 0 && gameY < 0) return 'Southwest';
  if (gameX >= 0 && gameY < 0) return 'Southeast';
  return 'Unknown';
}

/**
 * Calculate distance between two game coordinates
 * @param x1 First point X
 * @param y1 First point Y
 * @param x2 Second point X
 * @param y2 Second point Y
 * @returns Distance in game units
 */
export function calculateDistance(x1: number, y1: number, x2: number, y2: number): number {
  const dx = x2 - x1;
  const dy = y2 - y1;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Format coordinates for display
 * @param gameX Game X coordinate
 * @param gameY Game Y coordinate
 * @returns Formatted string
 */
export function formatCoordinates(gameX: number, gameY: number): string {
  return `(${Math.round(gameX)}, ${Math.round(gameY)})`;
}

/**
 * Convert mouse event to game coordinates
 * @param event Mouse event
 * @param canvas Canvas element
 * @param zoom Current zoom level
 * @param panX Current pan X
 * @param panY Current pan Y
 * @returns Game coordinates
 */
export function mouseEventToGameCoordinates(
  event: React.MouseEvent<HTMLCanvasElement>,
  canvas: HTMLCanvasElement,
  zoom: number,
  panX: number,
  panY: number
): Coordinates {
  const rect = canvas.getBoundingClientRect();
  const scaleX = GAME_MAP_SIZE / rect.width;
  const scaleY = GAME_MAP_SIZE / rect.height;
  
  // Get canvas coordinates
  const canvasX = (event.clientX - rect.left) * scaleX;
  const canvasY = (event.clientY - rect.top) * scaleY;
  
  // Apply inverse transformations
  const adjustedX = (canvasX - panX) / zoom;
  const adjustedY = (canvasY - panY) / zoom;
  
  // Convert to game coordinates
  return canvasToGame(adjustedX, adjustedY);
}

/**
 * Example usage and coordinate mapping:
 * 
 * Game Coordinates → Canvas Coordinates:
 * (-2560, +2560) → (0, 0)      // Top-left
 * (0, +2560)     → (2560, 0)   // Top-center
 * (+2560, +2560) → (5120, 0)   // Top-right
 * (-2560, 0)     → (0, 2560)   // Middle-left
 * (0, 0)         → (2560, 2560) // Center
 * (+2560, 0)     → (5120, 2560) // Middle-right
 * (-2560, -2560) → (0, 5120)   // Bottom-left
 * (0, -2560)     → (2560, 5120) // Bottom-center
 * (+2560, -2560) → (5120, 5120) // Bottom-right
 */
