/**
 * Cfg<PERSON>arser - Parser for Yulgang .cfg files
 * Based on YulgangSimpleCfg format specifications
 * Supports XOR encryption and variable header sizes
 */

export interface CfgHeader {
  headerSize: number;
  originalHeaderBytes: Uint8Array;
  tableHeaderLength: number; // Number of columns
  totalRows: number;
}

export interface CfgRow {
  id: number; // Row index
  columns: string[];
  originalBytes?: Uint8Array[]; // Keep original bytes for each column
}

export interface CfgFile {
  header: CfgHeader;
  rows: CfgRow[];
  fileName: string;
  fileSize: number;
}

export enum HeaderSize {
  SMALL = 8,    // 4 + 4
  MEDIUM = 12,  // 8 + 4  
  LARGE = 16    // 12 + 4
}

export enum LanguageCodePage {
  Korean = 949,
  Vietnamese = 1258,
  Chinese = 936
}

export class CfgParser {
  // XOR key from YulgangSimpleCfg
  private static readonly XOR_KEY = new Uint8Array([
    0x2D, 0x97, 0x84, 0xF2, 0x28, 0xD1, 0x29, 0x54,
    0xF1, 0x12, 0x02, 0x28, 0x6B, 0x20, 0x61, 0x42,
    0xF5, 0xEC, 0x1F, 0x52, 0x22, 0x44, 0x3B, 0x23,
    0x57, 0x3F, 0x62, 0x6F, 0xF2, 0xA3, 0x24, 0xD2,
    0xA7, 0x16, 0x56, 0xC1, 0xF2, 0x02, 0x48, 0x62,
    0x27, 0x42, 0x21, 0xAC, 0x23, 0xAD, 0x43, 0xF2,
    0x32, 0x18, 0x04, 0x50, 0x45, 0x71, 0xBF, 0x6E,
    0x78, 0x61, 0x72, 0x58, 0x22, 0x92, 0x16, 0x02,
    0x78, 0x62, 0xD7, 0x28, 0x52, 0xDF, 0xE8, 0xB3,
    0x42, 0x14, 0xB1, 0xA6, 0x6E, 0x31, 0xAF, 0x26,
    0x21, 0xB3, 0xD7, 0x54, 0xE9, 0x2F, 0x72, 0x2C,
    0x3F, 0x51, 0xF4, 0x11, 0x02, 0xF2, 0xB7, 0x40,
    0x25, 0xC3, 0x25, 0x82, 0x43, 0x32, 0x24, 0xF1,
    0xEE, 0xFF, 0x42, 0x12, 0x04, 0x9E, 0x2F, 0xF9,
    0x64, 0x21, 0xF6, 0x31, 0x72, 0x08, 0x74, 0x82,
    0x23, 0x19, 0xDE, 0xCF, 0x23, 0x27, 0x38, 0x36,
    0xED, 0xF2, 0x49, 0x62, 0x71, 0x28, 0xE8, 0x22,
    0x3B, 0xB7, 0x35, 0x42, 0x77, 0x42, 0x2F, 0xA4,
    0x42, 0x27, 0x94, 0x02, 0x62, 0xA7
  ]);

  /**
   * XOR decrypt/encrypt data
   */
  private static crypt(data: Uint8Array): Uint8Array {
    const result = new Uint8Array(data.length);
    for (let i = 0; i < data.length; i++) {
      result[i] = data[i] ^ this.XOR_KEY[i % this.XOR_KEY.length];
    }
    return result;
  }

  /**
   * Parse .cfg file from ArrayBuffer
   */
  static parse(buffer: ArrayBuffer, fileName: string = 'config.cfg', headerSize: HeaderSize = HeaderSize.LARGE): CfgFile {
    // Decrypt the data first
    const encryptedData = new Uint8Array(buffer);
    const decryptedData = this.crypt(encryptedData);
    
    const view = new DataView(decryptedData.buffer);
    
    // Parse header
    const header = this.parseHeader(view, headerSize);
    
    // Parse table data
    const rows = this.parseRows(decryptedData, header);

    return {
      header,
      rows,
      fileName,
      fileSize: buffer.byteLength
    };
  }

  /**
   * Parse header section
   */
  private static parseHeader(view: DataView, headerSize: HeaderSize): CfgHeader {
    // Store original header bytes (before table length)
    const originalHeaderBytes = new Uint8Array(view.buffer, 0, headerSize);
    
    // Read table header length (number of columns) at headerSize offset
    const tableHeaderLength = view.getUint32(headerSize, true);
    
    // Calculate total rows (we'll update this after parsing)
    const totalRows = 0;

    return {
      headerSize,
      originalHeaderBytes,
      tableHeaderLength,
      totalRows
    };
  }

  /**
   * Parse table rows
   */
  private static parseRows(data: Uint8Array, header: CfgHeader): CfgRow[] {
    const rows: CfgRow[] = [];
    let offset = header.headerSize + 4; // Skip header + table length
    let rowId = 0;

    while (offset < data.length) {
      const row: CfgRow = {
        id: rowId++,
        columns: [],
        originalBytes: []
      };

      // Read columns for this row
      for (let col = 0; col < header.tableHeaderLength; col++) {
        if (offset >= data.length) break;
        
        // Read column length (1 byte)
        const columnLength = data[offset];
        offset++;
        
        if (offset + columnLength > data.length) break;
        
        // Read column data
        const columnBytes = data.slice(offset, offset + columnLength);
        const columnText = this.decodeText(columnBytes);
        
        row.columns.push(columnText);
        row.originalBytes!.push(columnBytes);
        
        offset += columnLength;
      }

      // Only add row if it has the expected number of columns
      if (row.columns.length === header.tableHeaderLength) {
        rows.push(row);
      } else {
        break; // Incomplete row, stop parsing
      }
    }

    // Update total rows in header
    header.totalRows = rows.length;

    return rows;
  }

  /**
   * Decode text from bytes (Latin1 encoding)
   */
  private static decodeText(bytes: Uint8Array): string {
    let result = '';
    for (let i = 0; i < bytes.length; i++) {
      result += String.fromCharCode(bytes[i]);
    }
    return result.trim();
  }

  /**
   * Encode text to bytes (Latin1 encoding)
   */
  private static encodeText(text: string): Uint8Array {
    const bytes = new Uint8Array(text.length);
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i);
      bytes[i] = charCode <= 255 ? charCode : 63; // Use '?' for unsupported chars
    }
    return bytes;
  }

  /**
   * Generate .cfg file from CfgFile
   */
  static generate(cfgFile: CfgFile): ArrayBuffer {
    // Calculate total size needed
    let totalSize = cfgFile.header.headerSize + 4; // Header + table length
    
    for (const row of cfgFile.rows) {
      for (const column of row.columns) {
        totalSize += 1; // Length byte
        totalSize += this.encodeText(column).length; // Column data
      }
    }

    const buffer = new ArrayBuffer(totalSize);
    const view = new DataView(buffer);
    const data = new Uint8Array(buffer);
    let offset = 0;

    // Write original header
    for (let i = 0; i < cfgFile.header.originalHeaderBytes.length; i++) {
      data[offset + i] = cfgFile.header.originalHeaderBytes[i];
    }
    offset += cfgFile.header.headerSize;

    // Write table header length (number of columns)
    view.setUint32(offset, cfgFile.header.tableHeaderLength, true);
    offset += 4;

    // Write rows
    for (const row of cfgFile.rows) {
      for (const column of row.columns) {
        const columnBytes = this.encodeText(column);
        
        // Write column length
        data[offset] = columnBytes.length;
        offset++;
        
        // Write column data
        for (let i = 0; i < columnBytes.length; i++) {
          data[offset + i] = columnBytes[i];
        }
        offset += columnBytes.length;
      }
    }

    // Encrypt the data
    const encryptedData = this.crypt(data);
    
    return encryptedData.buffer as ArrayBuffer;
  }

  /**
   * Validate .cfg file structure
   */
  static validate(buffer: ArrayBuffer, headerSize: HeaderSize = HeaderSize.LARGE): { valid: boolean; error?: string } {
    try {
      // Check minimum size
      if (buffer.byteLength < headerSize + 4) {
        return { valid: false, error: 'File too small' };
      }

      // Try to decrypt and parse header
      const encryptedData = new Uint8Array(buffer);
      const decryptedData = this.crypt(encryptedData);
      const view = new DataView(decryptedData.buffer);
      
      // Check if table header length is reasonable
      const tableHeaderLength = view.getUint32(headerSize, true);
      if (tableHeaderLength <= 0 || tableHeaderLength > 100) {
        return { valid: false, error: `Invalid table header length: ${tableHeaderLength}` };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Auto-detect header size by trying different sizes
   */
  static detectHeaderSize(buffer: ArrayBuffer): HeaderSize {
    const sizes = [HeaderSize.LARGE, HeaderSize.MEDIUM, HeaderSize.SMALL];
    
    for (const size of sizes) {
      const validation = this.validate(buffer, size);
      if (validation.valid) {
        return size;
      }
    }
    
    // Default to LARGE if detection fails
    return HeaderSize.LARGE;
  }

  /**
   * Convert text using Windows codepage (for display purposes)
   */
  static convertToWideChar(text: string, codePage: LanguageCodePage): string {
    // This is a simplified version - in a real implementation,
    // you might want to use a proper encoding library
    switch (codePage) {
      case LanguageCodePage.Korean:
        // Korean text handling
        return text;
      case LanguageCodePage.Vietnamese:
        // Vietnamese text handling
        return text;
      case LanguageCodePage.Chinese:
        // Chinese text handling
        return text;
      default:
        return text;
    }
  }
}
