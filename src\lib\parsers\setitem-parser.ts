/**
 * SetItemParser - Parser for Yulgang SetItem.cfg files
 * Based on YbiReader format specifications
 * Handles SetItem and SetOption data structures
 */

export interface SetItemOption {
  id: number;
  value: number;
}

export interface SetItem {
  id: number;
  name: string;
  part1: number;
  part2: number;
  part3: number;
  part4: number;
  part5: number;
  part6: number;
  options: SetItemOption[];
  offset?: number; // For tracking position in file
}

export interface SetOption {
  id: number;
  name: string;
  offset?: number;
}

export interface SetItemFile {
  setItems: SetItem[];
  setOptions: SetOption[];
  fileName: string;
  fileSize: number;
}

export class SetItemParser {
  // Constants from YbiReader
  private static readonly SET_ITEM_LENGTH = 0xEF4; // 3828 bytes per SetItem
  private static readonly SET_OPTION_LENGTH = 0x44; // 68 bytes per SetOption

  // SetItem offsets
  private static readonly SET_ITEM_ID = 0x0;
  private static readonly SET_ITEM_NAME = 0x4;
  private static readonly SET_ITEM_NAME_LENGTH = 0x40; // 64 bytes
  private static readonly SET_ITEM_PART_1 = 0x44;
  private static readonly SET_ITEM_PART_2 = 0x4C; // Part1 + 8
  private static readonly SET_ITEM_PART_3 = 0x54; // Part2 + 8
  private static readonly SET_ITEM_PART_4 = 0x5C; // Part3 + 8
  private static readonly SET_ITEM_PART_5 = 0x64; // Part4 + 8
  private static readonly SET_ITEM_PART_6 = 0x6C; // Part5 + 8
  private static readonly SET_ITEM_OPTION_OFFSET = 0x19C; // 412 bytes from start

  // SetOption offsets
  private static readonly SET_OPTION_ID = 0x0;
  private static readonly SET_OPTION_NAME = 0x4;
  private static readonly SET_OPTION_NAME_LENGTH = 0x40; // 64 bytes

  /**
   * Parse SetItem.cfg file from ArrayBuffer
   */
  static parse(buffer: ArrayBuffer, fileName: string = 'SetItem.cfg'): SetItemFile {
    const data = new Uint8Array(buffer);
    const view = new DataView(buffer);
    
    // Parse header to determine structure
    const { setItemCount, setOptionCount, setItemsOffset, setOptionsOffset } = this.parseHeader(view);
    
    console.log('SetItem file structure:', {
      setItemCount,
      setOptionCount,
      setItemsOffset,
      setOptionsOffset,
      fileSize: buffer.byteLength
    });

    // Parse SetItems
    const setItems = this.parseSetItems(data, view, setItemsOffset, setItemCount);
    
    // Parse SetOptions
    const setOptions = this.parseSetOptions(data, view, setOptionsOffset, setOptionCount);

    return {
      setItems,
      setOptions,
      fileName,
      fileSize: buffer.byteLength
    };
  }

  /**
   * Parse file header to determine structure
   */
  private static parseHeader(view: DataView): {
    setItemCount: number;
    setOptionCount: number;
    setItemsOffset: number;
    setOptionsOffset: number;
  } {
    // Assume header structure similar to other Yulgang files
    // First 4 bytes might be SetItem count
    const setItemCount = view.getUint32(0, true);
    
    // Next 4 bytes might be SetOption count
    const setOptionCount = view.getUint32(4, true);
    
    // Calculate offsets
    const setItemsOffset = 8; // After header
    const setOptionsOffset = setItemsOffset + (setItemCount * this.SET_ITEM_LENGTH);

    return {
      setItemCount,
      setOptionCount,
      setItemsOffset,
      setOptionsOffset
    };
  }

  /**
   * Parse SetItems from data
   */
  private static parseSetItems(data: Uint8Array, view: DataView, offset: number, count: number): SetItem[] {
    const setItems: SetItem[] = [];
    
    for (let i = 0; i < count; i++) {
      const itemOffset = offset + (i * this.SET_ITEM_LENGTH);
      
      if (itemOffset + this.SET_ITEM_LENGTH > data.length) {
        console.warn(`SetItem ${i} extends beyond file bounds, stopping parse`);
        break;
      }

      const setItem = this.parseSetItem(data, view, itemOffset);
      if (setItem) {
        setItems.push(setItem);
      }
    }

    return setItems;
  }

  /**
   * Parse single SetItem
   */
  private static parseSetItem(data: Uint8Array, view: DataView, offset: number): SetItem | null {
    try {
      // Read ID
      const id = view.getUint32(offset + this.SET_ITEM_ID, true);
      
      // Read Name
      const nameBytes = data.slice(
        offset + this.SET_ITEM_NAME, 
        offset + this.SET_ITEM_NAME + this.SET_ITEM_NAME_LENGTH
      );
      const name = this.decodeString(nameBytes);

      // Read Parts
      const part1 = view.getUint32(offset + this.SET_ITEM_PART_1, true);
      const part2 = view.getUint32(offset + this.SET_ITEM_PART_2, true);
      const part3 = view.getUint32(offset + this.SET_ITEM_PART_3, true);
      const part4 = view.getUint32(offset + this.SET_ITEM_PART_4, true);
      const part5 = view.getUint32(offset + this.SET_ITEM_PART_5, true);
      const part6 = view.getUint32(offset + this.SET_ITEM_PART_6, true);

      // Parse options (from offset 0x19C to end of SetItem)
      const options = this.parseSetItemOptions(data, view, offset + this.SET_ITEM_OPTION_OFFSET);

      return {
        id,
        name,
        part1,
        part2,
        part3,
        part4,
        part5,
        part6,
        options,
        offset
      };
    } catch (error) {
      console.error(`Error parsing SetItem at offset ${offset}:`, error);
      return null;
    }
  }

  /**
   * Parse SetItem options
   */
  private static parseSetItemOptions(data: Uint8Array, view: DataView, offset: number): SetItemOption[] {
    const options: SetItemOption[] = [];
    
    // Options are stored as pairs of ID (4 bytes) + Value (4 bytes)
    // Continue until we hit zeros or end of SetItem
    let currentOffset = offset;
    const maxOffset = offset + (this.SET_ITEM_LENGTH - this.SET_ITEM_OPTION_OFFSET);

    while (currentOffset + 8 <= maxOffset) {
      const id = view.getUint32(currentOffset, true);
      const value = view.getUint32(currentOffset + 4, true);
      
      // Stop if we hit zero ID (end of options)
      if (id === 0) break;
      
      options.push({ id, value });
      currentOffset += 8;
    }

    return options;
  }

  /**
   * Parse SetOptions from data
   */
  private static parseSetOptions(data: Uint8Array, view: DataView, offset: number, count: number): SetOption[] {
    const setOptions: SetOption[] = [];
    
    for (let i = 0; i < count; i++) {
      const optionOffset = offset + (i * this.SET_OPTION_LENGTH);
      
      if (optionOffset + this.SET_OPTION_LENGTH > data.length) {
        console.warn(`SetOption ${i} extends beyond file bounds, stopping parse`);
        break;
      }

      const setOption = this.parseSetOption(data, view, optionOffset);
      if (setOption) {
        setOptions.push(setOption);
      }
    }

    return setOptions;
  }

  /**
   * Parse single SetOption
   */
  private static parseSetOption(data: Uint8Array, view: DataView, offset: number): SetOption | null {
    try {
      // Read ID
      const id = view.getUint32(offset + this.SET_OPTION_ID, true);
      
      // Read Name
      const nameBytes = data.slice(
        offset + this.SET_OPTION_NAME, 
        offset + this.SET_OPTION_NAME + this.SET_OPTION_NAME_LENGTH
      );
      const name = this.decodeString(nameBytes);

      return {
        id,
        name,
        offset
      };
    } catch (error) {
      console.error(`Error parsing SetOption at offset ${offset}:`, error);
      return null;
    }
  }

  /**
   * Decode string from bytes (null-terminated, Latin1)
   */
  private static decodeString(bytes: Uint8Array): string {
    let result = '';
    for (let i = 0; i < bytes.length; i++) {
      if (bytes[i] === 0) break; // Null terminator
      result += String.fromCharCode(bytes[i]);
    }
    return result.trim();
  }

  /**
   * Encode string to bytes (null-terminated, Latin1)
   */
  private static encodeString(text: string, maxLength: number): Uint8Array {
    const bytes = new Uint8Array(maxLength);
    const truncatedText = text.substring(0, maxLength - 1); // Leave space for null terminator
    
    for (let i = 0; i < truncatedText.length; i++) {
      const charCode = truncatedText.charCodeAt(i);
      bytes[i] = charCode <= 255 ? charCode : 63; // Use '?' for unsupported chars
    }
    
    // Null terminator is already 0 from Uint8Array initialization
    return bytes;
  }

  /**
   * Generate SetItem.cfg file from SetItemFile
   */
  static generate(setItemFile: SetItemFile): ArrayBuffer {
    // Calculate total size
    const headerSize = 8; // SetItem count + SetOption count
    const setItemsSize = setItemFile.setItems.length * this.SET_ITEM_LENGTH;
    const setOptionsSize = setItemFile.setOptions.length * this.SET_OPTION_LENGTH;
    const totalSize = headerSize + setItemsSize + setOptionsSize;

    const buffer = new ArrayBuffer(totalSize);
    const data = new Uint8Array(buffer);
    const view = new DataView(buffer);
    let offset = 0;

    // Write header
    view.setUint32(offset, setItemFile.setItems.length, true);
    offset += 4;
    view.setUint32(offset, setItemFile.setOptions.length, true);
    offset += 4;

    // Write SetItems
    for (const setItem of setItemFile.setItems) {
      this.writeSetItem(data, view, offset, setItem);
      offset += this.SET_ITEM_LENGTH;
    }

    // Write SetOptions
    for (const setOption of setItemFile.setOptions) {
      this.writeSetOption(data, view, offset, setOption);
      offset += this.SET_OPTION_LENGTH;
    }

    return buffer;
  }

  /**
   * Write SetItem to buffer
   */
  private static writeSetItem(data: Uint8Array, view: DataView, offset: number, setItem: SetItem): void {
    // Clear the entire SetItem area
    data.fill(0, offset, offset + this.SET_ITEM_LENGTH);

    // Write ID
    view.setUint32(offset + this.SET_ITEM_ID, setItem.id, true);

    // Write Name
    const nameBytes = this.encodeString(setItem.name, this.SET_ITEM_NAME_LENGTH);
    data.set(nameBytes, offset + this.SET_ITEM_NAME);

    // Write Parts
    view.setUint32(offset + this.SET_ITEM_PART_1, setItem.part1, true);
    view.setUint32(offset + this.SET_ITEM_PART_2, setItem.part2, true);
    view.setUint32(offset + this.SET_ITEM_PART_3, setItem.part3, true);
    view.setUint32(offset + this.SET_ITEM_PART_4, setItem.part4, true);
    view.setUint32(offset + this.SET_ITEM_PART_5, setItem.part5, true);
    view.setUint32(offset + this.SET_ITEM_PART_6, setItem.part6, true);

    // Write Options
    let optionOffset = offset + this.SET_ITEM_OPTION_OFFSET;
    for (const option of setItem.options) {
      view.setUint32(optionOffset, option.id, true);
      view.setUint32(optionOffset + 4, option.value, true);
      optionOffset += 8;
    }
  }

  /**
   * Write SetOption to buffer
   */
  private static writeSetOption(data: Uint8Array, view: DataView, offset: number, setOption: SetOption): void {
    // Clear the entire SetOption area
    data.fill(0, offset, offset + this.SET_OPTION_LENGTH);

    // Write ID
    view.setUint32(offset + this.SET_OPTION_ID, setOption.id, true);

    // Write Name
    const nameBytes = this.encodeString(setOption.name, this.SET_OPTION_NAME_LENGTH);
    data.set(nameBytes, offset + this.SET_OPTION_NAME);
  }

  /**
   * Validate SetItem.cfg file structure
   */
  static validate(buffer: ArrayBuffer): { valid: boolean; error?: string } {
    try {
      if (buffer.byteLength < 8) {
        return { valid: false, error: 'File too small for header' };
      }

      const view = new DataView(buffer);
      const setItemCount = view.getUint32(0, true);
      const setOptionCount = view.getUint32(4, true);

      // Check if counts are reasonable
      if (setItemCount < 0 || setItemCount > 10000) {
        return { valid: false, error: `Invalid SetItem count: ${setItemCount}` };
      }

      if (setOptionCount < 0 || setOptionCount > 10000) {
        return { valid: false, error: `Invalid SetOption count: ${setOptionCount}` };
      }

      // Check if file size matches expected size
      const expectedSize = 8 + (setItemCount * this.SET_ITEM_LENGTH) + (setOptionCount * this.SET_OPTION_LENGTH);
      if (buffer.byteLength < expectedSize) {
        return { valid: false, error: `File too small. Expected: ${expectedSize}, Got: ${buffer.byteLength}` };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get SetOption name by ID
   */
  static getSetOptionName(setOptions: SetOption[], id: number): string {
    const option = setOptions.find(opt => opt.id === id);
    return option ? option.name : `Unknown Option ${id}`;
  }

  /**
   * Create new SetItem with default values
   */
  static createNewSetItem(id: number): SetItem {
    return {
      id,
      name: `New Set Item ${id}`,
      part1: 0,
      part2: 0,
      part3: 0,
      part4: 0,
      part5: 0,
      part6: 0,
      options: []
    };
  }

  /**
   * Create new SetOption with default values
   */
  static createNewSetOption(id: number): SetOption {
    return {
      id,
      name: `New Set Option ${id}`
    };
  }
}
