/**
 * YbiParser - Parser for Ybi.cfg files
 * Based on YbiReader C++ implementation
 * Handles encryption/decryption and parsing of game data structures
 */

// Data structures based on YbiReader
export interface YbiItem {
  offset: number;
  id: number;
  name: string;
  unknown1: number;
  level: number;
  jobLevel: number;
  sex: number;
  zx: number;
  maxAtk: number;
  minAtk: number;
  def: number;
  reside1: number;
  reside2: number;
  weight: number;
  shield: number;
  gold: number;
  nj: number;
  el: number;
  unknown2: number;
  unknown3: number;
  unknown4: number;
  unknown5: number;
  unknown6: number;
  recycleGold: number;
  wx: number;
  wxjd: number;
  unknown7: number;
  qg_sll: number;
  itemType: number;
  setBonusDamage: number;
  lock: number;
  desc: string;
}

export interface YbiSkill {
  offset: number;
  type: number; // 0 for label, 1 for skill
  id: number;
  name: string;
  unknown1: number;
  zx: number;
  job: number;
  jobLevel: number;
  level: number;
  mana: number;
  atk: number;
  sp: number;
  u_54: number;
  u_58: number;
  u_60: number;
  effect: number;
  index: number;
  desc: string;
  u_194: number;
  u_198: number;
  sp_upgrade: number;
  atkEachLevel: number;
  sp_tt: number;
  totalSkill: number;
}

export interface YbiAbility {
  offset: number;
  id: number;
  name: string;
  u_44: number;
  job: number;
  level: number;
  jobLevel: number;
  o_1_perLevel: number;
  o_2_perLevel: number;
  o_3_perLevel: number;
  o_4_perLevel: number;
  o_5_perLevel: number;
  o_6_perLevel: number;
  o_7_perLevel: number;
  o_8_perLevel: number;
  o_9_perLevel: number;
  u_5a: number;
  u_6a: number;
  o_1: string;
  o_1_prefix: string;
  o_2: string;
  o_2_prefix: string;
  o_3: string;
  o_3_prefix: string;
  o_4: string;
  o_4_prefix: string;
  o_5: string;
  o_5_prefix: string;
  desc: string;
}

export interface YbiHeroTitle {
  offset: number;
  name: string;
  u_40: number;
  zx: number;
  job: number;
  jobLevel: number;
  level: number;
}

export interface YbiNpcInfo {
  offset: number;
  id: number;
  name: string;
  level: number;
  hp: number;
  desc: string;
  menu1: number;
  menu2: number;
  menu3: number;
  menu4: number;
  u_474: number;
  u_478: number;
  u_47c: number;
  u_480: number;
  u_4f4: number;
  u_4f8: number;
  u_4fc: number;
  u_500: number;
  u_504: number;
  u_508: number;
  u_50c: number;
  u_510: number;
  u_514: number;
  u_518: number;
  desc2: string;
  desc3: string;
  desc4: string;
  desc5: string;
  desc6: string;
  desc7: string;
  desc8: string;
  desc9: string;
  desc10: string;
  desc11: string;
}

export interface YbiMapInfo {
  offset: number;
  id: number;
  name: string;
  x: number;
  z: number;
  y: number;
  x_2: number;
  y_2: number;
  z_2: number;
  u_5c: number;
  bgm1: string;
  bgm2: string;
  u_168: string;
  u_1e8: string;
  u_268: string;
}

export interface YbiHeader {
  totalItems: number;
  headerSize: number;
  originalHeaderBytes?: Uint8Array;
}

export interface YbiParserConfig {
  id: string;
  name: string;
  description: string;
  skillFormat: 'v23' | 'normal';
  npcFormat: 'v20' | 'v24_1' | 'v24_2';
  constants: {
    skillByteLength: number;
    skillByteSize: number;
    npcByteLength: number;
    maxNpcs: number;
    npcOffsetAdjustment: number;
    mapOffsetAdjustment: number;
  };
}

// Predefined parser configurations for different versions
export const YBI_PARSER_CONFIGS: YbiParserConfig[] = [
  {
    id: 'v24_2',
    name: 'V24.2 (Latest)',
    description: 'Kr New Update',
    skillFormat: 'normal',
    npcFormat: 'v24_2',
    constants: {
      skillByteLength: 6992,
      skillByteSize: 0x1A0, // 416 bytes
      npcByteLength: 0x1eb8 + 4, // 7868 bytes
      maxNpcs: 3134,
      npcOffsetAdjustment: 3088,
      mapOffsetAdjustment: 2552 // Adjusted: 3296 - 744 (1 map back)
    }
  },
  {
    id: 'v24_1',
    name: 'V24.1',
    description: 'V24.1 Kr Old',
    skillFormat: 'normal',
    npcFormat: 'v24_1',
    constants: {
      skillByteLength: 6992,
      skillByteSize: 0x1A0, // 416 bytes
      npcByteLength: 0x1eb8 + 4, // 7868 bytes
      maxNpcs: 3134,
      npcOffsetAdjustment: 3088,
      mapOffsetAdjustment: 2552 // Adjusted: 3296 - 744 (1 map back)
    }
  },
  {
    id: 'v23',
    name: 'V23',
    description: 'V23 VTC',
    skillFormat: 'v23',
    npcFormat: 'v20',
    constants: {
      skillByteLength: 6928,
      skillByteSize: 0x19c, // 412 bytes
      npcByteLength: 0x1eb8, // 7860 bytes
      maxNpcs: 3134,
      npcOffsetAdjustment: 3624,
      mapOffsetAdjustment: 3088 // Corrected based on pattern analysis
    }
  },
  {
    id: 'v20',
    name: 'V20 (Legacy)',
    description: 'V20',
    skillFormat: 'normal',
    npcFormat: 'v20',
    constants: {
      skillByteLength: 6992,
      skillByteSize: 0x1A0, // 416 bytes
      npcByteLength: 0x1eb4, // 7860 bytes
      maxNpcs: 2182,
      npcOffsetAdjustment: 3624,
      mapOffsetAdjustment: 3088 // Corrected based on pattern analysis
    }
  }
];

// Helper function to get parser config by ID
export function getParserConfig(id: string): YbiParserConfig | undefined {
  return YBI_PARSER_CONFIGS.find(config => config.id === id);
}

// Helper function to get default parser config
export function getDefaultParserConfig(): YbiParserConfig {
  return YBI_PARSER_CONFIGS[0]; // V24.2 as default
}

export interface YbiFile {
  header: YbiHeader;
  items: YbiItem[];
  skills: YbiSkill[];
  abilities: YbiAbility[];
  heroTitles: YbiHeroTitle[];
  npcInfos: YbiNpcInfo[];
  mapInfos: YbiMapInfo[];
  fileName: string;
  fileSize: number;
  parserConfig: YbiParserConfig;
  originalDecryptedBuffer?: ArrayBuffer; // Keep original decrypted data for preservation
}

export class YbiParser {
  // Constants from YbiReader
  private static readonly HEADER_SIZE = 8;
  private static readonly BYTE_OF_SEPARATION = 0x354; // 852 bytes per item
  private static readonly ABILITY_BYTE_LENGTH = 2964; // 0xB94 = 2964 bytes
  private static readonly NPC_INFO_BYTE_LENGTH = 0x1eb8 + 4; // 7868 bytes (v24)
  private static readonly NPC_INFO_BYTE_LENGTH_V20 = 0x1eb4; // 7860 bytes (v20)
  private static readonly HERO_TITLE_BYTE_LENGTH = 0x48; // 72 bytes
  private static readonly MAP_INFO_BYTE_LENGTH = 0x2e8; // 744 bytes
  private static readonly SKILL_BYTE_LENGTH_V23 = 6928;
  private static readonly SKILL_BYTE_LENGTH = 6992;
  private static readonly SKILL_BYTE_LABEL_SIZE = 0x150; // 336 bytes
  private static readonly SKILL_BYTE_SIZE = 0x1A0; // 416 bytes
  private static readonly SKILL_BYTE_SIZE_V23 = 0x19c; // 412 bytes

  // Encryption key positions from YbiReader
  private static readonly SRC_POSITIONS = [
    26, 31, 17, 10, 30, 16, 24, 2,
    29, 8, 20, 15, 28, 11, 13,
    4, 19, 23, 0, 12, 14, 27,
    6, 18, 21, 3, 9, 7, 22,
    1, 25, 5
  ];

  /**
   * Decrypt/Encrypt file data using bit manipulation
   * Exact C++ implementation - modify buffer in-place
   */
  public static cryptData(buffer: ArrayBuffer): ArrayBuffer {
    // Create a copy to modify (C++ modifies in-place, but we return a new buffer)
    const result = new Uint8Array(buffer);

    // C++ loop: while (startIndex < buffer.size())
    let startIndex = 0;
    while (startIndex < result.length) {
      // C++: int src = *reinterpret_cast<int*>(buffer.data() + startIndex);
      // This reads 4 bytes as little-endian int, even if it goes beyond buffer
      let src = 0;

      // Read 4 bytes directly from current buffer state (like C++)
      // C++ doesn't check bounds here - it just reads memory
      if (startIndex < result.length) src |= result[startIndex];
      if (startIndex + 1 < result.length) src |= (result[startIndex + 1] << 8);
      if (startIndex + 2 < result.length) src |= (result[startIndex + 2] << 16);
      if (startIndex + 3 < result.length) src |= (result[startIndex + 3] << 24);

      // Convert to signed 32-bit (C++ int)
      src = src | 0;

      let num = 0;
      for (let i = 0; i < this.SRC_POSITIONS.length; i++) {
        num |= this.moveBit(src, this.SRC_POSITIONS[i], i);
      }

      // C++ writes back to same positions:
      // buffer[startIndex] = static_cast<char>(num & 0xFF);
      // buffer[startIndex + 1] = static_cast<char>((num >> 8) & 0xFF);
      // buffer[startIndex + 2] = static_cast<char>((num >> 16) & 0xFF);
      // buffer[startIndex + 3] = static_cast<char>((num >> 24) & 0xFF);
      if (startIndex < result.length) result[startIndex] = num & 0xFF;
      if (startIndex + 1 < result.length) result[startIndex + 1] = (num >> 8) & 0xFF;
      if (startIndex + 2 < result.length) result[startIndex + 2] = (num >> 16) & 0xFF;
      if (startIndex + 3 < result.length) result[startIndex + 3] = (num >> 24) & 0xFF;

      startIndex += 4;
    }

    return result.buffer;
  }

  /**
   * Move bit from old location to new location
   */
  private static moveBit(src: number, oldLoc: number, newLoc: number): number {
    return ((src >> oldLoc) & 1) << newLoc;
  }

  /**
   * Decode Latin1 string from bytes
   */
  private static decodeString(bytes: Uint8Array): string {
    let result = '';
    for (let i = 0; i < bytes.length; i++) {
      if (bytes[i] === 0) break; // Stop at null terminator
      result += String.fromCharCode(bytes[i]);
    }
    return result.trim();
  }

  /**
   * Encode string to Latin1 bytes with padding
   * Must match exactly with decodeString logic
   */
  private static encodeString(str: string, length: number): Uint8Array {
    const bytes = new Uint8Array(length);

    // Use Latin-1 encoding (same as decodeString uses String.fromCharCode)
    for (let i = 0; i < Math.min(str.length, length); i++) {
      const charCode = str.charCodeAt(i);
      // Ensure we stay within Latin-1 range (0-255)
      bytes[i] = charCode > 255 ? 63 : charCode; // Use '?' (63) for unsupported chars
    }

    // Remaining bytes are already 0 (null padding)
    return bytes;
  }

  /**
   * Parse Ybi.cfg file from encrypted ArrayBuffer with auto-detection
   */
  static parse(encryptedBuffer: ArrayBuffer, fileName: string = 'Ybi.cfg'): YbiFile {
    // Try to auto-detect the best parser config
    const detectedConfig = this.detectParserConfig(encryptedBuffer);
    return this.parseWithConfig(encryptedBuffer, detectedConfig, fileName);
  }

  /**
   * Parse Ybi.cfg file from encrypted ArrayBuffer with specific parser config
   */
  static parseWithConfig(encryptedBuffer: ArrayBuffer, config: YbiParserConfig, fileName: string = 'Ybi.cfg'): YbiFile {
    // Decrypt the file
    const decryptedBuffer = this.cryptData(encryptedBuffer);
    const view = new DataView(decryptedBuffer);

    // Parse header
    const header = this.parseHeader(view);

    // Calculate offsets based on parser config
    const skillOffset = this.HEADER_SIZE + (header.totalItems + 1) * this.BYTE_OF_SEPARATION + 64;
    const skillLen = config.constants.skillByteLength;
    const abilityOffset = skillOffset + 1024 * skillLen;
    const classNameOffset = abilityOffset + 1024 * this.ABILITY_BYTE_LENGTH;
    const npcOffset = classNameOffset + 256 * this.HERO_TITLE_BYTE_LENGTH;

    // Parse data sections using config
    const items = this.parseItems(view, header.totalItems);
    const skills = this.parseSkills(view, skillOffset, config.skillFormat === 'v23');
    const abilities = this.parseAbilities(view, abilityOffset);
    const heroTitles = this.parseHeroTitles(view, classNameOffset);
    const npcInfos = this.parseNpcInfos(view, npcOffset, config.npcFormat === 'v20', config.constants.maxNpcs, config.constants.npcByteLength);

    // Calculate map offset based on config
    const mapInfoOffset = npcOffset + config.constants.maxNpcs * config.constants.npcByteLength + config.constants.mapOffsetAdjustment;
    const mapInfos = this.parseMapInfos(view, mapInfoOffset);

    return {
      header,
      items,
      skills,
      abilities,
      heroTitles,
      npcInfos,
      mapInfos,
      fileName,
      fileSize: encryptedBuffer.byteLength,
      parserConfig: config,
      originalDecryptedBuffer: decryptedBuffer.slice(0) // Keep copy of original decrypted data
    };
  }

  /**
   * Auto-detect the best parser configuration for a file
   */
  private static detectParserConfig(encryptedBuffer: ArrayBuffer): YbiParserConfig {
    // Decrypt the file for analysis
    const decryptedBuffer = this.cryptData(encryptedBuffer);
    const view = new DataView(decryptedBuffer);

    // Parse header to get basic info
    const header = this.parseHeader(view);
    const skillOffset = this.HEADER_SIZE + (header.totalItems + 1) * this.BYTE_OF_SEPARATION + 64;

    // Try each config and score them
    let bestConfig = getDefaultParserConfig();
    let bestScore = 0;

    for (const config of YBI_PARSER_CONFIGS) {
      const score = this.scoreParserConfig(view, config, skillOffset);
      if (score > bestScore) {
        bestScore = score;
        bestConfig = config;
      }
    }

    return bestConfig;
  }

  /**
   * Score a parser configuration based on how well it parses the data
   */
  private static scoreParserConfig(view: DataView, config: YbiParserConfig, skillOffset: number): number {
    let score = 0;

    try {
      // Test skill parsing
      const skills = this.parseSkills(view, skillOffset, config.skillFormat === 'v23');
      if (skills.length > 0) {
        score += 10;
        // Check if skills have reasonable data
        const validSkills = skills.filter(s => s.id > 0 && s.name.length > 0);
        score += Math.min(validSkills.length, 50); // Max 50 points for valid skills
      }

      // Test NPC parsing
      const classNameOffset = skillOffset + 1024 * config.constants.skillByteLength + 1024 * this.ABILITY_BYTE_LENGTH;
      const npcOffset = classNameOffset + 256 * this.HERO_TITLE_BYTE_LENGTH;

      if (npcOffset + config.constants.npcByteLength < view.buffer.byteLength) {
        const testNpc = this.parseNpcInfo(view, npcOffset);
        if (testNpc.id > 0 && testNpc.id < 10000 && testNpc.name.length > 0) {
          score += 20;
        }
      }

    } catch {
      // Parsing failed, low score
      score = 0;
    }

    return score;
  }

  /**
   * Parse header (8 bytes)
   */
  private static parseHeader(view: DataView): YbiHeader {
    // Store original header bytes
    const originalHeaderBytes = new Uint8Array(view.buffer, 0, this.HEADER_SIZE);

    // Read total items from offset 8 (little endian) - but offset 8 is outside header!
    // Actually, total items should be at offset within the 8-byte header
    // Let's check if we have enough data and read from offset 4 or 6
    let totalItems = 0;
    if (view.buffer.byteLength >= 10) {
      totalItems = view.getUint16(8, true);
    } else if (view.buffer.byteLength >= 6) {
      totalItems = view.getUint16(4, true);
    }

    return {
      totalItems,
      headerSize: this.HEADER_SIZE,
      originalHeaderBytes: new Uint8Array(originalHeaderBytes)
    };
  }

  /**
   * Parse items section
   */
  private static parseItems(view: DataView, itemCount: number): YbiItem[] {
    const items: YbiItem[] = [];
    const startOffset = this.HEADER_SIZE;

    for (let i = 1; i <= itemCount; i++) {
      const offset = startOffset + i * this.BYTE_OF_SEPARATION;
      const item = this.parseItem(view, offset);
      items.push(item);
    }

    return items;
  }

  /**
   * Parse individual item (852 bytes)
   */
  private static parseItem(view: DataView, offset: number): YbiItem {
    // Item offsets from YbiReader
    const ID_OFFSET = 0x00;
    const NAME_OFFSET = 0x08;
    const NAME_LENGTH = 0x40;
    const UNKNOWN1_OFFSET = 0x48;
    const ZX_OFFSET = 0x49;
    const RESIDE1_OFFSET = 0x4A;
    const LEVEL_OFFSET = 0x4C;
    const JOB_LEVEL_OFFSET = 0x4E;
    const SEX_OFFSET = 0x4F;
    const RESIDE2_OFFSET = 0x50;
    const WEIGHT_OFFSET = 0x52;
    const ATK_MAX_OFFSET = 0x54;
    const ATK_MIN_OFFSET = 0x56;
    const DEF_OFFSET = 0x58;
    const NJ_OFFSET = 0x60;
    const GOLD_OFFSET = 0x64;
    const MM1_OFFSET = 0x6C;
    const LOCK_OFFSET = 0x6F;
    const EL_OFFSET = 0x71;
    const UNKNOWN2_OFFSET = 0x75;
    const UNKNOWN3_OFFSET = 0x76;
    const UNKNOWN4_OFFSET = 0x77;
    const UNKNOWN5_OFFSET = 0x78;
    const UNKNOWN6_OFFSET = 0x79;
    const SHIELD_OFFSET = 0x8E;
    const DESC_OFFSET = 0x9C;
    const DESC_LENGTH = 256;
    const WX_OFFSET = 0x19C;
    const WXJD_OFFSET = 0x1A0;
    const UNKNOWN7_OFFSET = 0x1AC;
    const QG_SLL_OFFSET = 0x1C4;
    const ITEM_TYPE_OFFSET = 0x250;
    const BONUS_DAMAGE_OFFSET = 0x251;

    return {
      offset,
      id: view.getUint32(offset + ID_OFFSET, true),
      name: this.decodeString(new Uint8Array(view.buffer, offset + NAME_OFFSET, NAME_LENGTH)),
      unknown1: view.getUint8(offset + UNKNOWN1_OFFSET),
      level: view.getUint16(offset + LEVEL_OFFSET, true),
      jobLevel: view.getUint8(offset + JOB_LEVEL_OFFSET),
      sex: view.getUint8(offset + SEX_OFFSET),
      zx: view.getUint8(offset + ZX_OFFSET),
      maxAtk: view.getUint16(offset + ATK_MAX_OFFSET, true),
      minAtk: view.getUint16(offset + ATK_MIN_OFFSET, true),
      def: view.getUint16(offset + DEF_OFFSET, true),
      reside1: view.getUint8(offset + RESIDE1_OFFSET),
      reside2: view.getUint8(offset + RESIDE2_OFFSET),
      weight: view.getUint16(offset + WEIGHT_OFFSET, true),
      shield: view.getUint16(offset + SHIELD_OFFSET, true),
      gold: Number(view.getBigUint64(offset + GOLD_OFFSET, true)),
      nj: view.getUint16(offset + NJ_OFFSET, true),
      el: view.getUint16(offset + EL_OFFSET, true),
      unknown2: view.getUint8(offset + UNKNOWN2_OFFSET),
      unknown3: view.getUint8(offset + UNKNOWN3_OFFSET),
      unknown4: view.getUint8(offset + UNKNOWN4_OFFSET),
      unknown5: view.getUint8(offset + UNKNOWN5_OFFSET),
      unknown6: view.getUint8(offset + UNKNOWN6_OFFSET),
      recycleGold: view.getUint32(offset + MM1_OFFSET, true),
      wx: view.getUint32(offset + WX_OFFSET, true),
      wxjd: view.getUint32(offset + WXJD_OFFSET, true),
      unknown7: view.getUint8(offset + UNKNOWN7_OFFSET),
      qg_sll: view.getUint32(offset + QG_SLL_OFFSET, true),
      itemType: view.getUint8(offset + ITEM_TYPE_OFFSET),
      setBonusDamage: view.getUint8(offset + BONUS_DAMAGE_OFFSET),
      lock: view.getUint8(offset + LOCK_OFFSET),
      desc: this.decodeString(new Uint8Array(view.buffer, offset + DESC_OFFSET, DESC_LENGTH))
    };
  }

  /**
   * Parse skills section - following YbiReader logic exactly
   */
  private static parseSkills(view: DataView, skillOffset: number, isV23: boolean): YbiSkill[] {
    const skills: YbiSkill[] = [];
    const skillLen = isV23 ? this.SKILL_BYTE_LENGTH_V23 : this.SKILL_BYTE_LENGTH;
    const skillSize = isV23 ? this.SKILL_BYTE_SIZE_V23 : this.SKILL_BYTE_SIZE;

    // Check if we have enough data for skills
    if (skillOffset >= view.buffer.byteLength) {
      return skills; // Not enough data for skills
    }

    // Limit iterations to avoid going beyond buffer - parse more skill packs to find all skills
    const maxSkillPacks = Math.min(1024, Math.floor((view.buffer.byteLength - skillOffset) / skillLen));

    for (let i = 0; i < maxSkillPacks; i++) {
      const packOffset = skillOffset + i * skillLen;

      // Check if we have enough data for this skill pack
      if (packOffset + skillLen > view.buffer.byteLength) {
        break;
      }

      // Create a view for this skill pack (matching QByteArray skillPack = skillByte.mid(skillOffset+ i * len, len))
      const skillPackView = new DataView(view.buffer, packOffset, skillLen);
      let offset = 0; // offset within the skill pack

      // Each skill pack contains 10 skills (1 label + 9 skills)
      for (let j = 0; j < 10; j++) {
        const length = (j === 0) ? this.SKILL_BYTE_LABEL_SIZE : skillSize;
        const type = (j === 0) ? 0 : 1;

        // Check bounds before parsing
        if (offset + length > skillLen) {
          break; // This skill would exceed the skill pack boundary
        }

        // Parse skill from the skill pack view (matching QByteArray skillBytes = skillPack.mid(offset, length))
        const skill = this.parseSkillFromPack(skillPackView, offset, type);

        if (skill.id !== 0) {
          // Calculate absolute offset for storage (matching skill.offset = offset + skillOffset + i * len)
          // This is the absolute position in the file where this skill starts
          skill.offset = offset + skillOffset + i * skillLen;
          skills.push(skill);
        }

        offset += length;
      }
    }

    return skills;
  }

  /**
   * Parse individual skill from skill pack view (matching YbiReader logic)
   */
  private static parseSkillFromPack(skillPackView: DataView, offset: number, type: number): YbiSkill {
    // Skill offsets from YbiReader (relative to skill start, not absolute)
    const SKILL_ID_OFFSET = 0x0;
    const SKILL_NAME_OFFSET = 0x4;
    const SKILL_NAME_LENGTH = 0x40;
    const SKILL_U1_OFFSET = 0x44;
    const SKILL_ZX_OFFSET = 0x45;
    const SKILL_JOB_OFFSET = 0x46;
    const SKILL_LEVEL_OFFSET = 0x48;
    const SKILL_JOB_LEVEL_OFFSET = 0x4A;
    const SKILL_SP_OFFSET = 0x4C;
    const SKILL_MANA_OFFSET = 0x50;
    const SKILL_ATK_OFFSET = 0x52;
    const SKILL_U_54_OFFSET = 0x54;
    const SKILL_U_58_OFFSET = 0x58;
    const SKILL_U_60_OFFSET = 0x60;
    const SKILL_EFFECT_OFFSET = 0x62;
    const SKILL_INDEX_OFFSET = 0x67;
    const SKILL_DESC_OFFSET = 0x68;
    const SKILL_DESC_LENGTH = 0x100;
    const SKILL_LIST_NUMBER_OFFSET = 0x14D;
    const SKILL_ATK_PER_LEVEL_OFFSET = 0x170;
    const SKILL_POINT_TT_OFFSET = 0x174;
    const SKILL_POINT_UPGRADE = 0x16C;
    const SKILL_U_194_OFFSET = 0x194;
    const SKILL_U_198_OFFSET = 0x198;

    return {
      offset: 0, // Will be set by caller
      type,
      id: skillPackView.getUint32(offset + SKILL_ID_OFFSET, true),
      name: this.decodeString(new Uint8Array(skillPackView.buffer, skillPackView.byteOffset + offset + SKILL_NAME_OFFSET, SKILL_NAME_LENGTH)),
      unknown1: skillPackView.getUint8(offset + SKILL_U1_OFFSET),
      zx: skillPackView.getUint8(offset + SKILL_ZX_OFFSET),
      job: skillPackView.getUint8(offset + SKILL_JOB_OFFSET),
      jobLevel: skillPackView.getUint8(offset + SKILL_JOB_LEVEL_OFFSET),
      level: skillPackView.getUint16(offset + SKILL_LEVEL_OFFSET, true),
      mana: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_MANA_OFFSET, true),
      atk: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_ATK_OFFSET, true),
      sp: type === 0 ? 0 : skillPackView.getUint32(offset + SKILL_SP_OFFSET, true),
      u_54: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_U_54_OFFSET, true),
      u_58: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_U_58_OFFSET, true),
      u_60: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_U_60_OFFSET, true),
      effect: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_EFFECT_OFFSET, true),
      index: type === 0 ? 0 : skillPackView.getUint8(offset + SKILL_INDEX_OFFSET),
      desc: this.decodeString(new Uint8Array(skillPackView.buffer, skillPackView.byteOffset + offset + SKILL_DESC_OFFSET, SKILL_DESC_LENGTH)),
      u_194: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_U_194_OFFSET, true),
      u_198: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_U_198_OFFSET, true),
      sp_upgrade: type === 0 ? 0 : skillPackView.getUint32(offset + SKILL_POINT_UPGRADE, true),
      atkEachLevel: type === 0 ? 0 : skillPackView.getUint16(offset + SKILL_ATK_PER_LEVEL_OFFSET, true),
      sp_tt: type === 0 ? 0 : skillPackView.getUint8(offset + SKILL_POINT_TT_OFFSET),
      totalSkill: type === 0 ? skillPackView.getUint8(offset + SKILL_LIST_NUMBER_OFFSET) : 0
    };
  }

  /**
   * Parse individual skill (legacy method - kept for compatibility)
   */
  // private static parseSkill(view: DataView, offset: number, type: number): YbiSkill {
  //   // Skill offsets from YbiReader
  //   const SKILL_ID_OFFSET = 0x0;
  //   const SKILL_NAME_OFFSET = 0x4;
  //   const SKILL_NAME_LENGTH = 0x40;
  //   const SKILL_U1_OFFSET = 0x44;
  //   const SKILL_ZX_OFFSET = 0x45;
  //   const SKILL_JOB_OFFSET = 0x46;
  //   const SKILL_LEVEL_OFFSET = 0x48;
  //   const SKILL_JOB_LEVEL_OFFSET = 0x4A;
  //   const SKILL_SP_OFFSET = 0x4C;
  //   const SKILL_MANA_OFFSET = 0x50;
  //   const SKILL_ATK_OFFSET = 0x52;
  //   const SKILL_U_54_OFFSET = 0x54;
  //   const SKILL_U_58_OFFSET = 0x58;
  //   const SKILL_U_60_OFFSET = 0x60;
  //   const SKILL_EFFECT_OFFSET = 0x62;
  //   const SKILL_INDEX_OFFSET = 0x67;
  //   const SKILL_DESC_OFFSET = 0x68;
  //   const SKILL_DESC_LENGTH = 0x100;
  //   const SKILL_LIST_NUMBER_OFFSET = 0x14D;
  //   const SKILL_ATK_PER_LEVEL_OFFSET = 0x170;
  //   const SKILL_POINT_TT_OFFSET = 0x174;
  //   const SKILL_POINT_UPGRADE = 0x16C;
  //   const SKILL_U_194_OFFSET = 0x194;
  //   const SKILL_U_198_OFFSET = 0x198;

  //   return {
  //     offset,
  //     type,
  //     id: view.getUint32(offset + SKILL_ID_OFFSET, true),
  //     name: this.decodeString(new Uint8Array(view.buffer, offset + SKILL_NAME_OFFSET, SKILL_NAME_LENGTH)),
  //     unknown1: view.getUint8(offset + SKILL_U1_OFFSET),
  //     zx: view.getUint8(offset + SKILL_ZX_OFFSET),
  //     job: view.getUint8(offset + SKILL_JOB_OFFSET),
  //     jobLevel: view.getUint8(offset + SKILL_JOB_LEVEL_OFFSET),
  //     level: view.getUint16(offset + SKILL_LEVEL_OFFSET, true),
  //     mana: type === 0 ? 0 : view.getUint16(offset + SKILL_MANA_OFFSET, true),
  //     atk: type === 0 ? 0 : view.getUint16(offset + SKILL_ATK_OFFSET, true),
  //     sp: type === 0 ? 0 : view.getUint32(offset + SKILL_SP_OFFSET, true),
  //     u_54: type === 0 ? 0 : view.getUint16(offset + SKILL_U_54_OFFSET, true),
  //     u_58: type === 0 ? 0 : view.getUint16(offset + SKILL_U_58_OFFSET, true),
  //     u_60: type === 0 ? 0 : view.getUint16(offset + SKILL_U_60_OFFSET, true),
  //     effect: type === 0 ? 0 : view.getUint16(offset + SKILL_EFFECT_OFFSET, true),
  //     index: type === 0 ? 0 : view.getUint8(offset + SKILL_INDEX_OFFSET),
  //     desc: this.decodeString(new Uint8Array(view.buffer, offset + SKILL_DESC_OFFSET, SKILL_DESC_LENGTH)),
  //     u_194: type === 0 ? 0 : view.getUint16(offset + SKILL_U_194_OFFSET, true),
  //     u_198: type === 0 ? 0 : view.getUint16(offset + SKILL_U_198_OFFSET, true),
  //     sp_upgrade: type === 0 ? 0 : view.getUint32(offset + SKILL_POINT_UPGRADE, true),
  //     atkEachLevel: type === 0 ? 0 : view.getUint16(offset + SKILL_ATK_PER_LEVEL_OFFSET, true),
  //     sp_tt: type === 0 ? 0 : view.getUint8(offset + SKILL_POINT_TT_OFFSET),
  //     totalSkill: type === 0 ? view.getUint8(offset + SKILL_LIST_NUMBER_OFFSET) : 0
  //   };
  // }

  /**
   * Parse abilities section
   */
  private static parseAbilities(view: DataView, abilityOffset: number): YbiAbility[] {
    const abilities: YbiAbility[] = [];

    for (let i = 0; i < 1024; i++) {
      const offset = abilityOffset + i * this.ABILITY_BYTE_LENGTH;
      const ability = this.parseAbility(view, offset);

      if (ability.id !== 0) {
        ability.offset = offset;
        abilities.push(ability);
      }
    }

    return abilities;
  }

  /**
   * Parse individual ability (2964 bytes)
   */
  private static parseAbility(view: DataView, offset: number): YbiAbility {
    // Ability offsets from YbiReader
    const ABI_ID_OFFSET = 0x0;
    const ABI_NAME_OFFSET = 0x4;
    const ABI_NAME_LENGTH = 0x40;
    const ABI_U_44 = 0x44;
    const ABI_JOB_OFFSET = 0x46;
    const ABI_LEVEL_OFFSET = 0x48;
    const ABI_JOB_LEVEL_OFFSET = 0x4A;
    const ABI_PER_LEVEL_1 = 0x4C;
    const O_1 = 0x94;
    const O_1_PREFIX = O_1 + 0x100;
    const O_2 = O_1_PREFIX + 0x100;
    const O_2_PREFIX = O_2 + 0x100;
    const O_3 = O_2_PREFIX + 0x100;
    const O_3_PREFIX = O_3 + 0x100;
    const O_4 = O_3_PREFIX + 0x100;
    const O_4_PREFIX = O_4 + 0x100;
    const O_5 = O_4_PREFIX + 0x100;
    const O_5_PREFIX = O_5 + 0x100;
    const O_6 = O_5_PREFIX + 0x100;

    return {
      offset,
      id: view.getUint32(offset + ABI_ID_OFFSET, true),
      name: this.decodeString(new Uint8Array(view.buffer, offset + ABI_NAME_OFFSET, ABI_NAME_LENGTH)),
      u_44: view.getUint16(offset + ABI_U_44, true),
      job: view.getUint8(offset + ABI_JOB_OFFSET),
      level: view.getUint8(offset + ABI_LEVEL_OFFSET),
      jobLevel: view.getUint8(offset + ABI_JOB_LEVEL_OFFSET),
      o_1_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1, true),
      o_2_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 4, true),
      o_3_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 8, true),
      o_4_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 12, true),
      o_5_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 16, true),
      o_6_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 20, true),
      o_7_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 24, true),
      o_8_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 28, true),
      o_9_perLevel: view.getFloat32(offset + ABI_PER_LEVEL_1 + 32, true),
      u_5a: 0, // Not used in current implementation
      u_6a: 0, // Not used in current implementation
      o_1: this.decodeString(new Uint8Array(view.buffer, offset + O_1, 0x100)),
      o_1_prefix: this.decodeString(new Uint8Array(view.buffer, offset + O_1_PREFIX, 0x100)),
      o_2: this.decodeString(new Uint8Array(view.buffer, offset + O_2, 0x100)),
      o_2_prefix: this.decodeString(new Uint8Array(view.buffer, offset + O_2_PREFIX, 0x100)),
      o_3: this.decodeString(new Uint8Array(view.buffer, offset + O_3, 0x100)),
      o_3_prefix: this.decodeString(new Uint8Array(view.buffer, offset + O_3_PREFIX, 0x100)),
      o_4: this.decodeString(new Uint8Array(view.buffer, offset + O_4, 0x100)),
      o_4_prefix: this.decodeString(new Uint8Array(view.buffer, offset + O_4_PREFIX, 0x100)),
      o_5: this.decodeString(new Uint8Array(view.buffer, offset + O_5, 0x100)),
      o_5_prefix: this.decodeString(new Uint8Array(view.buffer, offset + O_5_PREFIX, 0x100)),
      desc: this.decodeString(new Uint8Array(view.buffer, offset + O_6, 0x100))
    };
  }

  /**
   * Parse hero titles section
   */
  private static parseHeroTitles(view: DataView, classNameOffset: number): YbiHeroTitle[] {
    const titles: YbiHeroTitle[] = [];

    for (let i = 0; i < 256; i++) {
      const offset = classNameOffset + i * this.HERO_TITLE_BYTE_LENGTH;
      const title = this.parseHeroTitle(view, offset);

      if (title.name.length > 0) {
        title.offset = offset;
        titles.push(title);
      }
    }

    return titles;
  }

  /**
   * Parse individual hero title (72 bytes)
   */
  private static parseHeroTitle(view: DataView, offset: number): YbiHeroTitle {
    // HeroTitle offsets from YbiReader
    const HERO_TITLE_NAME_OFFSET = 0x0;
    const HERO_TITLE_NAME_LENGTH = 0x40;
    const HERO_TITLE_U_40 = 0x40;
    const HERO_TITLE_ZX = 0x41;
    const HERO_TITLE_JOB = 0x42;
    const HERO_TITLE_JOB_LEVEL = 0x44;
    const HERO_TITLE_LEVEL = 0x46;

    return {
      offset,
      name: this.decodeString(new Uint8Array(view.buffer, offset + HERO_TITLE_NAME_OFFSET, HERO_TITLE_NAME_LENGTH)),
      u_40: view.getUint8(offset + HERO_TITLE_U_40),
      zx: view.getUint8(offset + HERO_TITLE_ZX),
      job: view.getUint8(offset + HERO_TITLE_JOB),
      jobLevel: view.getUint8(offset + HERO_TITLE_JOB_LEVEL),
      level: view.getUint8(offset + HERO_TITLE_LEVEL)
    };
  }

  /**
   * Parse NPC infos section
   */
  private static parseNpcInfos(view: DataView, npcOffset: number, isV20: boolean = false, maxNpcs?: number, npcLength?: number): YbiNpcInfo[] {
    const npcs: YbiNpcInfo[] = [];
    const actualNpcLength = npcLength || (isV20 ? this.NPC_INFO_BYTE_LENGTH_V20 : this.NPC_INFO_BYTE_LENGTH);
    const actualMaxNpcs = maxNpcs || (isV20 ? 2182 : 3134);

    for (let i = 0; i < actualMaxNpcs; i++) {
      const offset = npcOffset + i * actualNpcLength;

      // Check bounds
      if (offset + actualNpcLength > view.buffer.byteLength) {
        break;
      }

      const npc = this.parseNpcInfo(view, offset);
      npc.offset = offset;
      npcs.push(npc);
    }

    return npcs;
  }

  /**
   * Parse individual NPC info
   */
  private static parseNpcInfo(view: DataView, offset: number): YbiNpcInfo {
    // NpcInfo offsets from YbiReader
    const NPC_INFO_ID_OFFSET = 0x0;
    const NPC_INFO_NAME_OFFSET = 0x4;
    const NPC_INFO_NAME_LENGTH = 0x40;
    const NPC_INFO_LEVEL_OFFSET = 0x46;
    const NPC_INFO_HP_OFFSET = 0x48;
    const NPC_INFO_DESC_OFFSET = 0x4C;
    const NPC_INFO_DESC_LENGTH = 0x400;
    const NPC_INFO_MENU_1 = 0x44C;
    const NPC_INFO_MENU_2 = 0x450;
    const NPC_INFO_MENU_3 = 0x454;
    const NPC_INFO_MENU_4 = 0x458;
    const NPC_INFO_DESC_LENGTH_SMALL = 0xC8;
    const NPC_INFO_DESC2 = 0x574;

    return {
      offset,
      id: view.getUint32(offset + NPC_INFO_ID_OFFSET, true),
      name: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_NAME_OFFSET, NPC_INFO_NAME_LENGTH)),
      level: view.getUint8(offset + NPC_INFO_LEVEL_OFFSET),
      hp: view.getUint32(offset + NPC_INFO_HP_OFFSET, true),
      desc: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC_OFFSET, NPC_INFO_DESC_LENGTH)),
      menu1: view.getUint32(offset + NPC_INFO_MENU_1, true),
      menu2: view.getUint32(offset + NPC_INFO_MENU_2, true),
      menu3: view.getUint32(offset + NPC_INFO_MENU_3, true),
      menu4: view.getUint32(offset + NPC_INFO_MENU_4, true),
      u_474: view.getUint32(offset + 0x474, true),
      u_478: view.getUint32(offset + 0x478, true),
      u_47c: view.getUint32(offset + 0x47C, true),
      u_480: view.getUint32(offset + 0x480, true),
      u_4f4: view.getUint32(offset + 0x4F4, true),
      u_4f8: view.getUint32(offset + 0x4F8, true),
      u_4fc: view.getUint32(offset + 0x4FC, true),
      u_500: view.getUint32(offset + 0x500, true),
      u_504: view.getUint32(offset + 0x504, true),
      u_508: view.getUint32(offset + 0x508, true),
      u_50c: view.getUint32(offset + 0x50C, true),
      u_510: view.getUint32(offset + 0x510, true),
      u_514: view.getUint32(offset + 0x514, true),
      u_518: view.getUint32(offset + 0x518, true),
      desc2: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2, NPC_INFO_DESC_LENGTH_SMALL)),
      desc3: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc4: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 2 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc5: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 3 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc6: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 4 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc7: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 5 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc8: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 6 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc9: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 7 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc10: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 8 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL)),
      desc11: this.decodeString(new Uint8Array(view.buffer, offset + NPC_INFO_DESC2 + 9 * NPC_INFO_DESC_LENGTH_SMALL, NPC_INFO_DESC_LENGTH_SMALL))
    };
  }

  /**
   * Parse map infos section
   */
  private static parseMapInfos(view: DataView, mapOffset: number): YbiMapInfo[] {
    const maps: YbiMapInfo[] = [];
    let i = 0;

    while (i * this.MAP_INFO_BYTE_LENGTH < view.buffer.byteLength - mapOffset) {
      const offset = mapOffset + i * this.MAP_INFO_BYTE_LENGTH;

      if (offset + this.MAP_INFO_BYTE_LENGTH > view.buffer.byteLength) {
        break;
      }

      const map = this.parseMapInfo(view, offset);
      map.offset = offset;
      maps.push(map);
      i++;
    }

    return maps;
  }

  /**
   * Parse individual map info (744 bytes)
   */
  private static parseMapInfo(view: DataView, offset: number): YbiMapInfo {
    // MapInfo offsets from YbiReader (original structure is correct)
    const MAP_INFO_ID_OFFSET = 0x0;
    const MAP_INFO_NAME_OFFSET = 0x4; // Original offset is correct
    const MAP_INFO_NAME_LENGTH = 0x40;
    const MAP_INFO_X_OFFSET = 0x44; // Original offset is correct
    const MAP_INFO_Z_OFFSET = 0x48; // Original offset is correct
    const MAP_INFO_Y_OFFSET = 0x4C; // Original offset is correct
    const MAP_INFO_X2_OFFSET = 0x50; // Back to original
    const MAP_INFO_Z2_OFFSET = 0x54; // Back to original
    const MAP_INFO_Y2_OFFSET = 0x58; // Back to original
    const MAP_INFO_U5C = 0x5C;
    const MAP_INFO_BGM_LENGTH = 0x80;
    const MAP_INFO_BGM = 0x68;
    const MAP_INFO_BGM2 = MAP_INFO_BGM + MAP_INFO_BGM_LENGTH;
    const MAP_INFO_BGM3 = MAP_INFO_BGM2 + MAP_INFO_BGM_LENGTH;
    const MAP_INFO_BGM4 = MAP_INFO_BGM3 + MAP_INFO_BGM_LENGTH;
    const MAP_INFO_BGM5 = MAP_INFO_BGM4 + MAP_INFO_BGM_LENGTH;

    return {
      offset,
      id: view.getUint32(offset + MAP_INFO_ID_OFFSET, true),
      name: this.decodeString(new Uint8Array(view.buffer, offset + MAP_INFO_NAME_OFFSET, MAP_INFO_NAME_LENGTH)),
      x: view.getFloat32(offset + MAP_INFO_X_OFFSET, true),
      z: view.getFloat32(offset + MAP_INFO_Z_OFFSET, true),
      y: view.getFloat32(offset + MAP_INFO_Y_OFFSET, true),
      x_2: view.getFloat32(offset + MAP_INFO_X2_OFFSET, true),
      y_2: view.getFloat32(offset + MAP_INFO_Y2_OFFSET, true),
      z_2: view.getFloat32(offset + MAP_INFO_Z2_OFFSET, true),
      u_5c: view.getUint32(offset + MAP_INFO_U5C, true),
      bgm1: this.decodeString(new Uint8Array(view.buffer, offset + MAP_INFO_BGM, MAP_INFO_BGM_LENGTH)),
      bgm2: this.decodeString(new Uint8Array(view.buffer, offset + MAP_INFO_BGM2, MAP_INFO_BGM_LENGTH)),
      u_168: this.decodeString(new Uint8Array(view.buffer, offset + MAP_INFO_BGM3, MAP_INFO_BGM_LENGTH)),
      u_1e8: this.decodeString(new Uint8Array(view.buffer, offset + MAP_INFO_BGM4, MAP_INFO_BGM_LENGTH)),
      u_268: this.decodeString(new Uint8Array(view.buffer, offset + MAP_INFO_BGM5, MAP_INFO_BGM_LENGTH))
    };
  }

  /**
   * Generate encrypted binary file from YbiFile
   * Strategy: Work directly on decrypted buffer, modify only changed sections, preserve everything else
   */
  static generate(ybiFile: YbiFile): ArrayBuffer {
    console.log(`📝 Generating YBI file: ${ybiFile.fileName}`);

    // Use original decrypted buffer as base to preserve all unchanged data
    if (!ybiFile.originalDecryptedBuffer) {
      throw new Error('Original decrypted buffer not available - cannot generate file safely');
    }

    // Create working copy of original decrypted buffer
    const decryptedBuffer = ybiFile.originalDecryptedBuffer.slice(0);
    const view = new DataView(decryptedBuffer);

    console.log(`📋 Working on decrypted buffer: ${decryptedBuffer.byteLength} bytes`);

    // Write items section if items have been modified
    console.log(`✏️ Writing ${ybiFile.items.length} items to decrypted buffer`);
    this.writeItemsToDecryptedBuffer(view, ybiFile.items);

    // TODO: Add writing for other sections when needed
    // this.writeSkillsToDecryptedBuffer(view, ybiFile.skills, ybiFile.parserConfig);
    // this.writeAbilitiesToDecryptedBuffer(view, ybiFile.abilities);
    // etc.

    // Encrypt the modified decrypted buffer
    const encryptedBuffer = this.cryptData(decryptedBuffer);
    return encryptedBuffer;
  }

  /**
   * Write items section to decrypted buffer
   * This preserves the exact structure and only overwrites item data
   */
  private static writeItemsToDecryptedBuffer(view: DataView, items: YbiItem[]): void {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      // Use the original offset from parsing - this is already correct
      const offset = item.offset;

      console.log(`  Writing item ${item.id} at offset 0x${offset.toString(16)}`);
      this.writeItemToDecryptedBuffer(view, offset, item);
    }
  }

  /**
   * Write individual item to decrypted buffer at specific offset
   * Based on exact C++ YbiReader constants and field sizes
   */
  private static writeItemToDecryptedBuffer(view: DataView, offset: number, item: YbiItem): void {
    // C++ YbiReader constants - EXACT MATCH from itemreader.h
    const ID_OFFSET = 0x00;                    // 4 bytes (quint32)
    const NAME_OFFSET = 0x08;                  // ID_Offset + 8
    const NAME_LENGTH = 0x40;                  // 64 bytes (QByteArray)
    const UNKNOWN1_OFFSET = 0x48;              // Name_Offset + Name_Length
    const ZX_OFFSET = 0x49;                    // 73 (quint8)
    const RESIDE1_OFFSET = 0x4A;               // 74 (quint8)
    const LEVEL_OFFSET = 0x4C;                 // 76 (quint16)
    const JOB_LEVEL_OFFSET = 0x4E;             // 78 (quint8)
    const SEX_OFFSET = 0x4F;                   // 79 (quint8)
    const RESIDE2_OFFSET = 0x50;               // 80 (quint8)
    const WEIGHT_OFFSET = 0x52;                // 82 (quint16)
    const ATK_MAX_OFFSET = 0x54;               // 84 (quint16)
    const ATK_MIN_OFFSET = 0x56;               // 86 (quint16)
    const DEF_OFFSET = 0x58;                   // 88 (quint16)
    const NJ_OFFSET = 0x60;                    // 96 (quint16)
    const GOLD_OFFSET = 0x64;                  // 100 (quint64 - 8 bytes)
    const MM1_OFFSET = 0x6C;                   // 108 (quint64 - recycleGold)
    const LOCK_OFFSET = 0x6F;                  // 111 (quint8)
    const EL_OFFSET = 0x71;                    // 113 (quint16)
    const UNKNOWN2_OFFSET = 0x75;              // 117 (quint8)
    const UNKNOWN3_OFFSET = 0x76;              // 118 (quint8)
    const UNKNOWN4_OFFSET = 0x77;              // 119 (quint8)
    const UNKNOWN5_OFFSET = 0x78;              // 120 (quint8)
    const UNKNOWN6_OFFSET = 0x79;              // 121 (quint8)
    const SHIELD_OFFSET = 0x8E;                // 142 (quint16)
    const DESC_OFFSET = 0x9C;                  // 156 (QByteArray)
    const DESC_LENGTH = 0x100;                 // 256 bytes
    const WX_OFFSET = 0x19C;                   // 412 (quint32)
    const WXJD_OFFSET = 0x1A0;                 // 416 (quint32)
    const UNKNOWN7_OFFSET = 0x1AC;             // 428 (quint8)
    const QG_SLL_OFFSET = 0x1C4;               // 452 (quint32)
    const ITEM_TYPE_OFFSET = 0x250;            // 592 (quint8)
    const BONUS_DAMAGE_OFFSET = 0x251;         // 593 (quint8)

    // Write all fields with EXACT C++ types and byte order (little-endian)
    view.setUint32(offset + ID_OFFSET, item.id, true);

    // Write name: pad to exact 64 bytes with null termination
    this.writeFixedLengthString(view, offset + NAME_OFFSET, item.name, NAME_LENGTH);

    view.setUint8(offset + UNKNOWN1_OFFSET, item.unknown1);
    view.setUint8(offset + ZX_OFFSET, item.zx);
    view.setUint8(offset + RESIDE1_OFFSET, item.reside1);
    view.setUint16(offset + LEVEL_OFFSET, item.level, true);
    view.setUint8(offset + JOB_LEVEL_OFFSET, item.jobLevel);
    view.setUint8(offset + SEX_OFFSET, item.sex);
    view.setUint8(offset + RESIDE2_OFFSET, item.reside2);
    view.setUint16(offset + WEIGHT_OFFSET, item.weight, true);
    view.setUint16(offset + ATK_MAX_OFFSET, item.maxAtk, true);
    view.setUint16(offset + ATK_MIN_OFFSET, item.minAtk, true);
    view.setUint16(offset + DEF_OFFSET, item.def, true);
    view.setUint16(offset + NJ_OFFSET, item.nj, true);

    // Gold is 8-byte (quint64) in C++
    view.setBigUint64(offset + GOLD_OFFSET, BigInt(item.gold), true);

    // RecycleGold is also 8-byte (quint64) in C++
    view.setBigUint64(offset + MM1_OFFSET, BigInt(item.recycleGold), true);

    view.setUint8(offset + LOCK_OFFSET, item.lock);
    view.setUint16(offset + EL_OFFSET, item.el, true);
    view.setUint8(offset + UNKNOWN2_OFFSET, item.unknown2);
    view.setUint8(offset + UNKNOWN3_OFFSET, item.unknown3);
    view.setUint8(offset + UNKNOWN4_OFFSET, item.unknown4);
    view.setUint8(offset + UNKNOWN5_OFFSET, item.unknown5);
    view.setUint8(offset + UNKNOWN6_OFFSET, item.unknown6);
    view.setUint16(offset + SHIELD_OFFSET, item.shield, true);

    // Write description: pad to exact 256 bytes with null termination
    this.writeFixedLengthString(view, offset + DESC_OFFSET, item.desc, DESC_LENGTH);

    view.setUint32(offset + WX_OFFSET, item.wx, true);
    view.setUint32(offset + WXJD_OFFSET, item.wxjd, true);
    view.setUint8(offset + UNKNOWN7_OFFSET, item.unknown7);
    view.setUint32(offset + QG_SLL_OFFSET, item.qg_sll, true);
    view.setUint8(offset + ITEM_TYPE_OFFSET, item.itemType);
    view.setUint8(offset + BONUS_DAMAGE_OFFSET, item.setBonusDamage);
  }

  /**
   * Write fixed-length string to buffer with exact C++ behavior
   * Matches C++ QByteArray behavior: pad with null bytes, truncate if too long
   */
  private static writeFixedLengthString(view: DataView, offset: number, str: string, fixedLength: number): void {
    // Clear the entire field first (fill with 0x00)
    for (let i = 0; i < fixedLength; i++) {
      view.setUint8(offset + i, 0);
    }

    // Encode string to bytes (using same encoding as parsing)
    const encoded = this.encodeString(str, fixedLength);

    // Write bytes directly - encodeString already handles truncation and padding
    for (let i = 0; i < fixedLength; i++) {
      view.setUint8(offset + i, encoded[i]);
    }

    // Remaining bytes are already 0x00 from clearing above
  }

  /**
   * Write string to buffer at specific offset with exact length and padding
   */
  private static writeStringToBuffer(view: DataView, offset: number, str: string, length: number): void {
    // Use the new fixed-length string function
    this.writeFixedLengthString(view, offset, str, length);
  }

  /**
   * Write header to buffer
   */
  private static writeHeader(view: DataView, offset: number, ybiFile: YbiFile): number {
    // Use original header bytes if available, otherwise create new header
    if (ybiFile.header.originalHeaderBytes) {
      const headerBytes = new Uint8Array(view.buffer, offset, this.HEADER_SIZE);
      headerBytes.set(ybiFile.header.originalHeaderBytes);
    } else {
      // Create new header
      view.setUint32(offset, 0, true); // Unknown field
      view.setUint32(offset + 4, 0, true); // Unknown field
    }

    // Write total items at offset 8
    view.setUint16(offset + 8, ybiFile.header.totalItems, true);

    return offset + this.HEADER_SIZE;
  }

  /**
   * Write items section
   */
  private static writeItems(view: DataView, startOffset: number, items: YbiItem[]): number {
    for (let i = 0; i < items.length; i++) {
      const offset = startOffset + (i + 1) * this.BYTE_OF_SEPARATION;
      this.writeItem(view, offset, items[i]);
    }

    return startOffset + (items.length + 1) * this.BYTE_OF_SEPARATION;
  }

  /**
   * Write individual item
   */
  private static writeItem(view: DataView, offset: number, item: YbiItem): void {
    // Item offsets (same as parse offsets)
    const ID_OFFSET = 0x00;
    const NAME_OFFSET = 0x08;
    const NAME_LENGTH = 0x40;
    const UNKNOWN1_OFFSET = 0x48;
    const ZX_OFFSET = 0x49;
    const RESIDE1_OFFSET = 0x4A;
    const LEVEL_OFFSET = 0x4C;
    const JOB_LEVEL_OFFSET = 0x4E;
    const SEX_OFFSET = 0x4F;
    const RESIDE2_OFFSET = 0x50;
    const WEIGHT_OFFSET = 0x52;
    const ATK_MAX_OFFSET = 0x54;
    const ATK_MIN_OFFSET = 0x56;
    const DEF_OFFSET = 0x58;
    const NJ_OFFSET = 0x60;
    const GOLD_OFFSET = 0x64;
    const MM1_OFFSET = 0x6C;
    const LOCK_OFFSET = 0x6F;
    const EL_OFFSET = 0x71;
    const UNKNOWN2_OFFSET = 0x75;
    const UNKNOWN3_OFFSET = 0x76;
    const UNKNOWN4_OFFSET = 0x77;
    const UNKNOWN5_OFFSET = 0x78;
    const UNKNOWN6_OFFSET = 0x79;
    const SHIELD_OFFSET = 0x8E;
    const DESC_OFFSET = 0x9C;
    const DESC_LENGTH = 256;
    const WX_OFFSET = 0x19C;
    const WXJD_OFFSET = 0x1A0;
    // const UNKNOWN7_OFFSET = 0x1AC;
    const QG_SLL_OFFSET = 0x1C4;
    const ITEM_TYPE_OFFSET = 0x250;
    const BONUS_DAMAGE_OFFSET = 0x251;

    // Write all fields
    view.setUint32(offset + ID_OFFSET, item.id, true);

    // Write name
    const nameBytes = this.encodeString(item.name, NAME_LENGTH);
    new Uint8Array(view.buffer, offset + NAME_OFFSET, NAME_LENGTH).set(nameBytes);

    view.setUint8(offset + UNKNOWN1_OFFSET, item.unknown1);
    view.setUint16(offset + LEVEL_OFFSET, item.level, true);
    view.setUint8(offset + JOB_LEVEL_OFFSET, item.jobLevel);
    view.setUint8(offset + SEX_OFFSET, item.sex);
    view.setUint8(offset + ZX_OFFSET, item.zx);
    view.setUint16(offset + ATK_MAX_OFFSET, item.maxAtk, true);
    view.setUint16(offset + ATK_MIN_OFFSET, item.minAtk, true);
    view.setUint16(offset + DEF_OFFSET, item.def, true);
    view.setUint8(offset + RESIDE1_OFFSET, item.reside1);
    view.setUint8(offset + RESIDE2_OFFSET, item.reside2);
    view.setUint16(offset + WEIGHT_OFFSET, item.weight, true);
    view.setUint16(offset + SHIELD_OFFSET, item.shield, true);
    view.setBigUint64(offset + GOLD_OFFSET, BigInt(item.gold), true);
    view.setUint16(offset + NJ_OFFSET, item.nj, true);
    view.setUint16(offset + EL_OFFSET, item.el, true);
    view.setUint8(offset + UNKNOWN2_OFFSET, item.unknown2);
    view.setUint8(offset + UNKNOWN3_OFFSET, item.unknown3);
    view.setUint8(offset + UNKNOWN4_OFFSET, item.unknown4);
    view.setUint8(offset + UNKNOWN5_OFFSET, item.unknown5);
    view.setUint8(offset + UNKNOWN6_OFFSET, item.unknown6);
    view.setUint32(offset + MM1_OFFSET, item.recycleGold, true);
    view.setUint32(offset + WX_OFFSET, item.wx, true);
    view.setUint32(offset + WXJD_OFFSET, item.wxjd, true);
    view.setUint32(offset + QG_SLL_OFFSET, item.qg_sll, true);
    view.setUint8(offset + ITEM_TYPE_OFFSET, item.itemType);
    view.setUint8(offset + BONUS_DAMAGE_OFFSET, item.setBonusDamage);
    view.setUint8(offset + LOCK_OFFSET, item.lock);

    // Write description
    const descBytes = this.encodeString(item.desc, DESC_LENGTH);
    new Uint8Array(view.buffer, offset + DESC_OFFSET, DESC_LENGTH).set(descBytes);
  }

  /**
   * Write skills section (placeholder - complex implementation)
   */
  // private static writeSkills(view: DataView, skillOffset: number, skills: YbiSkill[], isV23: boolean): void {
  //   // This is a simplified implementation
  //   // Full implementation would need to reconstruct skill packs
  //   console.warn('Skill writing not fully implemented - data may be lost');
  // }

  // /**
  //  * Write abilities section (placeholder - complex implementation)
  //  */
  // private static writeAbilities(view: DataView, abilityOffset: number, abilities: YbiAbility[]): void {
  //   // This is a simplified implementation
  //   console.warn('Ability writing not fully implemented - data may be lost');
  // }

  // /**
  //  * Write hero titles section (placeholder - complex implementation)
  //  */
  // private static writeHeroTitles(view: DataView, classNameOffset: number, heroTitles: YbiHeroTitle[]): void {
  //   // This is a simplified implementation
  //   console.warn('HeroTitle writing not fully implemented - data may be lost');
  // }

  // /**
  //  * Write NPC infos section (placeholder - complex implementation)
  //  */
  // private static writeNpcInfos(view: DataView, npcOffset: number, npcInfos: YbiNpcInfo[]): void {
  //   // This is a simplified implementation
  //   console.warn('NpcInfo writing not fully implemented - data may be lost');
  // }

  // /**
  //  * Write map infos section (placeholder - complex implementation)
  //  */
  // private static writeMapInfos(view: DataView, mapInfoOffset: number, mapInfos: YbiMapInfo[]): void {
  //   // This is a simplified implementation
  //   console.warn('MapInfo writing not fully implemented - data may be lost');
  // }
}
