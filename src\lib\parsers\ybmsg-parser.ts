/**
 * YbmsgParser - Parser for ybmsg.cfg files
 * Based on YbiReader format specifications
 * Each record is 0x204 (516) bytes: 0x200 (512) bytes message + 2 bytes ID + 2 bytes unknown
 */

export interface YbmsgHeader {
  totalRecords: number;
  headerSize: number;
  originalHeaderBytes?: Uint8Array; // Store original header bytes
}

export interface YbmsgRecord {
  id: number;
  message: string;
  u_203: number; // Unknown byte at offset 0x202
  u_204: number; // Unknown byte at offset 0x203
  offset?: number; // For tracking position in file
}

export interface YbmsgFile {
  header: YbmsgHeader;
  records: YbmsgRecord[];
  fileName: string;
  fileSize: number;
}

export class YbmsgParser {
  // Constants based on YbiReader format
  private static readonly HEADER_SIZE = 8; // 8 bytes header
  private static readonly RECORD_SIZE = 0x204; // 516 bytes per record
  private static readonly MESSAGE_LENGTH = 0x200; // 512 bytes for message
  private static readonly ID_OFFSET = 0x200; // ID at offset 512
  private static readonly U_203_OFFSET = 0x202; // Unknown byte at 514
  private static readonly U_204_OFFSET = 0x203; // Unknown byte at 515

  /**
   * Parse ybmsg.cfg file from ArrayBuffer
   */
  static parse(buffer: ArrayBuffer, fileName: string = 'ybmsg.cfg'): YbmsgFile {
    const view = new DataView(buffer);

    // Parse header (8 bytes)
    const header = this.parseHeader(view);

    // Parse records starting from offset 8
    const records: YbmsgRecord[] = [];
    let offset = this.HEADER_SIZE;

    for (let i = 0; i < header.totalRecords; i++) {
      const record = this.parseRecord(view, offset);
      records.push(record);
      offset += this.RECORD_SIZE;
    }

    return {
      header,
      records,
      fileName,
      fileSize: buffer.byteLength
    };
  }

  /**
   * Parse header section (8 bytes total)
   */
  private static parseHeader(view: DataView): YbmsgHeader {
    // Store original header bytes
    const originalHeaderBytes = new Uint8Array(view.buffer, 0, this.HEADER_SIZE);

    // Read total records from offset 4 (2 bytes, little endian)
    const totalRecords = view.getUint16(4, true);

    return {
      totalRecords,
      headerSize: this.HEADER_SIZE,
      originalHeaderBytes
    };
  }

  /**
   * Parse individual record (516 bytes total)
   */
  private static parseRecord(view: DataView, offset: number): YbmsgRecord {
    // Read message (512 bytes from offset 0)
    const messageBytes = new Uint8Array(view.buffer, offset, this.MESSAGE_LENGTH);
    const message = this.decodeMessage(messageBytes);

    // Read ID (2 bytes from offset 512, little endian)
    const id = view.getUint16(offset + this.ID_OFFSET, true);

    // Read unknown bytes
    const u_203 = view.getUint8(offset + this.U_203_OFFSET);
    const u_204 = view.getUint8(offset + this.U_204_OFFSET);

    return {
      id,
      message,
      u_203,
      u_204,
      offset
    };
  }

  /**
   * Decode message from Latin1 encoding (like YbiReader)
   */
  private static decodeMessage(bytes: Uint8Array): string {
    // Find null terminator
    let length = bytes.length;
    for (let i = 0; i < bytes.length; i++) {
      if (bytes[i] === 0) {
        length = i;
        break;
      }
    }

    // Convert to string using Latin1 encoding
    let result = '';
    for (let i = 0; i < length; i++) {
      result += String.fromCharCode(bytes[i]);
    }

    return result.trim();
  }

  /**
   * Encode message to Latin1 encoding (like YbiReader)
   */
  static encodeMessage(message: string): Uint8Array {
    const bytes: number[] = [];

    // Convert string to Latin1 bytes
    for (let i = 0; i < message.length; i++) {
      const charCode = message.charCodeAt(i);
      // Latin1 supports characters 0-255
      bytes.push(charCode <= 255 ? charCode : 63); // Use '?' for unsupported chars
    }

    // Add null terminator
    bytes.push(0);

    return new Uint8Array(bytes);
  }

  /**
   * Generate binary file from YbmsgFile
   */
  static generate(ybmsgFile: YbmsgFile): ArrayBuffer {
    // Calculate total size: header + (records * record_size)
    const totalSize = this.HEADER_SIZE + (ybmsgFile.records.length * this.RECORD_SIZE);

    const buffer = new ArrayBuffer(totalSize);
    const view = new DataView(buffer);
    let offset = 0;

    // Write header
    offset = this.writeHeader(view, offset, ybmsgFile);

    // Write records
    for (const record of ybmsgFile.records) {
      offset = this.writeRecord(view, offset, record);
    }

    return buffer;
  }

  /**
   * Write header to buffer (8 bytes)
   */
  private static writeHeader(view: DataView, offset: number, ybmsgFile: YbmsgFile): number {
    if (ybmsgFile.header.originalHeaderBytes) {
      // Use original header bytes but update record count
      const originalHeader = new Uint8Array(ybmsgFile.header.originalHeaderBytes);

      // Copy original header
      for (let i = 0; i < this.HEADER_SIZE; i++) {
        view.setUint8(offset + i, originalHeader[i]);
      }

      // Update only the record count at offset 4 (2 bytes, little endian)
      view.setUint16(offset + 4, ybmsgFile.records.length, true);
    } else {
      // Fallback to default header if original not available
      view.setUint32(offset, 0, true); // Unknown/version
      view.setUint16(offset + 4, ybmsgFile.records.length, true); // Record count
      view.setUint16(offset + 6, 0, true); // Padding
    }

    return offset + this.HEADER_SIZE;
  }

  /**
   * Write record to buffer (516 bytes)
   */
  private static writeRecord(view: DataView, offset: number, record: YbmsgRecord): number {
    // Encode message to 512 bytes
    const messageBytes = this.encodeMessage(record.message);

    // Write message (512 bytes, pad with zeros if shorter)
    for (let i = 0; i < this.MESSAGE_LENGTH; i++) {
      view.setUint8(offset + i, i < messageBytes.length ? messageBytes[i] : 0);
    }

    // Write ID (2 bytes at offset 512, little endian)
    view.setUint16(offset + this.ID_OFFSET, record.id, true);

    // Write unknown bytes
    view.setUint8(offset + this.U_203_OFFSET, record.u_203 || 0);
    view.setUint8(offset + this.U_204_OFFSET, record.u_204 || 0);

    return offset + this.RECORD_SIZE;
  }

  /**
   * Validate ybmsg file structure
   */
  static validate(buffer: ArrayBuffer): { valid: boolean; error?: string } {
    try {
      // Check minimum size (header + at least one record)
      if (buffer.byteLength < this.HEADER_SIZE + this.RECORD_SIZE) {
        return { valid: false, error: 'File too small' };
      }

      // Check if file size matches expected format
      const view = new DataView(buffer);
      const totalRecords = view.getUint16(4, true);
      const expectedSize = this.HEADER_SIZE + (totalRecords * this.RECORD_SIZE);

      if (buffer.byteLength !== expectedSize) {
        return { valid: false, error: `File size mismatch: expected ${expectedSize}, got ${buffer.byteLength}` };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}
