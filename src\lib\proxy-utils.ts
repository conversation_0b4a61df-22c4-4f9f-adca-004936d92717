import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { headers } from 'next/headers';
import { baseUrl, timeout } from '@/app/constant';

/**
 * Interface for proxy request options
 */
export interface ProxyRequestOptions {
  method?: string;
  body?: unknown;
  headers?: Record<string, string>;
  requiredPermission?: string;
}

/**
 * Interface for session validation response
 */
export interface SessionInfo {
  sessionId: string;
  userId: string;
  token: string;
  permissions: string[];
}

/**
 * Validate session and check permissions
 */
export async function validateSessionAndPermissions(
  requiredPermission?: string
): Promise<{ success: boolean; sessionInfo?: SessionInfo; message?: string }> {
  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: await headers()
    });

    if (!session?.user) {
      return {
        success: false,
        message: 'Authentication required'
      };
    }
    // For now, we'll create a basic session info
    // In a real implementation, you might want to validate with HeroLogin
    const sessionInfo: SessionInfo = {
      sessionId: session.session.id,
      userId: session.user.id,
      token: session.session.token,
      permissions: [] // This should come from your permission system
    };

    // Check permission if required
    if (requiredPermission) {
      // TODO: Implement actual permission checking logic
      // For now, we'll assume all authenticated users have all permissions
      console.log(`Checking permission: ${requiredPermission}`);
    }

    return {
      success: true,
      sessionInfo
    };
  } catch (error) {
    console.error('Session validation error:', error);
    return {
      success: false,
      message: 'Authentication failed'
    };
  }
}

/**
 * Make authenticated request to game server API
 */
export async function makeProxyRequest<T>(
  endpoint: string,
  options: ProxyRequestOptions = {}
): Promise<T> {
  // Validate session first
  const validation = await validateSessionAndPermissions(options.requiredPermission);
  if (!validation.success) {
    throw new Error(validation.message || 'Authentication failed');
  }

  const url = `${baseUrl}${endpoint}`;
  const requestHeaders = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${validation.sessionInfo?.token}`,
    ...options.headers
  };
  const requestOptions: RequestInit = {
    method: options.method || 'GET',
    headers: requestHeaders,
    signal: AbortSignal.timeout(timeout)
  };
  // console.log('Proxy request options:', requestOptions);
  if (options.body && (options.method === 'POST' || options.method === 'PUT' || options.method === 'PATCH')) {
    requestOptions.body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
  }

  const response = await fetch(url, requestOptions);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));

    // Handle validation errors (400 Bad Request)
    if (response.status === 400 && errorData.errors) {
      const validationErrors = Object.entries(errorData.errors)
        .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
        .join('; ');

      const error = new Error(`Validation failed: ${validationErrors}`);
      (error as any).status = 400;
      (error as any).details = errorData;
      throw error;
    }

    // Handle other errors
    const error = new Error(errorData.message || errorData.title || `HTTP ${response.status}: ${response.statusText}`);
    (error as any).status = response.status;
    (error as any).details = errorData;
    throw error;
  }

  return await response.json();
}

/**
 * Handle API route with common error handling
 */
export async function handleApiRoute<T>(
  handler: () => Promise<T>
): Promise<NextResponse> {
  try {
    const result = await handler();
    return NextResponse.json(result);
  } catch (error) {
    console.error('API route error:', error);

    if (error instanceof Error) {
      // Check if error has status and details (from makeProxyRequest)
      const errorStatus = (error as any).status;
      const errorDetails = (error as any).details;

      if (errorStatus) {
        // Return the original error response with details
        return NextResponse.json(
          {
            success: false,
            message: error.message,
            details: errorDetails
          },
          { status: errorStatus }
        );
      }

      // Handle specific error types
      if (error.message.includes('Authentication')) {
        return NextResponse.json(
          { success: false, message: error.message },
          { status: 401 }
        );
      }

      if (error.message.includes('required')) {
        return NextResponse.json(
          { success: false, message: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

/**
 * Parse query parameters from URL
 */
export function parseQueryParams(request: NextRequest): URLSearchParams {
  const { searchParams } = new URL(request.url);
  return searchParams;
}



