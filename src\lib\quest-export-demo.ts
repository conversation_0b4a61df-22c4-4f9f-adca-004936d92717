// Quest Export Demo - Minh họa cách sử dụng tính năng export
import { QuestExporter } from './quest-exporter';
import { parseYbqFile } from './ybq-parser';
import {
  QuestExportConfig,
  QuestRequirementType,
  QuestRewardType
} from '@/types/quest-export';

/**
 * Demo function để export quest data
 */
export async function demoQuestExport() {
  console.log('=== Quest Export Demo ===');

  // 1. Cấu hình export
  const exportConfig: QuestExportConfig = {
    includeUnknownFields: true,
    includeDialogs: true,
    includeCoordinates: true,
    minifyOutput: false,
    validateRequirements: true
  };

  console.log('Export Config:', exportConfig);

  // 2. Tạo exporter
  const exporter = new QuestExporter(exportConfig);

  // 3. Giả sử đã có YBQ data (trong thực tế sẽ load từ file)
  // const ybqData = parseYbqFile(fileBuffer);

  // 4. Demo với mock data
  const mockYbqData = createDemoYbqData();

  try {
    // 5. Export tất cả quest
    console.log('\n--- Exporting all quests ---');
    const exportData = exporter.exportQuestData(mockYbqData);

    console.log(`Exported ${exportData.totalQuests} quests`);
    console.log('Export metadata:', exportData.metadata);

    // 6. Export specific quests
    console.log('\n--- Exporting specific quests ---');
    const specificExport = exporter.exportQuestData(mockYbqData, [1001, 1002]);
    console.log(`Exported ${specificExport.totalQuests} specific quests`);

    // 7. Export to different formats
    console.log('\n--- Export to JSON ---');
    const jsonOutput = exporter.exportToJson(exportData);
    console.log('JSON size:', jsonOutput.length, 'characters');

    console.log('\n--- Export to XML ---');
    const xmlOutput = exporter.exportToXml(exportData);
    console.log('XML size:', xmlOutput.length, 'characters');

    // 8. Show statistics
    console.log('\n--- Export Statistics ---');
    const stats = exporter.getStats();
    console.log('Stats:', stats);

    // 9. Demo validation
    console.log('\n--- Validation Demo ---');
    for (const quest of exportData.quests) {
      console.log(`Quest ${quest.questId}: ${quest.questName}`);
      console.log(`  - Level: ${quest.questLevel}`);
      console.log(`  - Accept Requirements: ${quest.acceptRequirements.length}`);
      console.log(`  - Completion Requirements: ${quest.completionRequirements.length}`);
      console.log(`  - Rewards: ${quest.rewards.length}`);
      console.log(`  - Stages: ${quest.stages.length}`);
      console.log(`  - Special Quest: ${quest.isSpecialQuest}`);
      console.log(`  - Category: ${quest.category}`);
    }

    return exportData;

  } catch (error) {
    console.error('Export failed:', error);
    throw error;
  }
}

/**
 * Demo function để tạo mock YBQ data với nhiều quest types
 */
function createDemoYbqData() {
  // Import các function cần thiết từ ybq-parser
  const { newLenData, newStringData } = require('./ybq-parser');

  const quest1 = {
    questID: newLenData(1001),
    questName: newStringData('Beginner Quest'),
    questLevel: newLenData(1),
    unknown1: newLenData(0),
    unknown2: newLenData(0),
    unknown3: newLenData(0),
    unknown4: newLenData(0),
    unknown5: newLenData(0),
    unknown6: newLenData(0),
    unknown7: newLenData(0),
    unknown8: newLenData(0),
    unknown9: newLenData(0),
    unknown10: newLenData(0), // Normal quest
    unknown11: newLenData(0),
    questAccept0: newStringData('Welcome, new adventurer!'),
    questAccept1: newStringData('Are you ready for your first quest?'),
    questAccept2: newStringData('Excellent! Let\'s start your journey.'),
    questRefuse1: newStringData('Take your time to prepare.'),
    questRefuse2: newStringData('Come back when you\'re ready.'),
    welcomeAcceptPrompt1: newStringData('Quest accepted! Good luck!'),
    welcomeAcceptPrompt2: newStringData(''),
    welcomeAcceptPrompt3: newStringData(''),
    welcomeAcceptPrompt4: newStringData(''),
    welcomeAcceptPrompt5: newStringData(''),
    welcomeRefusePrompt1: newStringData('Maybe next time.'),
    welcomeRefusePrompt2: newStringData(''),
    welcomeRefusePrompt3: newStringData(''),
    welcomeRefusePrompt4: newStringData(''),
    welcomeRefusePrompt5: newStringData(''),
    questStageNumber: newLenData(1),
    npcID: newLenData(1001),
    npcUnknown1: newLenData(0),
    npcCoords: {
      mapID: newLenData(1),
      coordsX: newLenData(100),
      coordsY: newLenData(100),
      coordsZ: newLenData(0)
    },
    questStages: [],
    requiredItems: [
      {
        itemID: newLenData(1001),
        itemAmount: newLenData(1),
        mapID: newLenData(1),
        coordsX: newLenData(0),
        coordsY: newLenData(0),
        coordsZ: newLenData(0)
      }
    ],
    rewardItems: [
      {
        itemID: newLenData(2001),
        itemAmount: newLenData(1)
      }
    ],
    footerExtend: newStringData('Beginner quest completed!')
  };

  const quest2 = {
    questID: newLenData(1002),
    questName: newStringData('Special Elite Quest'),
    questLevel: newLenData(50),
    unknown1: newLenData(0),
    unknown2: newLenData(0),
    unknown3: newLenData(0),
    unknown4: newLenData(0),
    unknown5: newLenData(0),
    unknown6: newLenData(0),
    unknown7: newLenData(0),
    unknown8: newLenData(0),
    unknown9: newLenData(0),
    unknown10: newLenData(16), // Special quest
    unknown11: newLenData(0),
    questAccept0: newStringData('This is a special elite quest!'),
    questAccept1: newStringData('Only the strongest can complete this.'),
    questAccept2: newStringData('Are you prepared for the challenge?'),
    questRefuse1: newStringData('Wise choice. Train more first.'),
    questRefuse2: newStringData('Return when you are stronger.'),
    welcomeAcceptPrompt1: newStringData('Elite quest accepted!'),
    welcomeAcceptPrompt2: newStringData('Prepare for battle!'),
    welcomeAcceptPrompt3: newStringData(''),
    welcomeAcceptPrompt4: newStringData(''),
    welcomeAcceptPrompt5: newStringData(''),
    welcomeRefusePrompt1: newStringData('Train harder and return.'),
    welcomeRefusePrompt2: newStringData(''),
    welcomeRefusePrompt3: newStringData(''),
    welcomeRefusePrompt4: newStringData(''),
    welcomeRefusePrompt5: newStringData(''),
    questStageNumber: newLenData(3),
    npcID: newLenData(2001),
    npcUnknown1: newLenData(0),
    npcCoords: {
      mapID: newLenData(5),
      coordsX: newLenData(500),
      coordsY: newLenData(500),
      coordsZ: newLenData(0)
    },
    questStages: [
      {
        content: newStringData('Defeat 10 elite monsters'),
        npcID: newLenData(2002),
        npcUnknown1: newLenData(0),
        npcMapID: newLenData(5),
        npcCoordsX: newLenData(550),
        npcCoordsY: newLenData(550),
        npcCoordsZ: newLenData(0),
        requiredItems: [],
        conditionMatchPrompt1: newStringData('Excellent combat skills!'),
        conditionMatchPrompt2: newStringData(''),
        conditionMatchPrompt3: newStringData(''),
        conditionMatchPrompt4: newStringData(''),
        conditionMatchPrompt5: newStringData(''),
        conditionNoMatchPrompt1: newStringData('You need to defeat more monsters.'),
        conditionNoMatchPrompt2: newStringData(''),
        conditionNoMatchPrompt3: newStringData(''),
        conditionNoMatchPrompt4: newStringData(''),
        conditionNoMatchPrompt5: newStringData('')
      },
      {
        content: newStringData('Collect rare materials'),
        npcID: newLenData(2003),
        npcUnknown1: newLenData(0),
        npcMapID: newLenData(5),
        npcCoordsX: newLenData(600),
        npcCoordsY: newLenData(600),
        npcCoordsZ: newLenData(0),
        requiredItems: [
          {
            itemID: newLenData(5001),
            itemAmount: newLenData(3),
            mapID: newLenData(5),
            coordsX: newLenData(0),
            coordsY: newLenData(0),
            coordsZ: newLenData(0)
          }
        ],
        conditionMatchPrompt1: newStringData('Perfect! You found the materials.'),
        conditionMatchPrompt2: newStringData(''),
        conditionMatchPrompt3: newStringData(''),
        conditionMatchPrompt4: newStringData(''),
        conditionMatchPrompt5: newStringData(''),
        conditionNoMatchPrompt1: newStringData('Keep searching for rare materials.'),
        conditionNoMatchPrompt2: newStringData(''),
        conditionNoMatchPrompt3: newStringData(''),
        conditionNoMatchPrompt4: newStringData(''),
        conditionNoMatchPrompt5: newStringData('')
      }
    ],
    requiredItems: [
      {
        itemID: newLenData(5001),
        itemAmount: newLenData(3),
        mapID: newLenData(5),
        coordsX: newLenData(0),
        coordsY: newLenData(0),
        coordsZ: newLenData(0)
      }
    ],
    rewardItems: [
      {
        itemID: newLenData(6001),
        itemAmount: newLenData(1)
      },
      {
        itemID: newLenData(6002),
        itemAmount: newLenData(5)
      },
      {
        itemID: newLenData(6003),
        itemAmount: newLenData(10)
      }
    ],
    footerExtend: newStringData('Elite quest mastered!')
  };

  return {
    sign: 'DEMO',
    signEx: 'DEMO_EXPORT',
    encrypted: [],
    decrypted: [],
    loaded: true,
    totalQuest: 2,
    quests: {
      1001: quest1,
      1002: quest2
    }
  };
}

/**
 * Demo function để test các requirement types mới
 */
export function demoExtendedRequirements() {
  console.log('\n=== Extended Requirements Demo ===');

  // Demo các loại requirement mới
  const requirements = [
    {
      type: QuestRequirementType.LEVEL,
      value: 50,
      description: 'Character must be level 50 or higher'
    },
    {
      type: QuestRequirementType.JOB,
      value: 1,
      jobId: 1,
      description: 'Must be a Warrior'
    },
    {
      type: QuestRequirementType.JOB_LEVEL,
      value: 30,
      jobId: 1,
      description: 'Warrior job level must be 30+'
    },
    {
      type: QuestRequirementType.ABILITY_POINT,
      value: 0,
      description: 'All ability points must be spent'
    },
    {
      type: QuestRequirementType.FACTION,
      value: 1,
      factionId: 1,
      description: 'Must belong to faction 1'
    },
    {
      type: QuestRequirementType.GUILD,
      value: 1,
      guildId: 100,
      guildRank: 3,
      description: 'Must be rank 3+ in guild 100'
    }
  ];

  console.log('Supported requirement types:');
  requirements.forEach(req => {
    console.log(`- ${req.type}: ${req.description}`);
  });

  // Demo các loại reward mới
  const rewards = [
    {
      type: QuestRewardType.ITEM,
      value: 1,
      itemId: 1001,
      itemAmount: 1,
      description: 'Legendary Sword'
    },
    {
      type: QuestRewardType.EXPERIENCE,
      value: 10000,
      description: '10,000 experience points'
    },
    {
      type: QuestRewardType.GOLD,
      value: 5000,
      description: '5,000 gold coins'
    },
    {
      type: QuestRewardType.SKILL_POINT,
      value: 3,
      description: '3 skill points'
    }
  ];

  console.log('\nSupported reward types:');
  rewards.forEach(reward => {
    console.log(`- ${reward.type}: ${reward.description}`);
  });
}

// Export demo functions
export { demoQuestExport as default };