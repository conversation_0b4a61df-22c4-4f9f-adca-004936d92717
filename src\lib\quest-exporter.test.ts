// Quest Exporter Test Suite
import { QuestExporter } from './quest-exporter';
import {
  QuestExportConfig,
  QuestRequirementType,
  QuestRewardType
} from '@/types/quest-export';
import {
  YbqData,
  Quest,
  QuestStage,
  newLenData,
  newStringData
} from '@/types/ybq';

/**
 * Mock YBQ data for testing
 */
function createMockYbqData(): YbqData {
  const mockQuest: Quest = {
    questID: newLenData(1001),
    questName: newStringData('Test Quest'),
    questLevel: newLenData(10),
    unknown1: newLenData(0),
    unknown2: newLenData(0),
    unknown3: newLenData(0),
    unknown4: newLenData(0),
    unknown5: newLenData(0),
    unknown6: newLenData(0),
    unknown7: newLenData(0),
    unknown8: newLenData(0),
    unknown9: newLenData(0),
    unknown10: newLenData(0),
    unknown11: newLenData(0),
    questAccept0: newStringData('Welcome to test quest!'),
    questAccept1: newStringData('Do you accept this quest?'),
    questAccept2: newStringData('Great! Let\'s begin.'),
    questRefuse1: newStringData('Maybe next time.'),
    questRefuse2: newStringData('Come back when ready.'),
    welcomeAcceptPrompt1: newStringData('Quest accepted!'),
    welcomeAcceptPrompt2: newStringData(''),
    welcomeAcceptPrompt3: newStringData(''),
    welcomeAcceptPrompt4: newStringData(''),
    welcomeAcceptPrompt5: newStringData(''),
    welcomeRefusePrompt1: newStringData('Quest declined.'),
    welcomeRefusePrompt2: newStringData(''),
    welcomeRefusePrompt3: newStringData(''),
    welcomeRefusePrompt4: newStringData(''),
    welcomeRefusePrompt5: newStringData(''),
    questStageNumber: newLenData(2),
    npcID: newLenData(5001),
    npcUnknown1: newLenData(0),
    npcCoords: {
      mapID: newLenData(1),
      coordsX: newLenData(100),
      coordsY: newLenData(200),
      coordsZ: newLenData(0)
    },
    questStages: [
      {
        content: newStringData('Collect 5 test items'),
        npcID: newLenData(5002),
        npcUnknown1: newLenData(0),
        npcMapID: newLenData(1),
        npcCoordsX: newLenData(150),
        npcCoordsY: newLenData(250),
        npcCoordsZ: newLenData(0),
        requiredItems: [
          {
            itemID: newLenData(2001),
            itemAmount: newLenData(5),
            mapID: newLenData(1),
            coordsX: newLenData(0),
            coordsY: newLenData(0),
            coordsZ: newLenData(0)
          }
        ],
        conditionMatchPrompt1: newStringData('You have the items!'),
        conditionMatchPrompt2: newStringData(''),
        conditionMatchPrompt3: newStringData(''),
        conditionMatchPrompt4: newStringData(''),
        conditionMatchPrompt5: newStringData(''),
        conditionNoMatchPrompt1: newStringData('You need more items.'),
        conditionNoMatchPrompt2: newStringData(''),
        conditionNoMatchPrompt3: newStringData(''),
        conditionNoMatchPrompt4: newStringData(''),
        conditionNoMatchPrompt5: newStringData('')
      }
    ],
    requiredItems: [
      {
        itemID: newLenData(2001),
        itemAmount: newLenData(5),
        mapID: newLenData(1),
        coordsX: newLenData(0),
        coordsY: newLenData(0),
        coordsZ: newLenData(0)
      }
    ],
    rewardItems: [
      {
        itemID: newLenData(3001),
        itemAmount: newLenData(1)
      },
      {
        itemID: newLenData(3002),
        itemAmount: newLenData(3)
      }
    ],
    footerExtend: newStringData('Test quest footer')
  };

  return {
    sign: 'TEST',
    signEx: 'TEST_SIGN_EX',
    encrypted: [],
    decrypted: [],
    loaded: true,
    totalQuest: 1,
    quests: {
      1001: mockQuest
    }
  };
}

/**
 * Test configuration
 */
const testConfig: QuestExportConfig = {
  includeUnknownFields: true,
  includeDialogs: true,
  includeCoordinates: true,
  minifyOutput: false,
  validateRequirements: true
};

/**
 * Test suite
 */
describe('QuestExporter', () => {
  let exporter: QuestExporter;
  let mockData: YbqData;

  beforeEach(() => {
    exporter = new QuestExporter(testConfig);
    mockData = createMockYbqData();
  });

  test('should create exporter with config', () => {
    expect(exporter).toBeDefined();
    expect(exporter.getStats().totalQuests).toBe(0);
  });

  test('should export quest data successfully', () => {
    const exportData = exporter.exportQuestData(mockData);

    expect(exportData).toBeDefined();
    expect(exportData.version).toBe('1.0.0');
    expect(exportData.totalQuests).toBe(1);
    expect(exportData.quests).toHaveLength(1);
    expect(exportData.metadata.exportFormat).toBe('gameserver_csharp');
  });

  test('should convert quest correctly', () => {
    const exportData = exporter.exportQuestData(mockData);
    const quest = exportData.quests[0];

    expect(quest.questId).toBe(1001);
    expect(quest.questName).toBe('Test Quest');
    expect(quest.questLevel).toBe(10);
    expect(quest.isSpecialQuest).toBe(false);
  });

  test('should convert requirements correctly', () => {
    const exportData = exporter.exportQuestData(mockData);
    const quest = exportData.quests[0];

    // Should have level requirement
    const levelReq = quest.acceptRequirements.find(r => r.type === QuestRequirementType.LEVEL);
    expect(levelReq).toBeDefined();
    expect(levelReq?.value).toBe(10);

    // Should have item requirements
    const itemReq = quest.completionRequirements.find(r => r.type === QuestRequirementType.ITEM);
    expect(itemReq).toBeDefined();
    expect(itemReq?.itemId).toBe(2001);
    expect(itemReq?.itemAmount).toBe(5);
  });

  test('should convert rewards correctly', () => {
    const exportData = exporter.exportQuestData(mockData);
    const quest = exportData.quests[0];

    expect(quest.rewards).toHaveLength(2);

    const reward1 = quest.rewards[0];
    expect(reward1.type).toBe(QuestRewardType.ITEM);
    expect(reward1.itemId).toBe(3001);
    expect(reward1.itemAmount).toBe(1);

    const reward2 = quest.rewards[1];
    expect(reward2.type).toBe(QuestRewardType.ITEM);
    expect(reward2.itemId).toBe(3002);
    expect(reward2.itemAmount).toBe(3);
  });
});