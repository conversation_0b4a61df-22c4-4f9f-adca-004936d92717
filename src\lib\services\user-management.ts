import { 
  UserWithRoles, 
  CreateUserData, 
  UpdateUserData,
  RoleWithStats,
  CreateRoleData,
  UpdateRoleData,
  ApiResponse 
} from '@/lib/types/user-management';

const API_BASE = '/api';

// User API functions
export async function fetchUsers(): Promise<UserWithRoles[]> {
  const response = await fetch(`${API_BASE}/users`);
  const result: ApiResponse<UserWithRoles[]> = await response.json();
  
  if (!result.success) {
    throw new Error(result.message || 'Failed to fetch users');
  }
  
  return result.data || [];
}

export async function createUser(data: CreateUserData): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/users`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  return await response.json();
}

export async function updateUser(userId: string, data: UpdateUserData & { roleIds?: string[] }): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/users/${userId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  return await response.json();
}

export async function deleteUser(userId: string): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/users/${userId}`, {
    method: 'DELETE',
  });
  
  return await response.json();
}

export async function toggleUserStatus(userId: string): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/users/${userId}`, {
    method: 'PATCH',
  });
  
  return await response.json();
}

// Role API functions
export async function fetchRoles(): Promise<RoleWithStats[]> {
  const response = await fetch(`${API_BASE}/roles`);
  const result: ApiResponse<RoleWithStats[]> = await response.json();
  
  if (!result.success) {
    throw new Error(result.message || 'Failed to fetch roles');
  }
  
  return result.data || [];
}

export async function fetchPermissions(): Promise<string[]> {
  const response = await fetch(`${API_BASE}/roles?type=permissions`);
  const result: ApiResponse<string[]> = await response.json();
  
  if (!result.success) {
    throw new Error(result.message || 'Failed to fetch permissions');
  }
  
  return result.data || [];
}

export async function createRole(data: CreateRoleData): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/roles`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  return await response.json();
}

export async function updateRole(roleId: string, data: UpdateRoleData): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/roles/${roleId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  return await response.json();
}

export async function deleteRole(roleId: string): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/roles/${roleId}`, {
    method: 'DELETE',
  });
  
  return await response.json();
}

// User-Role assignment functions
export async function assignRoleToUser(userId: string, roleId: string): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/users/${userId}/roles`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ roleId }),
  });
  
  return await response.json();
}

export async function removeRoleFromUser(userId: string, roleId: string): Promise<ApiResponse> {
  const response = await fetch(`${API_BASE}/users/${userId}/roles?roleId=${roleId}`, {
    method: 'DELETE',
  });
  
  return await response.json();
}
