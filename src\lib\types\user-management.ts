// User types
export interface UserWithRoles {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  isActive: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  roles: Array<{
    id: string;
    name: string;
    description: string | null;
    level: number;
    permissions: string[] | null;
  }>;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  isActive?: boolean;
  roleIds?: string[];
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  isActive?: boolean;
  password?: string;
  password_old?: string;
}

// Role types
export interface RoleWithStats {
  id: string;
  name: string;
  description: string | null;
  level: number;
  permissions: string[] | null;
  createdAt: Date;
  updatedAt: Date;
  userCount: number;
}

export interface CreateRoleData {
  name: string;
  description?: string;
  level: number;
  permissions: string[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  level?: number;
  permissions?: string[];
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T;
}

// Role levels
export const ROLE_LEVELS = {
  ADMIN: 1,
  MANAGER: 2,
  MODERATOR: 3,
  EDITOR: 4,
} as const;

export type RoleLevel = typeof ROLE_LEVELS[keyof typeof ROLE_LEVELS];

// Helper functions
export function getRoleLevelText(level: number): string {
  switch (level) {
    case 1: return 'Admin';
    case 2: return 'Manager';
    case 3: return 'Moderator';
    case 4: return 'Editor';
    default: return 'Unknown';
  }
}

export function getRoleLevelColor(level: number): 'destructive' | 'default' | 'secondary' | 'outline' {
  switch (level) {
    case 1: return 'destructive';
    case 2: return 'default';
    case 3: return 'secondary';
    case 4: return 'outline';
    default: return 'outline';
  }
}
