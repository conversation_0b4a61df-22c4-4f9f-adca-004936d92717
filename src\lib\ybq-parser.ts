// YBQ Parser - Client-side implementation
// Tương ứng với logic trong Go project

import {
  YbqData,
  Quest,
  QuestStage,
  LenData,
  StringData,
} from '@/types/ybq';

/**
 * Encryption key array (FIXED - tương ứng chính xác với Go version)
 */
const ENCRYPTION_KEY: number[] = (() => {
  const key = new Array(256);
  for (let i = 0; i < 256; i++) {
    // Chính xác theo Go version: ((uint(i>>4) & 1) | (uint(i>>2) & 0x18) | (uint(i>>1) & 0x40) | uint(2*((i&3)|(4*((i&4)|(2*(i&0xF8)))))))
    const part1 = (i >> 4) & 1;
    const part2 = (i >> 2) & 0x18;
    const part3 = (i >> 1) & 0x40;
    const part4 = 2 * ((i & 3) | (4 * ((i & 4) | (2 * (i & 0xF8)))));
    key[i] = (part1 | part2 | part3 | part4) & 0xFF; // FIXED: Ensure byte value (0-255)
  }
  return key;
})();

/**
 * Text decoder/encoder for Windows-1252
 */
class Windows1252Codec {
  private static readonly WINDOWS_1252_MAP = new Map([
    [128, 8364], [129, 129], [130, 8218], [131, 402], [132, 8222], [133, 8230], [134, 8224], [135, 8225],
    [136, 710], [137, 8240], [138, 352], [139, 8249], [140, 338], [141, 141], [142, 381], [143, 143],
    [144, 144], [145, 8216], [146, 8217], [147, 8220], [148, 8221], [149, 8226], [150, 8211], [151, 8212],
    [152, 732], [153, 8482], [154, 353], [155, 8250], [156, 339], [157, 157], [158, 382], [159, 376]
  ]);

  static decode(bytes: number[]): string {
    return bytes.map(byte => {
      if (byte < 128 || byte > 159) {
        return String.fromCharCode(byte);
      }
      return String.fromCharCode(this.WINDOWS_1252_MAP.get(byte) || byte);
    }).join('');
  }

  static encode(str: string): number[] {
    const result: number[] = [];
    for (let i = 0; i < str.length; i++) {
      const charCode = str.charCodeAt(i);
      if (charCode < 128) {
        result.push(charCode);
      } else {
        // Find reverse mapping
        let found = false;
        for (const [byte, unicode] of this.WINDOWS_1252_MAP) {
          if (unicode === charCode) {
            result.push(byte);
            found = true;
            break;
          }
        }
        if (!found) {
          result.push(63); // '?' character
        }
      }
    }
    return result;
  }
}

/**
 * Decrypt data using encryption key
 */
export function decrypt(data: number[]): number[] {
  return data.map(byte => ENCRYPTION_KEY[byte]);
}

/**
 * Encrypt data using encryption key
 */
export function encrypt(data: number[]): number[] {
  const result = new Array(data.length);
  for (let i = 0; i < data.length; i++) {
    const byte = data[i];
    const keyIndex = ENCRYPTION_KEY.indexOf(byte);
    result[i] = keyIndex !== -1 ? keyIndex : 0;
  }
  return result;
}

/**
 * Read LenData from byte array - FIXED TO MATCH GO VERSION
 */
function getLen(data: number[], pos: { value: number }): LenData {
  const startOffset = pos.value;

  // Read until space (32) or newline (10)
  while (pos.value < data.length) {
    const byte = data[pos.value];
    if ((byte === 32 || byte === 10) && pos.value > startOffset) {
      const delimiter = byte;

      // Copy buffer from start to current position
      const buffer: number[] = [];
      for (let i = startOffset; i < pos.value; i++) {
        buffer.push(data[i]);
      }

      const rawString = Windows1252Codec.decode(buffer);
      const value = parseInt(rawString) || 0;

      pos.value++; // Skip delimiter

      // Handle multiple consecutive whitespace (suffix) - IMPORTANT!
      const suffixStart = pos.value;
      while (pos.value < data.length && (data[pos.value] === 32 || data[pos.value] === 10)) {
        pos.value++;
      }

      // Store suffix data if any
      const suffixData: number[] = [];
      if (pos.value > suffixStart) {
        for (let i = suffixStart; i < pos.value; i++) {
          suffixData.push(data[i]);
        }
      }

      return {
        value,
        rawBytes: [...buffer],
        rawString,
        delimiter,
        suffixData: suffixData.length > 0 ? suffixData : undefined
      };
    }
    pos.value++;
  }

  // If we reach here, we've read to end of data
  const buffer: number[] = [];
  for (let i = startOffset; i < pos.value; i++) {
    buffer.push(data[i]);
  }

  return {
    value: 0,
    rawBytes: [...buffer],
    rawString: Windows1252Codec.decode(buffer),
    delimiter: 32
  };
}

/**
 * Read StringData from byte array - FIXED TO MATCH GO VERSION
 */
function getString(data: number[], pos: { value: number }, checkNewline?: boolean): StringData {
  // Read length first
  const lengthData = getLen(data, pos);
  const length = lengthData.value;

  if (length <= 0 || pos.value + length > data.length) {
    return {
      value: '',
      length: 0,
      lengthData,
      rawBytes: []
    };
  }

  // Read string content with exact length
  const stringBytes: number[] = [];
  let hasNewlineInContent = false;

  for (let i = 0; i < length; i++) {
    const byte = data[pos.value + i];
    stringBytes.push(byte);

    // Check if we encounter 0x0A (newline) in string content
    if (checkNewline && byte === 0x0A) {
      hasNewlineInContent = true;
    }
  }

  const value = Windows1252Codec.decode(stringBytes);

  pos.value += length;

  // Special handling when newline found in content - indicates possible data type mismatch
  if (hasNewlineInContent && checkNewline) {
    // console.log(`Found newline in string content. Possible data type mismatch. Value: "${value}", length: ${length}, rawBytes: [${stringBytes.join(', ')}]`);
    // // Move back one position to compensate for missing 0x20 byte
    // // This handles cases like: 20 30 20 20 30 20 0A 38 31 38 20 35 39 20 5B 4B 68 75 79 EA
    // // where a 0x20 byte might be missing before the actual data
    // if (pos.value > 0) {
    //   pos.value -= 1;
    // }
  }

  // Handle suffix whitespace - IMPORTANT!
  const suffixStart = pos.value;
  while (pos.value < data.length && (data[pos.value] === 32 || data[pos.value] === 10)) {
    pos.value++;
  }

  // Store suffix data if any
  const suffixData: number[] = [];
  if (pos.value > suffixStart) {
    for (let i = suffixStart; i < pos.value; i++) {
      suffixData.push(data[i]);
    }
  }

  return {
    value,
    length,
    lengthData,
    rawBytes: [...stringBytes],
    suffixData: suffixData.length > 0 ? suffixData : undefined,
    hasNewlineInContent // Add flag to indicate this special case
  };
}

/**
 * Create new LenData
 */
export function newLenData(value: number): LenData {
  const rawString = value.toString();
  const rawBytes = Windows1252Codec.encode(rawString);
  
  return {
    value,
    rawBytes,
    rawString,
    delimiter: 32
  };
}

/**
 * Create new StringData
 */
export function newStringData(value: string): StringData {
  const rawBytes = Windows1252Codec.encode(value);
  const length = rawBytes.length;
  
  return {
    value,
    length,
    lengthData: newLenData(length),
    rawBytes
  };
}

/**
 * Parse YBQ file from ArrayBuffer
 */
export function parseYbqFile(buffer: ArrayBuffer): YbqData {
  const data = new Uint8Array(buffer);
  const bytes = Array.from(data);

  console.log(`File size: ${bytes.length} bytes`);
  console.log(`First 20 bytes: [${bytes.slice(0, 20).join(', ')}]`);

  // Initialize result
  const result: YbqData = {
    sign: '',
    signEx: '',
    encrypted: [],
    decrypted: [],
    loaded: false,
    totalQuest: 0,
    quests: {}
  };

  let pos = 0;

  // Read sign (until space)
  const signBytes: number[] = [];
  while (pos < bytes.length && bytes[pos] !== 32) {
    signBytes.push(bytes[pos]);
    pos++;
  }
  result.sign = Windows1252Codec.decode(signBytes);
  pos++; // Skip space
  console.log(`Sign: "${result.sign}", pos after sign: ${pos}`);

  // Read signEx (until \r\n)
  const signExBytes: number[] = [];
  while (pos < bytes.length - 1 && !(bytes[pos] === 13 && bytes[pos + 1] === 10)) {
    signExBytes.push(bytes[pos]);
    pos++;
  }
  result.signEx = Windows1252Codec.decode(signExBytes);
  pos += 2; // Skip \r\n
  console.log(`SignEx: "${result.signEx}", pos after signEx: ${pos}`);

  // Read quest count
  console.log(`About to read quest count at position ${pos}`);
  console.log(`Next 20 bytes for quest count: [${bytes.slice(pos, pos + 20).join(', ')}]`);

  const questCountPos = { value: pos };
  const questCountData = getLen(bytes, questCountPos);
  result.totalQuest = questCountData.value;
  pos = questCountPos.value;
  console.log(`Total quest count: ${result.totalQuest}, pos after count: ${pos}`);
  console.log(`Quest count raw data: [${questCountData.rawBytes?.join(', ')}], rawString: "${questCountData.rawString}", delimiter: ${questCountData.delimiter}`);

  // Read encrypted data
  result.encrypted = bytes.slice(pos);
  result.decrypted = decrypt(result.encrypted);
  console.log(`Encrypted data size: ${result.encrypted.length} bytes`);
  console.log(`Decrypted data size: ${result.decrypted.length} bytes`);
  console.log(`First 20 decrypted bytes: [${result.decrypted.slice(0, 20).join(', ')}]`);

  // Parse quest data
  result.quests = parseQuestData(result.decrypted);
  result.loaded = true;

  return result;
}

/**
 * Parse quest data from decrypted bytes - UPDATED WITH TESTED LOGIC
 */
function parseQuestData(data: number[]): Record<number, Quest> {
  const quests: Record<number, Quest> = {};
  const pos = { value: 0 };

  console.log(`Starting to parse quest data, total bytes: ${data.length}`);
  console.log(`First 50 bytes of quest data: [${data.slice(0, 50).join(', ')}]`);

  while (pos.value < data.length) {
    console.log(`=== Attempting to read quest at position ${pos.value} ===`);
    console.log(`Next 20 bytes: [${data.slice(pos.value, pos.value + 20).join(', ')}]`);

    // Read quest ID
    const questIDData = getLen(data, pos);
    const questID = questIDData.value;

    console.log(`QuestID: ${questID}, Position: ${pos.value}, RawBytes: [${questIDData.rawBytes?.join(', ')}], RawString: "${questIDData.rawString}", Delimiter: ${questIDData.delimiter}`);

    if (questID === 0 || pos.value >= data.length) {
      console.log(`Stopping parse: questID=${questID}, pos=${pos.value}, dataLength=${data.length}`);
      break;
    }

    // Read quest name
    const questNameData = getString(data, pos);
    console.log(`Quest ${questID} name: "${questNameData.value}", length: ${questNameData.length}`);

    // Create quest object
    const quest: Quest = {
      questID: questIDData,
      questName: questNameData,
      questLevel: getLen(data, pos),
      unknown1: getLen(data, pos),
      unknown2: getLen(data, pos),
      unknown3: getLen(data, pos),
      unknown4: getLen(data, pos),
      unknown5: getLen(data, pos),
      unknown6: getLen(data, pos),
      unknown7: getLen(data, pos),
      unknown8: getLen(data, pos),
      unknown9: getLen(data, pos),
      unknown10: getLen(data, pos),
      unknown11: getLen(data, pos),
      questAccept0: newStringData(''),
      questAccept1: newStringData(''),
      questAccept2: newStringData(''),
      questRefuse1: newStringData(''),
      questRefuse2: newStringData(''),
      welcomeAcceptPrompt1: newStringData(''),
      welcomeAcceptPrompt2: newStringData(''),
      welcomeAcceptPrompt3: newStringData(''),
      welcomeAcceptPrompt4: newStringData(''),
      welcomeAcceptPrompt5: newStringData(''),
      welcomeRefusePrompt1: newStringData(''),
      welcomeRefusePrompt2: newStringData(''),
      welcomeRefusePrompt3: newStringData(''),
      welcomeRefusePrompt4: newStringData(''),
      welcomeRefusePrompt5: newStringData(''),
      questStageNumber: newLenData(0),
      npcID: newLenData(0),
      npcUnknown1: newLenData(0),
      npcCoords: {
        mapID: newLenData(0),
        coordsX: newLenData(0),
        coordsY: newLenData(0),
        coordsZ: newLenData(0)
      },
      questStages: [],
      requiredItems: [],
      rewardItems: [],
      footerExtend: newStringData(''),
      offset: pos.value
    };

    // Handle special quests vs normal quests - FIXED: Use Unknown10 = 16 instead of hardcoded IDs
    const isSpecialQuest = quest.unknown10.value === 16;

    console.log(`Quest ${questID} Unknown10: ${quest.unknown10.value}, isSpecialQuest: ${isSpecialQuest}`);

    if (isSpecialQuest) {
      console.log(`Quest ${questID} is a special quest`);
      // Read additional unknown fields for special quests
      quest.unknown12 = getLen(data, pos);
      quest.unknown13 = getLen(data, pos);
      quest.unknown14 = getLen(data, pos);
      quest.unknown15 = getLen(data, pos);
      quest.unknown16 = getLen(data, pos);
      quest.unknown17 = getLen(data, pos);
      quest.unknown18 = getLen(data, pos);
      quest.unknown19 = getLen(data, pos);

      quest.questAccept2 = getString(data, pos);
      quest.questRefuse1 = getString(data, pos);
      quest.questAccept1 = getString(data, pos);
      quest.questRefuse2 = getString(data, pos);
    } else {
      console.log(`Quest ${questID} is a normal quest`);
      // Normal quest dialog reading - FIXED: Skip 2 bytes as in Go version
      pos.value += 2; // Skip 2 bytes
      console.log(`Skipped 2 bytes, new pos: ${pos.value}`);

      quest.questAccept1 = getString(data, pos);
      console.log(`QuestAccept1 len: ${quest.questAccept1.length}`);

      quest.questRefuse1 = getString(data, pos);
      console.log(`QuestRefuse1 len: ${quest.questRefuse1.length}`);

      quest.questAccept2 = getString(data, pos);
      console.log(`QuestAccept2 len: ${quest.questAccept2.length}`);

      quest.questRefuse2 = getString(data, pos);
      console.log(`QuestRefuse2 len: ${quest.questRefuse2.length}`);
    }

    // Read reward items
    const totalReward = getLen(data, pos).value;
    quest.rewardItems = [];
    for (let j = 0; j < totalReward; j++) {
      quest.rewardItems.push({
        itemID: getLen(data, pos),
        itemAmount: getLen(data, pos)
      });
    }

    // Read quest stages
    quest.questStageNumber = getLen(data, pos);
    if (quest.questStageNumber.value > 0) {
      // Read NPC info
      quest.npcID = getLen(data, pos);
      quest.npcUnknown1 = getLen(data, pos);
      quest.npcCoords.mapID = getLen(data, pos);
      quest.npcCoords.coordsX = getLen(data, pos);
      quest.npcCoords.coordsY = getLen(data, pos);
      quest.npcCoords.coordsZ = getLen(data, pos);

      // Read required items
      const requiredItemsCount = getLen(data, pos).value;
      quest.requiredItems = [];
      for (let k = 0; k < requiredItemsCount; k++) {
        quest.requiredItems.push({
          itemID: getLen(data, pos),
          itemAmount: getLen(data, pos),
          mapID: getLen(data, pos),
          coordsX: getLen(data, pos),
          coordsY: getLen(data, pos),
          coordsZ: getLen(data, pos)
        });
      }

      // Read welcome prompts
      quest.welcomeAcceptPrompt1 = getString(data, pos);
      quest.welcomeAcceptPrompt2 = getString(data, pos);
      quest.welcomeAcceptPrompt3 = getString(data, pos);
      quest.welcomeAcceptPrompt4 = getString(data, pos);
      quest.welcomeAcceptPrompt5 = getString(data, pos);
      quest.welcomeRefusePrompt1 = getString(data, pos);
      quest.welcomeRefusePrompt2 = getString(data, pos);
      quest.welcomeRefusePrompt3 = getString(data, pos);
      quest.welcomeRefusePrompt4 = getString(data, pos);
      quest.welcomeRefusePrompt5 = getString(data, pos);

      // Read quest stages
      const stageCount = quest.questStageNumber.value - 1;
      if (stageCount > 0) {
        quest.questStages = [];
        for (let l = 0; l < stageCount; l++) {
          const stage: QuestStage = {
            content: getString(data, pos),
            npcID: getLen(data, pos),
            npcUnknown1: getLen(data, pos),
            npcMapID: getLen(data, pos),
            npcCoordsX: getLen(data, pos),
            npcCoordsY: getLen(data, pos),
            npcCoordsZ: getLen(data, pos),
            requiredItems: [],
            conditionMatchPrompt1: newStringData(''),
            conditionMatchPrompt2: newStringData(''),
            conditionMatchPrompt3: newStringData(''),
            conditionMatchPrompt4: newStringData(''),
            conditionMatchPrompt5: newStringData(''),
            conditionNoMatchPrompt1: newStringData(''),
            conditionNoMatchPrompt2: newStringData(''),
            conditionNoMatchPrompt3: newStringData(''),
            conditionNoMatchPrompt4: newStringData(''),
            conditionNoMatchPrompt5: newStringData('')
          };

          // Read stage required items
          const stageRequiredItemsCount = getLen(data, pos).value;
          for (let m = 0; m < stageRequiredItemsCount; m++) {
            stage.requiredItems.push({
              itemID: getLen(data, pos),
              itemAmount: getLen(data, pos),
              mapID: getLen(data, pos),
              coordsX: getLen(data, pos),
              coordsY: getLen(data, pos),
              coordsZ: getLen(data, pos)
            });
          }

          // Read condition prompts
          stage.conditionMatchPrompt1 = getString(data, pos);
          stage.conditionMatchPrompt2 = getString(data, pos);
          stage.conditionMatchPrompt3 = getString(data, pos);
          stage.conditionMatchPrompt4 = getString(data, pos);
          stage.conditionMatchPrompt5 = getString(data, pos);
          stage.conditionNoMatchPrompt1 = getString(data, pos);
          stage.conditionNoMatchPrompt2 = getString(data, pos);
          stage.conditionNoMatchPrompt3 = getString(data, pos);
          stage.conditionNoMatchPrompt4 = getString(data, pos);
          stage.conditionNoMatchPrompt5 = getString(data, pos);

          quest.questStages.push(stage);
        }
      }

      // Read footer extend
      if (questID != 817 && questID != 8168) // có vẻ là getlen
        quest.footerExtend = getString(data, pos, true);

      // Skip newline
      if (pos.value < data.length && data[pos.value] === 10) {
        pos.value++;
      }
    }

    // Add quest to collection
    quests[questID] = quest;
    console.log(`=== Finished parsing quest ${questID}: "${quest.questName.value}" at position ${pos.value} ===`);
  }

  console.log(`Finished parsing all quests. Total parsed: ${Object.keys(quests).length}`);
  return quests;
}

/**
 * Write LenData to byte array
 */
function writeLenData(lenData: LenData): number[] {
  if (lenData.rawBytes && lenData.rawBytes.length > 0) {
    const result = [...lenData.rawBytes];
    result.push(lenData.delimiter || 32);
    return result;
  }

  const valueStr = lenData.value.toString();
  const encoded = Windows1252Codec.encode(valueStr);
  encoded.push(lenData.delimiter || 32);
  return encoded;
}

/**
 * Write StringData to byte array
 */
function writeStringData(stringData: StringData): number[] {
  const result: number[] = [];

  // Write length
  const lengthBytes = writeLenData(stringData.lengthData);
  result.push(...lengthBytes);

  // Write string content
  if (stringData.rawBytes && stringData.rawBytes.length > 0) {
    result.push(...stringData.rawBytes);
  } else {
    const encoded = Windows1252Codec.encode(stringData.value);
    result.push(...encoded);
  }

  return result;
}

/**
 * Serialize quest data to bytes
 */
function writeQuestData(quests: Record<number, Quest>): number[] {
  const result: number[] = [];

  // Sort quest IDs
  const questIDs = Object.keys(quests)
    .map(id => parseInt(id))
    .filter(id => id > 0)
    .sort((a, b) => a - b);

  for (const questID of questIDs) {
    const quest = quests[questID];

    // Skip quests without name
    if (!quest.questName.value) {
      continue;
    }

    // Write quest ID
    result.push(...writeLenData(quest.questID));

    // Write quest name
    result.push(...writeStringData(quest.questName));

    // Write basic quest data
    result.push(...writeLenData(quest.questLevel));
    result.push(...writeLenData(quest.unknown1));
    result.push(...writeLenData(quest.unknown2));
    result.push(...writeLenData(quest.unknown3));
    result.push(...writeLenData(quest.unknown4));
    result.push(...writeLenData(quest.unknown5));
    result.push(...writeLenData(quest.unknown6));
    result.push(...writeLenData(quest.unknown7));
    result.push(...writeLenData(quest.unknown8));
    result.push(...writeLenData(quest.unknown9));
    result.push(...writeLenData(quest.unknown10));
    result.push(...writeLenData(quest.unknown11));

    // Handle special quests - FIXED: Use Unknown10 = 16 instead of hardcoded IDs
    const isSpecialQuest = quest.unknown10.value === 16;

    if (isSpecialQuest) {
      if (quest.unknown12) result.push(...writeLenData(quest.unknown12));
      if (quest.unknown13) result.push(...writeLenData(quest.unknown13));
      if (quest.unknown14) result.push(...writeLenData(quest.unknown14));
      if (quest.unknown15) result.push(...writeLenData(quest.unknown15));
      if (quest.unknown16) result.push(...writeLenData(quest.unknown16));
      if (quest.unknown17) result.push(...writeLenData(quest.unknown17));
      if (quest.unknown18) result.push(...writeLenData(quest.unknown18));
      if (quest.unknown19) result.push(...writeLenData(quest.unknown19));

      result.push(...writeStringData(quest.questAccept0));
      result.push(...writeStringData(quest.questAccept2));
      result.push(...writeStringData(quest.questRefuse1));
      result.push(...writeStringData(quest.questAccept1));
      result.push(...writeStringData(quest.questRefuse2));
    } else {
      result.push(...writeStringData(quest.questAccept0));
      result.push(...writeStringData(quest.questAccept1));
      result.push(...writeStringData(quest.questRefuse1));
      result.push(...writeStringData(quest.questAccept2));
      result.push(...writeStringData(quest.questRefuse2));
    }

    // Write reward items
    result.push(...writeLenData(newLenData(quest.rewardItems.length)));
    for (const item of quest.rewardItems) {
      result.push(...writeLenData(item.itemID));
      result.push(...writeLenData(item.itemAmount));
    }

    // Write quest stages
    result.push(...writeLenData(quest.questStageNumber));

    if (quest.questStageNumber.value > 0) {
      // Write NPC info
      result.push(...writeLenData(quest.npcID));
      result.push(...writeLenData(quest.npcUnknown1));
      result.push(...writeLenData(quest.npcCoords.mapID));
      result.push(...writeLenData(quest.npcCoords.coordsX));
      result.push(...writeLenData(quest.npcCoords.coordsY));
      result.push(...writeLenData(quest.npcCoords.coordsZ));

      // Write required items
      result.push(...writeLenData(newLenData(quest.requiredItems.length)));
      for (const item of quest.requiredItems) {
        result.push(...writeLenData(item.itemID));
        result.push(...writeLenData(item.itemAmount));
        result.push(...writeLenData(item.mapID));
        result.push(...writeLenData(item.coordsX));
        result.push(...writeLenData(item.coordsY));
        result.push(...writeLenData(item.coordsZ));
      }

      // Write welcome prompts
      result.push(...writeStringData(quest.welcomeAcceptPrompt1));
      result.push(...writeStringData(quest.welcomeAcceptPrompt2));
      result.push(...writeStringData(quest.welcomeAcceptPrompt3));
      result.push(...writeStringData(quest.welcomeAcceptPrompt4));
      result.push(...writeStringData(quest.welcomeAcceptPrompt5));
      result.push(...writeStringData(quest.welcomeRefusePrompt1));
      result.push(...writeStringData(quest.welcomeRefusePrompt2));
      result.push(...writeStringData(quest.welcomeRefusePrompt3));
      result.push(...writeStringData(quest.welcomeRefusePrompt4));
      result.push(...writeStringData(quest.welcomeRefusePrompt5));

      // Write quest stages
      const stageCount = quest.questStageNumber.value - 1;
      if (stageCount > 0) {
        for (const stage of quest.questStages) {
          result.push(...writeStringData(stage.content));
          result.push(...writeLenData(stage.npcID));
          result.push(...writeLenData(stage.npcUnknown1));
          result.push(...writeLenData(stage.npcMapID));
          result.push(...writeLenData(stage.npcCoordsX));
          result.push(...writeLenData(stage.npcCoordsY));
          result.push(...writeLenData(stage.npcCoordsZ));

          // Write stage required items
          result.push(...writeLenData(newLenData(stage.requiredItems.length)));
          for (const item of stage.requiredItems) {
            result.push(...writeLenData(item.itemID));
            result.push(...writeLenData(item.itemAmount));
            result.push(...writeLenData(item.mapID));
            result.push(...writeLenData(item.coordsX));
            result.push(...writeLenData(item.coordsY));
            result.push(...writeLenData(item.coordsZ));
          }

          // Write condition prompts
          result.push(...writeStringData(stage.conditionMatchPrompt1));
          result.push(...writeStringData(stage.conditionMatchPrompt2));
          result.push(...writeStringData(stage.conditionMatchPrompt3));
          result.push(...writeStringData(stage.conditionMatchPrompt4));
          result.push(...writeStringData(stage.conditionMatchPrompt5));
          result.push(...writeStringData(stage.conditionNoMatchPrompt1));
          result.push(...writeStringData(stage.conditionNoMatchPrompt2));
          result.push(...writeStringData(stage.conditionNoMatchPrompt3));
          result.push(...writeStringData(stage.conditionNoMatchPrompt4));
          result.push(...writeStringData(stage.conditionNoMatchPrompt5));
        }
      }

      // Write footer extend
      result.push(...writeStringData(quest.footerExtend));

      // Add newline
      result.push(10);
    }
  }

  // Add terminating zero
  result.push(...writeLenData(newLenData(0)));

  return result;
}

/**
 * Export YBQ data to different formats
 */
export function exportYbqData(ybqData: YbqData, format: 'ybq' | 'json' | 'decrypt'): Uint8Array {
  switch (format) {
    case 'json':
      const jsonStr = JSON.stringify(ybqData, null, 2);
      return new TextEncoder().encode(jsonStr);

    case 'decrypt':
      return new Uint8Array(ybqData.decrypted);

    case 'ybq':
    default:
      // Create full YBQ file
      const result: number[] = [];

      // Write sign
      result.push(...Windows1252Codec.encode(ybqData.sign));
      result.push(32); // space

      // Write signEx
      result.push(...Windows1252Codec.encode(ybqData.signEx));
      result.push(13, 10); // \r\n

      // Count valid quests
      const validQuestCount = Object.values(ybqData.quests)
        .filter(quest => quest.questID.value > 0 && quest.questName.value !== '').length;

      // Write quest count
      result.push(...Windows1252Codec.encode(validQuestCount.toString()));
      result.push(32); // space

      // Write encrypted quest data
      const serializedQuestData = writeQuestData(ybqData.quests);
      const encrypted = encrypt(serializedQuestData);
      result.push(...encrypted);

      return new Uint8Array(result);
  }
}

/**
 * Create new quest with default values
 */
export function createNewQuest(questID: number): Quest {
  return {
    questID: newLenData(questID),
    questName: newStringData(''),
    questLevel: newLenData(0),
    unknown1: newLenData(0),
    unknown2: newLenData(0),
    unknown3: newLenData(0),
    unknown4: newLenData(0),
    unknown5: newLenData(0),
    unknown6: newLenData(0),
    unknown7: newLenData(0),
    unknown8: newLenData(0),
    unknown9: newLenData(0),
    unknown10: newLenData(0),
    unknown11: newLenData(0),
    questAccept0: newStringData(''),
    questAccept1: newStringData(''),
    questAccept2: newStringData(''),
    questRefuse1: newStringData(''),
    questRefuse2: newStringData(''),
    welcomeAcceptPrompt1: newStringData(''),
    welcomeAcceptPrompt2: newStringData(''),
    welcomeAcceptPrompt3: newStringData(''),
    welcomeAcceptPrompt4: newStringData(''),
    welcomeAcceptPrompt5: newStringData(''),
    welcomeRefusePrompt1: newStringData(''),
    welcomeRefusePrompt2: newStringData(''),
    welcomeRefusePrompt3: newStringData(''),
    welcomeRefusePrompt4: newStringData(''),
    welcomeRefusePrompt5: newStringData(''),
    questStageNumber: newLenData(0),
    npcID: newLenData(0),
    npcUnknown1: newLenData(0),
    npcCoords: {
      mapID: newLenData(0),
      coordsX: newLenData(0),
      coordsY: newLenData(0),
      coordsZ: newLenData(0)
    },
    questStages: [],
    requiredItems: [],
    rewardItems: [],
    footerExtend: newStringData('')
  };
}

/**
 * Find next available quest ID
 */
export function findNextAvailableQuestID(quests: Record<number, Quest>): number {
  const maxID = Math.max(0, ...Object.keys(quests).map(id => parseInt(id)));
  return maxID + 1;
}

/**
 * Clone quest with new ID
 */
export function cloneQuest(sourceQuest: Quest, newQuestID: number): Quest {
  const cloned = JSON.parse(JSON.stringify(sourceQuest)) as Quest;
  cloned.questID = newLenData(newQuestID);
  cloned.questName = newStringData(sourceQuest.questName.value + ' (Copy)');
  return cloned;
}
