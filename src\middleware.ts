import { NextRequest, NextResponse } from "next/server";
import { betterFetch } from "@better-fetch/fetch";
import type { auth } from "@/lib/auth";
type Session = typeof auth.$Infer.Session;
// Protected routes that require authentication
const protectedRoutes = [
  "/dashboard",
  "/admin",
  "/api",
];

// Public routes that don't require authentication
const publicRoutes = [
  "/",
  "/login",
  "/api/auth",
];

// Check if route is protected
function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => pathname.startsWith(route));
}

// Check if route is public
function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => pathname.startsWith(route));
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes (except protected ones)
  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.startsWith("/public/") ||
    (pathname.startsWith("/api/") && !pathname.startsWith("/api/admin"))
  ) {
    return NextResponse.next();
  }

  try {
    // Get the correct base URL for API calls
    const baseURL = process.env.BETTER_AUTH_URL || request.nextUrl.origin;

    // Get session from better-auth with proper error handling
    const { data: session } = await betterFetch<Session>("/api/auth/get-session", {
      baseURL,
      headers: {
        cookie: request.headers.get("cookie") || "", // Forward the cookies from the request
      },
      // Add timeout for production
      timeout: 10000, // 10 seconds timeout
    });

    const isAuthenticated = !!session?.user;
    const isProtected = isProtectedRoute(pathname);
    const isPublic = isPublicRoute(pathname);

    if (isPublic) {
      return NextResponse.next();
    }

    // If user is not authenticated and trying to access protected route
    if (!isAuthenticated && isProtected) {
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("callbackUrl", pathname);
      return NextResponse.redirect(loginUrl);
    }

    // If user is authenticated and trying to access login page, redirect to dashboard
    if (isAuthenticated && pathname === "/login") {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }

    // If user is authenticated and accessing root, redirect to dashboard
    if (isAuthenticated && pathname === "/") {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }

    // Check if user account is active (if isActive field exists)
    if (isAuthenticated && session?.user && 'isActive' in session.user && !session.user.isActive) {
      // Log out inactive user
      const response = NextResponse.redirect(new URL("/login?error=account_inactive", request.url));

      // Clear session cookies
      response.cookies.delete("better-auth.session_token");
      response.cookies.delete("better-auth.csrf_token");

      return response;
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    
    // If there's an error and user is trying to access protected route, redirect to login
    if (isProtectedRoute(pathname)) {
      return NextResponse.redirect(new URL("/login?error=auth_error", request.url));
    }
    
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
