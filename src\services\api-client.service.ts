import {
  GetServerListRequest,
  GetServerListResponse,
  StartGameServerRequest,
  StartGameServerResponse,
  StopGameServerRequest,
  StopGameServerResponse,
  RestartGameServerRequest,
  RestartGameServerResponse,
  SaveCharactersRequest,
  SaveCharactersResponse,
  DisableNewConnectionsRequest,
  DisableNewConnectionsResponse,
  ReloadConfigRequest,
  ReloadConfigResponse,
  GetServerEventsRequest,
  GetServerEventsResponse,
  ManageEventRequest,
  ManageEventResponse,
  GetAllAccountsRequest,
  GetAllPlayersResponse,
  GetOnlinePlayersRequest,
  GetOnlinePlayersResponse,
  KickPlayerRequest,
  KickPlayerResponse,
  BanPlayerRequest,
  BanPlayerResponse,
  SendMessageRequest,
  SendMessageResponse
} from '@/types/gameserver';

/**
 * API Client Service - Thay thế gameServerService
 * Sử dụng fetch API để giao tiếp với web-api server thay vì gọi trực tiếp service
 */

class ApiClientService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
  }

  /**
   * Thực hiện HTTP request với error handling
   */
  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // Để gửi cookies authentication
    };

    const requestOptions = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, requestOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API Request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // ===== SERVER MANAGEMENT METHODS =====

  /**
   * Lấy danh sách game servers
   */
  async getServerList(request: GetServerListRequest = {}): Promise<GetServerListResponse> {
    const queryParams = new URLSearchParams();
    
    if (request.clusterId !== undefined) {
      queryParams.append('clusterId', request.clusterId.toString());
    }
    if (request.includeOffline !== undefined) {
      queryParams.append('includeOffline', request.includeOffline.toString());
    }

    const endpoint = `/api/gameserver/servers${
      queryParams.toString() ? `?${queryParams}` : ''
    }`;

    return this.makeRequest<GetServerListResponse>(endpoint, {
      method: 'GET',
    });
  }

  /**
   * Khởi động game server
   */
  async startGameServer(request: StartGameServerRequest): Promise<StartGameServerResponse> {
    const endpoint = `/api/gameserver/servers/${request.serverId}/start`;
    
    return this.makeRequest<StartGameServerResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        clusterId: request.clusterId,
        configOverrides: request.configOverrides,
      }),
    });
  }

  /**
   * Dừng game server
   */
  async stopGameServer(request: StopGameServerRequest): Promise<StopGameServerResponse> {
    const endpoint = `/api/gameserver/servers/${request.serverId}/stop`;
    
    return this.makeRequest<StopGameServerResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        clusterId: request.clusterId,
        graceful: request.graceful,
        timeoutSeconds: request.timeoutSeconds,
      }),
    });
  }

  /**
   * Khởi động lại game server
   */
  async restartGameServer(request: RestartGameServerRequest): Promise<RestartGameServerResponse> {
    const endpoint = `/api/gameserver/servers/${request.serverId}/restart`;

    return this.makeRequest<RestartGameServerResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        clusterId: request.clusterId,
        graceful: request.graceful,
      }),
    });
  }

  /**
   * Lưu tất cả characters trên server
   */
  async saveCharacters(request: SaveCharactersRequest): Promise<SaveCharactersResponse> {
    const endpoint = `/api/gameserver/servers/${request.serverId}/save-characters`;

    return this.makeRequest<SaveCharactersResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        clusterId: request.clusterId,
        forceAll: request.forceAll,
      }),
    });
  }

  /**
   * Tắt/bật kết nối mới đến server
   */
  async disableNewConnections(request: DisableNewConnectionsRequest): Promise<DisableNewConnectionsResponse> {
    const endpoint = `/api/gameserver/servers/${request.serverId}/disable-connections`;

    return this.makeRequest<DisableNewConnectionsResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        clusterId: request.clusterId,
        disable: request.disable,
        reason: request.reason,
      }),
    });
  }

  /**
   * Reload config của server
   */
  async reloadConfig(request: ReloadConfigRequest): Promise<ReloadConfigResponse> {
    const endpoint = `/api/gameserver/servers/${request.serverId}/reload-config`;

    return this.makeRequest<ReloadConfigResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        clusterId: request.clusterId,
        configType: request.configType,
      }),
    });
  }

  /**
   * Lấy danh sách events đang diễn ra trên server
   */
  async getServerEvents(request: GetServerEventsRequest): Promise<GetServerEventsResponse> {
    const queryParams = new URLSearchParams();

    queryParams.append('clusterId', request.clusterId.toString());
    if (request.activeOnly !== undefined) {
      queryParams.append('activeOnly', request.activeOnly.toString());
    }

    const endpoint = `/api/gameserver/servers/${request.serverId}/events?${queryParams}`;

    return this.makeRequest<GetServerEventsResponse>(endpoint, {
      method: 'GET',
    });
  }

  /**
   * Quản lý events trên server
   */
  async manageEvent(request: ManageEventRequest): Promise<ManageEventResponse> {
    const endpoint = `/api/gameserver/servers/${request.serverId}/events/${request.eventId}`;

    return this.makeRequest<ManageEventResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        clusterId: request.clusterId,
        action: request.action,
      }),
    });
  }

  // ===== ACCOUNT MANAGEMENT METHODS =====

  /**
   * Lấy danh sách tất cả accounts/players
   */
  async getAllAccounts(request: GetAllAccountsRequest = {}): Promise<GetAllPlayersResponse> {
    const queryParams = new URLSearchParams();
    
    if (request.page !== undefined) {
      queryParams.append('page', request.page.toString());
    }
    if (request.pageSize !== undefined) {
      queryParams.append('pageSize', request.pageSize.toString());
    }
    if (request.searchTerm) {
      queryParams.append('searchTerm', request.searchTerm);
    }
    if (request.serverId !== undefined) {
      queryParams.append('serverId', request.serverId.toString());
    }
    if (request.clusterId !== undefined) {
      queryParams.append('clusterId', request.clusterId.toString());
    }
    if (request.onlineOnly !== undefined) {
      queryParams.append('onlineOnly', request.onlineOnly.toString());
    }
    if (request.sortBy) {
      queryParams.append('sortBy', request.sortBy);
    }
    if (request.sortOrder) {
      queryParams.append('sortOrder', request.sortOrder);
    }

    const endpoint = `/api/webadmin/account/accounts${
      queryParams.toString() ? `?${queryParams}` : ''
    }`;

    return this.makeRequest<GetAllPlayersResponse>(endpoint, {
      method: 'GET',
    });
  }

  /**
   * Lấy thông tin chi tiết của một account
   */
  async getAccountInfo(accountId: string) {
    const endpoint = `/api/accounts/${encodeURIComponent(accountId)}`;

    return this.makeRequest(endpoint, {
      method: 'GET',
    });
  }

  // ===== PLAYER MANAGEMENT METHODS =====

  /**
   * Lấy danh sách players đang online
   */
  async getOnlinePlayers(request: GetOnlinePlayersRequest = {}): Promise<GetOnlinePlayersResponse> {
    const queryParams = new URLSearchParams();

    if (request.page !== undefined) {
      queryParams.append('page', request.page.toString());
    }
    if (request.pageSize !== undefined) {
      queryParams.append('pageSize', request.pageSize.toString());
    }
    if (request.searchTerm) {
      queryParams.append('searchTerm', request.searchTerm);
    }
    if (request.serverId !== undefined) {
      queryParams.append('serverId', request.serverId.toString());
    }
    if (request.clusterId !== undefined) {
      queryParams.append('clusterId', request.clusterId.toString());
    }

    const endpoint = `/api/gameserver/players/online${
      queryParams.toString() ? `?${queryParams}` : ''
    }`;

    return this.makeRequest<GetOnlinePlayersResponse>(endpoint, {
      method: 'GET',
    });
  }

  /**
   * Kick một player khỏi game
   */
  async kickPlayer(request: KickPlayerRequest): Promise<KickPlayerResponse> {
    const endpoint = `/api/gameserver/players/${request.playerId}/kick`;

    return this.makeRequest<KickPlayerResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        serverId: request.serverId,
        reason: request.reason,
      }),
    });
  }

  /**
   * Ban một player
   */
  async banPlayer(request: BanPlayerRequest): Promise<BanPlayerResponse> {
    const endpoint = `/api/gameserver/players/${request.playerId}/ban`;

    return this.makeRequest<BanPlayerResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        serverId: request.serverId,
        reason: request.reason,
        duration: request.duration,
        banType: request.banType,
      }),
    });
  }

  /**
   * Gửi message đến player
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    const endpoint = `/api/gameserver/players/${request.playerId}/message`;

    return this.makeRequest<SendMessageResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        serverId: request.serverId,
        message: request.message,
        messageType: request.messageType,
      }),
    });
  }
}

// Export singleton instance
export const apiClientService = new ApiClientService();
