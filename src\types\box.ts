// Box Management Types

export interface Box {
  fldPid: number;
  fldName?: string;
  rewardCount: number;
  totalPp: number;
}

export interface BoxReward {
  id?: number;           // Primary key (serial)
  fldPid: number;        // Box ID
  fldPidx: number;       // Reward item ID
  fldNumber: number;     // Quantity
  fldName?: string;      // Box name
  fldNamex?: string;     // Reward item name
  fldPp?: number;        // PP rate/probability
  fldMagic1?: number;    // Magic stat 1
  fldMagic2?: number;    // Magic stat 2
  fldMagic3?: number;    // Magic stat 3
  fldMagic4?: number;    // Magic stat 4
  fldMagic5?: number;    // Magic stat 5
  fldFjThuctinh?: number;      // Awakening stat
  fldFjTienhoa?: number;       // Evolution stat
  fldFjTrungcapphuhon?: number; // Mid-level soul stat
  fldBd?: number;        // Bind stat
  fldDays?: number;      // Days duration
  comothongbao?: number; // Notification flag
  sttHopEvent?: number;  // Event status
}

export interface BoxFilter {
  search?: string;
  fldPid?: number;
  sortBy?: keyof Box;
  sortOrder?: 'asc' | 'desc';
}

export interface BoxRewardFilter {
  search?: string;
  sortBy?: keyof BoxReward;
  sortOrder?: 'asc' | 'desc';
}

export interface BoxPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface BoxListResponse {
  success: boolean;
  message: string;
  data: {
    boxes: Box[];
    pagination: BoxPagination;
  };
}

export interface BoxResponse {
  success: boolean;
  message: string;
  data?: Box;
}

export interface BoxRewardListResponse {
  success: boolean;
  message: string;
  data: {
    rewards: BoxReward[];
    pagination: BoxPagination;
  };
}

export interface BoxRewardResponse {
  success: boolean;
  message: string;
  data?: BoxReward;
}

export interface CreateBoxRequest {
  fldPid: number;
  fldName?: string;
}

export interface UpdateBoxRequest {
  fldName?: string;
}

export interface CreateBoxRewardRequest {
  fldPidx: number;
  fldNumber?: number;
  fldNamex?: string;
  fldPp?: number;
  fldMagic1?: number;
  fldMagic2?: number;
  fldMagic3?: number;
  fldMagic4?: number;
  fldMagic5?: number;
  fldFjThuctinh?: number;
  fldFjTienhoa?: number;
  fldFjTrungcapphuhon?: number;
  fldBd?: number;
  fldDays?: number;
  comothongbao?: number;
  sttHopEvent?: number;
}

export interface UpdateBoxRewardRequest {
  fldPidx?: number;
  fldNumber?: number;
  fldNamex?: string;
  fldPp?: number;
  fldMagic1?: number;
  fldMagic2?: number;
  fldMagic3?: number;
  fldMagic4?: number;
  fldMagic5?: number;
  fldFjThuctinh?: number;
  fldFjTienhoa?: number;
  fldFjTrungcapphuhon?: number;
  fldBd?: number;
  fldDays?: number;
  comothongbao?: number;
  sttHopEvent?: number;
}

// Box field labels for UI display
export const BOX_FIELD_LABELS: { [key in keyof Box]?: string } = {
  fldPid: 'Box ID',
  fldName: 'Tên Box',
  rewardCount: 'Số Rewards',
  totalPp: 'Tổng PP'
};

// Box reward field labels for UI display
export const BOX_REWARD_FIELD_LABELS: { [key in keyof BoxReward]?: string } = {
  fldPid: 'Box ID',
  fldPidx: 'Item ID',
  fldNumber: 'Số lượng',
  fldName: 'Tên Box',
  fldNamex: 'Tên Item',
  fldPp: 'PP Rate',
  fldMagic1: 'Magic 1',
  fldMagic2: 'Magic 2',
  fldMagic3: 'Magic 3',
  fldMagic4: 'Magic 4',
  fldMagic5: 'Magic 5',
  fldFjThuctinh: 'Thức Tỉnh',
  fldFjTienhoa: 'Tiến Hóa',
  fldFjTrungcapphuhon: 'Trung Cấp Phù Hồn',
  fldBd: 'Bind',
  fldDays: 'Số ngày',
  comothongbao: 'Thông báo',
  sttHopEvent: 'Sự kiện'
};

// Box reward field categories for organized display
export const BOX_REWARD_FIELD_CATEGORIES = {
  basic: {
    label: 'Thông tin cơ bản',
    fields: ['fldPidx', 'fldNamex', 'fldNumber', 'fldPp'] as (keyof BoxReward)[]
  },
  magic: {
    label: 'Magic Stats',
    fields: ['fldMagic1', 'fldMagic2', 'fldMagic3', 'fldMagic4', 'fldMagic5'] as (keyof BoxReward)[]
  },
  enhancement: {
    label: 'Cường hóa',
    fields: ['fldFjThuctinh', 'fldFjTienhoa', 'fldFjTrungcapphuhon'] as (keyof BoxReward)[]
  },
  config: {
    label: 'Cấu hình',
    fields: ['fldBd', 'fldDays', 'comothongbao', 'sttHopEvent'] as (keyof BoxReward)[]
  }
};

// Sortable fields for UI
export const SORTABLE_BOX_FIELDS: (keyof Box)[] = [
  'fldPid',
  'fldName',
  'rewardCount',
  'totalPp'
];

export const SORTABLE_BOX_REWARD_FIELDS: (keyof BoxReward)[] = [
  'fldPidx',
  'fldNamex',
  'fldNumber',
  'fldPp',
  'fldMagic1'
];

// Default values for creation
export const DEFAULT_BOX_VALUES: Partial<CreateBoxRequest> = {
  fldName: ''
};

export const DEFAULT_BOX_REWARD_VALUES: Partial<CreateBoxRewardRequest> = {
  fldNumber: 1,
  fldPp: 0,
  fldMagic1: 0,
  fldMagic2: 0,
  fldMagic3: 0,
  fldMagic4: 0,
  fldMagic5: 0,
  fldFjThuctinh: 0,
  fldFjTienhoa: 0,
  fldFjTrungcapphuhon: 0,
  fldBd: 0,
  fldDays: 0,
  comothongbao: 0,
  sttHopEvent: 0
};

// Helper functions
export const formatPpRate = (pp: number): string => {
  return pp?.toString() || '0';
};

export const calculateTotalPpPercentage = (totalPp: number): string => {
  return totalPp?.toString() || '0';
};

export const getBoxImageUrl = (fldPid: number): string => {
  return `http://one.chamthoi.com/item/${fldPid}.jpg`;
};

export const getRewardImageUrl = (fldPidx: number): string => {
  return `http://one.chamthoi.com/item/${fldPidx}.jpg`;
};
