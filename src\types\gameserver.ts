// GameServer Management Types

export interface GameServer {
  id: number;
  clusterId: number;
  serverId: number;
  serverName: string;
  serverIP: string;
  gameServerPort: number;
  gameServerGrpcPort: number;
  status: boolean;
  currentPlayers: number;
  maximumOnline: number;
  lastHeartbeat?: Date;
  version?: string;
  uptime?: number;
}

export interface ServerMetrics {
  serverId: number;
  cpuUsage: number;
  memoryUsage: number;
  networkIn: number;
  networkOut: number;
  activeConnections: number;
  timestamp: Date;
}

export interface LogEntry {
  timestamp: Date;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL';
  message: string;
  source: string;
  serverId?: number;
}

export interface OnlinePlayer {
  playerId: number;
  characterName: string;
  accountName: string;
  level: number;
  mapId: number;
  mapName: string;
  loginTime: Date;
  ipAddress: string;
  serverId: number;
}

export interface ServerConfig {
  serverId: number;
  configKey: string;
  configValue: string;
  description?: string;
  category: string;
  isReadOnly: boolean;
  lastModified: Date;
  modifiedBy: string;
}

// API Request/Response Types
export interface GetServerListRequest {
  clusterId?: number;
  includeOffline?: boolean;
}

export interface GetServerListResponse {
  success: boolean;
  message: string;
  servers: GameServer[];
}

export interface GetServerStatusRequest {
  serverId: number;
  clusterId: number;
}

export interface GetServerStatusResponse {
  success: boolean;
  message: string;
  server: GameServer;
  metrics?: ServerMetrics;
}

export interface StartGameServerRequest {
  serverId: number;
  clusterId: number;
  configOverrides?: Record<string, string>;
}

export interface StartGameServerResponse {
  success: boolean;
  message: string;
  processId?: number;
  savedCount ?: number;
  failedCount ?: number;
}

export interface StopGameServerRequest {
  serverId: number;
  clusterId: number;
  graceful?: boolean;
  timeoutSeconds?: number;
}

export interface StopGameServerResponse {
  success: boolean;
  message: string;
  savedCount ?: number;
  failedCount ?: number;
}

export interface RestartGameServerRequest {
  serverId: number;
  clusterId: number;
  graceful?: boolean;
  configOverrides?: Record<string, string>;
}

export interface RestartGameServerResponse {
  success: boolean;
  message: string;
  processId?: number;
  savedCount ?: number;
  failedCount ?: number;
}

export interface SaveCharactersRequest {
  serverId: number;
  clusterId: number;
  forceAll?: boolean;
}

export interface SaveCharactersResponse {
  success: boolean;
  message: string;
  savedCount: number;
  failedCount: number;
}

export interface DisableNewConnectionsRequest {
  serverId: number;
  clusterId: number;
  disable: boolean;
  reason?: string;
  savedCount ?: number;
  failedCount ?: number;
}

export interface DisableNewConnectionsResponse {
  success: boolean;
  message: string;
  currentStatus: boolean;
   savedCount ?: number;
  failedCount ?: number;
}

export interface ReloadConfigRequest {
  serverId: number;
  clusterId: number;
  configType?: 'all' | 'config' | 'template';
}

export interface ReloadConfigResponse {
  success: boolean;
  message: string;
  reloadedConfigs: string[];
}

export interface ServerEventInfo {
  eventId: string;
  eventName: string;
  eventType: string;
  isActive: boolean;
  startTime: Date;
  endTime?: Date;
  participants: number;
  description?: string;
}

export interface GetServerEventsRequest {
  serverId: number;
  clusterId: number;
  activeOnly?: boolean;
}

export interface GetServerEventsResponse {
  success: boolean;
  message: string;
  events: ServerEventInfo[];
}

export interface ManageEventRequest {
  serverId: number;
  clusterId: number;
  eventId: string;
  action: 'start' | 'stop' | 'pause' | 'resume';
}

export interface ManageEventResponse {
  success: boolean;
  message: string;
  eventStatus: string;
}

export interface GetOnlinePlayersRequest {
  serverId?: number;
  clusterId?: number;
  page?: number;
  pageSize?: number;
  searchTerm?: string;
}

export interface GetOnlinePlayersResponse {
  success: boolean;
  message: string;
  players: OnlinePlayer[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

export interface KickPlayerRequest {
  playerId: number;
  serverId: number;
  reason?: string;
}

export interface KickPlayerResponse {
  success: boolean;
  message: string;
}

export interface BanPlayerRequest {
  playerId: number;
  serverId: number;
  reason: string;
  duration?: number; // minutes, 0 = permanent
  banType: 'ACCOUNT' | 'IP' | 'BOTH';
}

export interface BanPlayerResponse {
  success: boolean;
  message: string;
}

export interface SendMessageRequest {
  serverId?: number; // undefined = broadcast to all servers
  playerId?: number; // undefined = broadcast to all players
  targetType?: 'PLAYER' | 'ACCOUNT' | 'IP'; // default = PLAYER
  targetId?: number | string; // playerId or accountId or IP
  message: string;
  messageType: 'SYSTEM' | 'NOTICE' | 'WARNING' | 'ERROR';
}

export interface SendMessageResponse {
  success: boolean;
  message: string;
  recipientCount?: number;
}

export interface GetServerConfigRequest {
  serverId: number;
  category?: string;
  configKey?: string;
}

export interface GetServerConfigResponse {
  success: boolean;
  message: string;
  configs: ServerConfig[];
}

export interface UpdateServerConfigRequest {
  serverId: number;
  configs: Array<{
    configKey: string;
    configValue: string;
  }>;
}

export interface UpdateServerConfigResponse {
  success: boolean;
  message: string;
  updatedConfigs: ServerConfig[];
}

export interface ReloadServerConfigRequest {
  serverId: number;
  restartIfNeeded?: boolean;
  category?: string;
  configCategory?: string;
}

export interface ReloadServerConfigResponse {
  success: boolean;
  message: string;
}

export interface StreamServerLogsRequest {
  serverId: number;
  level?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL';
  source?: string;
  since?: Date;
}

export interface StreamServerMetricsRequest {
  serverId: number;
  interval?: number; // seconds
}

// Session Validation Types
export interface SessionValidationRequest {
  sessionToken: string;
  requiredPermission?: string;
  clientIP?: string;
}

export interface SessionValidationResponse {
  success: boolean;
  message: string;
  user?: {
    id: number;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    roles: string[];
    permissions: string[];
    isActive: boolean;
    lastLoginAt?: Date;
  };
  sessionInfo?: {
    sessionId: string;
    expiresAt: Date;
    lastActivity: Date;
  };
}

// Error Types
export interface GameServerError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
}

export interface ApiError {
  success: false;
  message: string;
  error?: GameServerError;
  timestamp: Date;
}

// Utility Types
export type ServerStatus = 'ONLINE' | 'OFFLINE' | 'STARTING' | 'STOPPING' | 'ERROR';
export type PlayerAction = 'KICK' | 'BAN' | 'MESSAGE' | 'TELEPORT';
export type ConfigCategory = 'GAME' | 'NETWORK' | 'DATABASE' | 'SECURITY' | 'LOGGING' | 'PERFORMANCE';
export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL';
export type MessageType = 'SYSTEM' | 'NOTICE' | 'WARNING' | 'ERROR';
export type BanType = 'ACCOUNT' | 'IP' | 'BOTH';

// Permission Constants
export const GAMESERVER_PERMISSIONS = {
  // Server management
  SERVER_READ: 'server:read',
  SERVER_START: 'server:start',
  SERVER_STOP: 'server:stop',
  SERVER_RESTART: 'server:restart',
  SERVER_CONFIG: 'server:config',
  SERVER_ALL: 'server:*',
  
  // Player management
  PLAYER_READ: 'players:read',
  PLAYER_KICK: 'players:kick',
  PLAYER_BAN: 'players:ban',
  PLAYER_MESSAGE: 'players:message',
  PLAYER_ALL: 'players:*',
  
  // Monitoring
  LOGS_READ: 'logs:read',
  METRICS_READ: 'metrics:read',
  MONITORING_ALL: 'monitoring:*',
  
  // Admin
  ADMIN_ALL: '*'
} as const;

export type GameServerPermission = typeof GAMESERVER_PERMISSIONS[keyof typeof GAMESERVER_PERMISSIONS];

// Account Management Types
export interface DetailedPlayer {
  accountId: string;
  characterName: string;
  characterIndex: number;
  level: number;
  job: number;
  jobName: string;
  jobLevel: number;
  posX: number;
  posY: number;
  posZ: number;
  mapId: number;
  mapName: string;
  money: number;
  experience: string;
  hp: number;
  mp: number;
  sp: number;
  maxHp: number;
  maxMp: number;
  maxSp: number;
  isOnline: boolean;
  isDead: boolean;
  offLevel: number;
  sessionId: number;
  ipAddress: string;
  loginTime: Date;
  lastActivity: Date;
  serverId: number;
  clusterId: number;
  // Party information
  partyInfo?: PartyInfo;
  // Additional stats
  wuXun: number; // 武勋
  qigongPoint: number; // 气功点
  fightExp: number; // 战斗经验
  gmMode: number; // GM模式
}

export interface PartyInfo {
  teamId: number;
  teamName?: string;
  leaderName: string;
  memberCount: number;
  maxMembers: number;
  isLeader: boolean;
  members: PartyMember[];
}

export interface PartyMember {
  sessionId: number;
  characterName: string;
  level: number;
  job: number;
  isOnline: boolean;
  isLeader: boolean;
}

export interface AccountInfo {
  accountId: string;
  email?: string;
  createdAt: Date;
  lastLoginAt?: Date;
  lastLoginIP?: string;
  isOnline: boolean;
  characters: DetailedPlayer[];
  totalCharacters: number;
  // Account stats
  rxPoint?: number;
  rxPointX?: number;
  coin?: number;
  vip?: number;
  vipTime?: Date;
}

// API Request/Response Types for Account Management
export interface GetAllAccountsRequest {
  page?: number;
  pageSize?: number;
  searchTerm?: string;
  serverId?: number;
  clusterId?: number;
  onlineOnly?: boolean;
  sortBy?: 'accountId' | 'level' | 'lastLogin' | 'characterName';
  sortOrder?: 'asc' | 'desc';
}

export interface GetAllPlayersResponse {
  success: boolean;
  message: string;
  players: DetailedPlayer[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  onlineCount: number;
  totalCharacters: number;
}

export interface GetDetailedPlayersRequest {
  page?: number;
  pageSize?: number;
  searchTerm?: string;
  serverId?: number;
  clusterId?: number;
  onlineOnly?: boolean;
  sortBy?: 'characterName' | 'level' | 'job' | 'mapId' | 'lastActivity';
  sortOrder?: 'asc' | 'desc';
  includePartyInfo?: boolean;
}

export interface GetDetailedPlayersResponse {
  success: boolean;
  message: string;
  players: DetailedPlayer[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  onlineCount: number;
  partyCount: number;
}



// Action button types for account management
export interface AccountAction {
  id: string;
  label: string;
  icon: string; // Icon name or path
  permission: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
}

export const ACCOUNT_ACTIONS: AccountAction[] = [
  {
    id: 'view',
    label: 'View Details',
    icon: 'Eye',
    permission: 'character:read',
    variant: 'outline',
    size: 'icon'
  },
  {
    id: 'edit',
    label: 'Edit Account',
    icon: 'Edit',
    permission: 'accounts:update',
    variant: 'outline',
    size: 'icon'
  },
  {
    id: 'kick',
    label: 'Kick Player',
    icon: 'UserX',
    permission: 'players:kick',
    variant: 'outline',
    size: 'icon'
  },
  {
    id: 'ban',
    label: 'Ban Account',
    icon: 'Ban',
    permission: 'accounts:ban',
    variant: 'destructive',
    size: 'icon'
  },
  {
    id: 'message',
    label: 'Send Message',
    icon: 'MessageSquare',
    permission: 'players:message',
    variant: 'outline',
    size: 'icon'
  },
  {
    id: 'teleport',
    label: 'Teleport To',
    icon: 'Navigation',
    permission: 'players:teleport',
    variant: 'outline',
    size: 'icon'
  },
  {
    id: 'inventory',
    label: 'View Inventory',
    icon: 'Package',
    permission: 'players:inventory',
    variant: 'outline',
    size: 'icon'
  },
  {
    id: 'logs',
    label: 'View Logs',
    icon: 'FileText',
    permission: 'logs:read',
    variant: 'outline',
    size: 'icon'
  }
];

// Column definitions for data table
export interface AccountTableColumn {
  id: string;
  label: string;
  sortable: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  type?: 'text' | 'number' | 'date' | 'boolean' | 'badge' | 'progress';
}

export const ACCOUNT_TABLE_COLUMNS: AccountTableColumn[] = [
  { id: 'characterName', label: 'Character Name', sortable: true, width: '200px' },
  { id: 'accountId', label: 'Account ID', sortable: true, width: '150px' },
  { id: 'level', label: 'Level', sortable: true, width: '80px', align: 'center', type: 'number' },
  { id: 'jobName', label: 'Job', sortable: true, width: '100px', type: 'badge' },
  { id: 'jobLevel', label: 'Job Lv', sortable: true, width: '80px', align: 'center', type: 'number' },
  { id: 'position', label: 'Position (X,Y)', sortable: false, width: '120px', align: 'center' },
  { id: 'mapName', label: 'Map', sortable: true, width: '150px' },
  { id: 'partyInfo', label: 'Party', sortable: false, width: '120px' },
  { id: 'isOnline', label: 'Status', sortable: true, width: '80px', align: 'center', type: 'badge' },
  { id: 'lastActivity', label: 'Last Activity', sortable: true, width: '150px', type: 'date' },
  { id: 'actions', label: 'Actions', sortable: false, width: '300px', align: 'center' }
];
