// Item Option Management Types

export interface ItemOption {
  id: number;
  fldPid?: number;
  fldName?: string;
  bonusHp?: number;
  bonusPercenthp?: number;
  bonusMp?: number;
  bonusPercentmp?: number;
  bonusAtk?: number;
  bonusPercentatk?: number;
  bonusPercentdf?: number;
  bonusDf?: number;
  bonusPercentatkskill?: number;
  bonusDefskill?: number;
  bonusQigong?: number;
  bonusDropgold?: number;
  bonusExp?: number;
  bonusLucky?: number;
  bonusAccuracy?: number;
  bonusEvasion?: number;
  bonusDiemhoangkim?: number;
  bonusAtkmonster?: number;
  bonusDefmonster?: number;
}

export interface ItemOptionFilter {
  search?: string;
  fldPid?: number;
  sortBy?: keyof ItemOption;
  sortOrder?: 'asc' | 'desc';
}

export interface ItemOptionPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ItemOptionListResponse {
  success: boolean;
  message: string;
  data: {
    itemOptions: ItemOption[];
    pagination: ItemOptionPagination;
  };
}

export interface ItemOptionResponse {
  success: boolean;
  message: string;
  data?: ItemOption;
}

export interface CreateItemOptionRequest {
  fldPid: number;
  fldName?: string;
  bonusHp?: number;
  bonusPercenthp?: number;
  bonusMp?: number;
  bonusPercentmp?: number;
  bonusAtk?: number;
  bonusPercentatk?: number;
  bonusPercentdf?: number;
  bonusDf?: number;
  bonusPercentatkskill?: number;
  bonusDefskill?: number;
  bonusQigong?: number;
  bonusDropgold?: number;
  bonusExp?: number;
  bonusLucky?: number;
  bonusAccuracy?: number;
  bonusEvasion?: number;
  bonusDiemhoangkim?: number;
  bonusAtkmonster?: number;
  bonusDefmonster?: number;
}

export interface UpdateItemOptionRequest extends Partial<CreateItemOptionRequest> {
  id?: number;
}

// Item Option field labels for UI display
export const ITEM_OPTION_FIELD_LABELS: { [key in keyof ItemOption]?: string } = {
  id: 'ID',
  fldPid: 'Item ID',
  fldName: 'Tên Item',
  bonusHp: 'Bonus HP',
  bonusPercenthp: 'Bonus HP %',
  bonusMp: 'Bonus MP',
  bonusPercentmp: 'Bonus MP %',
  bonusAtk: 'Bonus ATK',
  bonusPercentatk: 'Bonus ATK %',
  bonusPercentdf: 'Bonus DEF %',
  bonusDf: 'Bonus DEF',
  bonusPercentatkskill: 'Bonus ATK Skill %',
  bonusDefskill: 'Bonus DEF Skill',
  bonusQigong: 'Bonus Qigong',
  bonusDropgold: 'Bonus Drop Gold',
  bonusExp: 'Bonus EXP',
  bonusLucky: 'Bonus Lucky',
  bonusAccuracy: 'Bonus Accuracy',
  bonusEvasion: 'Bonus Evasion',
  bonusDiemhoangkim: 'Bonus Điểm Hoàng Kim',
  bonusAtkmonster: 'Bonus ATK Monster',
  bonusDefmonster: 'Bonus DEF Monster'
};

// Item Option field categories for organized display
export const ITEM_OPTION_FIELD_CATEGORIES = {
  basic: {
    label: 'Thông tin cơ bản',
    fields: ['fldPid', 'fldName'] as (keyof ItemOption)[]
  },
  hpMp: {
    label: 'HP/MP',
    fields: ['bonusHp', 'bonusPercenthp', 'bonusMp', 'bonusPercentmp'] as (keyof ItemOption)[]
  },
  combat: {
    label: 'Chỉ số chiến đấu',
    fields: ['bonusAtk', 'bonusPercentatk', 'bonusDf', 'bonusPercentdf', 'bonusPercentatkskill', 'bonusDefskill'] as (keyof ItemOption)[]
  },
  advanced: {
    label: 'Chỉ số nâng cao',
    fields: ['bonusAccuracy', 'bonusEvasion', 'bonusLucky', 'bonusQigong', 'bonusExp', 'bonusDropgold'] as (keyof ItemOption)[]
  },
  special: {
    label: 'Chỉ số đặc biệt',
    fields: ['bonusDiemhoangkim', 'bonusAtkmonster', 'bonusDefmonster'] as (keyof ItemOption)[]
  }
};

// Sortable fields for UI
export const SORTABLE_ITEM_OPTION_FIELDS: (keyof ItemOption)[] = [
  'id',
  'fldPid', 
  'fldName',
  'bonusHp',
  'bonusAtk',
  'bonusDf',
  'bonusExp',
  'bonusLucky'
];

// Default item option values for creation
export const DEFAULT_ITEM_OPTION_VALUES: Partial<CreateItemOptionRequest> = {
  bonusHp: 0,
  bonusPercenthp: 0,
  bonusMp: 0,
  bonusPercentmp: 0,
  bonusAtk: 0,
  bonusPercentatk: 0,
  bonusPercentdf: 0,
  bonusDf: 0,
  bonusPercentatkskill: 0,
  bonusDefskill: 0,
  bonusQigong: 0,
  bonusDropgold: 0,
  bonusExp: 0,
  bonusLucky: 0,
  bonusAccuracy: 0,
  bonusEvasion: 0,
  bonusDiemhoangkim: 0,
  bonusAtkmonster: 0,
  bonusDefmonster: 0
};
