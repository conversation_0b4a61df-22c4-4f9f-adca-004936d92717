// Pill Management Types

export interface Pill {
  id: number;
  pillId: number;
  pillName?: string;
  levelUse?: string;
  bonusHp: number;
  bonusHppercent: number;
  bonusMp: number;
  bonusMppercent: number;
  bonusAtk: number;
  bonusAtkpercent: number;
  bonusDf: number;
  bonusDfpercent: number;
  bonusEvasion: number;
  bonusEvapercent: number;
  bonusAccuracy: number;
  bonusAccupercent: number;
  bonusAtkskillpercent: number;
  bonusDfskill: number;
  bonusDfskillpercent: number;
  bonusAbilities: number;
  bonusLucky: number;
  bonusGoldpercent: number;
  bonusDroppercent: number;
  bonusExppercent: number;
  upgradeWeapon: number;
  upgradeArmor: number;
  pillTime: number;
  pillDays: number;
  publicPill: number;
  pillMerge: number;
  cantUse?: string;
  onOff: number;
  hatchItem: number;
  bonusDiemhoangkim: number;
  tanghoa: number;
}

export interface PillFilter {
  search?: string;
  pillId?: number;
  levelUse?: string;
  onOff?: number;
  publicPill?: number;
  sortBy?: keyof Pill;
  sortOrder?: 'asc' | 'desc';
}

export interface PillPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PillListResponse {
  success: boolean;
  message: string;
  data: {
    pills: Pill[];
    pagination: PillPagination;
  };
}

export interface PillResponse {
  success: boolean;
  message: string;
  data?: Pill;
}

export interface CreatePillRequest {
  pillId: number;
  pillName?: string;
  levelUse?: string;
  bonusHp?: number;
  bonusHppercent?: number;
  bonusMp?: number;
  bonusMppercent?: number;
  bonusAtk?: number;
  bonusAtkpercent?: number;
  bonusDf?: number;
  bonusDfpercent?: number;
  bonusEvasion?: number;
  bonusEvapercent?: number;
  bonusAccuracy?: number;
  bonusAccupercent?: number;
  bonusAtkskillpercent?: number;
  bonusDfskill?: number;
  bonusDfskillpercent?: number;
  bonusAbilities?: number;
  bonusLucky?: number;
  bonusGoldpercent?: number;
  bonusDroppercent?: number;
  bonusExppercent?: number;
  upgradeWeapon?: number;
  upgradeArmor?: number;
  pillTime?: number;
  pillDays?: number;
  publicPill?: number;
  pillMerge?: number;
  cantUse?: string;
  onOff?: number;
  hatchItem?: number;
  bonusDiemhoangkim?: number;
  tanghoa?: number;
}

export interface UpdatePillRequest extends Partial<CreatePillRequest> {
  id?: number;
}

// Pill field labels for UI display
export const PILL_FIELD_LABELS: { [key in keyof Pill]?: string } = {
  id: 'ID',
  pillId: 'Pill ID',
  pillName: 'Tên Pill',
  levelUse: 'Level sử dụng',
  bonusHp: 'Bonus HP',
  bonusHppercent: 'Bonus HP %',
  bonusMp: 'Bonus MP',
  bonusMppercent: 'Bonus MP %',
  bonusAtk: 'Bonus ATK',
  bonusAtkpercent: 'Bonus ATK %',
  bonusDf: 'Bonus DEF',
  bonusDfpercent: 'Bonus DEF %',
  bonusEvasion: 'Bonus Evasion',
  bonusEvapercent: 'Bonus Evasion %',
  bonusAccuracy: 'Bonus Accuracy',
  bonusAccupercent: 'Bonus Accuracy %',
  bonusAtkskillpercent: 'Bonus ATK Skill %',
  bonusDfskill: 'Bonus DEF Skill',
  bonusDfskillpercent: 'Bonus DEF Skill %',
  bonusAbilities: 'Bonus Abilities',
  bonusLucky: 'Bonus Lucky',
  bonusGoldpercent: 'Bonus Gold %',
  bonusDroppercent: 'Bonus Drop %',
  bonusExppercent: 'Bonus EXP %',
  upgradeWeapon: 'Upgrade Weapon',
  upgradeArmor: 'Upgrade Armor',
  pillTime: 'Thời gian Pill',
  pillDays: 'Số ngày Pill',
  publicPill: 'Public Pill',
  pillMerge: 'Pill Merge',
  cantUse: 'Không thể sử dụng',
  onOff: 'Bật/Tắt',
  hatchItem: 'Hatch Item',
  bonusDiemhoangkim: 'Bonus Điểm Hoàng Kim',
  tanghoa: 'Tăng Hoa'
};

// Pill field categories for organized display
export const PILL_FIELD_CATEGORIES = {
  basic: {
    label: 'Thông tin cơ bản',
    fields: ['pillId', 'pillName', 'levelUse', 'onOff'] as (keyof Pill)[]
  },
  stats: {
    label: 'Chỉ số cơ bản',
    fields: ['bonusHp', 'bonusHppercent', 'bonusMp', 'bonusMppercent', 'bonusAtk', 'bonusAtkpercent', 'bonusDf', 'bonusDfpercent'] as (keyof Pill)[]
  },
  combat: {
    label: 'Chỉ số chiến đấu',
    fields: ['bonusEvasion', 'bonusEvapercent', 'bonusAccuracy', 'bonusAccupercent', 'bonusAtkskillpercent', 'bonusDfskill', 'bonusDfskillpercent'] as (keyof Pill)[]
  },
  special: {
    label: 'Chỉ số đặc biệt',
    fields: ['bonusAbilities', 'bonusLucky', 'bonusGoldpercent', 'bonusDroppercent', 'bonusExppercent', 'bonusDiemhoangkim'] as (keyof Pill)[]
  },
  config: {
    label: 'Cấu hình',
    fields: ['pillTime', 'pillDays', 'publicPill', 'pillMerge', 'upgradeWeapon', 'upgradeArmor', 'hatchItem', 'tanghoa', 'cantUse'] as (keyof Pill)[]
  }
};

// Sortable fields for UI
export const SORTABLE_PILL_FIELDS: (keyof Pill)[] = [
  'id',
  'pillId', 
  'pillName',
  'levelUse',
  'bonusHp',
  'bonusAtk',
  'bonusDf',
  'pillTime',
  'pillDays',
  'onOff'
];

// Default pill values for creation
export const DEFAULT_PILL_VALUES: Partial<CreatePillRequest> = {
  bonusHp: 0,
  bonusHppercent: 0,
  bonusMp: 0,
  bonusMppercent: 0,
  bonusAtk: 0,
  bonusAtkpercent: 0,
  bonusDf: 0,
  bonusDfpercent: 0,
  bonusEvasion: 0,
  bonusEvapercent: 0,
  bonusAccuracy: 0,
  bonusAccupercent: 0,
  bonusAtkskillpercent: 0,
  bonusDfskill: 0,
  bonusDfskillpercent: 0,
  bonusAbilities: 0,
  bonusLucky: 0,
  bonusGoldpercent: 0,
  bonusDroppercent: 0,
  bonusExppercent: 0,
  upgradeWeapon: 0,
  upgradeArmor: 0,
  pillTime: 0,
  pillDays: 0,
  publicPill: 0,
  pillMerge: 0,
  onOff: 1,
  hatchItem: 0,
  bonusDiemhoangkim: 0,
  tanghoa: 0
};
