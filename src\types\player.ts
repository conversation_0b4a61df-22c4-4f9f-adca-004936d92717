// Player Detail Types

export interface GetPlayerDetailRequest {
  characterName: string;
  serverId: number;
  clusterId: number;
}

export interface PartyInfo {
  teamId: number;
  teamName: string;
  memberCount: number;
  isLeader: boolean;
  members?: PartyMember[];
}

export interface PartyMember {
  characterName: string;
  level: number;
  job: string;
  isOnline: boolean;
  isLeader: boolean;
}

export interface DetailedPlayerInfoFull {
  characterName: string;
  level: number;
  job: string;
  jobName: string;
  mapId: number;
  mapName: string;
  posX: number;
  posY: number;
  hp: number;
  mp: number;
  experience: number;
  money: number;
  bankMoney: number;
  pkPoint: number;
  offTrade: number;
  isOnline: boolean;
  sessionId: string;
  ipAddress: string;
  loginTime: string;
  lastActivity: string;
  serverId: number;
  clusterId: number;
  wuXun: number;
  qigongPoint: number;
  fightExp: number;
  gmMode: boolean;
  partyInfo?: PartyInfo;
  atk: number;
  def: number;
  matk: number;
  mdef: number;
  eva: number;
  acc: number;
  maxHp: number;
  maxMp: number;
  maxSp: number;
  jobLevel: number;
  wearItems: ItemInfo[];
  subWearItems: ItemInfo[];
  thirdWearItems: ItemInfo[];
  inventoryItems: ItemInfo[];
  personalWarehouse: ItemInfo[];
  publicWarehouse: ItemInfo[];
  skills: SkillInfo[];
  abilities: AbilityInfo[];
  ascAbilities: AbilityInfo[];
  antiAbilities: AbilityInfo[];
  sp: number;
}

export interface ItemInfo {
  globalId : number;
  itemId: number;
  quantity : number;
  itemOption: number;
  itemMagic1: number;
  itemMagic2: number;
  itemMagic3: number;
  itemMagic4: number;
  mediumSoul: number;
  day1: number;
  day2: number;
  lowSoul: number;
  quality: number;
  beast: number;
  lock: number;
  position?: number;
}

export interface SkillInfo {
  skillId: number;
  skillName: string;
  currentLevel: number;
  maxLevel: number;
  experience: number;
  skillType: number;
}

export interface AbilityInfo {
  abilityId: number;
  abilityName: string;
  currentLevel: number;
  maxLevel: number;
  points: number;
  abilityType: number;
}

// Item Management Types
export interface ItemManagementRequest {
  characterName: string;
  clusterId: number;
  channelId: number;
  actionType: 'create' | 'edit';
  deliveryMethod: 'mail' | 'direct';
  bagType?: number; // 0 = Item_Wear, 1 = Item_In_Bag
  slotPosition?: number;
  createNewSeries?: boolean;
  itemInfo: ItemCreationInfo;
}

export interface ItemCreationInfo {
  itemId: number;
  quantity: number;
  magic0: number;
  magic1: number;
  magic2: number;
  magic3: number;
  magic4: number;
  lowSoul: number;
  mediumSoul: number;
  tuLinh: number;
  day1: number;
  day2: number;
  tienHoa: number;
}

export interface ItemManagementResponse {
  success: boolean;
  message: string;
  deliveryMethod?: string;
  playerOnline?: boolean;
}


export interface GetPlayerDetailResponse {
  success: boolean;
  message: string;
  player?: DetailedPlayerInfoFull;
}

// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  timestamp?: string;
}
