// Template Management Types

export interface TemplateNPC {
  fldIndex?: number;
  fldPid: number;
  fldName: string;
  fldX?: number;
  fldZ?: number;
  fldY?: number;
  fldFace0?: number;
  fldFace?: number;
  fldMid?: number;
  fldHp?: number;
  fldAt?: number;
  fldDf?: number;
  fldNpc?: number;
  fldNewtime?: number;
  fldLevel?: number;
  fldExp?: number;
  fldAuto?: number;
  fldBoss?: number;
}

export interface TemplateShopItem {
  id?: number;
  fldNpcname?: string;
  fldNid: number; // NPC ID
  fldIndex: number; // Position in template (0-59 for page 1, 60-119 for page 2, etc.)
  fldPid: number; // Item ID
  fldMoney: number; // Price
  fldMagic0?: number;
  fldMagic1?: number;
  fldMagic2?: number;
  fldMagic3?: number;
  fldMagic4?: number;
  fldCanvohuan?: number;
  fldDays: number;
  fldBd?: number;
}

export interface TemplateItem {
  fldPid: number;
  fldName: string;
  fldReside1?: number; // Job requirement 1
  fldReside2?: number; // Job requirement 2
  fldSex?: number; // Gender requirement
  fldLevel?: number; // Level requirement
  fldUpLevel?: number;
  fldRecycleMoney?: number;
  fldSaleMoney?: number;
  fldQuestitem?: number;
  fldNj?: number;
  fldDf?: number;
  fldAt1?: number;
  fldAt2?: number;
  fldAp?: number;
  fldJobLevel?: number; // Job level requirement
  fldZx?: number;
  fldEl?: number;
  fldWx?: number;
  fldWxjd?: number;
  fldMoney?: number;
  fldWeight?: number;
  fldType?: number;
  fldNeedMoney?: number;
  fldNeedFightexp?: number;
  fldMagic1?: number;
  fldMagic2?: number;
  fldMagic3?: number;
  fldMagic4?: number;
  fldMagic5?: number;
  fldSide?: number;
  fldSellType?: number;
  fldLock?: number;
  fldSeries?: number;
  fldIntegration?: number;
  fldDes?: string;
  fldHeadWear?: number;
}

export interface TemplatePage {
  pageNumber: number;
  items: (TemplateShopItem | null)[]; // 60 slots per page, null for empty slots
}

export interface TemplateData {
  npc: TemplateNPC;
  pages: TemplatePage[];
  totalPages: number;
}

// Filter types for template item selection
export interface TemplateItemFilter {
  name?: string;
  fldReside1?: number;
  fldReside2?: number;
  minLevel?: number;
  maxLevel?: number;
  job?: number;
  minJobLevel?: number;
  maxJobLevel?: number;
  fldType?: number;
}

// Job mappings
export const JOB_NAMES: { [key: number]: string } = {
  1: 'Đao khách',
  2: 'Kiếm khách', 
  3: 'Thương khách',
  4: 'Cung thủ',
  5: 'Đại phu',
  6: 'Thích khách',
  7: 'Cầm Sư',
  8: 'HBQ',
  9: 'DHL',
  10: 'QS',
  11: 'Diệu yến',
  12: 'Tử hào',
  13: 'Thần nữ'
};

// Item type mappings
export const ITEM_TYPE_NAMES: { [key: number]: string } = {
  1: 'Vũ khí',
  2: 'Áo giáp',
  3: 'Mũ',
  4: 'Giày',
  5: 'Găng tay',
  6: 'Trang sức',
  7: 'Thuốc',
  8: 'Nguyên liệu',
  9: 'Khác'
};

// Template management operations
export interface TemplateOperation {
  type: 'add' | 'update' | 'delete' | 'move';
  npcId: number;
  slotIndex: number;
  item?: TemplateShopItem;
  targetSlotIndex?: number; // For move operations
}

// API Response types
export interface TemplateListResponse {
  success: boolean;
  message: string;
  data?: {
    templates: TemplateData[];
    total: number;
  };
}

export interface TemplateDetailResponse {
  success: boolean;
  message: string;
  data?: TemplateData;
}

export interface TemplateItemListResponse {
  success: boolean;
  message: string;
  data?: {
    items: TemplateItem[];
    total: number;
  };
}

export interface TemplateNPCListResponse {
  success: boolean;
  message: string;
  data?: {
    npcs: TemplateNPC[];
    total: number;
  };
}

// Constants
export const TEMPLATE_SLOTS_PER_PAGE = 60;
export const TEMPLATE_SLOTS_PER_ROW = 6;
export const TEMPLATE_ROWS_PER_PAGE = 10;
