// YBQ (Quest) Data Types
// Tương ứng với cấu trúc trong Go project

/**
 * LenData chứa thông tin về một số nguyên đọc được từ dữ liệu
 */
export interface LenData {
  value: number;           // Gi<PERSON> trị số nguyên
  rawBytes?: number[];     // Dữ liệu gốc dạng byte
  rawString?: string;      // Dữ liệu gốc dạng chuỗi
  delimiter?: number;      // Ký tự kết thúc (32: space hoặc 10: newline)
  prefixData?: number[];   // Dữ liệu prefix (nếu có)
  suffixData?: number[];   // Dữ liệu suffix (nếu có)
}

/**
 * StringData chứa thông tin về một chuỗi đọc được từ dữ liệu
 */
export interface StringData {
  value: string;           // Giá trị chuỗi
  length: number;          // Độ dài của chuỗi
  lengthData: LenData;     // Thông tin về độ dài
  rawBytes?: number[];     // Dữ liệu gốc dạng byte
  prefixData?: number[];   // Dữ liệu prefix (nếu có)
  suffixData?: number[];   // Dữ liệu suffix (nếu có)
  hasNewlineInContent?: boolean; // Flag để đánh dấu có byte 0x0A trong nội dung (chỉ ra có thể nhầm kiểu dữ liệu)
}

/**
 * Coords chứa thông tin về tọa độ
 */
export interface Coords {
  mapID: LenData;
  coordsX: LenData;
  coordsY: LenData;
  coordsZ: LenData;
}

/**
 * QuestRequiredItem chứa thông tin về vật phẩm yêu cầu để hoàn thành nhiệm vụ
 */
export interface QuestRequiredItem {
  itemID: LenData;
  itemAmount: LenData;
  mapID: LenData;
  coordsX: LenData;
  coordsY: LenData;
  coordsZ: LenData;
}

/**
 * QuestRewardItem chứa thông tin về phần thưởng của nhiệm vụ
 */
export interface QuestRewardItem {
  itemID: LenData;
  itemAmount: LenData;
}

/**
 * QuestStage chứa thông tin về một giai đoạn của nhiệm vụ
 */
export interface QuestStage {
  content: StringData;
  npcID: LenData;
  npcUnknown1: LenData;
  npcMapID: LenData;
  npcCoordsX: LenData;
  npcCoordsY: LenData;
  npcCoordsZ: LenData;
  requiredItems: QuestRequiredItem[];
  conditionMatchPrompt1: StringData;
  conditionMatchPrompt2: StringData;
  conditionMatchPrompt3: StringData;
  conditionMatchPrompt4: StringData;
  conditionMatchPrompt5: StringData;
  conditionNoMatchPrompt1: StringData;
  conditionNoMatchPrompt2: StringData;
  conditionNoMatchPrompt3: StringData;
  conditionNoMatchPrompt4: StringData;
  conditionNoMatchPrompt5: StringData;
}

/**
 * Quest chứa thông tin về một nhiệm vụ
 */
export interface Quest {
  questID: LenData;
  questName: StringData;
  questLevel: LenData;
  unknown1: LenData;
  unknown2: LenData;
  unknown3: LenData;
  unknown4: LenData;
  unknown5: LenData;
  unknown6: LenData;
  unknown7: LenData;
  unknown8: LenData;
  unknown9: LenData;
  unknown10: LenData;
  unknown11: LenData;
  unknown12?: LenData;
  unknown13?: LenData;
  unknown14?: LenData;
  unknown15?: LenData;
  unknown16?: LenData;
  unknown17?: LenData;
  unknown18?: LenData;
  unknown19?: LenData;
  unknown20?: LenData;
  questAccept0: StringData;
  questAccept1: StringData;
  questAccept2: StringData;
  questRefuse1: StringData;
  questRefuse2: StringData;
  welcomeAcceptPrompt1: StringData;
  welcomeAcceptPrompt2: StringData;
  welcomeAcceptPrompt3: StringData;
  welcomeAcceptPrompt4: StringData;
  welcomeAcceptPrompt5: StringData;
  welcomeRefusePrompt1: StringData;
  welcomeRefusePrompt2: StringData;
  welcomeRefusePrompt3: StringData;
  welcomeRefusePrompt4: StringData;
  welcomeRefusePrompt5: StringData;
  questStageNumber: LenData;
  npcID: LenData;
  npcUnknown1: LenData;
  npcCoords: Coords;
  questStages: QuestStage[];
  requiredItems: QuestRequiredItem[];
  rewardItems: QuestRewardItem[];
  footerExtend: StringData;
  offset?: number;
}

/**
 * YBQ File Data Structure
 */
export interface YbqData {
  sign: string;
  signEx: string;
  encrypted: number[];
  decrypted: number[];
  loaded: boolean;
  totalQuest: number;
  quests: Record<number, Quest>;
}

/**
 * API Request/Response Types
 */
export interface ParseYbqRequest {
  fileData: string; // Base64 encoded file data
  fileName: string;
}

export interface ParseYbqResponse {
  success: boolean;
  message: string;
  data?: YbqData;
}

export interface ExportYbqRequest {
  data: YbqData;
  format: 'ybq' | 'json' | 'decrypt';
}

export interface ExportYbqResponse {
  success: boolean;
  message: string;
  fileData?: string; // Base64 encoded file data
  fileName?: string;
}

export interface QuestListItem {
  id: number;
  name: string;
  level: number;
  stageCount: number;
  rewardCount: number;
  requiredItemCount: number;
}

export interface GetQuestsResponse {
  success: boolean;
  message: string;
  data?: QuestListItem[];
}

export interface GetQuestResponse {
  success: boolean;
  message: string;
  data?: Quest;
}

export interface UpdateQuestRequest {
  questId: number;
  quest: Quest;
}

export interface UpdateQuestResponse {
  success: boolean;
  message: string;
  data?: Quest;
}

export interface CreateQuestRequest {
  quest: Omit<Quest, 'questID'>;
}

export interface CreateQuestResponse {
  success: boolean;
  message: string;
  data?: Quest;
}

export interface DeleteQuestRequest {
  questId: number;
}

export interface DeleteQuestResponse {
  success: boolean;
  message: string;
}

/**
 * Helper Types
 */
export type QuestFormData = {
  // Basic Info
  questName: string;
  questLevel: number;
  
  // Unknown fields
  unknown1: number;
  unknown2: number;
  unknown3: number;
  unknown4: number;
  unknown5: number;
  unknown6: number;
  unknown7: number;
  unknown8: number;
  unknown9: number;
  unknown10: number;
  unknown11: number;
  unknown12?: number;
  unknown13?: number;
  unknown14?: number;
  unknown15?: number;
  unknown16?: number;
  unknown17?: number;
  unknown18?: number;
  unknown19?: number;
  unknown20?: number;
  
  // Dialog
  questAccept0: string;
  questAccept1: string;
  questAccept2: string;
  questRefuse1: string;
  questRefuse2: string;
  
  // Welcome prompts
  welcomeAcceptPrompt1: string;
  welcomeAcceptPrompt2: string;
  welcomeAcceptPrompt3: string;
  welcomeAcceptPrompt4: string;
  welcomeAcceptPrompt5: string;
  welcomeRefusePrompt1: string;
  welcomeRefusePrompt2: string;
  welcomeRefusePrompt3: string;
  welcomeRefusePrompt4: string;
  welcomeRefusePrompt5: string;
  
  // NPC Info
  npcID: number;
  npcUnknown1: number;
  npcMapID: number;
  npcCoordsX: number;
  npcCoordsY: number;
  npcCoordsZ: number;
  
  // Footer
  footerExtend: string;
};

/**
 * Constants
 */
export const ENCRYPTION_KEY_SIZE = 256;
